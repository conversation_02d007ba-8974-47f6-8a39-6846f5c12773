'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Button from '../ui/Button';

export default function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className={`navbar bg-white px-4 md:px-20 py-4 shadow-sm fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled ? 'bg-white/95 backdrop-blur-sm' : 'bg-white'
    }`}>
      <div className="flex-1">
        <div className="flex items-center">
          <svg
            width="44"
            height="44"
            viewBox="0 0 500 500"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="mr-2"
          >
            <g transform="translate(125, 100) scale(0.55)">
              <path d="M248 45C319.36 45 439.36 142.56 439.36 203C439.36 236.64 392.68 264 355.28 264C313.96 264 168.4 264 140.72 264C103.32 264 56.64 236.64 56.64 203C56.64 142.56 176.64 45 248 45Z" fill="#F4BB45"/>
              <path d="M248 506C176.64 506 56.64 408.44 56.64 348C56.64 314.36 103.32 287 140.72 287C182.04 287 327.6 287 355.28 287C392.68 287 439.36 314.36 439.36 348C439.36 408.44 319.36 506 248 506Z" fill="#4E9E97"/>
              <circle cx="248" cy="264" r="50" fill="#4E9E97"/>
            </g>
          </svg>
          <motion.a 
            className="text-2xl font-bold"
            whileHover={{ scale: 1.05 }}
          >
            <span className="text-primary">Break</span>
            <span className="text-secondary">time</span>
          </motion.a>
        </div>
      </div>
      <div className="flex-none">
        <ul className="menu menu-horizontal px-1 hidden lg:flex">
          {['Layanan', 'Destinasi', 'Tentang', 'Testimoni'].map((item, index) => (
            <li key={index}>
              <motion.a 
                className="text-gray-600 font-medium hover:text-primary"
                whileHover={{ scale: 1.05 }}
              >
                {item}
              </motion.a>
            </li>
          ))}
        </ul>
        <Button variant="outline" size="sm" className="mr-2">Login</Button>
        <Button variant="primary" size="sm">Daftar</Button>
        <div className="dropdown dropdown-end lg:hidden ml-2">
          <label tabIndex={0} className="btn btn-ghost">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16m-7 6h7" /></svg>
          </label>
          <ul tabIndex={0} className="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
            <li><a>Layanan</a></li>
            <li><a>Destinasi</a></li>
            <li><a>Tentang</a></li>
            <li><a>Testimoni</a></li>
          </ul>
        </div>
      </div>
    </div>
  );
}
