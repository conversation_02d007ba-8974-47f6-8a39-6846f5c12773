import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { logService } from '@/lib/logger';
import { getUserIdFromToken } from '@/lib/auth-utils';

// GET semua layanan
export async function GET(request: NextRequest) {
  try {
    // Ambil URL parameters
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');
    const category = searchParams.get('category');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const includeInactive = searchParams.get('includeInactive') === 'true';

    // Build query
    const where: Record<string, any> = {};

    // Filter berdasarkan status aktif (kecuali jika includeInactive=true)
    if (!includeInactive) {
      where.isActive = true;
    }

    // Filter berdasarkan outlet jika ada
    if (outletId) {
      where.outlets = {
        some: {
          outletId: outletId
        }
      };
    }

    // Filter berdasarkan kategori jika ada
    if (category) {
      where.category = {
        equals: category
      };
    }

    // Filter berdasarkan harga
    if (minPrice || maxPrice) {
      where.price = {};

      if (minPrice) {
        where.price.gte = parseInt(minPrice);
      }

      if (maxPrice) {
        where.price.lte = parseInt(maxPrice);
      }
    }

    // Ambil semua layanan dari database (baik yang aktif maupun tidak aktif berdasarkan filter)
    const services = await prisma.service.findMany({
      where,
      include: {
        outlets: {
          include: {
            outlet: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      },
      orderBy: [
        { isActive: 'desc' }, // Tampilkan layanan aktif terlebih dahulu, kemudian yang nonaktif
        { price: 'asc' } // Layanan dengan harga terendah ditampilkan terlebih dahulu
      ]
    });

    // Jika tidak ada layanan, kembalikan array kosong (bukan 404)
    if (services.length === 0) {
      return NextResponse.json({
        message: 'Tidak ada layanan yang tersedia',
        services: []
      });
    }

    // Kembalikan data layanan
    return NextResponse.json({
      message: 'Data layanan berhasil diambil',
      services
    });
  } catch (error: unknown) {
    console.error('Error fetching services:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data layanan' },
      { status: 500 }
    );
  }
}

// POST untuk membuat layanan baru
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, duration, price, commission, outletIds, isActive } = body;

    // Validasi input dasar
    if (!name || !duration || price === undefined || price === null || !outletIds || !outletIds.length) {
      return NextResponse.json(
        { error: 'Nama, durasi, harga, dan minimal satu outlet diperlukan' },
        { status: 400 }
      );
    }

    // Validasi harga tidak boleh negatif
    if (price < 0) {
      return NextResponse.json(
        { error: 'Harga tidak boleh negatif' },
        { status: 400 }
      );
    }
    // Validasi tipe data (opsional tapi bagus)
    if (typeof duration !== 'number' || typeof price !== 'number') {
      return NextResponse.json(
        { error: 'Durasi dan Harga harus berupa angka' },
        { status: 400 }
      );
    }

    // Cek outlets
    const outlets = await prisma.outlet.findMany({
      where: {
        id: { in: outletIds }
      }
    });

    if (outlets.length !== outletIds.length) {
      return NextResponse.json({ error: 'Satu atau beberapa outlet tidak ditemukan' }, { status: 404 });
    }

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Buat layanan baru dengan relasi ke outlets
    const newService = await prisma.service.create({
      data: {
        name,
        description: description || null,
        duration,
        price,
        commission: commission || 0,
        isActive: isActive !== undefined ? isActive : true,
        outlets: {
          create: outletIds.map(outletId => ({
            outletId
          }))
        }
      },
      include: {
        outlets: {
          include: {
            outlet: true
          }
        }
      }
    });

    // Log pembuatan layanan baru
    await logService(
      'create',
      newService.id,
      {
        name: newService.name,
        description: newService.description,
        duration: newService.duration,
        price: newService.price,
        commission: newService.commission,
        isActive: newService.isActive,
        outlets: newService.outlets.map(o => o.outlet.name).join(', ')
      },
      null, // Tidak ada outletId spesifik karena bisa multiple
      userId // Gunakan userId dari token
    );

    return NextResponse.json({ message: 'Layanan berhasil dibuat', service: newService }, { status: 201 });

  } catch (error: unknown) {
    console.error('Error creating service:', error);
    // Ambil pesan error yang lebih spesifik jika mungkin
    let errorMessage = 'Terjadi kesalahan saat membuat layanan';
    if (error instanceof Error) {
      errorMessage = error.message;
      // Cek jika ini validation error dari Prisma
      if ('code' in error && error.code === 'P2002') { // Contoh cek unique constraint
        errorMessage = 'Layanan dengan nama/detail ini mungkin sudah ada.';
      } else if ('message' in error && error.message.includes('Unknown argument')) {
        errorMessage = 'Data yang dikirim tidak sesuai dengan skema.'; // Pesan lebih umum
      }
    }
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}