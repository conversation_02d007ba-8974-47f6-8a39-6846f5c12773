import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { logTransaction } from '@/lib/logger';

// PUT untuk mengupdate komisi terapis pada transaksi tertentu
export async function PUT(
  request: Request,
  { params }: { params: { id: string } } // id di sini adalah transactionId
) {
  try {
    // Pastikan params.id sudah diawait
    const { id } = params;

    // Validasi transactionId
    if (!id) {
      return NextResponse.json(
        { error: 'ID Transaksi diperlukan' },
        { status: 400 }
      );
    }

    // Parse body request
    let body;
    try {
      body = await request.json();
    } catch (e) {
      console.error("Error parsing JSON body:", e);
      return NextResponse.json(
        { error: 'Format JSON tidak valid' },
        { status: 400 }
      );
    }

    const { commission } = body || {};

    // Validasi nilai komisi
    if (commission === undefined || commission === null) {
      return NextResponse.json(
        { error: 'Nilai komisi harus diisi' },
        { status: 400 }
      );
    }

    // Pastikan nilai komisi adalah angka
    const commissionValue = Number(commission);
    if (isNaN(commissionValue)) {
      return NextResponse.json(
        { error: 'Nilai komisi harus berupa angka' },
        { status: 400 }
      );
    }

    // Pastikan nilai komisi tidak negatif
    if (commissionValue < 0) {
      return NextResponse.json(
        { error: 'Nilai komisi tidak boleh negatif' },
        { status: 400 }
      );
    }

    // Cek apakah transaksi ada
    let existingTransaction;

    // Coba cari berdasarkan displayId (string) terlebih dahulu
    if (id.startsWith('TR')) {
      existingTransaction = await prisma.transaction.findUnique({
        where: {
          displayId: id,
        },
        include: {
          customer: true,
          therapist: true,
        }
      });
    } else {
      // Jika bukan format TR, coba parse sebagai integer
      try {
        const numericId = parseInt(id, 10);
        if (!isNaN(numericId)) {
          existingTransaction = await prisma.transaction.findUnique({
            where: {
              id: numericId,
            },
            include: {
              customer: true,
              therapist: true,
            }
          });
        }
      } catch (e) {
        console.error('Error parsing transaction ID:', e);
      }
    }

    if (!existingTransaction) {
      return NextResponse.json(
        { error: 'Transaksi tidak ditemukan' },
        { status: 404 }
      );
    }

    // Update komisi terapis pada transaksi
    let updatedTransaction;

    try {
      // Update berdasarkan ID yang sesuai (numeric atau displayId)
      if (existingTransaction.id && typeof existingTransaction.id === 'number') {
        updatedTransaction = await prisma.transaction.update({
          where: {
            id: existingTransaction.id,
          },
          data: {
            therapistCommissionEarned: commissionValue,
          },
          include: {
            customer: true,
            therapist: true,
          }
        });
      } else if (existingTransaction.displayId) {
        updatedTransaction = await prisma.transaction.update({
          where: {
            displayId: existingTransaction.displayId,
          },
          data: {
            therapistCommissionEarned: commissionValue,
          },
          include: {
            customer: true,
            therapist: true,
          }
        });
      }
    } catch (updateError) {
      console.error('Error updating transaction commission:', updateError);
      return NextResponse.json(
        { error: 'Gagal mengupdate komisi transaksi' },
        { status: 500 }
      );
    }

    // Log komisi yang diupdate
    // Komisi berhasil diupdate

    // Log update komisi transaksi
    const transactionId = existingTransaction.id || id;
    await logTransaction(
      'update',
      transactionId,
      {
        previousCommission: existingTransaction.therapistCommissionEarned,
        newCommission: commissionValue,
        customerName: existingTransaction.customer?.name,
        therapistName: existingTransaction.therapist?.name
      },
      existingTransaction.outletId,
      null // Gunakan userId dari request jika tersedia, atau null jika tidak
    );

    return NextResponse.json({
      message: 'Komisi terapis berhasil diupdate',
      transaction: updatedTransaction || {
        ...existingTransaction,
        therapistCommissionEarned: commissionValue
      },
    });

  } catch (error) {
    console.error('Error updating therapist commission:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengupdate komisi terapis' },
      { status: 500 }
    );
  }
}