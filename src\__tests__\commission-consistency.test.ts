import { calculateTherapistCommission, validateCommissionConsistency, ServiceCommissionData, TherapistSpecialCommission } from '@/lib/commission-utils';

describe('Commission Consistency Tests', () => {
  describe('Booking vs Walk-in Consistency', () => {
    const mockServiceData: ServiceCommissionData[] = [
      { serviceId: 'service1', quantity: 2, defaultCommission: 50000 },
      { serviceId: 'service2', quantity: 1, defaultCommission: 75000 },
      { serviceId: 'service3', quantity: 3, defaultCommission: 30000 }
    ];

    const mockSpecialCommissions: TherapistSpecialCommission[] = [
      { serviceId: 'service1', commission: 60000 } // Special commission for service1
    ];

    test('should calculate same commission for booking and walk-in with identical data', () => {
      // Simulate booking data
      const bookingCommission = calculateTherapistCommission(mockServiceData, mockSpecialCommissions);
      
      // Simulate walk-in data (same services, same quantities)
      const walkinCommission = calculateTherapistCommission(mockServiceData, mockSpecialCommissions);
      
      expect(bookingCommission).toBe(walkinCommission);
      expect(bookingCommission).toBe(285000); // (60000*2) + (75000*1) + (30000*3)
    });

    test('should validate commission consistency correctly', () => {
      const expectedCommission = 285000;
      const isConsistent = validateCommissionConsistency(
        mockServiceData,
        mockSpecialCommissions,
        expectedCommission
      );
      
      expect(isConsistent).toBe(true);
    });

    test('should detect commission inconsistency', () => {
      const wrongCommission = 300000; // Incorrect amount
      const isConsistent = validateCommissionConsistency(
        mockServiceData,
        mockSpecialCommissions,
        wrongCommission
      );
      
      expect(isConsistent).toBe(false);
    });

    test('should handle services with zero commission', () => {
      const dataWithZeroCommission: ServiceCommissionData[] = [
        { serviceId: 'service1', quantity: 2, defaultCommission: 50000 },
        { serviceId: 'service2', quantity: 1, defaultCommission: 0 }, // Zero commission
        { serviceId: 'service3', quantity: 3, defaultCommission: 30000 }
      ];

      const commission = calculateTherapistCommission(dataWithZeroCommission, []);
      expect(commission).toBe(190000); // (50000*2) + (0*1) + (30000*3)
    });

    test('should prioritize therapist special commission over default', () => {
      const specialCommissions: TherapistSpecialCommission[] = [
        { serviceId: 'service1', commission: 80000 }, // Higher than default
        { serviceId: 'service2', commission: 40000 }  // Lower than default
      ];

      const commission = calculateTherapistCommission(mockServiceData, specialCommissions);
      // (80000*2) + (40000*1) + (30000*3) = 160000 + 40000 + 90000 = 290000
      expect(commission).toBe(290000);
    });

    test('should handle empty service data', () => {
      const commission = calculateTherapistCommission([], []);
      expect(commission).toBe(0);
    });

    test('should handle missing therapist special commissions', () => {
      const commission = calculateTherapistCommission(mockServiceData, []);
      // Use all default commissions: (50000*2) + (75000*1) + (30000*3)
      expect(commission).toBe(265000);
    });
  });

  describe('Edge Cases', () => {
    test('should handle fractional quantities correctly', () => {
      const fractionalData: ServiceCommissionData[] = [
        { serviceId: 'service1', quantity: 1.5, defaultCommission: 100000 }
      ];

      const commission = calculateTherapistCommission(fractionalData, []);
      expect(commission).toBe(150000); // 100000 * 1.5
    });

    test('should handle large numbers correctly', () => {
      const largeData: ServiceCommissionData[] = [
        { serviceId: 'service1', quantity: 100, defaultCommission: 1000000 }
      ];

      const commission = calculateTherapistCommission(largeData, []);
      expect(commission).toBe(100000000); // 1000000 * 100
    });

    test('should handle negative quantities as zero', () => {
      const negativeData: ServiceCommissionData[] = [
        { serviceId: 'service1', quantity: -1, defaultCommission: 50000 },
        { serviceId: 'service2', quantity: 2, defaultCommission: 30000 }
      ];

      const commission = calculateTherapistCommission(negativeData, []);
      // Negative quantity should be treated as 0, so only service2 contributes
      expect(commission).toBe(60000); // 0 + (30000*2)
    });
  });

  describe('Real-world Scenarios', () => {
    test('should handle complex booking with multiple same services', () => {
      // Simulate a booking where same service is booked multiple times
      const complexBookingData: ServiceCommissionData[] = [
        { serviceId: 'massage', quantity: 3, defaultCommission: 75000 },
        { serviceId: 'facial', quantity: 2, defaultCommission: 50000 },
        { serviceId: 'manicure', quantity: 1, defaultCommission: 25000 }
      ];

      const specialCommissions: TherapistSpecialCommission[] = [
        { serviceId: 'massage', commission: 85000 } // Therapist gets higher commission for massage
      ];

      const commission = calculateTherapistCommission(complexBookingData, specialCommissions);
      // (85000*3) + (50000*2) + (25000*1) = 255000 + 100000 + 25000 = 380000
      expect(commission).toBe(380000);

      // Validate consistency
      const isConsistent = validateCommissionConsistency(
        complexBookingData,
        specialCommissions,
        commission
      );
      expect(isConsistent).toBe(true);
    });

    test('should handle walk-in transaction with mixed services', () => {
      const walkinData: ServiceCommissionData[] = [
        { serviceId: 'quickMassage', quantity: 1, defaultCommission: 40000 },
        { serviceId: 'consultation', quantity: 1, defaultCommission: 0 }, // Free consultation
        { serviceId: 'aromatherapy', quantity: 2, defaultCommission: 60000 }
      ];

      const commission = calculateTherapistCommission(walkinData, []);
      // (40000*1) + (0*1) + (60000*2) = 40000 + 0 + 120000 = 160000
      expect(commission).toBe(160000);
    });
  });
});