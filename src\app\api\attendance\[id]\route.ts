import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import * as jwt from 'jsonwebtoken';

// Function untuk mengecek apakah user adalah admin
async function isAdmin() {
  try {
    // Ambil token dari cookies
    const cookieStore = cookies();
    const token = cookieStore.get('user_token')?.value;
    
    if (!token) {
      return false;
    }
    
    // Verifikasi token
    const JWT_SECRET = process.env.JWT_SECRET || 'breaktime_session_secret_key_for_jwt_auth';
    const decoded = jwt.verify(token, JWT_SECRET) as {
      id: string;
      name: string;
      email: string;
      role: string;
    };
    
    // Cek apakah user adalah admin
    return decoded.role === 'ADMIN';
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// DELETE endpoint untuk menghapus data absensi
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Cek apakah user adalah admin
    const admin = await isAdmin();
    if (!admin) {
      return NextResponse.json(
        { error: 'Unauthorized: Hanya admin yang dapat menghapus data absensi' },
        { status: 403 }
      );
    }

    const id = params.id;
    if (!id) {
      return NextResponse.json(
        { error: 'ID absensi diperlukan' },
        { status: 400 }
      );
    }

    // Coba hapus dari TherapistAttendance
    let deletedRecord = null;
    try {
      deletedRecord = await prisma.therapistAttendance.delete({
        where: { id },
      });
      console.log('Deleted therapist attendance:', id);
    } catch (therapistError) {
      console.log('Not a therapist attendance, trying system log:', id);
      // Jika tidak ada di TherapistAttendance, coba dari SystemLog
      try {
        deletedRecord = await prisma.systemLog.delete({
          where: { id },
        });
        console.log('Deleted user attendance (system log):', id);
      } catch (userError) {
        console.error('Record not found in either table:', id);
        return NextResponse.json(
          { error: 'Absensi tidak ditemukan' },
          { status: 404 }
        );
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Absensi berhasil dihapus',
      data: { id }
    });
  } catch (error) {
    console.error('Error deleting attendance:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus data absensi' },
      { status: 500 }
    );
  }
}