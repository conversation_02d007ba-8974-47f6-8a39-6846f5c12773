'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { FiArrowLeft, FiMail } from 'react-icons/fi';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simu<PERSON>i permintaan reset password
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
    }, 1500);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        when: 'beforeChildren',
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.4 },
    },
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50" data-theme="breaktime">
      <motion.div
        className="max-w-md w-full mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Logo dan Judul */}
        <motion.div className="text-center mb-8" variants={itemVariants}>
          <div className="flex justify-center mb-4">
            <Image src="/logo.png" alt="Breaktime Logo" width={80} height={80} />
          </div>
          <h1 className="text-2xl font-bold mb-2 text-gray-800">Lupa Kata Sandi?</h1>
          <p className="text-gray-600">
            Masukkan email Anda untuk mendapatkan link reset kata sandi
          </p>
        </motion.div>

        {/* Kartu Form */}
        <motion.div
          variants={itemVariants}
          className="bg-white rounded-xl shadow-lg p-8 md:p-10"
        >
          {!isSubmitted ? (
            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label
                  htmlFor="email"
                  className="block mb-2 text-sm font-medium text-gray-700"
                >
                  Email
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FiMail className="text-gray-500" />
                  </div>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-teal-400 focus:border-teal-400"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full py-3 px-4 rounded-lg font-medium text-white bg-teal-500 hover:bg-teal-600 ${
                  isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
                }`}
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Mengirim...
                  </span>
                ) : (
                  'Kirim Link Reset'
                )}
              </button>
            </form>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center"
            >
              <div className="bg-teal-50 text-teal-700 p-4 rounded-lg mb-4 flex items-start">
                <div className="mr-3 flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-teal-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    ></path>
                  </svg>
                </div>
                <div className="text-sm text-left">
                  <p className="font-medium">Email terkirim!</p>
                  <p className="mt-1">
                    Kami telah mengirimkan link reset kata sandi ke {email}. Harap periksa kotak masuk Anda.
                  </p>
                </div>
              </div>
              <p className="text-gray-600 text-sm mb-4">
                Jika Anda tidak menerima email dalam beberapa menit, periksa folder spam Anda.
              </p>
              <button
                onClick={() => setIsSubmitted(false)}
                className="w-full py-3 px-4 rounded-lg font-medium text-teal-600 bg-teal-50 hover:bg-teal-100 mb-4"
              >
                Coba Lagi
              </button>
            </motion.div>
          )}

          <div className="mt-6 text-center">
            <Link
              href="/auth/login"
              className="inline-flex items-center text-teal-600 hover:text-teal-700 font-medium text-sm"
            >
              <FiArrowLeft className="mr-2" /> Kembali ke Login
            </Link>
          </div>
        </motion.div>

        {/* Footer */}
        <motion.div
          variants={itemVariants}
          className="text-center mt-6 text-sm text-gray-500"
        >
          <p>
            &copy; {new Date().getFullYear()} Breaktime. All rights reserved.
          </p>
        </motion.div>
      </motion.div>
    </div>
  );
} 