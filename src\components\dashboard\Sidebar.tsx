'use client'; // Sidebar mungkin butuh state atau interaksi client-side

import Link from 'next/link';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { useThemeContext } from '@/contexts/ThemeContext'; // Import theme context hook
import { useOutletContext } from '@/contexts/OutletContext'; // Import outlet context
import { useAuth } from '@/contexts/AuthContext'; // <-- Import useAuth
import { useState, useEffect, useRef, FormEvent } from 'react'; // Import useState, useEffect, useRef dan FormEvent
import {
  FiGrid, FiShoppingCart, FiCalendar, FiUsers, FiUserCheck,
  FiBarChart2, FiSettings, FiLogOut, FiSun, FiMoon, FiClock,
  FiScissors, FiUserPlus, FiMapPin, FiChevronDown, FiAlertTriangle, FiRefreshCw,
  FiExternalLink, FiDollarSign, FiLock, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ye<PERSON>ff, <PERSON><PERSON>ser, FiUserX, FiPackage
} from 'react-icons/fi';

// Interface untuk outlet (sesuaikan dengan API response)
interface Outlet {
  id: string;
  name: string;
  address?: string; // Buat optional jika tidak selalu ada
  phone?: string;   // Buat optional
  operationalHours?: string; // Buat optional
  isOpen: boolean;
  isMain: boolean;
  // createdAt tidak selalu dibutuhkan di sidebar
}

// Interface untuk item navigasi
interface NavItem {
  href: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  label: string;
}

const mainNavItems = [
  {
    href: '/dashboard',
    icon: FiGrid,
    label: 'Beranda',
  },
  {
    href: '/dashboard/transaksi',
    icon: FiShoppingCart,
    label: 'Transaksi',
  },
  {
    href: '/dashboard/booking',
    icon: FiCalendar,
    label: 'Booking',
  },
  {
    href: '/dashboard/riwayat',
    icon: FiClock,
    label: 'Riwayat',
  },
  {
    href: '/dashboard/pelanggan',
    icon: FiUsers,
    label: 'Pelanggan',
  },
  {
    href: '/dashboard/terapis',
    icon: FiUserCheck,
    label: 'Terapis',
  },
  {
    href: '/dashboard/absensi',
    icon: FiUserX,
    label: 'Absensi',
  },
  {
    href: '/dashboard/layanan',
    icon: FiScissors,
    label: 'Layanan',
  },
  {
    href: '/dashboard/outlet',
    icon: FiMapPin,
    label: 'Outlet',
  },
  {
    href: '/dashboard/inventori',
    icon: FiPackage,
    label: 'Inventori',
  },
  {
    href: '/dashboard/reports',
    icon: FiBarChart2,
    label: 'Laporan',
  },
  {
    href: '/dashboard/crm',
    icon: FiUser,
    label: 'CRM',
  },
  {
    href: '/dashboard/settings',
    icon: FiSettings,
    label: 'Pengaturan',
  },
  {
    href: '/dashboard/users',
    icon: FiUserPlus,
    label: 'Pengguna',
  },
];

// Menu tambahan yang tidak memerlukan outlet
const additionalNavItems: NavItem[] = [
  // Kosong - menu tambahan dapat ditambahkan di sini jika diperlukan di masa depan
];

// Link eksternal khusus untuk admin
const adminExternalLinks = [
  { href: 'https://form.mybreaktime.co.id/', icon: FiExternalLink, label: 'Form Breaktime' },
  { href: 'https://finance.mybreaktime.co.id/', icon: FiDollarSign, label: 'Finance Breaktime' },
];

export default function Sidebar() {
  const pathname = usePathname();
  const router = useRouter();
  const { theme, toggleTheme } = useThemeContext(); // Dapatkan state dan fungsi tema
  const { selectedOutletId, setSelectedOutletId } = useOutletContext(); // Dapatkan outlet context
  const { user, logout } = useAuth(); // <-- Dapatkan user dan fungsi logout dari AuthContext

  // State untuk dropdown outlet
  const [isOutletDropdownOpen, setIsOutletDropdownOpen] = useState(false);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [currentOutlet, setCurrentOutlet] = useState<Outlet | null>(null);
  const [isLoadingOutlets, setIsLoadingOutlets] = useState(true);
  const [outletError, setOutletError] = useState<string | null>(null);

  // State dan ref untuk modal logout
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const logoutModalRef = useRef<HTMLDialogElement>(null);

  // State dan ref untuk modal ubah password
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const passwordModalRef = useRef<HTMLDialogElement>(null);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Load outlet data dari API
  useEffect(() => {
    const fetchOutlets = async () => {
      setIsLoadingOutlets(true);
      setOutletError(null);
      try {
        const response = await fetch('/api/outlets');
        if (!response.ok) {
          throw new Error('Gagal memuat data outlet');
        }
        const data = await response.json();
        setOutlets(data.outlets || []);
      } catch (error) {
        console.error('Gagal memuat data outlet:', error);
        setOutletError(error instanceof Error ? error.message : 'Terjadi kesalahan');
        setOutlets([]); // Kosongkan jika error
      } finally {
        setIsLoadingOutlets(false);
      }
    };
    fetchOutlets();
  }, []); // Hanya fetch sekali saat mount

  // Set currentOutlet berdasarkan selectedOutletId dari context dan data outlets
  useEffect(() => {
    if (!isLoadingOutlets && outlets.length > 0) {
      let activeOutlet: Outlet | undefined;
      if (selectedOutletId) {
        activeOutlet = outlets.find(o => o.id === selectedOutletId);
      }
      // Jika tidak ada selectedOutletId ATAU outlet dengan ID tsb tidak ditemukan (misal baru dihapus), cari yg main
      if (!activeOutlet) {
         activeOutlet = outlets.find(o => o.isMain);
         // Jika outlet utama ditemukan dan berbeda dari yg dipilih (atau belum dipilih), update context
         if (activeOutlet && activeOutlet.id !== selectedOutletId) {
            setSelectedOutletId(activeOutlet.id);
         }
      }

      setCurrentOutlet(activeOutlet || null);
    }
     // Jika tidak loading, tapi tidak ada outlet, set current ke null
    else if (!isLoadingOutlets && outlets.length === 0) {
       setCurrentOutlet(null);
       if(selectedOutletId) { // Jika sebelumnya ada yg dipilih, reset
         setSelectedOutletId(null);
       }
    }
  }, [selectedOutletId, outlets, isLoadingOutlets, setSelectedOutletId]);

  // Handle outlet selection - Langsung gunakan string ID
  const handleSelectOutlet = (outlet: Outlet) => {
    if (outlet.id !== selectedOutletId) {
      setSelectedOutletId(outlet.id);
      // setCurrentOutlet akan diupdate oleh useEffect di atas
    }
    setIsOutletDropdownOpen(false);
  };

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === href;
    }
    return pathname?.startsWith(href) || false;
  };

  // useEffect untuk menutup dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isOutletDropdownOpen && !target.closest('.outlet-dropdown')) {
        setIsOutletDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOutletDropdownOpen]);

  // Effect untuk modal logout
  useEffect(() => {
    if (isLogoutModalOpen && logoutModalRef.current) {
      logoutModalRef.current.showModal();
    } else if (!isLogoutModalOpen && logoutModalRef.current) {
      logoutModalRef.current.close();
    }
  }, [isLogoutModalOpen]);

  // Effect untuk modal ubah password
  useEffect(() => {
    if (isPasswordModalOpen && passwordModalRef.current) {
      passwordModalRef.current.showModal();
      // Reset form fields saat modal dibuka
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      setPasswordError(null);
      setPasswordSuccess(null);
    } else if (!isPasswordModalOpen && passwordModalRef.current) {
      passwordModalRef.current.close();
    }
  }, [isPasswordModalOpen]);

  // Handle tombol logout
  const handleLogoutClick = () => {
    setIsLogoutModalOpen(true);
  };

  // Handle konfirmasi logout
  const handleConfirmLogout = async () => { // Jadikan async jika logout context/API async
    try {
      // Panggil fungsi logout dari AuthContext
      // Diasumsikan fungsi logout menangani pembersihan state user & token
      await logout();

      // Pembersihan tambahan spesifik untuk UI ini
      localStorage.removeItem('selectedOutletId'); // Hapus juga ID outlet terpilih
      setSelectedOutletId(null); // Reset context outlet

      // Redirect ke login SETELAH logout berhasil
      router.push('/auth/login');
      setIsLogoutModalOpen(false);

    } catch (error) {
      console.error("Logout failed:", error);
      // Tampilkan notifikasi error jika perlu
      // toast.error("Logout gagal, silakan coba lagi.");
      setIsLogoutModalOpen(false); // Tutup modal meskipun gagal?
    }
  };

  // Handle tombol ubah password
  const handlePasswordClick = () => {
    setIsPasswordModalOpen(true);
  };

  // Handle submit form ubah password
  const handlePasswordSubmit = async (e: FormEvent) => {
    e.preventDefault();

    // Reset messages
    setPasswordError(null);
    setPasswordSuccess(null);

    // Validasi
    if (!currentPassword) {
      setPasswordError("Password saat ini harus diisi");
      return;
    }

    if (!newPassword) {
      setPasswordError("Password baru harus diisi");
      return;
    }

    if (newPassword.length < 6) {
      setPasswordError("Password baru minimal 6 karakter");
      return;
    }

    if (newPassword !== confirmPassword) {
      setPasswordError("Konfirmasi password tidak sesuai");
      return;
    }

    // Submit ke API
    setIsSubmitting(true);
    try {
      const response = await fetch('/api/users/change-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          currentPassword,
          newPassword
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Gagal mengubah password');
      }

      // Success
      setPasswordSuccess("Password berhasil diubah");

      // Clear form after success
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');

      // Close modal after delay
      setTimeout(() => {
        setIsPasswordModalOpen(false);
      }, 2000);

    } catch (error) {
      console.error('Change password error:', error);
      setPasswordError(error instanceof Error ? error.message : 'Terjadi kesalahan');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <ul className="menu p-4 w-64 min-h-full bg-base-100 text-base-content">
        {/* Logo - jadi list item */}
        <li className="mb-4">
          <div className="flex items-center justify-center p-2">
            <Image
              src="/logo.png"
              alt="Breaktime Logo"
              width={40}
              height={40}
              className="mr-2"
            />
            <span className="text-xl font-bold">
              <span className="text-primary">Break</span>
              <span className="text-secondary">time</span>
            </span>
          </div>
        </li>

        {/* Outlet Selector Dropdown */}
        <li className="mb-4">
          <div className="dropdown dropdown-bottom w-full outlet-dropdown">
            <label
              tabIndex={0}
              className={`relative flex w-full cursor-pointer items-start justify-between rounded-lg border border-base-300 bg-base-100 px-3 py-1.5 text-sm font-normal text-base-content outline-none transition-colors duration-200 ease-in-out h-auto hover:border-base-300 hover:bg-base-200 ${isLoadingOutlets || !!outletError ? 'cursor-not-allowed opacity-60' : ''}`}
              onClick={() => { if (!isLoadingOutlets && !outletError) setIsOutletDropdownOpen(!isOutletDropdownOpen); }}
            >
              <div className="flex flex-1 items-start mr-1">
                <FiMapPin className="mr-2 h-4 w-4 min-w-[1rem] text-primary flex-shrink-0 mt-0.5"/>
                <span className="min-w-0">
                  {isLoadingOutlets ? 'Memuat...' : outletError ? 'Error' : currentOutlet?.name || 'Pilih Outlet'}
                </span>
              </div>
              {isLoadingOutlets ?
                <span className="loading loading-spinner loading-xs"></span> :
                outletError ?
                <FiAlertTriangle className="h-4 w-4 min-w-[1rem] text-error flex-shrink-0"/> :
                <FiChevronDown className={`transition-transform duration-300 h-4 w-4 min-w-[1rem] text-base-content/70 ${isOutletDropdownOpen ? 'rotate-180' : ''} flex-shrink-0`} />
              }
            </label>
            <ul
              tabIndex={0}
              className={`dropdown-content z-[1] menu p-2 shadow-md bg-base-100 border border-base-300 rounded-box w-full mt-1 max-h-60 overflow-y-auto ${isOutletDropdownOpen && !isLoadingOutlets && !outletError ? 'block' : 'hidden'}`}
            >
              {outletError && (
                 <li className="px-3 py-2 text-error text-xs flex items-center">
                   <FiAlertTriangle className="mr-1 h-3 w-3"/> {outletError}
                 </li>
              )}
              {!outletError && outlets.length === 0 && (
                <li className="text-center py-2 text-base-content/60 text-sm">
                  Tidak ada outlet
                </li>
              )}

              {outlets.filter(o => o.isOpen).map((outlet) => (
                <li key={outlet.id}>
                  <button
                    type="button"
                    onClick={() => handleSelectOutlet(outlet)}
                    className={`w-full text-left text-sm py-2 px-3 text-base-content hover:bg-base-200 ${outlet.id === selectedOutletId ? 'bg-primary/10 text-primary font-medium' : ''}`}
                  >
                    <div className="flex justify-between items-start w-full">
                      <span className="min-w-0 mr-1">{outlet.name}</span>
                      {outlet.isMain && (
                        <span className="badge badge-primary badge-xs text-[10px] px-1 ml-1 text-primary-content flex-shrink-0">Utama</span>
                      )}
                    </div>
                  </button>
                </li>
              ))}

              {outlets.filter(o => o.isOpen).length > 0 && !outletError && (
                <div className="divider my-1 h-px bg-base-300"></div>
              )}

              {!outletError && (
                 <li>
                  <Link
                    href="/dashboard/outlet"
                    className="text-xs py-2 text-center text-base-content/70 hover:text-primary hover:bg-base-200 block"
                    onClick={() => setIsOutletDropdownOpen(false)}
                  >
                    <FiMapPin className="h-3 w-3 mr-1 inline" />
                    Kelola Semua Outlet
                  </Link>
                </li>
              )}
               {outletError && (
                 <li>
                   <button
                     type="button"
                     onClick={() => window.location.reload()}
                     className="text-xs py-2 text-center text-error hover:bg-error/10 w-full"
                   >
                     <FiRefreshCw className="h-3 w-3 mr-1 inline" />
                     Coba Lagi
                   </button>
                 </li>
               )}
            </ul>
          </div>
        </li>

        {/* Navigation Items */}
        {mainNavItems.map((item) => {
          const active = isActive(item.href);
          return (
            <li key={item.label}>
              <Link
                href={item.href}
                className={`${active ? 'active bg-primary/10 text-primary' : 'text-base-content/80 hover:bg-base-200'} ${!selectedOutletId && item.href !== '/dashboard/outlet' && item.href !== '/dashboard/settings' ? 'pointer-events-none opacity-50' : ''}`}
                aria-disabled={!selectedOutletId && item.href !== '/dashboard/outlet' && item.href !== '/dashboard/settings'}
              >
                <item.icon className={`h-5 w-5 ${active ? 'text-primary' : 'text-base-content/60'}`} />
                {item.label}
              </Link>
            </li>
          );
        })}

        {/* Additional Navigation Items (tidak memerlukan outlet) */}
        {additionalNavItems.length > 0 && additionalNavItems.map((item) => {
          const active = isActive(item.href);
          return (
            <li key={item.label}>
              <Link
                href={item.href}
                className={`${active ? 'active bg-primary/10 text-primary' : 'text-base-content/80 hover:bg-base-200'}`}
              >
                <item.icon className={`h-5 w-5 ${active ? 'text-primary' : 'text-base-content/60'}`} />
                {item.label}
              </Link>
            </li>
          );
        })}

        {/* External Links for Admin Only */}
        {user?.role === 'ADMIN' && adminExternalLinks.length > 0 && (
          <>
            <li className="menu-title mt-4">
              <span className="text-xs font-semibold text-base-content/50 uppercase tracking-wider px-4">Link Eksternal</span>
            </li>
            {adminExternalLinks.map((item) => (
              <li key={item.label}>
                <a
                  href={item.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-base-content/80 hover:bg-base-200"
                >
                  <item.icon className="h-5 w-5 text-base-content/60" />
                  {item.label}
                  <FiExternalLink className="h-3 w-3 ml-auto text-base-content/40" />
                </a>
              </li>
            ))}
          </>
        )}

        {/* Spacer (jika perlu untuk mendorong item bawah) */}
        <li className="mt-auto"></li>

        {/* User Info - dibuat clickable untuk buka modal ubah password */}
        <li>
          <button
            type="button"
            className="flex items-center py-3 px-4 border-t border-base-200 dark:border-base-700 mb-2 w-full hover:bg-base-200 rounded-lg"
            onClick={handlePasswordClick}
          >
            <div className="avatar placeholder mr-3">
              <div className="bg-primary text-primary-content rounded-full w-8">
                <span className="text-sm">{user?.name ? user.name.charAt(0).toUpperCase() : '?'}</span>
              </div>
            </div>
            <div className="flex flex-col items-start">
              <span className="text-sm font-medium text-base-content">{user?.name || 'User'}</span>
              <span className="text-xs text-base-content/60 capitalize">{user?.role || 'Role'}</span>
            </div>
            <FiLock className="ml-auto h-4 w-4 text-base-content/40" />
          </button>
        </li>

        {/* Theme Toggle - jadi list item */}
        <li>
          <label className="flex items-center justify-between cursor-pointer py-2 px-4 rounded-lg">
            <span className="flex items-center text-base-content/80">
              {theme === 'breaktime' ? <FiSun className="mr-3 h-5 w-5" /> : <FiMoon className="mr-3 h-5 w-5" />}
              Mode {theme === 'breaktime' ? 'Terang' : 'Gelap'}
            </span>
            <input
              type="checkbox"
              className="toggle toggle-sm toggle-primary"
              checked={theme === 'breaktime-dark'}
              onChange={toggleTheme}
            />
          </label>
        </li>

        {/* Logout Button - jadi list item */}
        <li>
          <button
            type="button"
            className="flex items-center w-full text-red-500 hover:bg-red-500/10 py-2 px-4 rounded-lg"
            onClick={handleLogoutClick}
          >
            <FiLogOut className="h-5 w-5 mr-3" />
            Logout
          </button>
        </li>
      </ul>

      {/* Modal Konfirmasi Logout */}
      <dialog ref={logoutModalRef} className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg flex items-center">
            <FiAlertTriangle className="text-error mr-2" /> Konfirmasi Logout
          </h3>
          <p className="py-4">Apakah Anda yakin ingin keluar dari akun Anda?</p>
          <div className="modal-action">
            <button
              type="button"
              className="btn btn-ghost"
              onClick={() => setIsLogoutModalOpen(false)}
            >
              Batal
            </button>
            <button
              type="button"
              className="btn bg-red-500 hover:bg-red-600 text-white"
              onClick={handleConfirmLogout}
            >
              Logout
            </button>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop">
          <button type="button" onClick={() => setIsLogoutModalOpen(false)} aria-label="Close modal">close</button>
        </form>
      </dialog>

      {/* Modal Ubah Password */}
      <dialog ref={passwordModalRef} className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg flex items-center">
            <FiUser className="text-primary mr-2" /> Ubah Password
          </h3>

          <form onSubmit={handlePasswordSubmit} className="py-4">
            {/* Password saat ini */}
            <div className="form-control w-full mb-4">
              <label className="label">
                <span className="label-text">Password Saat Ini</span>
              </label>
              <div className="relative">
                <input
                  type={showCurrentPassword ? "text" : "password"}
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  className="input input-bordered w-full pr-10"
                  placeholder="Masukkan password saat ini"
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  {showCurrentPassword ?
                    <FiEyeOff className="h-5 w-5 text-base-content/40" /> :
                    <FiEye className="h-5 w-5 text-base-content/40" />
                  }
                </button>
              </div>
            </div>

            {/* Password baru */}
            <div className="form-control w-full mb-4">
              <label className="label">
                <span className="label-text">Password Baru</span>
              </label>
              <div className="relative">
                <input
                  type={showNewPassword ? "text" : "password"}
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  className="input input-bordered w-full pr-10"
                  placeholder="Minimal 6 karakter"
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  {showNewPassword ?
                    <FiEyeOff className="h-5 w-5 text-base-content/40" /> :
                    <FiEye className="h-5 w-5 text-base-content/40" />
                  }
                </button>
              </div>
            </div>

            {/* Konfirmasi password baru */}
            <div className="form-control w-full mb-4">
              <label className="label">
                <span className="label-text">Konfirmasi Password Baru</span>
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="input input-bordered w-full pr-10"
                  placeholder="Masukkan kembali password baru"
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ?
                    <FiEyeOff className="h-5 w-5 text-base-content/40" /> :
                    <FiEye className="h-5 w-5 text-base-content/40" />
                  }
                </button>
              </div>
            </div>

            {/* Error message */}
            {passwordError && (
              <div className="alert alert-error shadow-lg mb-4 py-2">
                <FiAlertTriangle className="h-4 w-4" />
                <span className="text-sm">{passwordError}</span>
              </div>
            )}

            {/* Success message */}
            {passwordSuccess && (
              <div className="alert alert-success shadow-lg mb-4 py-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm">{passwordSuccess}</span>
              </div>
            )}

            <div className="modal-action">
              <button
                type="button"
                className="btn btn-ghost"
                onClick={() => setIsPasswordModalOpen(false)}
                disabled={isSubmitting}
              >
                Batal
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="loading loading-spinner loading-xs"></span>
                    Menyimpan...
                  </>
                ) : (
                  'Simpan'
                )}
              </button>
            </div>
          </form>
        </div>
        <form method="dialog" className="modal-backdrop">
          <button type="button" onClick={() => setIsPasswordModalOpen(false)} aria-label="Close modal">close</button>
        </form>
      </dialog>
    </>
  );
}