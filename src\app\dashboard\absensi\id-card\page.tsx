'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { useRouter, useSearchParams } from 'next/navigation';
import IDCard from '@/components/attendance/IDCard';
import {
  PrinterIcon, 
  ArrowPathIcon, 
  UserIcon, 
  UserGroupIcon, 
  CameraIcon, 
  ArrowDownTrayIcon, 
  XCircleIcon,
  ChevronLeftIcon
} from '@heroicons/react/24/outline';
import { toast, Toaster } from 'react-hot-toast';
import html2canvas from 'html2canvas';
import * as htmlToImage from 'html-to-image';
import Link from 'next/link';

export default function IDCardPage() {
  const [loading, setLoading] = useState(false);
  const [therapists, setTherapists] = useState([]);
  const [users, setUsers] = useState([]);
  const [selectedType, setSelectedType] = useState<'therapist' | 'user'>('therapist');
  const [selectedId, setSelectedId] = useState('');
  const [cardData, setCardData] = useState<any>(null);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [customRoleText, setCustomRoleText] = useState<string>('');
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);
  
  const router = useRouter();
  const searchParams = useSearchParams();

  // Jika ada parameter id dan type, langsung load ID card
  useEffect(() => {
    const id = searchParams.get('id');
    const type = searchParams.get('type') as 'therapist' | 'user';
    
    if (id && (type === 'therapist' || type === 'user')) {
      setSelectedType(type);
      setSelectedId(id);
      generateIDCard(id, type);
    }
  }, [searchParams]);

  // Load data terapis dan user
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Ambil data terapis
        const therapistsRes = await fetch('/api/therapists');
        const therapistsData = await therapistsRes.json();
        
        if (therapistsRes.ok && therapistsData.therapists) {
          setTherapists(therapistsData.therapists);
        } else {
          console.error('Error fetching therapists:', therapistsData);
        }
        
        // Ambil data user
        const usersRes = await fetch('/api/users');
        const usersData = await usersRes.json();
        
        if (usersRes.ok && usersData.users) {
          setUsers(usersData.users);
        } else {
          console.error('Error fetching users:', usersData);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Gagal memuat data');
      }
    };
    
    fetchData();
  }, []);

  // Fungsi untuk generate ID card
  const generateIDCard = async (id: string, type: 'therapist' | 'user') => {
    if (!id) {
      toast.error('Pilih terapis atau user terlebih dahulu');
      return;
    }
    
    setLoading(true);
    
    try {
      // Generate QR code
      const queryParams = type === 'therapist' 
        ? `?therapistId=${id}&type=therapist` 
        : `?userId=${id}&type=user`;
      
      const qrResponse = await fetch(`/api/attendance/generate-qr${queryParams}`);
      const qrData = await qrResponse.json();
      
      if (!qrResponse.ok) {
        throw new Error(qrData.error || 'Gagal generate QR Code');
      }
      
      // Reset profile image saat ganti orang
      setProfileImage(null);
      
      // Siapkan default role text berdasarkan tipe
      const defaultRole = type === 'therapist' 
        ? 'Terapis' 
        : qrData.data.role.charAt(0).toUpperCase() + qrData.data.role.slice(1).toLowerCase();
      
      // Set default custom role text
      setCustomRoleText(defaultRole);
      
      // Set data untuk ID card tanpa outlet
      setCardData({
        id: qrData.data.id || id, // Gunakan ID dari respons API jika ada
        name: qrData.data.name,
        role: defaultRole,
        qrCode: qrData.qrCode,
        type: type
      });
      
      // Update URL dengan parameter id dan type untuk bookmark/sharing
      const newUrl = `${window.location.pathname}?id=${id}&type=${type}`;
      window.history.pushState({}, '', newUrl);
      
    } catch (error) {
      console.error('Error generating ID card:', error);
      toast.error('Gagal membuat ID Card');
    } finally {
      setLoading(false);
    }
  };

  // Handler untuk upload gambar profil
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    // Batasi ukuran file (2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('Ukuran gambar maksimal 2MB');
      return;
    }
    
    // Validasi tipe file
    if (!file.type.startsWith('image/')) {
      toast.error('File harus berupa gambar');
      return;
    }
    
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setProfileImage(e.target.result as string);
      }
    };
    reader.readAsDataURL(file);
  };
  
  // Handler untuk hapus gambar profil
  const handleRemoveImage = () => {
    setProfileImage(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handler untuk print
  const handlePrint = () => {
    // Simpan CSS print kedalam head
    const style = document.createElement('style');
    style.innerHTML = `
      @media print {
        body * {
          visibility: hidden;
        }
        .print-container, .print-container * {
          visibility: visible;
        }
        .print-container {
          position: absolute;
          left: 50%;
          top: 0;
          transform: translateX(-50%);
        }
        .print-container .hide-on-print {
          display: none !important;
        }
      }
    `;
    document.head.appendChild(style);
    
    // Print halaman
    window.print();
    
    // Hapus style setelah print
    document.head.removeChild(style);
  };

  // Handler untuk simpan sebagai gambar
  const handleSaveAsImage = async () => {
    if (!cardRef.current || !cardData) return;
    
    try {
      setIsSaving(true);
      
      const loadingToast = toast.loading('Menyimpan gambar ID Card...');
      
      try {
        // Gunakan htmlToImage dengan konfigurasi untuk kualitas terbaik
        const dataUrl = await htmlToImage.toPng(cardRef.current, {
          cacheBust: true,
          pixelRatio: 2,
          backgroundColor: '#ffffff',
          style: {
            margin: '0',
            padding: '0',
            border: 'none'
          },
          quality: 1.0,
          canvasWidth: 1280, // Ukuran lebih besar untuk resolusi tinggi
          canvasHeight: 2200 // Ukuran lebih besar untuk resolusi tinggi
        });
        
        // Unduh gambar
        downloadImage(dataUrl);
        toast.dismiss(loadingToast);
        toast.success('ID Card berhasil disimpan sebagai gambar HD');
      } 
      catch (htmlToImageError) {
        console.error('html-to-image failed, trying html2canvas:', htmlToImageError);
        
        try {
          // Fallback ke html2canvas jika htmlToImage gagal
          const canvas = await html2canvas(cardRef.current, {
            backgroundColor: '#ffffff',
            scale: 3, // Tingkatkan scale untuk kualitas lebih tinggi
            useCORS: true,
            allowTaint: true,
            logging: false,
            imageTimeout: 0
          });
          
          // Konversi ke PNG dan unduh
          const image = canvas.toDataURL('image/png', 1.0);
          downloadImage(image);
          toast.dismiss(loadingToast);
          toast.success('ID Card berhasil disimpan sebagai gambar HD');
        }
        catch (canvasError) {
          console.error('html2canvas also failed:', canvasError);
          toast.dismiss(loadingToast);
          throw new Error('Gagal menyimpan gambar dengan semua metode');
        }
      }
    } catch (error) {
      console.error('Error saving image:', error);
      toast.error('Gagal menyimpan ID Card sebagai gambar, silakan gunakan fitur screenshot');
    } finally {
      setIsSaving(false);
    }
  };

  // Helper untuk download gambar
  const downloadImage = (dataUrl: string) => {
    const link = document.createElement('a');
    link.href = dataUrl;
    link.download = `ID-Card-${cardData?.name.replace(/\s+/g, '-')}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Toaster />
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="mb-6 pb-6 border-b border-gray-200 flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Generator ID Card</h1>
          <p className="text-gray-600 mt-1">Buat dan cetak ID card dengan QR code untuk terapis dan staff.</p>
        </div>
        <Link 
          href="/dashboard/absensi" 
          className="inline-flex items-center text-sm font-medium text-[#4EA799] hover:text-[#3a8a7e] transition-colors py-2 px-4 rounded-lg hover:bg-[#4EA799]/10 ml-4 print:hidden"
        >
          <ChevronLeftIcon className="w-5 h-5 mr-1.5" />
          <span>Kembali ke Absensi</span>
        </Link>
      </motion.div>
      
      <div className="flex flex-wrap -mx-4">
        {/* Form Section */}
        <div className="w-full lg:w-1/3 px-4 mb-8 lg:mb-0 print:hidden">
          <motion.div 
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="bg-white p-6 rounded-lg shadow-md"
          >
            <h2 className="text-lg font-semibold mb-4 text-gray-800 flex items-center">
              <span className="bg-emerald-100 text-emerald-800 w-6 h-6 rounded-full inline-flex items-center justify-center mr-2 text-sm">1</span>
              Pilih Tipe ID Card
            </h2>
            
            {/* Type selector */}
            <div className="flex space-x-2 mb-6">
              <button
                onClick={() => setSelectedType('therapist')}
                className={`flex-1 flex items-center justify-center py-3 px-4 rounded-md transition-all ${
                  selectedType === 'therapist'
                    ? 'bg-emerald-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <UserGroupIcon className="w-5 h-5 mr-2" />
                Terapis
              </button>
              <button
                onClick={() => setSelectedType('user')}
                className={`flex-1 flex items-center justify-center py-3 px-4 rounded-md transition-all ${
                  selectedType === 'user'
                    ? 'bg-emerald-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <UserIcon className="w-5 h-5 mr-2" />
                Staff
              </button>
            </div>
            
            {/* Person selector */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-4 text-gray-800 flex items-center">
                <span className="bg-emerald-100 text-emerald-800 w-6 h-6 rounded-full inline-flex items-center justify-center mr-2 text-sm">2</span>
                Pilih {selectedType === 'therapist' ? 'Terapis' : 'Staff'}
              </h2>
              <select
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                value={selectedId}
                onChange={(e) => setSelectedId(e.target.value)}
                aria-label={`Pilih ${selectedType === 'therapist' ? 'Terapis' : 'Staff'}`}
              >
                <option value="">-- Pilih {selectedType === 'therapist' ? 'Terapis' : 'Staff'} --</option>
                {selectedType === 'therapist'
                  ? therapists.map((therapist: any) => (
                      <option key={therapist.id} value={therapist.id}>
                        {therapist.name} {therapist.outlet?.name && `(${therapist.outlet.name})`}
                      </option>
                    ))
                  : users.map((user: any) => (
                      <option key={user.id} value={user.id}>
                        {user.name} ({user.role})
                      </option>
                    ))}
              </select>
            </div>
            
            {/* Upload Foto */}
            {cardData && (
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-4 text-gray-800 flex items-center">
                  <span className="bg-emerald-100 text-emerald-800 w-6 h-6 rounded-full inline-flex items-center justify-center mr-2 text-sm">3</span>
                  Foto Profil (Opsional)
                </h2>
                
                <div className="mb-3">
                  {profileImage ? (
                    <div className="border rounded-lg p-3 bg-gray-50">
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-sm text-gray-600">Foto terpilih</p>
                        <button 
                          onClick={handleRemoveImage} 
                          className="text-red-500 hover:text-red-700"
                          title="Hapus foto"
                        >
                          <XCircleIcon className="w-5 h-5" />
                        </button>
                      </div>
                      <div className="w-20 h-20 rounded-full overflow-hidden mx-auto">
                        <img src={profileImage} alt="Preview" className="w-full h-full object-cover" />
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-6 bg-gray-50">
                      <CameraIcon className="w-10 h-10 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 mb-2">Pilih foto profil (opsional)</p>
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
                      >
                        Pilih Gambar
                      </button>
                      <input 
                        type="file"
                        ref={fileInputRef}
                        onChange={handleImageUpload}
                        accept="image/*"
                        className="hidden"
                        aria-label="Upload foto profil"
                        title="Upload foto profil"
                        placeholder="Upload foto profil"
                      />
                    </div>
                  )}
                </div>
                
                <p className="text-xs text-gray-500 italic">
                  Jika tidak memilih foto, ID card akan menggunakan inisial nama.
                </p>
              </div>
            )}
            
            {/* Custom Role Text */}
            {cardData && (
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-4 text-gray-800 flex items-center">
                  <span className="bg-emerald-100 text-emerald-800 w-6 h-6 rounded-full inline-flex items-center justify-center mr-2 text-sm">{cardData ? "4" : "3"}</span>
                  Jabatan / Posisi
                </h2>
                
                <input
                  type="text"
                  value={customRoleText}
                  onChange={(e) => setCustomRoleText(e.target.value)}
                  placeholder="Masukkan jabatan atau posisi"
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                  maxLength={30}
                />
                <p className="text-xs text-gray-500 mt-1 italic">
                  Sesuaikan jabatan yang akan ditampilkan di ID Card
                </p>
              </div>
            )}
            
            {/* Generate Button */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-4 text-gray-800 flex items-center">
                <span className="bg-emerald-100 text-emerald-800 w-6 h-6 rounded-full inline-flex items-center justify-center mr-2 text-sm">{cardData ? "5" : "3"}</span>
                Generate ID Card
              </h2>
              <button
                onClick={() => generateIDCard(selectedId, selectedType)}
                disabled={loading || !selectedId}
                className="w-full flex items-center justify-center py-3 px-4 bg-emerald-500 text-white rounded-md hover:bg-emerald-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? (
                  <ArrowPathIcon className="w-5 h-5 mr-2 animate-spin" />
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                  </svg>
                )}
                {loading ? 'Generating...' : 'Generate ID Card'}
              </button>
            </div>
          </motion.div>
          
          {/* Print and Save buttons */}
          {cardData && (
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              className="mt-6 flex flex-col space-y-3"
            >
              <button
                onClick={handlePrint}
                className="w-full flex items-center justify-center py-3 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <PrinterIcon className="w-5 h-5 mr-2" />
                Cetak ID Card
              </button>
              
              <button
                onClick={handleSaveAsImage}
                disabled={isSaving}
                className="w-full flex items-center justify-center py-3 px-4 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors disabled:bg-purple-400"
              >
                {isSaving ? (
                  <ArrowPathIcon className="w-5 h-5 mr-2 animate-spin" />
                ) : (
                  <ArrowDownTrayIcon className="w-5 h-5 mr-2" />
                )}
                {isSaving ? 'Menyimpan...' : 'Simpan Sebagai Gambar HD'}
              </button>
              
              <div className="mt-1 bg-amber-50 border border-amber-200 rounded-md p-4">
                <div className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-amber-500 mt-0.5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <p className="text-sm text-amber-800">
                      Pastikan printer sudah siap dan setting printer disesuaikan dengan ukuran kartu.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
        
        {/* Preview Section */}
        <div className="w-full lg:w-2/3 px-4">
          <div className="flex justify-center">
            {cardData ? (
              <div id="id-card-container">
                <div className="print-container" ref={cardRef}>
                  <IDCard
                    name={cardData.name}
                    role={cardData.role}
                    id={cardData.id}
                    qrCode={cardData.qrCode}
                    outletName={cardData.outletName}
                    type={cardData.type}
                    profileImage={profileImage}
                    customRoleText={customRoleText}
                  />
                </div>
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="w-[320px] h-[550px] bg-gray-100 rounded-xl border border-dashed border-gray-300 flex items-center justify-center text-gray-500"
              >
                <div className="text-center p-8">
                  <UserIcon className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                  <p>Pilih terapis atau user dan generate ID card untuk melihat preview</p>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}