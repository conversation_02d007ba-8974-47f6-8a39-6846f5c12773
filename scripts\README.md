# Script Migrasi Data Pelanggan

Script ini digunakan untuk melakukan migrasi data pelanggan dari file Excel ke database aplikasi BreakTime.

## Persiapan

1. Pastikan file Excel `Data_Pelanggan_2025-04-21 (1).xls` berada di root folder project
2. Pastikan database sudah terhubung dengan benar
3. Pastikan semua dependensi sudah terinstall dengan menjalankan:
   ```
   npm install
   ```

## Cara Penggunaan

### 1. Menjalankan Migrasi

Untuk menjalankan migrasi data pelanggan, gunakan perintah:

```bash
node scripts/run-migrasi.js
```

atau langsung:

```bash
node scripts/migrasi-pelanggan.js
```

### 2. Verifikasi Hasil Migrasi

Setelah migrasi selesai, Anda dapat memverifikasi hasilnya dengan menjalankan:

```bash
node scripts/verifikasi-migrasi.js
```

## Catatan Penting

- Script ini akan mengkonversi kolom "Jumlah Datang" dari file Excel menjadi poin pelanggan di database
- <PERSON>elang<PERSON> yang sudah ada (berdasarkan nomor telepon) akan diupdate poinnya, bukan dibuat ulang
- Semua pelanggan yang dimigrasi akan diberi tag "Migrasi" untuk memudahkan identifikasi
- Jika terjadi error selama migrasi, log error akan disimpan di file `migrasi-error-log.json`

## Format Data Excel

Script ini mendukung beberapa format penamaan kolom di Excel:

- Nama: "Nama", "NAMA", "nama"
- Telepon: "Telepon", "TELEPON", "telepon", "HP", "No HP", "NO HP", "No Telepon"
- Alamat: "Alamat", "ALAMAT", "alamat"
- Jumlah Datang: "Jumlah Datang", "JUMLAH DATANG", "jumlah datang"

## Troubleshooting

Jika terjadi error selama migrasi:

1. Periksa file `migrasi-error-log.json` untuk detail error
2. Pastikan format data di Excel sesuai dengan yang diharapkan
3. Pastikan database berjalan dan dapat diakses
4. Pastikan file Excel berada di lokasi yang benar
