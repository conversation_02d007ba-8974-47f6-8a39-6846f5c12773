/**
 * Fungsi untuk membuat URL WhatsApp dengan pesan yang sudah diformat
 * @param phone Nomor telepon tujuan (format: 08xxxxxxxxxx)
 * @param message Pesan yang akan dikirim
 * @returns URL WhatsApp yang dapat dibuka di browser
 */
export function createWhatsAppUrl(phone: string, message: string): string {
  // Bersihkan nomor telepon (hapus spasi, tanda baca, dll)
  let cleanPhone = phone.replace(/[^0-9]/g, '');

  // Jika nomor dimulai dengan 0, ganti dengan 62 (kode negara Indonesia)
  if (cleanPhone.startsWith('0')) {
    cleanPhone = `62${cleanPhone.substring(1)}`;
  }

  // Jika nomor belum memiliki kode negara, tambahkan 62
  if (!cleanPhone.startsWith('62')) {
    cleanPhone = `62${cleanPhone}`;
  }

  // Encode pesan untuk URL
  const encodedMessage = encodeURIComponent(message);

  // Gunakan API WhatsApp yang mendukung emoji dengan baik
  // Berdasarkan penelitian, api.whatsapp.com/send bekerja lebih baik dengan emoji
  // daripada wa.me yang memiliki masalah dengan encoding emoji
  return `https://api.whatsapp.com/send?phone=${cleanPhone}&text=${encodedMessage}`;
}

/**
 * Fungsi untuk membuat pesan konfirmasi booking
 * @param bookingData Data booking yang akan digunakan untuk membuat pesan
 * @returns Pesan konfirmasi booking yang sudah diformat
 */
export function createBookingConfirmationMessage(bookingData: {
  customerName: string;
  outletName: string;
  bookingDate: string;
  bookingTime: string;
  services: { name: string; duration: number; price: number; quantity?: number }[];
  totalDuration: number;
  totalPrice: number;
  bookingId: string;
  customerPoints?: number;
}): string {
  const { customerName, outletName, bookingDate, bookingTime, services, totalDuration, totalPrice, bookingId, customerPoints } = bookingData;

  // Format daftar layanan dengan emoji dan quantity
  const servicesList = services.map(service => {
    const quantity = service.quantity || 1;
    const totalPrice = service.price * quantity;

    return `\u{2022} ${service.name}${quantity > 1 ? ` (${quantity}x)` : ''} (${service.duration * quantity} menit) - Rp ${totalPrice.toLocaleString('id-ID')}`;
  }).join('\n');

  // Buat pesan dengan emoji Unicode
  let message = `KONFIRMASI BOOKING BREAKTIME

Halo kak ${customerName},

Booking Anda telah berhasil dibuat dengan detail sebagai berikut:

\u{1F4C5} Tanggal: ${bookingDate}
\u{23F0} Waktu: ${bookingTime}
\u{1F4CD} Outlet: ${outletName}
\u{1F522} ID Booking: ${bookingId}`;

  // Tambahkan informasi poin jika tersedia
  if (customerPoints !== undefined) {
    message += `
\u{1F31F} Poin Anda: ${customerPoints} poin`;
  }

  message += `

Layanan yang dipilih:
${servicesList}

\u{23F1}\u{FE0F} Total Durasi: ${totalDuration} menit
\u{1F4B0} Total Harga: Rp ${totalPrice.toLocaleString('id-ID')}

Mohon hadir 10 menit sebelum jadwal / jam yang di tentukan!

Jika ada perubahan jadwal / jam, silahkan hubungi kami minimal 2 jam sebelum jadwal / jam yang sudah ditentukan sebelumnya.

Terima kasih telah memilih Breaktime!

Breaktime
Badan segar urusan lancar
Kembali semangat!`;

  return message;
}
