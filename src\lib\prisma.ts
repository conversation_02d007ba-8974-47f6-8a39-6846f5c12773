// Singleton PrismaClient untuk aplikasi
// import { PrismaClient } from '@prisma/client'; // <-- Impor lama
import { PrismaClient } from '../../prisma/generated/client'; // <-- Impor baru

// Mencegah multiple instances pada development
declare global {
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined;
}

const prisma = global.prisma || new PrismaClient();

if (process.env.NODE_ENV === 'development') {
  global.prisma = prisma;
}

export { prisma };
