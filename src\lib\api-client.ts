import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

// Utility function untuk fetch dengan error handling global
export async function apiClient(url: string, options: RequestInit = {}) {
  const defaultOptions: RequestInit = {
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, defaultOptions);
    
    // Jika response 401 (Unauthorized), token kemungkinan expired
    if (response.status === 401) {
      // Hapus token dari cookie dan redirect ke login
      document.cookie = 'user_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      
      // Redirect ke login dengan current path sebagai redirect parameter
      const currentPath = window.location.pathname + window.location.search;
      const loginUrl = `/auth/login?redirect=${encodeURIComponent(currentPath)}`;
      window.location.href = loginUrl;
      
      throw new Error('Session expired. Redirecting to login...');
    }
    
    return response;
  } catch (error) {
    // Re-throw error untuk handling di level komponen
    throw error;
  }
}

// Hook untuk menggunakan apiClient dengan context
export function useApiClient() {
  const router = useRouter();
  const { logout } = useAuth();
  
  const apiClientWithAuth = async (url: string, options: RequestInit = {}) => {
    const defaultOptions: RequestInit = {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);
      
      // Jika response 401 (Unauthorized), token kemungkinan expired
      if (response.status === 401) {
        console.log('🔒 Session expired, redirecting to login...');
        
        // Gunakan logout function dari AuthContext untuk cleanup yang proper
        await logout();
        
        throw new Error('Session expired. Redirecting to login...');
      }
      
      return response;
    } catch (error) {
      // Re-throw error untuk handling di level komponen
      throw error;
    }
  };
  
  return apiClientWithAuth;
}

// Wrapper untuk fetch yang otomatis handle 401
export async function fetchWithAuth(url: string, options: RequestInit = {}) {
  const defaultOptions: RequestInit = {
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(url, defaultOptions);
  
  // Jika response 401 (Unauthorized), token kemungkinan expired
  if (response.status === 401) {
    // Hapus token dari cookie
    document.cookie = 'user_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    
    // Redirect ke login dengan current path sebagai redirect parameter
    const currentPath = window.location.pathname + window.location.search;
    const loginUrl = `/auth/login?redirect=${encodeURIComponent(currentPath)}`;
    
    // Tampilkan notifikasi sebelum redirect
    if (typeof window !== 'undefined') {
      alert('⚠️ Sesi Anda telah berakhir. Silakan login kembali.');
      window.location.href = loginUrl;
    }
    
    throw new Error('Session expired');
  }
  
  return response;
}