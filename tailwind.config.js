const daisyuiThemes = require("daisyui/src/theming/themes");

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    screens: {
      'xs': '480px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      colors: {
        primary: '#F4BB45', // <PERSON>na kuning/oranye dari logo
        'primary-light': '#FFD670', // Varian lebih terang
        'primary-dark': '#D8A03A', // Varian lebih gelap
        secondary: '#4E9E97', // <PERSON>na teal/hijau dari logo
        'secondary-light': '#7BC7C1', // Varian lebih terang
        'secondary-dark': '#3E7C77', // Varian lebih gelap
        accent: '#7BC7C1', // Tambahkan accent di sini
      },
      animation: {
        'subtle-zoom': 'subtle-zoom 15s ease-in-out infinite',
      },
      keyframes: {
        'subtle-zoom': {
          '0%, 100%': { transform: 'scale(1.1)' },
          '50%': { transform: 'scale(1.15)' },
        }
      }
    },
  },
  plugins: [
    require("daisyui")
  ],
  // Konfigurasi DaisyUI
  daisyui: {
    themes: [
      {
        breaktime: {
          ...daisyuiThemes["light"],
          "primary": "#F4BB45",        // Oranye
          "primary-focus": "#D8A03A", // Lebih gelap untuk focus (bisa primary-dark)
          "primary-content": "#43300F", // Konten di atas primary (gelap)
          "secondary": "#4E9E97",      // Teal
          "secondary-focus": "#3E7C77", // Lebih gelap untuk focus (bisa secondary-dark)
          "secondary-content": "#FFFFFF", // Konten di atas secondary (putih)
          "accent": "#7BC7C1",        // Bisa secondary-light atau warna lain
          "neutral": "#3D4451",        // Warna netral (dari default light)
          "base-100": "#FFFFFF",        // Background utama (putih)
          "base-200": "#F9FAFB",        // Background sedikit lebih gelap (misal untuk area hover)
          "base-300": "#F3F4F6",        // Background lebih gelap lagi
          "base-content": "#1F2937",    // Warna teks default (gelap)
          // Sesuaikan warna lain jika perlu (info, success, warning, error)
          "info": "#3ABFF8",
          "success": "#36D399",
          "warning": "#FBBD23",
          "error": "#F87272",
        },
        "breaktime-dark": {
          ...daisyuiThemes["dark"], // Mulai dari tema dark
          "primary": "#F4BB45",       // Oranye tetap sama atau sedikit desaturasi/terang?
          "primary-focus": "#FFD670", // Mungkin lebih terang untuk focus di dark mode?
          "primary-content": "#43300F", // Konten di atas primary (gelap agar kontras)
          "secondary": "#4E9E97",     // Teal tetap sama atau sedikit lebih terang?
          "secondary-focus": "#7BC7C1",// Mungkin lebih terang untuk focus
          "secondary-content": "#FFFFFF", // Konten di atas secondary (putih)
          "accent": "#D8A03A",       // Bisa primary-dark atau warna lain
          "base-100": "#1F2937",     // Background utama gelap (bukan hitam pekat)
          "base-200": "#2A323C",     // Background sedikit lebih terang
          "base-300": "#3D4451",     // Background lebih terang lagi
          "base-content": "#A6ADBB",   // Warna teks default (terang, tidak putih pekat)
          // Sesuaikan warna lain jika perlu (info, success, warning, error)
          "info": "#3ABFF8",
          "success": "#36D399",
          "warning": "#FBBD23",
          "error": "#F87272",
        },
      },
    ],
    darkTheme: "breaktime-dark", // Tentukan tema mana yang dianggap dark
    base: true, // Menerapkan warna dasar dan utilitas
    styled: true, // Menerapkan style komponen DaisyUI
    utils: true, // Menambahkan kelas utilitas DaisyUI
    logs: true, // Menampilkan log DaisyUI saat build (berguna untuk debug)
  },
};