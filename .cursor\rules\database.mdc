---
description:
globs:
alwaysApply: true
---
# Panduan Database (Prisma) 💾

Proyek ini menggunakan Prisma sebagai ORM.

- **Skema**: Skema database didefinisikan dalam file `[schema.prisma](mdc:prisma/schema.prisma)`.
- **Migrasi**: File migrasi database disimpan di direktori `prisma/migrations/`. Setiap migrasi memiliki folder sendiri.
- **Klien Prisma**: Klien Prisma yang dihasilkan secara otomatis berada di `prisma/generated/client/`. Klien ini digunakan untuk berinteraksi dengan database dalam kode aplikasi.
- **Perbaikan Impor**: Skrip `[fix-prisma-imports.js](mdc:fix-prisma-imports.js)` mungkin digunakan untuk mengatasi masalah impor Prisma.