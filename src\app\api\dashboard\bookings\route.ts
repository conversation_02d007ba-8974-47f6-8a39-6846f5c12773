import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET: Mendapatkan semua booking
export async function GET(request: Request) {
  try {
    // Mendapatkan query parameters
    const url = new URL(request.url);
    const outletId = url.searchParams.get('outletId');
    const fromDate = url.searchParams.get('fromDate');
    const toDate = url.searchParams.get('toDate');

    // Build query filter
    const filter: Record<string, unknown> = {};
    
    if (outletId) {
      filter.outletId = outletId;
    }

    if (fromDate && toDate) {
      filter.bookingDate = {
        gte: new Date(fromDate),
        lte: new Date(toDate),
      };
    } else if (fromDate) {
      filter.bookingDate = {
        gte: new Date(fromDate),
      };
    } else if (toDate) {
      filter.bookingDate = {
        lte: new Date(toDate),
      };
    }

    // Perlu disesuaikan dengan model database yang aktual
    const bookings = await prisma.booking.findMany({
      where: filter,
      include: {
        customer: true,
        therapist: true,
        bookingServices: {
          include: {
            service: true
          }
        }
      },
      orderBy: [
        { bookingDate: 'asc' },
        { bookingTime: 'asc' }
      ]
    });

    return NextResponse.json(bookings);
  } catch (error) {
    console.error('Error getting bookings:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data booking' },
      { status: 500 }
    );
  }
}

// POST: Membuat booking baru
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { 
      outletId, 
      customerId, 
      therapistId, 
      createdById, // User yang membuat booking
      bookingDate, 
      bookingTime, 
      services, 
      notes 
    } = body;

    // Validasi data
    if (!outletId || !customerId || !therapistId || !createdById || !bookingDate || !bookingTime || !services || services.length === 0) {
      return NextResponse.json(
        { error: 'Semua field wajib diisi' },
        { status: 400 }
      );
    }

    // Ambil data layanan untuk kalkulasi harga
    const servicesData = await prisma.service.findMany({
      where: {
        id: {
          in: services
        }
      }
    });

    // Buat booking
    const booking = await prisma.booking.create({
      data: {
        outletId,
        customerId,
        therapistId,
        createdById,
        bookingDate: new Date(bookingDate),
        bookingTime,
        status: 'PENDING', // Menggunakan string literal matching enum di database
        notes: notes || '',
        bookingServices: {
          create: servicesData.map(service => ({
            serviceId: service.id,
            price: service.price
          }))
        }
      },
      include: {
        customer: true,
        therapist: true,
        bookingServices: {
          include: {
            service: true
          }
        }
      }
    });

    return NextResponse.json({
      message: 'Booking berhasil dibuat',
      booking
    });
  } catch (error) {
    console.error('Error creating booking:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat booking' },
      { status: 500 }
    );
  }
}

// PUT: Update booking
export async function PUT(request: Request) {
  try {
    const body = await request.json();
    const { 
      id,
      therapistId, 
      bookingDate, 
      bookingTime, 
      services, 
      notes,
      status
    } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID booking harus disediakan' },
        { status: 400 }
      );
    }

    // Cek apakah booking ada
    const existingBooking = await prisma.booking.findUnique({
      where: { id }
    });

    if (!existingBooking) {
      return NextResponse.json(
        { error: 'Booking tidak ditemukan' },
        { status: 404 }
      );
    }

    // Data untuk update
    const updateData: Record<string, unknown> = {};
    
    if (therapistId) updateData.therapistId = therapistId;
    if (bookingDate) updateData.bookingDate = new Date(bookingDate);
    if (bookingTime) updateData.bookingTime = bookingTime;
    if (notes !== undefined) updateData.notes = notes;
    if (status) updateData.status = status;

    // Update booking
    const updatedBooking = await prisma.booking.update({
      where: { id },
      data: updateData
    });

    // Jika ada perubahan services
    if (services && services.length > 0) {
      // Hapus services yang ada
      await prisma.bookingService.deleteMany({
        where: { bookingId: id }
      });

      // Ambil data layanan untuk kalkulasi harga
      const servicesData = await prisma.service.findMany({
        where: {
          id: {
            in: services
          }
        }
      });

      // Tambahkan services baru
      await Promise.all(servicesData.map(service => 
        prisma.bookingService.create({
          data: {
            bookingId: id,
            serviceId: service.id,
            price: service.price
          }
        })
      ));
    }

    return NextResponse.json({
      message: 'Booking berhasil diperbarui',
      booking: updatedBooking
    });
  } catch (error) {
    console.error('Error updating booking:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui booking' },
      { status: 500 }
    );
  }
}

// DELETE: Hapus booking
export async function DELETE(request: Request) {
  try {
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'ID booking harus disediakan' },
        { status: 400 }
      );
    }

    // Cek apakah booking ada
    const existingBooking = await prisma.booking.findUnique({
      where: { id }
    });

    if (!existingBooking) {
      return NextResponse.json(
        { error: 'Booking tidak ditemukan' },
        { status: 404 }
      );
    }

    // Hapus booking (cascade akan menghapus bookingServices)
    await prisma.booking.delete({
      where: { id }
    });

    return NextResponse.json({
      message: 'Booking berhasil dihapus'
    });
  } catch (error) {
    console.error('Error deleting booking:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus booking' },
      { status: 500 }
    );
  }
} 