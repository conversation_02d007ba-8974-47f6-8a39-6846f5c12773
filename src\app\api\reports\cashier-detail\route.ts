import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const cashierId = searchParams.get('cashierId');
    const outletId = searchParams.get('outletId');

    // Ambil parameter tanggal dari query
    const rawStartDate = searchParams.get('startDate');
    const rawEndDate = searchParams.get('endDate');

    if (!cashierId || !rawStartDate || !rawEndDate) {
      throw new Error('cashierId, tanggal mulai, dan tanggal akhir harus disediakan');
    }

    // Buat tanggal dengan pendekatan yang sama seperti di reports/route.ts
    // Format: YYYY-MM-DD -> parse sebagai tanggal lokal
    const [startYear, startMonth, startDay] = rawStartDate.split('-').map(Number);
    const [endYear, endMonth, endDay] = rawEndDate.split('-').map(Number);

    // Buat tanggal dengan komponen tanggal lokal
    // Bulan dalam JavaScript dimulai dari 0 (Januari = 0)
    const startDate = new Date(startYear, startMonth - 1, startDay, 0, 0, 0, 0);
    const endDate = new Date(endYear, endMonth - 1, endDay, 23, 59, 59, 999);

    // Validasi tanggal
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.log(`Invalid date: startDate=${startDate}, endDate=${endDate}`);
      throw new Error('Format tanggal tidak valid');
    }
    
    // Log tanggal yang akan digunakan
    console.log(`[API GET /api/reports/cashier-detail] Using dates: startDate=${startDate.toISOString()}, endDate=${endDate.toISOString()}`);

    // Filter dasar untuk transaksi berdasarkan tanggal, kasir, dan outlet
    const transactionWhere: Prisma.TransactionWhereInput = {
      transactionDate: { gte: startDate, lte: endDate },
      createdById: cashierId,
      ...(outletId && { outletId: outletId }),
    };

    // Ambil data kasir untuk informasi nama
    const cashier = await prisma.user.findUnique({
      where: { id: cashierId },
      select: {
        id: true,
        name: true,
        username: true,
      },
    });

    if (!cashier) {
      throw new Error('Kasir tidak ditemukan');
    }

    // Ambil data outlet jika outletId disediakan
    let outlet = null;
    if (outletId) {
      outlet = await prisma.outlet.findUnique({
        where: { id: outletId },
        select: {
          id: true,
          name: true,
        },
      });
    }

    // Ambil semua transaksi dengan detail lengkap
    const transactions = await prisma.transaction.findMany({
      where: transactionWhere,
      select: {
        id: true,
        displayId: true,
        transactionDate: true,
        totalAmount: true,
        paymentMethod: true,
        customer: {
          select: {
            id: true,
            name: true,
          },
        },
        therapist: {
          select: {
            id: true,
            name: true,
          },
        },
        outlet: {
          select: {
            id: true,
            name: true,
          },
        },
        transactionItems: {
          select: {
            quantity: true,
            price: true,
            service: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        transactionDate: 'desc',
      },
    });

    // Transform data untuk response
    const transformedTransactions = transactions.map(transaction => ({
      id: transaction.id,
      displayId: transaction.displayId,
      transactionDate: transaction.transactionDate.toISOString(),
      totalAmount: transaction.totalAmount,
      customerName: transaction.customer?.name || 'Walk-in Customer',
      therapistName: transaction.therapist?.name || 'Unknown',
      paymentMethod: transaction.paymentMethod || 'Unknown',
      services: transaction.transactionItems.map(item => ({
        name: item.service.name,
        quantity: item.quantity,
        price: item.price,
      })),
    }));

    // Hitung statistik
    const totalTransactions = transactions.length;
    const totalAmount = transactions.reduce((sum, trx) => sum + trx.totalAmount, 0);

    const cashierDetail = {
      cashierName: cashier.name,
      outletName: outlet?.name || 'Semua Outlet',
      totalTransactions,
      totalAmount,
      transactions: transformedTransactions,
    };

    return NextResponse.json({
      message: 'Detail transaksi kasir berhasil diambil',
      cashierDetail,
      filters: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        cashierId,
        outletId,
      },
    });
  } catch (error: unknown) {
    console.error('[API GET /api/reports/cashier-detail] Error:', error);

    let errorMessage = 'Terjadi kesalahan saat mengambil detail transaksi kasir';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat mengambil detail transaksi kasir',
      cashierDetail: null,
      filters: {
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        cashierId: null,
        outletId: null,
      },
    }, { status: statusCode });
  }
} 