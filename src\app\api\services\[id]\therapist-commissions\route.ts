import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserIdFromToken } from '@/lib/auth-utils';
import { logService } from '@/lib/logger';

// GET komisi terapis khusus untuk layanan tertentu
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const serviceId = params.id;

    if (!serviceId) {
      return NextResponse.json(
        { error: 'ID layanan diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah layanan ada
    const service = await prisma.service.findUnique({
      where: { id: serviceId }
    });

    if (!service) {
      return NextResponse.json(
        { error: 'Layanan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Ambil semua komisi terapis khusus untuk layanan ini
    const therapistCommissions = await prisma.therapistServiceCommission.findMany({
      where: { serviceId },
      include: {
        therapist: {
          select: {
            id: true,
            name: true,
            outletId: true,
            outlet: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    });

    // Ambil semua terapis yang belum memiliki komisi khusus untuk layanan ini
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');

    let whereClause: any = {
      isActive: true,
      id: {
        notIn: therapistCommissions.map(tc => tc.therapistId)
      }
    };

    if (outletId) {
      whereClause.outletId = outletId;
    }

    const availableTherapists = await prisma.therapist.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        outletId: true,
        outlet: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    return NextResponse.json({
      message: 'Data komisi terapis berhasil diambil',
      therapistCommissions,
      availableTherapists,
      defaultCommission: service.commission
    });
  } catch (error) {
    console.error('Error fetching therapist commissions:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data komisi terapis' },
      { status: 500 }
    );
  }
}

// POST untuk menambahkan komisi terapis khusus
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const serviceId = params.id;
    const body = await request.json();
    const { therapistId, commission } = body;

    if (!serviceId || !therapistId || commission === undefined) {
      return NextResponse.json(
        { error: 'ID layanan, ID terapis, dan komisi diperlukan' },
        { status: 400 }
      );
    }

    // Validasi nilai komisi
    if (commission === undefined || commission === null) {
      return NextResponse.json(
        { error: 'Nilai komisi harus diisi' },
        { status: 400 }
      );
    }

    // Pastikan nilai komisi adalah angka
    if (typeof commission !== 'number') {
      return NextResponse.json(
        { error: 'Nilai komisi harus berupa angka' },
        { status: 400 }
      );
    }

    // Pastikan nilai komisi tidak negatif
    if (commission < 0) {
      return NextResponse.json(
        { error: 'Nilai komisi tidak boleh negatif' },
        { status: 400 }
      );
    }

    // Cek apakah layanan ada
    const service = await prisma.service.findUnique({
      where: { id: serviceId }
    });

    if (!service) {
      return NextResponse.json(
        { error: 'Layanan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Cek apakah terapis ada
    const therapist = await prisma.therapist.findUnique({
      where: { id: therapistId }
    });

    if (!therapist) {
      return NextResponse.json(
        { error: 'Terapis tidak ditemukan' },
        { status: 404 }
      );
    }

    // Cek apakah komisi terapis khusus sudah ada
    const existingCommission = await prisma.therapistServiceCommission.findUnique({
      where: {
        therapistId_serviceId: {
          therapistId,
          serviceId
        }
      }
    });

    if (existingCommission) {
      return NextResponse.json(
        { error: 'Komisi khusus untuk terapis ini sudah ada' },
        { status: 409 }
      );
    }

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Buat komisi terapis khusus baru
    const newCommission = await prisma.therapistServiceCommission.create({
      data: {
        therapistId,
        serviceId,
        commission
      },
      include: {
        therapist: {
          select: {
            name: true,
            outlet: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        service: {
          select: {
            name: true
          }
        }
      }
    });

    // Log pembuatan komisi terapis khusus
    await logService(
      'update',
      serviceId,
      {
        action: 'add_therapist_commission',
        therapistId,
        therapistName: newCommission.therapist.name,
        serviceName: newCommission.service.name,
        commission
      },
      newCommission.therapist.outlet.id,
      userId
    );

    return NextResponse.json({
      message: 'Komisi terapis khusus berhasil ditambahkan',
      therapistCommission: newCommission
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating therapist commission:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menambahkan komisi terapis khusus' },
      { status: 500 }
    );
  }
}
