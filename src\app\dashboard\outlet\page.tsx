'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  FiPlus, FiSearch, FiEdit, FiTrash2, FiMapPin,
  FiInbox, FiCheck, FiX, FiHome, FiPhone, FiClock
} from 'react-icons/fi';
// Import toast dari sonner
import { toast } from 'sonner';

// --- Interfaces ---
// Definisi interface untuk outlet
interface Outlet {
  id: string;
  name: string;
  address: string;
  phone: string;
  operationalHours: string;
  isOpen: boolean;
  isMain: boolean;
  createdAt: string;
}

// Varian Animasi
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
};
const staggerContainer = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

export default function OutletManagementPage() {
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // State Modal Add/Edit Outlet
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
  const [selectedOutletForEdit, setSelectedOutletForEdit] = useState<Outlet | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false); // State loading submit
  const [formDataOutlet, setFormDataOutlet] = useState<Omit<Outlet, 'id' | 'createdAt'>>({
    name: '',
    address: '',
    phone: '',
    operationalHours: '',
    isOpen: true,
    isMain: false
  });

  // Ekstrak fungsi fetchOutlets agar bisa dipanggil dari tempat lain
  const fetchOutlets = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/outlets');
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Gagal memuat outlet (Status: ${response.status})`);
      }
      const data = await response.json();
      setOutlets(data.outlets || []);
    } catch (error) {
      console.error("Gagal load data outlet dari API:", error);
      // Tampilkan toast error jika API gagal
      toast.error(`Gagal memuat data outlet: ${error instanceof Error ? error.message : 'Error tidak diketahui'}`);
      setOutlets([]); // Reset jika error
    } finally {
      setIsLoading(false);
    }
  };

  // Load Outlet Data dari API
  useEffect(() => {
    fetchOutlets();
  }, []);

  // Filter Outlets
  const filteredOutlets = useMemo(() => {
    if (!searchTerm) return outlets;
    const lowerSearch = searchTerm.toLowerCase();
    return outlets.filter(o =>
      o.name.toLowerCase().includes(lowerSearch) ||
      o.address.toLowerCase().includes(lowerSearch) ||
      o.phone.toLowerCase().includes(lowerSearch)
    );
  }, [outlets, searchTerm]);

  // --- Handlers Add/Edit Outlet ---
  const handleOpenAddEditModal = (outlet: Outlet | null = null) => {
    setSelectedOutletForEdit(outlet);
    if (outlet) {
      setFormDataOutlet({
        name: outlet.name,
        address: outlet.address,
        phone: outlet.phone,
        operationalHours: outlet.operationalHours,
        isOpen: outlet.isOpen,
        isMain: outlet.isMain
      });
    } else {
      setFormDataOutlet({
        name: '',
        address: '',
        phone: '',
        operationalHours: '',
        isOpen: true,
        isMain: false
      });
    }
    setIsAddEditModalOpen(true);
  };

  const handleCloseAddEditModal = () => {
    setIsAddEditModalOpen(false);
    setSelectedOutletForEdit(null); // Reset juga outlet yg dipilih
    setIsSubmitting(false); // Reset loading
  }

  const handleInputChangeOutlet = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormDataOutlet(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormDataOutlet(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSaveOutlet = async () => {
    // Validasi dasar, gunakan toast untuk error
    if (!formDataOutlet.name.trim()) return toast.error('Nama Outlet wajib diisi.');
    if (!formDataOutlet.address.trim()) return toast.error('Alamat wajib diisi.');
    if (!formDataOutlet.phone.trim()) return toast.error('Nomor telepon wajib diisi.');
    if (!formDataOutlet.operationalHours.trim()) return toast.error('Jam operasional wajib diisi.');

    setIsSubmitting(true);
    const isEditMode = !!selectedOutletForEdit;
    const url = isEditMode ? `/api/outlets/${selectedOutletForEdit.id}` : '/api/outlets';
    const method = isEditMode ? 'PUT' : 'POST';

    try {
      const response = await fetch(url, {
        method: method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formDataOutlet),
      });

      const result = await response.json(); // Baca response JSON

      if (!response.ok) {
        throw new Error(result.error || `Gagal ${isEditMode ? 'memperbarui' : 'menambah'} outlet`);
      }

      // Tampilkan toast sukses
      toast.success(`Outlet ${result.outlet.name} berhasil ${isEditMode ? 'diperbarui' : 'ditambahkan'}!`);

      // Update state lokal setelah sukses
      if (isEditMode) {
        // Jika outlet ini diset sebagai utama, update outlet lain
        let updatedOutlets = outlets;
        if (result.outlet.isMain) {
           updatedOutlets = outlets.map(o =>
              o.id === result.outlet.id ? result.outlet : { ...o, isMain: false }
           );
        } else {
          updatedOutlets = outlets.map(o =>
             o.id === result.outlet.id ? result.outlet : o
          );
        }
        setOutlets(updatedOutlets);
      } else {
        // Jika outlet baru ini utama, update outlet lain
        let newOutletList = [...outlets, result.outlet];
        if (result.outlet.isMain) {
            newOutletList = newOutletList.map(o =>
               o.id === result.outlet.id ? o : { ...o, isMain: false }
            );
        }
        setOutlets(newOutletList);
      }

      handleCloseAddEditModal(); // Tutup modal setelah sukses

    } catch (error) {
      console.error(`Error saving outlet:`, error);
      // Tampilkan toast error
      toast.error(`Gagal menyimpan outlet: ${error instanceof Error ? error.message : 'Error tidak diketahui'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteOutlet = async (outletId: string, outletName: string, isMain: boolean) => {
    // Validasi: jangan hapus outlet utama
    if (isMain) {
      return toast.error('Outlet utama tidak dapat dihapus. Jadikan outlet lain sebagai utama terlebih dahulu.');
    }

    // Konfirmasi dengan toast atau modal (contoh dengan toast)
    toast.warning(
      <div className="flex flex-col gap-2">
        <p>Anda yakin ingin menghapus outlet <strong>{outletName}</strong>? Tindakan ini tidak dapat dibatalkan.</p>
        <div className="flex gap-2 justify-end">
          <button className="btn btn-xs btn-outline" onClick={() => toast.dismiss()}>Batal</button>
          <button className="btn btn-xs btn-error" onClick={async () => {
            toast.dismiss(); // Tutup toast konfirmasi
            toast.promise(
              // Langsung panggil fetch DELETE di sini
              fetch(`/api/outlets/${outletId}`, { method: 'DELETE' })
                .then(async (res) => {
                  if (!res.ok) {
                     const errorData = await res.json().catch(() => ({}));
                    throw new Error(errorData.error || `Gagal menghapus (Status: ${res.status})`);
                  }
                  return res.json();
                })
                .then(() => {
                  // Refresh data outlet dari API setelah sukses delete
                  return fetchOutlets();
                }),
              {
                loading: `Menghapus ${outletName}...`,
                success: `Outlet ${outletName} berhasil dihapus!`, // Pesan sukses akan muncul dari promise
                error: (err) => `Gagal menghapus ${outletName}: ${err.message}` // Pesan error dari promise
              }
            );
          }}>Ya, Hapus</button>
        </div>
      </div>,
      { duration: Infinity } // Biarkan toast tampil sampai user klik tombol
    );
  };

  const handleToggleOutletStatus = async (outletId: string, isCurrentlyOpen: boolean) => {
    const action = isCurrentlyOpen ? 'menutup' : 'membuka';
    const newStatus = !isCurrentlyOpen;

    toast.promise(
      fetch(`/api/outlets/${outletId}`, {
        method: 'PUT',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({ isOpen: newStatus }) // Kirim hanya status isOpen
      })
      .then(async (res) => {
        if (!res.ok) {
          const errorData = await res.json().catch(() => ({}));
          throw new Error(errorData.error || `Gagal mengubah status (Status: ${res.status})`);
        }
        return res.json();
      })
      .then((data) => {
        // Update state setelah sukses
        setOutlets(prev => prev.map(o => o.id === outletId ? data.outlet : o));
        // Tidak perlu saveOutlets
      }),
      {
        loading: `Sedang ${action} outlet...`,
        success: `Outlet berhasil ${isCurrentlyOpen ? 'ditutup' : 'dibuka'}.`,
        error: (err) => `Gagal ${action} outlet: ${err.message}`
      }
    );
  };
  // --- End Handlers Add/Edit Outlet ---

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <span className="loading loading-spinner loading-lg text-primary"></span>
      </div>
    );
  }

  return (
    <motion.div
      className="container mx-auto p-4 md:p-6"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {/* Header & Tombol Tambah */}
      <motion.div variants={fadeInUp} className="flex justify-between items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-gray-800">Manajemen Outlet</h1>
        <button onClick={() => handleOpenAddEditModal()} className="btn btn-primary">
          <FiPlus className="mr-2"/> Tambah Outlet
        </button>
      </motion.div>

      {/* Search Bar */}
      <motion.div variants={fadeInUp} className="mb-6">
         <div className="form-control">
            <div className="join">
               <input
                 type="text"
                 placeholder="Cari nama outlet, alamat, atau nomor telepon..."
                 className="input input-bordered join-item w-full lg:w-1/2 placeholder:text-gray-400 text-gray-700"
                 value={searchTerm}
                 onChange={(e) => setSearchTerm(e.target.value)}
                />
               <button className="btn btn-ghost join-item">
                 <FiSearch className="text-gray-500"/>
               </button>
            </div>
         </div>
      </motion.div>

      {/* Daftar Outlet dalam Card */}
      <motion.div
        variants={fadeInUp}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
      >
        {filteredOutlets.length === 0 && (
          <div className="col-span-full flex flex-col items-center justify-center p-12 text-center text-base-content/70 bg-base-200/30 rounded-lg">
            <FiInbox className="w-16 h-16 mb-4 text-base-content/30"/>
            <p className="text-lg font-medium">
              {searchTerm ? `Tidak ada outlet cocok dengan "${searchTerm}".` : 'Belum ada data outlet.'}
            </p>
            <p className="mt-2 max-w-md">
              {searchTerm
                ? 'Coba kata kunci lain atau reset pencarian.'
                : 'Tambahkan outlet pertama Anda dengan mengklik tombol "Tambah Outlet" di atas.'}
            </p>
          </div>
        )}

        {filteredOutlets.map((outlet) => (
          <motion.div
            key={outlet.id}
            variants={fadeInUp}
            className="card bg-base-100 shadow-md border border-base-300 overflow-hidden"
          >
            <div className="card-body p-5">
              <div className="flex justify-between items-start">
                <h3 className="card-title text-base-content flex items-center gap-1">
                  <FiHome className={outlet.isMain ? 'text-primary' : 'text-base-content/60'} />
                  {outlet.name}
                  {outlet.isMain && (
                    <span className="badge badge-primary badge-sm text-xs font-normal">Utama</span>
                  )}
                </h3>

                <div className="dropdown dropdown-end">
                  <label tabIndex={0} className="btn btn-ghost btn-xs">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="inline-block w-5 h-5 stroke-current"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z"></path></svg>
                  </label>
                  <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52 text-base-content">
                    <li>
                      <button onClick={() => handleOpenAddEditModal(outlet)}>
                        <FiEdit className="text-info" /> Edit Outlet
                      </button>
                    </li>
                    <li>
                      <button
                        onClick={() => handleToggleOutletStatus(outlet.id, outlet.isOpen)}
                        className={outlet.isOpen ? 'text-error' : 'text-success'}
                      >
                        {outlet.isOpen
                          ? <><FiX className="text-error" /> Tutup Outlet</>
                          : <><FiCheck className="text-success" /> Buka Outlet</>
                        }
                      </button>
                    </li>
                    <li>
                      <button
                        onClick={() => handleDeleteOutlet(outlet.id, outlet.name, outlet.isMain)}
                        className="text-error"
                        disabled={outlet.isMain}
                      >
                        <FiTrash2 /> Hapus Outlet
                      </button>
                    </li>
                  </ul>
                </div>
              </div>

              <div className="mt-2 space-y-2">
                <div className="flex items-start text-base-content/80">
                  <FiMapPin className="min-w-[18px] h-[18px] mt-1 mr-2 text-base-content/60" />
                  <p className="text-sm">{outlet.address}</p>
                </div>

                <div className="flex items-center text-base-content/80">
                  <FiPhone className="min-w-[18px] h-[18px] mr-2 text-base-content/60" />
                  <p className="text-sm">{outlet.phone}</p>
                </div>

                <div className="flex items-center text-base-content/80">
                  <FiClock className="min-w-[18px] h-[18px] mr-2 text-base-content/60" />
                  <p className="text-sm">{outlet.operationalHours}</p>
                </div>
              </div>

              <div className="mt-4 pt-2 border-t border-base-200">
                <div className="flex items-center justify-between">
                  <span className={`text-sm font-medium ${outlet.isOpen ? 'text-success' : 'text-error'}`}>
                    {outlet.isOpen ? 'Buka' : 'Tutup'}
                  </span>
                  <div className="flex gap-2">
                    <button
                      className="btn btn-sm btn-outline btn-info"
                      onClick={() => handleOpenAddEditModal(outlet)}
                    >
                      <FiEdit className="h-4 w-4" /> Edit
                    </button>
                    <button
                      className={`btn btn-sm ${outlet.isOpen ? 'btn-error' : 'btn-success'}`}
                      onClick={() => handleToggleOutletStatus(outlet.id, outlet.isOpen)}
                    >
                      {outlet.isOpen ? <FiX className="h-4 w-4" /> : <FiCheck className="h-4 w-4" />}
                      {outlet.isOpen ? 'Tutup' : 'Buka'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* --- Modal Tambah/Edit Outlet --- */}
      <dialog id="outlet_add_edit_modal" className={`modal ${isAddEditModalOpen ? 'modal-open' : ''}`}>
         <div className="modal-box w-11/12 max-w-md border border-base-300">
            <button onClick={handleCloseAddEditModal} className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2 z-10 text-base-content">✕</button>
            <h3 className="font-bold text-lg mb-4 text-base-content">
              {selectedOutletForEdit ? 'Edit Outlet' : 'Tambah Outlet Baru'}
            </h3>
            <div className="space-y-4 py-4">
               <div className="form-control">
                 <label className="label">
                   <span className="label-text text-base-content/80">
                     Nama Outlet<span className="text-error">*</span>
                   </span>
                 </label>
                 <input
                   type="text"
                   name="name"
                   placeholder="Nama outlet"
                   value={formDataOutlet.name}
                   onChange={handleInputChangeOutlet}
                   className="input input-bordered w-full text-base-content"
                 />
               </div>
               <div className="form-control">
                 <label className="label">
                   <span className="label-text text-base-content/80">
                     Alamat<span className="text-error">*</span>
                   </span>
                 </label>
                 <textarea
                   name="address"
                   placeholder="Alamat lengkap outlet"
                   value={formDataOutlet.address}
                   onChange={handleInputChangeOutlet}
                   className="textarea textarea-bordered w-full text-base-content h-20"
                 />
               </div>
               <div className="form-control">
                  <label className="label">
                    <span className="label-text text-base-content/80">
                      Nomor Telepon<span className="text-error">*</span>
                    </span>
                  </label>
                  <input
                    type="text"
                    name="phone"
                    placeholder="Nomor telepon outlet"
                    value={formDataOutlet.phone}
                    onChange={handleInputChangeOutlet}
                    className="input input-bordered w-full text-base-content"
                  />
               </div>
               <div className="form-control">
                  <label className="label">
                    <span className="label-text text-base-content/80">
                      Jam Operasional<span className="text-error">*</span>
                    </span>
                  </label>
                  <input
                    type="text"
                    name="operationalHours"
                    placeholder="Contoh: 10:00 - 22:00"
                    value={formDataOutlet.operationalHours}
                    onChange={handleInputChangeOutlet}
                    className="input input-bordered w-full text-base-content"
                  />
               </div>

               <div className="form-control">
                  <label className="label cursor-pointer justify-start gap-4">
                    <span className="label-text text-base-content/80">
                      Status Buka
                    </span>
                    <input
                      type="checkbox"
                      name="isOpen"
                      checked={formDataOutlet.isOpen}
                      onChange={handleInputChangeOutlet}
                      className="toggle toggle-success toggle-sm"
                    />
                  </label>
                  <p className="text-xs text-base-content/70 ml-4 mt-1">
                    Outlet yang tutup tidak akan muncul di daftar outlet yang tersedia untuk transaksi.
                  </p>
               </div>

               <div className="form-control">
                  <label className="label cursor-pointer justify-start gap-4">
                    <span className="label-text text-base-content/80">
                      Outlet Utama
                    </span>
                    <input
                      type="checkbox"
                      name="isMain"
                      checked={formDataOutlet.isMain}
                      onChange={handleInputChangeOutlet}
                      className="toggle toggle-primary toggle-sm"
                    />
                  </label>
                  <p className="text-xs text-base-content/70 ml-4 mt-1">
                    Hanya boleh ada satu outlet utama. Mengubah outlet ini menjadi utama akan mengubah status outlet lain menjadi bukan utama.
                  </p>
               </div>
            </div>
            <div className="modal-action">
              <button className="btn btn-sm" onClick={handleCloseAddEditModal}>Batal</button>
              <button className="btn btn-primary btn-sm" onClick={handleSaveOutlet}>
                Simpan
              </button>
            </div>
         </div>
         <form method="dialog" className="modal-backdrop">
           <button onClick={handleCloseAddEditModal}>close</button>
         </form>
      </dialog>

    </motion.div>
  );
}