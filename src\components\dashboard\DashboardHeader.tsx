import React from 'react';

interface DashboardHeaderProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  actions?: React.ReactNode;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  title,
  description,
  icon,
  actions
}) => {
  return (
    <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
      <div className="flex items-center">
        {icon && <div className="mr-3 text-primary">{icon}</div>}
        <div>
          <h1 className="text-2xl font-bold">{title}</h1>
          {description && <p className="text-gray-500 mt-1">{description}</p>}
        </div>
      </div>
      {actions && (
        <div className="flex items-center gap-2 md:justify-end">
          {actions}
        </div>
      )}
    </div>
  );
};

export default DashboardHeader; 