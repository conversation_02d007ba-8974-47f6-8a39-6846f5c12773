import React, { useState, useEffect } from 'react';
import { format, startOfMonth, endOfDay, parse } from 'date-fns';
import { id } from 'date-fns/locale';
import { motion } from 'framer-motion';
import {
  CalendarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  AdjustmentsHorizontalIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  ArrowPathIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

interface Outlet {
  id: string;
  name: string;
}

interface FilterProps {
  onFilter: (filters: any) => void;
  outlets: Outlet[];
  loading: boolean;
  initialFilters?: {
    startDate?: string;
    endDate?: string;
    outletId?: string;
    type?: string;
  };
}

const AttendanceFilter: React.FC<FilterProps> = ({
  onFilter,
  outlets,
  loading,
  initialFilters = {}
}) => {
  // State untuk filter
  const [startDate, setStartDate] = useState(initialFilters.startDate || '');
  const [endDate, setEndDate] = useState(initialFilters.endDate || '');
  const [selectedOutlet, setSelectedOutlet] = useState(initialFilters.outletId || '');
  const [selectedType, setSelectedType] = useState(initialFilters.type || '');
  
  // State untuk kalender
  const [showStartDateCalendar, setShowStartDateCalendar] = useState(false);
  const [showEndDateCalendar, setShowEndDateCalendar] = useState(false);
  const [startDateMonth, setStartDateMonth] = useState(new Date());
  const [endDateMonth, setEndDateMonth] = useState(new Date());

  // Set default date range (tanggal 1 bulan berjalan sampai hari ini)
  useEffect(() => {
    if (!initialFilters.startDate && !initialFilters.endDate) {
      const today = new Date();
      const firstDayOfMonth = startOfMonth(today);
      
      setStartDate(format(firstDayOfMonth, 'yyyy-MM-dd'));
      setEndDate(format(today, 'yyyy-MM-dd'));
      
      setStartDateMonth(firstDayOfMonth);
      setEndDateMonth(today);
    } else if (initialFilters.startDate) {
      setStartDateMonth(new Date(initialFilters.startDate));
    } else if (initialFilters.endDate) {
      setEndDateMonth(new Date(initialFilters.endDate));
    }
  }, [initialFilters]);

  // Handle filter submit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Tutup kalender jika masih terbuka
    setShowStartDateCalendar(false);
    setShowEndDateCalendar(false);
    
    onFilter({
      startDate,
      endDate,
      outletId: selectedOutlet,
      type: selectedType
    });
  };
  
  // Reset filter
  const handleReset = () => {
    const today = new Date();
    const firstDayOfMonth = startOfMonth(today);
    
    setStartDate(format(firstDayOfMonth, 'yyyy-MM-dd'));
    setEndDate(format(today, 'yyyy-MM-dd'));
    setSelectedOutlet('');
    setSelectedType('');
    setStartDateMonth(firstDayOfMonth);
    setEndDateMonth(today);
    
    onFilter({
      startDate: format(firstDayOfMonth, 'yyyy-MM-dd'),
      endDate: format(today, 'yyyy-MM-dd'),
      outletId: '',
      type: ''
    });
  };
  
  // Helper untuk render kalender
  const renderCalendar = (
    currentMonth: Date,
    setCurrentMonth: React.Dispatch<React.SetStateAction<Date>>,
    selectedDate: string,
    setSelectedDate: (date: string) => void,
    closeCalendar: () => void
  ) => {
    // Get days in month
    const firstDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
    const lastDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);
    const startingDayOfWeek = firstDayOfMonth.getDay();
    
    // Navigation handlers
    const prevMonth = () => {
      setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
    };
    
    const nextMonth = () => {
      setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
    };
    
    // Date selection handler
    const handleDateSelect = (day: number) => {
      const newDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
      setSelectedDate(format(newDate, 'yyyy-MM-dd'));
      closeCalendar();
    };
    
    return (
      <div className="absolute top-[calc(100%+10px)] left-0 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-[100]">
        <div className="p-3">
          <div className="flex justify-between items-center mb-3">
            <button
              type="button"
              className="p-1 rounded-md hover:bg-gray-100"
              onClick={prevMonth}
              aria-label="Bulan sebelumnya"
            >
              <ChevronLeftIcon className="w-4 h-4 text-gray-600" />
            </button>
            <span className="text-sm font-medium text-gray-800">
              {format(currentMonth, 'MMMM yyyy', { locale: id })}
            </span>
            <button
              type="button"
              className="p-1 rounded-md hover:bg-gray-100"
              onClick={nextMonth}
              aria-label="Bulan berikutnya"
            >
              <ChevronRightIcon className="w-4 h-4 text-gray-600" />
            </button>
          </div>
          
          <div className="grid grid-cols-7 gap-1 text-center">
            {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map((day) => (
              <div key={day} className="text-xs font-medium text-gray-500 py-1">
                {day}
              </div>
            ))}
            
            {Array.from({ length: 42 }, (_, i) => {
              const dayNumber = i - startingDayOfWeek + 1;
              const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), dayNumber);
              
              // Check if date is within current month
              const isCurrentMonth = date.getMonth() === currentMonth.getMonth();
              
              // Check if date is selected
              const isSelected = format(date, 'yyyy-MM-dd') === selectedDate;
              
              // Check if date is today
              const isToday = format(date, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd');
              
              return (
                <button
                  key={i}
                  type="button"
                  onClick={() => isCurrentMonth && handleDateSelect(dayNumber)}
                  disabled={!isCurrentMonth}
                  aria-label={isCurrentMonth ? `Pilih tanggal ${dayNumber}` : ''}
                  className={`w-8 h-8 text-xs flex items-center justify-center rounded-full
                    ${isCurrentMonth ? 'hover:bg-gray-100' : 'text-gray-300 cursor-default'}
                    ${isSelected ? 'bg-emerald-500 text-white hover:bg-emerald-600' : ''}
                    ${isToday && !isSelected ? 'border border-emerald-500 text-emerald-500' : ''}
                  `}
                >
                  {dayNumber > 0 && dayNumber <= lastDayOfMonth.getDate() ? dayNumber : ''}
                </button>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-md mb-6 overflow-visible">
      <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
        <h3 className="text-base font-semibold text-gray-700 flex items-center">
          <AdjustmentsHorizontalIcon className="w-5 h-5 mr-2 text-[#4EA799]" />
          Filter Data Absensi
        </h3>
      </div>
      
      <form onSubmit={handleSubmit} className="p-4 pb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
          {/* Filter Tanggal Mulai (dengan Calendar Dropdown) */}
          <div className="relative">
            <label htmlFor="start-date" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
              <CalendarIcon className="w-4 h-4 mr-1 text-[#4EA799]" />
              Tanggal Mulai
            </label>
            <div className="relative">
              <input
                id="start-date"
                type="text"
                readOnly
                value={startDate ? format(new Date(startDate), 'dd MMMM yyyy', { locale: id }) : ''}
                onClick={() => {
                  setShowStartDateCalendar(!showStartDateCalendar);
                  setShowEndDateCalendar(false);
                }}
                className="w-full cursor-pointer rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#4EA799] bg-white pr-10"
                aria-label="Tanggal Mulai"
              />
              <button
                type="button"
                onClick={() => {
                  setShowStartDateCalendar(!showStartDateCalendar);
                  setShowEndDateCalendar(false);
                }}
                className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                aria-label="Buka kalender tanggal mulai"
              >
                <CalendarIcon className="w-5 h-5" />
              </button>
              
              {showStartDateCalendar && (
                <div className="fixed inset-0 bg-transparent z-[90]" onClick={() => setShowStartDateCalendar(false)} />
              )}
              
              {showStartDateCalendar && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="relative z-[100]"
                >
                  {renderCalendar(
                    startDateMonth,
                    setStartDateMonth,
                    startDate,
                    setStartDate,
                    () => setShowStartDateCalendar(false)
                  )}
                </motion.div>
              )}
            </div>
          </div>
          
          {/* Filter Tanggal Akhir (dengan Calendar Dropdown) */}
          <div className="relative">
            <label htmlFor="end-date" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
              <CalendarIcon className="w-4 h-4 mr-1 text-[#4EA799]" />
              Tanggal Akhir
            </label>
            <div className="relative">
              <input
                id="end-date"
                type="text"
                readOnly
                value={endDate ? format(new Date(endDate), 'dd MMMM yyyy', { locale: id }) : ''}
                onClick={() => {
                  setShowEndDateCalendar(!showEndDateCalendar);
                  setShowStartDateCalendar(false);
                }}
                className="w-full cursor-pointer rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#4EA799] bg-white pr-10"
                aria-label="Tanggal Akhir"
              />
              <button
                type="button"
                onClick={() => {
                  setShowEndDateCalendar(!showEndDateCalendar);
                  setShowStartDateCalendar(false);
                }}
                className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                aria-label="Buka kalender tanggal akhir"
              >
                <CalendarIcon className="w-5 h-5" />
              </button>
              
              {showEndDateCalendar && (
                <div className="fixed inset-0 bg-transparent z-[90]" onClick={() => setShowEndDateCalendar(false)} />
              )}
              
              {showEndDateCalendar && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="relative z-[100]"
                >
                  {renderCalendar(
                    endDateMonth,
                    setEndDateMonth,
                    endDate,
                    setEndDate,
                    () => setShowEndDateCalendar(false)
                  )}
                </motion.div>
              )}
            </div>
          </div>
          
          {/* Filter Outlet */}
          <div>
            <label htmlFor="outlet" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
              <BuildingOfficeIcon className="w-4 h-4 mr-1 text-[#4EA799]" />
              Outlet
            </label>
            <select
              id="outlet"
              value={selectedOutlet}
              onChange={(e) => setSelectedOutlet(e.target.value)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#4EA799] appearance-none bg-white"
              aria-label="Pilih Outlet"
              style={{ backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`, backgroundPosition: `right 0.5rem center`, backgroundRepeat: `no-repeat`, backgroundSize: `1.5em 1.5em`, paddingRight: `2.5rem` }}
            >
              <option value="">Semua Outlet</option>
              {outlets.map((outlet) => (
                <option key={outlet.id} value={outlet.id}>
                  {outlet.name}
                </option>
              ))}
            </select>
          </div>
          
          {/* Filter Tipe */}
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
              <UserGroupIcon className="w-4 h-4 mr-1 text-[#4EA799]" />
              Tipe
            </label>
            <select
              id="type"
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#4EA799] appearance-none bg-white"
              aria-label="Pilih Tipe"
              style={{ backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`, backgroundPosition: `right 0.5rem center`, backgroundRepeat: `no-repeat`, backgroundSize: `1.5em 1.5em`, paddingRight: `2.5rem` }}
            >
              <option value="">Semua</option>
              <option value="therapist">Terapis</option>
              <option value="user">Staff</option>
            </select>
          </div>
        </div>
        
        {/* Tombol */}
        <div className="mt-6 flex justify-end space-x-3">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            type="button"
            onClick={handleReset}
            disabled={loading}
            className="px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 flex items-center shadow-sm"
          >
            <ArrowPathIcon className="w-4 h-4 mr-1" />
            Reset
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            type="submit"
            disabled={loading}
            className="px-5 py-2 bg-[#4EA799] text-white rounded-md hover:bg-[#3d8a7e] focus:outline-none focus:ring-2 focus:ring-[#4EA799] disabled:opacity-50 flex items-center shadow-sm"
          >
            {loading ? (
              <>
                <ArrowPathIcon className="w-4 h-4 mr-1 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <FunnelIcon className="w-4 h-4 mr-1" />
                Filter
              </>
            )}
          </motion.button>
        </div>
      </form>
    </div>
  );
};

export default AttendanceFilter; 