# 🕒 Implementasi Fitur Lembur Terapis

Dokumen ini berisi panduan untuk implementasi fitur lembur terapis yang dihitung per menit layanan dengan tarif Rp 100 per menit.

## 📋 Ringkasan Fitur

Fitur lembur terapis memungkinkan:
1. Menghitung kompensasi lembur terapis berdasarkan menit layanan (Rp 100/menit)
2. Mengaktifkan lembur melalui tombol "Lembur" di halaman transaksi
3. Melacak total menit lembur dan pendapatan lembur terapis
4. Menyertakan kompensasi lembur dalam perhitungan gaji terapis

## 🗃️ Perubahan Database

### Migrasi Database

File migrasi SQL telah dibuat di `prisma/migrations/overtime_implementation.sql` yang menambahkan kolom-kolom berikut:

1. **Tabel Transaction**
   - `overtimeMinutes`: Jumlah menit lembur pada transaksi (default: 0)
   - `overtimeAmount`: Jumlah kompensasi lembur pada transaksi (default: 0)

2. **Tabel Therapist**
   - `totalOvertimeMinutes`: Total menit lembur terapis sepanjang waktu (default: 0)
   - `totalOvertimeEarnings`: Total pendapatan lembur terapis sepanjang waktu (default: 0)

3. **Tabel TherapistSalary** (Baru)
   - Model baru untuk mengelola gaji terapis, termasuk komponen lembur
   - `totalOvertimeMinutes`: Total menit lembur dalam periode gaji
   - `totalOvertimeAmount`: Total kompensasi lembur dalam periode gaji

### Cara Menerapkan Migrasi

1. **Backup Database**
   ```bash
   node scripts/backup-database.js
   ```

2. **Terapkan Migrasi**
   ```bash
   npx prisma migrate dev --name add_overtime_feature
   ```

3. **Verifikasi Migrasi**
   ```bash
   npx prisma db pull
   npx prisma generate
   ```

## 💻 Perubahan Frontend

### 1. Halaman Transaksi

#### Tambahkan Tombol Lembur
```jsx
<Button 
  variant="outline"
  onClick={() => setOvertimeEnabled(!overtimeEnabled)}
  className={overtimeEnabled ? "bg-yellow-100" : ""}
>
  {overtimeEnabled ? "Lembur Aktif" : "Aktifkan Lembur"}
</Button>
```

#### Tambahkan State untuk Lembur
```jsx
const [overtimeEnabled, setOvertimeEnabled] = useState(false);
const [overtimeMinutes, setOvertimeMinutes] = useState(0);
const [overtimeAmount, setOvertimeAmount] = useState(0);
```

#### Hitung Lembur Berdasarkan Durasi Layanan
```jsx
useEffect(() => {
  if (overtimeEnabled && selectedServices.length > 0) {
    // Hitung total menit dari semua layanan
    const totalMinutes = selectedServices.reduce(
      (total, service) => total + (service.duration || 0) * service.quantity, 
      0
    );
    setOvertimeMinutes(totalMinutes);
    setOvertimeAmount(totalMinutes * 100); // Rp 100 per menit
  } else {
    setOvertimeMinutes(0);
    setOvertimeAmount(0);
  }
}, [overtimeEnabled, selectedServices]);
```

#### Tambahkan Informasi Lembur di Form Transaksi
```jsx
{overtimeEnabled && (
  <div className="mt-4 p-3 bg-yellow-50 rounded-md">
    <h3 className="font-medium">Informasi Lembur</h3>
    <div className="grid grid-cols-2 gap-2 mt-2">
      <div>
        <p className="text-sm text-gray-500">Total Menit Lembur</p>
        <p className="font-medium">{overtimeMinutes} menit</p>
      </div>
      <div>
        <p className="text-sm text-gray-500">Kompensasi Lembur</p>
        <p className="font-medium">Rp {overtimeAmount.toLocaleString()}</p>
      </div>
    </div>
  </div>
)}
```

#### Kirim Data Lembur ke API
```jsx
const transactionData = {
  // Data transaksi lainnya
  overtimeMinutes: overtimeEnabled ? overtimeMinutes : 0,
  overtimeAmount: overtimeEnabled ? overtimeAmount : 0,
};
```

### 2. Halaman Detail Terapis

#### Tambahkan Informasi Lembur di Profil Terapis
```jsx
<div className="mt-4">
  <h3 className="text-lg font-medium">Informasi Lembur</h3>
  <div className="grid grid-cols-2 gap-4 mt-2">
    <div>
      <p className="text-sm text-gray-500">Total Menit Lembur</p>
      <p className="font-medium">{therapist.totalOvertimeMinutes || 0} menit</p>
    </div>
    <div>
      <p className="text-sm text-gray-500">Total Pendapatan Lembur</p>
      <p className="font-medium">Rp {(therapist.totalOvertimeEarnings || 0).toLocaleString()}</p>
    </div>
  </div>
</div>
```

### 3. Halaman Gaji Terapis

#### Tambahkan Komponen Lembur di Perhitungan Gaji
```jsx
<div className="p-4 border rounded-md mb-4">
  <h3 className="font-medium">Kompensasi Lembur</h3>
  <div className="grid grid-cols-2 gap-2 mt-2">
    <div>
      <p className="text-sm text-gray-500">Total Menit Lembur</p>
      <p className="font-medium">{salary.totalOvertimeMinutes || 0} menit</p>
    </div>
    <div>
      <p className="text-sm text-gray-500">Total Kompensasi Lembur</p>
      <p className="font-medium">Rp {(salary.totalOvertimeAmount || 0).toLocaleString()}</p>
    </div>
  </div>
</div>
```

## 🔄 Perubahan Backend API

### 1. API Transaksi

#### Perbarui Handler Pembuatan Transaksi
```javascript
// Tambahkan field overtimeMinutes dan overtimeAmount ke data transaksi
const transaction = await prisma.transaction.create({
  data: {
    // Data transaksi lainnya
    overtimeMinutes: req.body.overtimeMinutes || 0,
    overtimeAmount: req.body.overtimeAmount || 0,
    // ...
  },
});

// Update total lembur terapis
await prisma.therapist.update({
  where: { id: req.body.therapistId },
  data: {
    totalOvertimeMinutes: {
      increment: req.body.overtimeMinutes || 0
    },
    totalOvertimeEarnings: {
      increment: req.body.overtimeAmount || 0
    }
  }
});
```

### 2. API Gaji Terapis

#### Perbarui Perhitungan Gaji Terapis
```javascript
// Hitung total lembur dalam periode gaji
const overtimeData = await prisma.transaction.aggregate({
  where: {
    therapistId: therapistId,
    transactionDate: {
      gte: periodStart,
      lte: periodEnd
    },
    overtimeMinutes: { gt: 0 }
  },
  _sum: {
    overtimeMinutes: true,
    overtimeAmount: true
  }
});

// Buat data gaji terapis dengan komponen lembur
const therapistSalary = await prisma.therapistSalary.create({
  data: {
    therapistId,
    periodStart,
    periodEnd,
    // Data gaji lainnya
    totalOvertimeMinutes: overtimeData._sum.overtimeMinutes || 0,
    totalOvertimeAmount: overtimeData._sum.overtimeAmount || 0,
    totalAmount: {
      // Tambahkan komponen lembur ke total gaji
      add: [baseSalary, commissionTotal, overtimeData._sum.overtimeAmount || 0, bonusAmount || 0]
    }
  }
});
```

## 📊 Laporan dan Analitik

### 1. Tambahkan Metrik Lembur di Dashboard

```jsx
<div className="stat">
  <div className="stat-title">Total Lembur Bulan Ini</div>
  <div className="stat-value">{totalOvertimeMinutes} menit</div>
  <div className="stat-desc">Rp {totalOvertimeAmount.toLocaleString()}</div>
</div>
```

### 2. Tambahkan Filter Lembur di Laporan Transaksi

```jsx
<div className="form-control">
  <label className="label cursor-pointer">
    <span className="label-text">Hanya Transaksi dengan Lembur</span>
    <input 
      type="checkbox" 
      checked={overtimeFilter} 
      onChange={(e) => setOvertimeFilter(e.target.checked)} 
      className="checkbox" 
    />
  </label>
</div>
```

## 🧪 Pengujian

1. **Uji Fitur Lembur di Transaksi**
   - Buat transaksi baru dengan lembur aktif
   - Verifikasi jumlah menit dan kompensasi lembur dihitung dengan benar
   - Verifikasi data lembur tersimpan di database

2. **Uji Update Total Lembur Terapis**
   - Verifikasi total lembur terapis bertambah setelah transaksi dengan lembur
   - Verifikasi tampilan total lembur di profil terapis

3. **Uji Perhitungan Gaji dengan Komponen Lembur**
   - Buat perhitungan gaji untuk periode yang memiliki transaksi dengan lembur
   - Verifikasi komponen lembur dihitung dan ditampilkan dengan benar

## 📝 Catatan Penting

1. Implementasi fitur ini tidak akan mereset database yang sudah ada
2. Migrasi database bersifat aditif (menambahkan kolom baru tanpa mengubah data yang ada)
3. Backup database sebelum menerapkan perubahan
4. Uji fitur di lingkungan development sebelum diterapkan di produksi

## 📅 Rencana Implementasi

1. Backup database
2. Terapkan migrasi database
3. Update skema Prisma dan generate client
4. Implementasi perubahan backend API
5. Implementasi perubahan frontend
6. Pengujian menyeluruh
7. Deploy ke produksi