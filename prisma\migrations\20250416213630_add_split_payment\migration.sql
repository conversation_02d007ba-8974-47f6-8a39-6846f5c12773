-- AlterEnum
ALTER TYPE "PaymentMethod" ADD VALUE 'SPLIT';

-- CreateTable
CREATE TABLE "SplitPayment" (
    "id" SERIAL NOT NULL,
    "firstMethod" "PaymentMethod" NOT NULL,
    "secondMethod" "PaymentMethod" NOT NULL,
    "cashAmount" DOUBLE PRECISION DEFAULT 0,
    "changeAmount" DOUBLE PRECISION DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "transactionId" INTEGER NOT NULL,

    CONSTRAINT "SplitPayment_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "SplitPayment_transactionId_key" ON "SplitPayment"("transactionId");

-- AddForeignKey
ALTER TABLE "SplitPayment" ADD CONSTRAINT "SplitPayment_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "Transaction"("id") ON DELETE CASCADE ON UPDATE CASCADE;
