// Script untuk memverifikasi hasil migrasi pelanggan
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function verifikasiMigrasi() {
  try {
    console.log('🔍 Memverifikasi hasil migrasi pelanggan...');
    
    // Hitung jumlah pelanggan dengan tag 'Migrasi'
    const countMigrasi = await prisma.customer.count({
      where: {
        tags: {
          has: 'Migrasi'
        }
      }
    });
    
    // Ambil statistik poin
    const stats = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as total,
        SUM(points) as totalPoints,
        AVG(points) as avgPoints,
        MAX(points) as maxPoints
      FROM "Customer"
      WHERE 'Migrasi' = ANY(tags)
    `;
    
    // Ambil 5 pelanggan dengan poin tertinggi
    const topCustomers = await prisma.customer.findMany({
      where: {
        tags: {
          has: 'Migrasi'
        }
      },
      orderBy: {
        points: 'desc'
      },
      take: 5,
      select: {
        id: true,
        name: true,
        phone: true,
        points: true
      }
    });
    
    // Tampilkan hasil
    console.log('\n📊 HASIL VERIFIKASI:');
    console.log(`📋 Jumlah pelanggan yang dimigrasi: ${countMigrasi}`);
    
    if (stats.length > 0) {
      const stat = stats[0];
      console.log(`💰 Total poin: ${stat.totalPoints || 0}`);
      console.log(`📈 Rata-rata poin: ${Math.round(stat.avgPoints || 0)}`);
      console.log(`🏆 Poin tertinggi: ${stat.maxPoints || 0}`);
    }
    
    console.log('\n🏆 TOP 5 PELANGGAN DENGAN POIN TERTINGGI:');
    topCustomers.forEach((customer, index) => {
      console.log(`${index + 1}. ${customer.name} (${customer.id}) - ${customer.points} poin - ${customer.phone}`);
    });
    
    console.log('\n✅ Verifikasi selesai!');
  } catch (error) {
    console.error('❌ Terjadi kesalahan saat verifikasi:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Jalankan verifikasi
verifikasiMigrasi();
