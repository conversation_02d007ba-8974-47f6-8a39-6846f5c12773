'use client';

import React, { useRef } from 'react';
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  Legend, BarChart, Bar, Cell, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar
} from 'recharts';

// Helper <PERSON><PERSON> (Gold & Teal)
const LOGO_COLORS = ['#FDBA74', '#2DD4BF', '#FCD34D', '#5EEAD4', '#FB923C', '#0D9488']; 

interface HourlyData {
  hour: number;
  count: number;
  prediction?: number;
}

interface DailyData {
  day: string;
  dayNum: number;
  count: number;
  prediction?: number;
}

interface VisitPatternChartProps {
  hourlyData: HourlyData[];
  dailyData: DailyData[];
  type?: 'hourly' | 'daily' | 'radar';
  showPrediction?: boolean;
  title?: string;
  height?: number;
  className?: string;
}

const VisitPatternChart: React.FC<VisitPatternChartProps> = ({
  hourlyData,
  dailyData,
  type = 'hourly',
  showPrediction = false,
  title = 'Pola Kunjungan',
  height = 300,
  className = ''
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  // Prepare hourly data
  const formattedHourlyData = hourlyData.map(item => ({
    ...item,
    hourLabel: `${item.hour}:00`
  }));

  // Prepare daily data for radar chart
  const radarData = dailyData.map(item => ({
    subject: item.day,
    A: item.count,
    fullMark: Math.max(...dailyData.map(d => d.count)) * 1.2
  }));

  return (
    <div className={`w-full ${className}`}>
      {title && <h3 className="text-lg font-semibold mb-2">{title}</h3>}
      <div ref={chartRef} style={{ width: '100%', height: `${height}px` }}>
        <ResponsiveContainer>
          {type === 'hourly' ? (
            <BarChart data={formattedHourlyData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
              <XAxis 
                dataKey="hourLabel" 
                tick={{ fill: '#6b7280' }} 
              />
              <YAxis 
                tick={{ fill: '#6b7280' }}
              />
              <Tooltip />
              <Legend />
              <Bar dataKey="count" name="Jumlah Kunjungan" fill="#10b981">
                {formattedHourlyData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={entry.hour >= 17 && entry.hour <= 20 ? '#f59e0b' : '#10b981'} 
                  />
                ))}
              </Bar>
              {showPrediction && (
                <Line 
                  type="monotone" 
                  dataKey="prediction" 
                  name="Prediksi" 
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  dot={false}
                />
              )}
            </BarChart>
          ) : type === 'daily' ? (
            <BarChart data={dailyData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
              <XAxis 
                dataKey="day" 
                tick={{ fill: '#6b7280' }} 
              />
              <YAxis 
                tick={{ fill: '#6b7280' }}
              />
              <Tooltip />
              <Legend />
              <Bar dataKey="count" name="Jumlah Kunjungan" fill="#10b981">
                {dailyData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={entry.dayNum === 0 || entry.dayNum === 6 ? '#f59e0b' : '#10b981'} 
                  />
                ))}
              </Bar>
              {showPrediction && (
                <Line 
                  type="monotone" 
                  dataKey="prediction" 
                  name="Prediksi" 
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  dot={false}
                />
              )}
            </BarChart>
          ) : (
            <RadarChart cx="50%" cy="50%" outerRadius="80%" data={radarData}>
              <PolarGrid />
              <PolarAngleAxis dataKey="subject" tick={{ fill: '#6b7280' }} />
              <PolarRadiusAxis angle={30} domain={[0, 'auto']} />
              <Radar 
                name="Jumlah Kunjungan" 
                dataKey="A" 
                stroke="#10b981" 
                fill="#10b981" 
                fillOpacity={0.6} 
              />
              <Tooltip />
              <Legend />
            </RadarChart>
          )}
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default VisitPatternChart;
