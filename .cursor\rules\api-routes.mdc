---
description:
globs:
alwaysApply: true
---
# Panduan Rute API Routes 🛣️

Semua rute API backend didefinisikan dalam direktori `src/app/api`. Struktur umumnya mengikuti konvensi Next.js App Router.

- **Autentikasi**: Rute terkait login, logout, dan info pengguna ada di `src/app/api/auth/`. Contoh: `[login/route.ts](mdc:src/app/api/auth/login/route.ts)`
- **Booking**: Rute untuk manajemen booking ada di `src/app/api/bookings/`. Contoh: `[route.ts](mdc:src/app/api/bookings/route.ts)` dan `[[id]/route.ts](mdc:src/app/api/bookings/[id]/route.ts)`
- **Pelanggan (Customers)**: Rute untuk pelanggan ada di `src/app/api/customers/`. Contoh: `[route.ts](mdc:src/app/api/customers/route.ts)`
- **<PERSON><PERSON><PERSON> (Services)**: Rute untuk layanan ada di `src/app/api/services/`. Contoh: `[route.ts](mdc:src/app/api/services/route.ts)`
- **Outlet**: Rute untuk outlet ada di `src/app/api/outlets/`. Contoh: `[route.ts](mdc:src/app/api/outlets/route.ts)`
- **Terapis**: Rute untuk terapis ada di `src/app/api/therapists/`. Contoh: `[route.ts](mdc:src/app/api/therapists/route.ts)`
- **Transaksi**: Rute untuk transaksi ada di `src/app/api/transactions/`. Contoh: `[route.ts](mdc:src/app/api/transactions/route.ts)`
- **Pengguna (Users)**: Rute untuk manajemen pengguna dan izin ada di `src/app/api/users/`. Contoh: `[route.ts](mdc:src/app/api/users/route.ts)`
- **Pengaturan (Settings)**: Rute untuk pengaturan aplikasi ada di `src/app/api/settings/`. Contoh: `[route.ts](mdc:src/app/api/settings/route.ts)`
- **Laporan (Reports)**: Rute untuk laporan ada di `src/app/api/reports/`. Contoh: `[route.ts](mdc:src/app/api/reports/route.ts)`
- **Log**: Rute untuk log sistem ada di `src/app/api/logs/`. Contoh: `[route.ts](mdc:src/app/api/logs/route.ts)`