'use client';

import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';

interface OutletContextType {
  selectedOutletId: string | null;
  setSelectedOutletId: (id: string | null) => void;
}

const OutletContext = createContext<OutletContextType | undefined>(undefined);

const LOCAL_STORAGE_KEY = 'selectedOutletId';

export function OutletProvider({ children }: { children: ReactNode }) {
  const [selectedOutletId, setSelectedOutletIdState] = useState<string | null>(() => {
    // Baca nilai awal dari Local Storage saat inisialisasi
    if (typeof window !== 'undefined') {
      const storedId = localStorage.getItem(LOCAL_STORAGE_KEY);
      return storedId || null;
    }
    return null;
  });

  // Update Local Storage setiap kali state berubah
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (selectedOutletId) {
        localStorage.setItem(LOCAL_STORAGE_KEY, selectedOutletId);
      } else {
        localStorage.removeItem(LOCAL_STORAGE_KEY);
      }
    }
  }, [selectedOutletId]);

  const setSelectedOutletId = (id: string | null) => {
    setSelectedOutletIdState(id);
  };

  return (
    <OutletContext.Provider value={{ selectedOutletId, setSelectedOutletId }}>
      {children}
    </OutletContext.Provider>
  );
}

export function useOutletContext() {
  const context = useContext(OutletContext);
  if (context === undefined) {
    throw new Error('useOutletContext must be used within an OutletProvider');
  }
  return context;
} 