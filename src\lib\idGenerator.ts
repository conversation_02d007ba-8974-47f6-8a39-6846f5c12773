import { prisma } from '@/lib/prisma';

/**
 * <PERSON><PERSON><PERSON>lkan ID booking dengan format B0000001, B0000002, dst.
 * @returns Promise<string> ID booking yang dihasilkan
 */
export async function generateBookingId(): Promise<string> {
  try {
    // Cari booking terakhir berdasarkan displayId
    const lastBooking = await prisma.booking.findFirst({
      where: {
        displayId: {
          startsWith: 'B',
          not: null
        }
      },
      orderBy: {
        displayId: 'desc'
      }
    });

    let nextNumber = 1;
    
    // Jika ada booking sebelumnya, ambil nomor terakhir dan tambahkan 1
    if (lastBooking?.displayId) {
      const lastNumber = parseInt(lastBooking.displayId.substring(1));
      if (!isNaN(lastNumber)) {
        nextNumber = lastNumber + 1;
      }
    }
    
    // Format ID dengan padding 7 digit
    return `B${String(nextNumber).padStart(7, '0')}`;
  } catch (error) {
    console.error('Error generating booking ID:', error);
    // Fallback jika terjadi error
    return `B${Date.now().toString().slice(-7)}`;
  }
}

/**
 * <PERSON><PERSON><PERSON>lkan ID transaksi dengan format TR0000001, TR0000002, dst.
 * @returns Promise<string> ID transaksi yang dihasilkan
 */
export async function generateTransactionId(): Promise<string> {
  try {
    // Cari transaksi terakhir berdasarkan displayId
    const lastTransaction = await prisma.transaction.findFirst({
      where: {
        displayId: {
          startsWith: 'TR',
          not: null
        }
      },
      orderBy: {
        displayId: 'desc'
      }
    });

    let nextNumber = 1;
    
    // Jika ada transaksi sebelumnya, ambil nomor terakhir dan tambahkan 1
    if (lastTransaction?.displayId) {
      const lastNumber = parseInt(lastTransaction.displayId.substring(2));
      if (!isNaN(lastNumber)) {
        nextNumber = lastNumber + 1;
      }
    }
    
    // Format ID dengan padding 7 digit
    return `TR${String(nextNumber).padStart(7, '0')}`;
  } catch (error) {
    console.error('Error generating transaction ID:', error);
    // Fallback jika terjadi error
    return `TR${Date.now().toString().slice(-7)}`;
  }
}
