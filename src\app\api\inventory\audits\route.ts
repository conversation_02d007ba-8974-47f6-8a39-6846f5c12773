import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserIdFromToken } from '@/lib/auth-utils';

// GET - Ambil semua audit inventori
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (outletId) {
      where.outletId = outletId;
    }
    
    if (status) {
      where.status = status;
    }

    const [audits, total] = await Promise.all([
      prisma.inventoryAudit.findMany({
        where,
        include: {
          outlet: true,
          createdBy: {
            select: {
              id: true,
              name: true
            }
          },

          auditItems: {
            include: {
              item: {
                include: {
                  category: true
                }
              },
              checkedBy: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          _count: {
            select: {
              auditItems: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.inventoryAudit.count({ where })
    ]);

    return NextResponse.json({
      message: 'Audit inventori berhasil diambil',
      audits,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching inventory audits:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil audit inventori' },
      { status: 500 }
    );
  }
}

// POST - Buat audit inventori baru
export async function POST(request: NextRequest) {
  try {
    const userId = getUserIdFromToken(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      title,
      description,
      outletId,
      scheduledDate,
      itemIds
    } = body;

    // Validasi input
    if (!title || title.trim() === '') {
      return NextResponse.json(
        { error: 'Judul audit harus diisi' },
        { status: 400 }
      );
    }

    if (!outletId) {
      return NextResponse.json(
        { error: 'Outlet harus dipilih' },
        { status: 400 }
      );
    }



    if (!scheduledDate) {
      return NextResponse.json(
        { error: 'Tanggal audit harus diisi' },
        { status: 400 }
      );
    }

    // Validasi tanggal tidak boleh di masa lalu
    const scheduledDateTime = new Date(scheduledDate);
    const now = new Date();
    now.setHours(0, 0, 0, 0); // Reset time to start of day
    
    if (scheduledDateTime < now) {
      return NextResponse.json(
        { error: 'Tanggal audit tidak boleh di masa lalu' },
        { status: 400 }
      );
    }

    // Jika itemIds tidak disediakan, ambil semua item dari outlet
    let finalItemIds = itemIds;
    if (!itemIds || itemIds.length === 0) {
      const allItems = await prisma.inventoryItem.findMany({
        where: { outletId },
        select: { id: true }
      });
      finalItemIds = allItems.map(item => item.id);
    }

    if (finalItemIds.length === 0) {
      return NextResponse.json(
        { error: 'Tidak ada item untuk diaudit di outlet ini' },
        { status: 400 }
      );
    }

    // Buat audit dengan transaction
    const audit = await prisma.$transaction(async (tx) => {
      // Buat audit
      const newAudit = await tx.inventoryAudit.create({
        data: {
          title: title.trim(),
          description: description?.trim() || null,
          outletId,

          scheduledDate: scheduledDateTime,
          createdById: userId
        }
      });

      // Buat audit items
      const auditItems = await Promise.all(
        finalItemIds.map(async (itemId: string) => {
          const item = await tx.inventoryItem.findUnique({
            where: { id: itemId }
          });

          if (!item) return null;

          return tx.inventoryAuditItem.create({
            data: {
              auditId: newAudit.id,
              itemId,
              expectedGood: item.goodCondition,
              expectedDamaged: item.damagedCondition || 0,
              expectedLost: item.lostCondition || 0
            }
          });
        })
      );

      return newAudit;
    });

    // Ambil audit lengkap dengan relasi
    const fullAudit = await prisma.inventoryAudit.findUnique({
      where: { id: audit.id },
      include: {
        outlet: true,
        createdBy: {
          select: {
            id: true,
            name: true
          }
        },

        auditItems: {
          include: {
            item: {
              include: {
                category: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      message: 'Audit inventori berhasil dibuat',
      audit: fullAudit
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating inventory audit:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat audit inventori' },
      { status: 500 }
    );
  }
}