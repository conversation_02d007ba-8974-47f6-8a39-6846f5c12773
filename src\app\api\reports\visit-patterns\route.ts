import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

// Helper function to calculate the difference in days between two dates
function daysBetween(date1: Date, date2: Date): number {
  const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
  // Adjust dates to the start of the day to avoid time zone issues influencing day count
  const startDate = new Date(date1.getFullYear(), date1.getMonth(), date1.getDate());
  const endDate = new Date(date2.getFullYear(), date2.getMonth(), date2.getDate());
  const diffDays = Math.round(Math.abs((startDate.getTime() - endDate.getTime()) / oneDay));
  return diffDays + 1; // Include both start and end date
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');
    const rawStartDate = searchParams.get('startDate');
    const rawEndDate = searchParams.get('endDate');

    if (!rawStartDate || !rawEndDate) {
      throw new Error('Tanggal mulai dan tanggal akhir harus disediakan');
    }

    const startDate = new Date(rawStartDate);
    const endDate = new Date(rawEndDate);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      throw new Error('Format tanggal tidak valid');
    }

    // Set time for accurate filtering
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);

    const transactionWhere: Prisma.TransactionWhereInput = {
      transactionDate: { gte: startDate, lte: endDate },
      ...(outletId && { outletId: outletId }),
    };

    // 1. Ambil hanya tanggal transaksi (lebih ringan dari seluruh data transaksi)
    const transactionsDates = await prisma.transaction.findMany({
      where: transactionWhere,
      select: {
        transactionDate: true,
      },
    });

    const totalVisits = transactionsDates.length;

    // Jika tidak ada kunjungan, kembalikan hasil kosong
    if (totalVisits === 0) {
       return NextResponse.json({
         message: 'Tidak ada data kunjungan untuk periode yang dipilih',
         visitPatterns: {
           peakHours: [],
           peakDays: [],
           avgVisitsPerDay: 0,
           avgVisitsPerHour: 0,
           totalVisits: 0,
         },
         filters: { startDate: startDate.toISOString(), endDate: endDate.toISOString(), outletId: outletId },
       });
     }

    // 2. Hitung kunjungan per jam (dengan penyesuaian zona waktu)
    const hourCounts: Record<number, number> = {};
    transactionsDates.forEach(t => {
      // Gunakan tanggal ISO string dan parsing manual untuk menghindari masalah zona waktu
      const transactionDate = new Date(t.transactionDate);
      // Konversi ke waktu lokal Asia/Makassar (WITA, GMT+8)
      const localHour = (transactionDate.getUTCHours() + 8) % 24;
      hourCounts[localHour] = (hourCounts[localHour] || 0) + 1;
    });
    const peakHours = Array.from({ length: 24 }, (_, hour) => ({
      hour,
      visitCount: hourCounts[hour] || 0,
    }));

    // 3. Hitung kunjungan per hari dalam seminggu (dengan penyesuaian zona waktu)
    const dayCounts: Record<number, number> = {};
    transactionsDates.forEach(t => {
      // Gunakan tanggal ISO string dan parsing manual untuk menghindari masalah zona waktu
      const transactionDate = new Date(t.transactionDate);
      // Untuk hari, kita perlu menghitung tanggal lokal Asia/Makassar
      // Tambahkan 8 jam ke UTC time untuk mendapatkan waktu WITA
      const localDate = new Date(transactionDate.getTime() + (8 * 60 * 60 * 1000));
      const day = localDate.getUTCDay(); // 0 = Minggu, 6 = Sabtu
      dayCounts[day] = (dayCounts[day] || 0) + 1;
    });
    const peakDays = Array.from({ length: 7 }, (_, dayOfWeek) => ({
      dayOfWeek,
      visitCount: dayCounts[dayOfWeek] || 0,
    }));

    // 4. Hitung rata-rata
    const numberOfDays = daysBetween(startDate, endDate);
    const avgVisitsPerDay = numberOfDays > 0 ? totalVisits / numberOfDays : 0;
    // Estimasi jam buka per hari (misal 12 jam, bisa disesuaikan jika ada data jam operasional)
    const estimatedOperationalHoursPerDay = 12;
    const avgVisitsPerHour = numberOfDays > 0 ? totalVisits / (numberOfDays * estimatedOperationalHoursPerDay) : 0;


    // Kembalikan data yang dibutuhkan frontend
    return NextResponse.json({
      message: 'Data pola kunjungan berhasil diambil',
      visitPatterns: {
        peakHours, // Sesuai interface frontend
        peakDays, // Sesuai interface frontend
        avgVisitsPerDay,
        avgVisitsPerHour,
        totalVisits,
      },
      filters: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        outletId: outletId,
      },
    });

  } catch (error: unknown) {
    console.error('[API GET /api/reports/visit-patterns] Error:', error);
    let errorMessage = 'Terjadi kesalahan saat mengambil data pola kunjungan';
    const statusCode = 500;
    if (error instanceof Error) { errorMessage = error.message; }

    // Struktur error response
    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat mengambil data pola kunjungan',
      visitPatterns: null, // Kembalikan null jika error
      filters: { startDate: new Date(0).toISOString(), endDate: new Date().toISOString(), outletId: null, },
    }, { status: statusCode });
  }
}
