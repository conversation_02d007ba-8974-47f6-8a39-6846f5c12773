-- CreateTable
CREATE TABLE "ActiveTherapistSession" (
    "id" TEXT NOT NULL,
    "therapistId" TEXT NOT NULL,
    "serviceId" TEXT NOT NULL,
    "startTime" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endTime" TIMESTAMP(3) NOT NULL,
    "transactionId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ActiveTherapistSession_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ActiveTherapistSession_therapistId_idx" ON "ActiveTherapistSession"("therapistId");

-- CreateIndex
CREATE INDEX "ActiveTherapistSession_serviceId_idx" ON "ActiveTherapistSession"("serviceId");

-- CreateIndex
CREATE INDEX "ActiveTherapistSession_transactionId_idx" ON "ActiveTherapistSession"("transactionId");

-- CreateIndex
CREATE INDEX "ActiveTherapistSession_startTime_idx" ON "ActiveTherapistSession"("startTime");

-- CreateIndex
CREATE INDEX "ActiveTherapistSession_endTime_idx" ON "ActiveTherapistSession"("endTime");

-- AddForeignKey
ALTER TABLE "ActiveTherapistSession" ADD CONSTRAINT "ActiveTherapistSession_therapistId_fkey" FOREIGN KEY ("therapistId") REFERENCES "Therapist"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ActiveTherapistSession" ADD CONSTRAINT "ActiveTherapistSession_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "Service"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ActiveTherapistSession" ADD CONSTRAINT "ActiveTherapistSession_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "Transaction"("id") ON DELETE CASCADE ON UPDATE CASCADE;
