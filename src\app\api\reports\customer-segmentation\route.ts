import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

// Definisi segmen pelanggan
const CUSTOMER_SEGMENTS = {
  NEW: {
    id: 'NEW',
    name: '<PERSON>elanggan Baru',
    color: '#3b82f6', // Biru
    description: 'Pelanggan dengan 1 kunjungan dalam periode.'
  },
  OCCASIONAL: {
    id: 'OCCASIONAL',
    name: 'Pelanggan Sesekali',
    color: '#10b981', // Hijau
    description: 'Pelanggan dengan 2-3 kunjungan dalam periode.'
  },
  REGULAR: {
    id: 'REGULAR',
    name: 'Pelanggan Reguler',
    color: '#f59e0b', // Kuning
    description: 'Pelanggan dengan 4-6 kunjungan dalam periode.'
  },
  LOYAL: {
    id: 'LOYAL',
    name: 'Pelanggan Setia',
    color: '#8b5cf6', // Ungu
    description: '<PERSON>elanggan dengan 7+ kunjungan dalam periode.'
  },
  HIGH_VALUE: {
    id: 'HIGH_VALUE',
    name: 'Pelanggan Nilai Tinggi',
    color: '#ef4444', // Merah
    description: 'Pelanggan dengan pengeluaran di atas rata-rata.'
  },
  INACTIVE: {
    id: 'INACTIVE',
    name: 'Pelanggan Tidak Aktif',
    color: '#6b7280', // Abu-abu
    description: 'Pelanggan yang tidak berkunjung dalam 3 bulan terakhir.'
  }
};

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');
    const rawStartDate = searchParams.get('startDate');
    const rawEndDate = searchParams.get('endDate');

    if (!rawStartDate || !rawEndDate) {
      throw new Error('Tanggal mulai dan tanggal akhir harus disediakan');
    }

    const startDate = new Date(rawStartDate);
    const endDate = new Date(rawEndDate);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      throw new Error('Format tanggal tidak valid');
    }

    // Set endDate to the end of the day for accurate filtering
    endDate.setHours(23, 59, 59, 999);

    // Tentukan tanggal 3 bulan sebelum tanggal akhir untuk cek aktivitas
    const threeMonthsAgo = new Date(endDate);
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
    threeMonthsAgo.setHours(0, 0, 0, 0); // Start of the day

    // Filter dasar untuk transaksi
    const transactionWherePeriod: Prisma.TransactionWhereInput = {
      transactionDate: { gte: startDate, lte: endDate },
      ...(outletId && { outletId: outletId }),
    };

    const transactionWhereRecent: Prisma.TransactionWhereInput = {
      transactionDate: { gte: threeMonthsAgo, lte: endDate },
      ...(outletId && { outletId: outletId }),
    };

    // 1. Hitung statistik transaksi per pelanggan (visitCount, totalSpent) dalam periode
    const customerTransactionStats = await prisma.transaction.groupBy({
      by: ['customerId'],
      where: transactionWherePeriod,
      _count: {
        id: true, // Menghitung jumlah transaksi sebagai visitCount
      },
      _sum: {
        totalAmount: true,
      },
      orderBy: {
        customerId: 'asc', // Optional: sorting
      }
    });

    // 2. Dapatkan ID pelanggan yang relevan dari statistik transaksi
    const relevantCustomerIds = customerTransactionStats.map(stat => stat.customerId);

    // 3. Dapatkan ID pelanggan yang aktif (transaksi dalam 3 bulan terakhir)
    const activeCustomerIdsSet = new Set(
      (await prisma.transaction.findMany({
        where: transactionWhereRecent,
        select: { customerId: true },
        distinct: ['customerId'], // Hanya ambil ID unik
      })).map(t => t.customerId)
    );

    // Jika tidak ada pelanggan relevan, kembalikan hasil kosong
    if (relevantCustomerIds.length === 0) {
      return NextResponse.json({
        message: 'Tidak ada data pelanggan untuk periode yang dipilih',
        customerSegmentation: {
          segments: CUSTOMER_SEGMENTS, customers: [], segmentCounts: {}, segmentSpending: {}, marketingRecommendations: {}, avgSpentThreshold: 0,
        },
        filters: { startDate: startDate.toISOString(), endDate: endDate.toISOString(), outletId: outletId, },
      });
    }

    // 4. Ambil data detail pelanggan HANYA untuk pelanggan yang relevan
    const relevantCustomersData = await prisma.customer.findMany({
      where: {
        id: { in: relevantCustomerIds },
      },
      select: {
        id: true,
        name: true,
        phone: true,
        registeredAt: true,
      },
    });

    // Buat map untuk akses data pelanggan yang efisien
    const customersMap = new Map(relevantCustomersData.map(c => [c.id, c]));

    // 5. Gabungkan data statistik dan detail pelanggan
    const customerStats = customerTransactionStats.map(stat => {
      const customerData = customersMap.get(stat.customerId);
      const visitCount = stat._count.id;
      const totalSpent = stat._sum.totalAmount || 0;
      const avgSpent = visitCount > 0 ? totalSpent / visitCount : 0;
      const isActive = activeCustomerIdsSet.has(stat.customerId);

      return {
        customerId: stat.customerId,
        customerName: customerData?.name ?? 'Unknown Customer',
        customerPhone: customerData?.phone ?? '',
        registeredAt: customerData?.registeredAt ?? new Date(0), // Default date if not found
        visitCount,
        totalSpent,
        avgSpent,
        isActive,
      };
    });

    // 6. Hitung rata-rata pengeluaran (sama seperti sebelumnya, tapi berdasarkan data yang sudah diagregasi)
    const totalCustomers = customerStats.length;
    const totalOverallSpent = customerStats.reduce((sum, c) => sum + c.totalSpent, 0);
    const avgSpentThreshold = totalCustomers > 0 ? totalOverallSpent / totalCustomers * 1.5 : 0; // Ambang batas nilai tinggi (1.5x rata-rata)

    // 7. Segmentasi pelanggan (logika segmentasi tetap sama)
    const segmentedCustomers = customerStats.map(customer => {
      let segment = CUSTOMER_SEGMENTS.INACTIVE.id;
      if (customer.isActive) {
        if (customer.totalSpent > avgSpentThreshold) {
          segment = CUSTOMER_SEGMENTS.HIGH_VALUE.id;
        } else if (customer.visitCount >= 7) {
          segment = CUSTOMER_SEGMENTS.LOYAL.id;
        } else if (customer.visitCount >= 4) {
          segment = CUSTOMER_SEGMENTS.REGULAR.id;
        } else if (customer.visitCount >= 2) {
          segment = CUSTOMER_SEGMENTS.OCCASIONAL.id;
        } else if (customer.visitCount === 1) {
          segment = CUSTOMER_SEGMENTS.NEW.id;
        }
      }
      return { ...customer, segment };
    });

    // 8. Hitung jumlah pelanggan & total spending per segmen (sama seperti sebelumnya)
    const segmentCounts = Object.values(CUSTOMER_SEGMENTS).reduce((acc, segment) => {
      acc[segment.id] = segmentedCustomers.filter(c => c.segment === segment.id).length;
      return acc;
    }, {} as Record<string, number>);

    const segmentSpending = Object.values(CUSTOMER_SEGMENTS).reduce((acc, segment) => {
      acc[segment.id] = segmentedCustomers
        .filter(c => c.segment === segment.id)
        .reduce((sum, c) => sum + c.totalSpent, 0);
      return acc;
    }, {} as Record<string, number>);

    // 9. Rekomendasi strategi pemasaran (tetap sama)
    const marketingRecommendations = {
      [CUSTOMER_SEGMENTS.NEW.id]: 'Tawarkan diskon untuk kunjungan kedua untuk mendorong kunjungan berulang.',
      [CUSTOMER_SEGMENTS.OCCASIONAL.id]: 'Kirim pesan pengingat dan promo khusus untuk meningkatkan frekuensi kunjungan.',
      [CUSTOMER_SEGMENTS.REGULAR.id]: 'Tawarkan program loyalitas dan reward untuk kunjungan rutin.',
      [CUSTOMER_SEGMENTS.LOYAL.id]: 'Berikan penghargaan eksklusif dan prioritas booking untuk mempertahankan loyalitas.',
      [CUSTOMER_SEGMENTS.HIGH_VALUE.id]: 'Tawarkan layanan premium dan perhatian khusus untuk memaksimalkan nilai pelanggan.',
      [CUSTOMER_SEGMENTS.INACTIVE.id]: 'Kirim pesan reaktivasi dengan penawaran khusus untuk mendorong kunjungan kembali.'
    };

    // Kembalikan hasil
    return NextResponse.json({
      message: 'Data segmentasi pelanggan berhasil diambil',
      customerSegmentation: {
        segments: CUSTOMER_SEGMENTS,
        customers: segmentedCustomers,
        segmentCounts,
        segmentSpending,
        marketingRecommendations,
        avgSpentThreshold,
      },
      filters: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(), // Use the modified endDate
        outletId: outletId,
      },
    });

  } catch (error: unknown) {
    console.error('[API GET /api/reports/customer-segmentation] Error:', error);
    let errorMessage = 'Terjadi kesalahan saat mengambil data segmentasi pelanggan';
    const statusCode = 500;
    if (error instanceof Error) { errorMessage = error.message; }

    // Struktur respons error tetap sama
    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat mengambil data segmentasi pelanggan',
      customerSegmentation: {
        segments: CUSTOMER_SEGMENTS, customers: [], segmentCounts: {}, segmentSpending: {}, marketingRecommendations: {}, avgSpentThreshold: 0,
      },
      filters: { /* Provide reasonable defaults or null */ startDate: new Date(0).toISOString(), endDate: new Date().toISOString(), outletId: null, },
    }, { status: statusCode });
  }
}
