'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { motion } from 'framer-motion';
import { useOutletContext } from '@/contexts/OutletContext';
import { useAuth } from '@/contexts/AuthContext';
import { useReactToPrint } from 'react-to-print';
import ReceiptComponent from '@/components/receipt/ReceiptComponent';
import { toast } from 'sonner';
import './styles.css';
import {
  FiPlus, FiUserPlus, FiTrash2, FiPrinter, FiCheckCircle,
  FiXCircle, FiTag, FiUser, FiCreditCard, FiSmartphone, FiDollarSign, FiUsers, FiAlertTriangle, FiCalendar, FiSearch,
  FiStar,
  FiList // Icon untuk antrian
} from 'react-icons/fi';
import { IconType } from 'react-icons';

// --- HAPUS DEFINISI MOCK DATA & TIPE LAMA ---
/*
type Service = { id: number; name: string; price: number; duration: number };
const mockServices: Service[] = [ ... ];
type Therapist = { id: number; name: string };
const mockTherapists: Therapist[] = [ ... ];
type Customer = { id: number; name: string; phone: string };
const mockCustomers: Customer[] = [ ... ];
*/
// --- END HAPUS ---

// --- Tipe Data dari API ---
interface ApiService {
    id: string;
    name: string;
    price: number;
    duration: number;
}

interface ApiTherapist {
    id: string;
    name: string;
    isServing?: boolean;
    availableAt?: string;
    activeSessionId?: string;
}

interface ApiCustomer {
    id: string;
    name: string;
    phone: string;
    points?: number;
}

// Tambahkan tipe baru untuk layanan dengan kuantitas
interface ServiceWithQuantity {
    service: ApiService;
    quantity: number;
}
// --- End Tipe Data API ---

// Definisikan kembali paymentMethods
type PaymentMethod = { key: string; label: string; icon: IconType };

// Varian Animasi
const fadeInUp = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.5 } } };
const staggerContainer = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1, delayChildren: 0.1 } } };

// Update Tipe Struk untuk menyertakan charge dan split bill
interface SplitPaymentData {
  firstMethod: string;
  secondMethod: string;
  cashAmount: number;
  changeAmount: number;
}

interface TransactionReceiptData {
  outletName: string;
  transactionId: string;
  dateTime: string;
  customerName: string; // Tetap string karena struk hanya menampilkan nama pelanggan utama
  customers: { id: string; name: string; phone: string }[]; // Tambah daftar semua pelanggan
  therapistName: string;
  items: { name: string; price: number; quantity: number }[]; // Tambahkan quantity pada item
  subtotal: number;
  discountType: 'percentage' | 'fixed' | 'none'; // Tambahkan tipe diskon
  discountValue: number; // Tambahkan nilai diskon (persen atau nominal)
  discountAmount: number; // Jumlah diskon aktual
  additionalCharge: number; // Tambahkan Biaya Tambahan
  tax: number;
  total: number;
  paymentMethod: string;
  splitPayment?: SplitPaymentData; // Data split bill (opsional)
  note: string;
}

// Interface untuk Booking (minimal untuk pencarian)
interface ApiBookingSummary {
  id: string;
  customer: { id: string; name: string; phone: string };
  bookingDate: string; // Atau Date
  bookingTime: string;
  therapist?: { id: string; name: string }; // Terapis mungkin belum assigned
  status: string; // e.g., PENDING, CONFIRMED
}

// Interface untuk Detail Booking (setelah dipilih)
interface ApiBookingDetail extends ApiBookingSummary {
   bookingServices: { service: ApiService }[]; // Asumsi service sudah detail
   notes?: string; // <-- Tambahkan notes (opsional)
   // tambahkan field lain jika perlu
}

interface SettingValue {
  key: string;
  value: string;
  category: string;
  label: string;
  type: string;
}

// Interface untuk data Antrian Terapis
interface ApiTherapistQueue {
  id: string; // ID entri antrian
  therapistId: string;
  name: string; // Nama terapis
  outletId: string;
  checkInTime: string; // Penting untuk pengurutan
  waitingTime: number; // Dalam menit
}

export default function TransactionPage() {
  const { selectedOutletId } = useOutletContext();
  const { user } = useAuth();
  const [outletName, setOutletName] = useState('');
  const [isDataLoading, setIsDataLoading] = useState(true);
  const [dataError, setDataError] = useState<string | null>(null);

  // --- State untuk Data dari API ---
  const [services, setServices] = useState<ApiService[]>([]);
  const [therapists, setTherapists] = useState<ApiTherapist[]>([]);
  // Ganti therapistQueue menjadi rawTherapistQueue
  const [rawTherapistQueue, setRawTherapistQueue] = useState<ApiTherapistQueue[]>([]);

  // State baru untuk antrian yang sudah diurutkan
  const sortedTherapistQueue = useMemo(() => {
    return [...rawTherapistQueue].sort((a, b) =>
      new Date(a.checkInTime).getTime() - new Date(b.checkInTime).getTime()
    );
  }, [rawTherapistQueue]);

  // State untuk pencarian layanan
  const [serviceSearchTerm, setServiceSearchTerm] = useState('');
  // State untuk pencarian terapis
  const [therapistSearchTerm, setTherapistSearchTerm] = useState('');
  // --- End State Data API ---

  // Ubah state selectedServices untuk menyimpan kuantitas
  const [selectedServices, setSelectedServices] = useState<ServiceWithQuantity[]>([]);
  const [selectedTherapist, setSelectedTherapist] = useState<ApiTherapist | null>(null);

  // Ubah selectedCustomer menjadi array untuk mendukung beberapa pelanggan
  const [selectedCustomers, setSelectedCustomers] = useState<ApiCustomer[]>([]);

  const [paymentMethod, setPaymentMethod] = useState<string>('cash');
  const [isSplitBill, setIsSplitBill] = useState<boolean>(false);
  const [secondPaymentMethod, setSecondPaymentMethod] = useState<string>('transfer');
  const [cashAmount, setCashAmount] = useState<number>(0);
  const [totalReceived, setTotalReceived] = useState<number>(0);
  const [changeAmount, setChangeAmount] = useState<number>(0);
  const [transactionNote, setTransactionNote] = useState('');

  // --- State Baru untuk Diskon ---
  const [discountType, setDiscountType] = useState<'percentage' | 'fixed' | 'none'>('none');
  const [discountValue, setDiscountValue] = useState<number>(0);
  // --- End State Diskon ---
  // --- State Baru untuk Biaya Tambahan ---
  const [additionalCharge, setAdditionalCharge] = useState<number>(0);
  // --- End State Biaya Tambahan ---

  // --- State Baru untuk Lembur ---
  const [overtimeMinutes, setOvertimeMinutes] = useState<number>(0);
  const [overtimeAmount, setOvertimeAmount] = useState<number>(0);
  const [overtimeEnabled, setOvertimeEnabled] = useState<boolean>(false);
  // --- End State Lembur ---

  const [transactionComplete, setTransactionComplete] = useState(false);
  const [transactionDetails, setTransactionDetails] = useState<TransactionReceiptData | null>(null);

  // State UI Pelanggan
  const [customerSearchTerm, setCustomerSearchTerm] = useState('');
  const [customerSearchResults, setCustomerSearchResults] = useState<ApiCustomer[]>([]);
  const [isCustomerSearching, setIsCustomerSearching] = useState(false);
  const [showAddCustomerModal, setShowAddCustomerModal] = useState(false);
  const [newCustomerName, setNewCustomerName] = useState('');
  const [newCustomerPhone, setNewCustomerPhone] = useState('');
  const [newCustomerAddress, setNewCustomerAddress] = useState('');
  const [isAddingCustomer, setIsAddingCustomer] = useState(false);

  // Ref untuk komponen struk
  const receiptRef = useRef<HTMLDivElement>(null);

  // Fungsi Cetak Struk dengan membuka halaman baru
  const handlePrint = () => {
    if (transactionDetails?.transactionId) {
      // Buka halaman cetak dalam jendela baru
      window.open(`/print/receipt?id=${transactionDetails.transactionId}`, '_blank', 'width=400,height=600');
    } else {
      toast.error('ID transaksi tidak ditemukan');
    }
  };

  // Filter layanan berdasarkan kata kunci pencarian
  const filteredServices = useMemo(() => {
    if (!serviceSearchTerm.trim()) return services;
    return services.filter(service =>
      service.name.toLowerCase().includes(serviceSearchTerm.toLowerCase())
    );
  }, [services, serviceSearchTerm]);

  // Filter terapis berdasarkan kata kunci pencarian dan status aktif
  const filteredTherapists = useMemo(() => {
    // Pertama filter berdasarkan kata kunci pencarian
    let searchFiltered = !therapistSearchTerm.trim()
      ? therapists
      : therapists.filter(therapist =>
          therapist.name.toLowerCase().includes(therapistSearchTerm.toLowerCase())
        );

    // Kemudian urutkan berdasarkan kriteria baru menggunakan sortedTherapistQueue
    searchFiltered.sort((a, b) => {
      const aQueueEntry = sortedTherapistQueue.find(q => q.therapistId === a.id);
      const bQueueEntry = sortedTherapistQueue.find(q => q.therapistId === b.id);

      // Prioritas 1: Status Layanan (Tidak Sibuk > Sibuk)
      if (!a.isServing && b.isServing) return -1;
      if (a.isServing && !b.isServing) return 1;

      // Pada titik ini, a.isServing === b.isServing

      // Jika keduanya TIDAK SIBUK
      if (!a.isServing) { // Ini juga berarti !b.isServing
        // Prioritas 2a: Keberadaan dalam Antrian (Dalam Antrian > Tidak Dalam Antrian)
        if (aQueueEntry && !bQueueEntry) return -1;
        if (!aQueueEntry && bQueueEntry) return 1;

        if (aQueueEntry && bQueueEntry) {
          // Keduanya TIDAK SIBUK dan Keduanya DALAM ANTRIAN
          // Urutkan berdasarkan posisi mereka di sortedTherapistQueue
          const aIndex = sortedTherapistQueue.indexOf(aQueueEntry);
          const bIndex = sortedTherapistQueue.indexOf(bQueueEntry);
          if (aIndex < bIndex) return -1; // a lebih awal di antrian terurut
          if (aIndex > bIndex) return 1; // b lebih awal di antrian terurut
          // Fallback jika checkInTime/index sama (seharusnya jarang)
          return a.name.localeCompare(b.name);
        }
        // Keduanya TIDAK SIBUK dan Keduanya TIDAK DALAM ANTRIAN
        // Urutkan berdasarkan nama
        return a.name.localeCompare(b.name);
      }

      // Jika keduanya SIBUK (karena a.isServing === b.isServing, dan !a.isServing sudah ditangani)
      // Urutkan berdasarkan nama
      return a.name.localeCompare(b.name);
    });

    return searchFiltered;
  }, [therapists, therapistSearchTerm, sortedTherapistQueue]); // Dependency diubah ke sortedTherapistQueue

  // --- Kalkulasi (Subtotal, Diskon, Charge, Total) ---
  // Subtotal adalah jumlah harga semua layanan sebelum diskon dan biaya tambahan
  const subtotal = useMemo(() =>
    selectedServices.reduce((sum, item) => sum + (item.service.price * item.quantity), 0),
    [selectedServices]
  );

  const discountAmount = useMemo(() => {
    if (discountType === 'percentage') {
      const validPercent = Math.max(0, Math.min(100, discountValue));
      return Math.round((validPercent / 100) * subtotal); // Bulatkan ke integer terdekat
    } else if (discountType === 'fixed') {
      return Math.max(0, Math.min(discountValue, subtotal));
    }
    return 0;
  }, [discountType, discountValue, subtotal]);

  const tax = 0; // Ganti jika ada pajak

  // Kalkulasi Total Baru (tidak termasuk lembur sesuai permintaan)
  const total = useMemo(() => {
    // Lembur tidak ditambahkan ke total transaksi
    let calculatedTotal = subtotal - discountAmount + additionalCharge + tax;
    return calculatedTotal;
  }, [subtotal, discountAmount, additionalCharge, tax]);
  
  // --- End Kalkulasi ---

  // Handler untuk input nilai diskon
  const handleDiscountValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    const newValue = isNaN(value) || value < 0 ? 0 : value;
    setDiscountValue(newValue);

    if (newValue === 0) {
      setDiscountType('none');
    } else if (discountType === 'none' && newValue > 0) {
      // Jika belum ada tipe & nilai > 0, default ke fixed
      setDiscountType('fixed');
    }
  };

  // Handler untuk input biaya tambahan
  const handleChargeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    setAdditionalCharge(isNaN(value) || value < 0 ? 0 : value);
  };

  // Hitung total durasi layanan dan menit lembur secara otomatis
  useEffect(() => {
    if (selectedServices.length > 0 && selectedTherapist && overtimeEnabled) {
      // Hitung total durasi layanan yang dipilih
      const totalServiceDuration = selectedServices.reduce((total, item) => {
        return total + (item.service.duration * item.quantity);
      }, 0);
      
      // Hitung lembur dari total durasi layanan (tanpa pengurangan 60 menit)
      const overtimeMinutesCalculated = totalServiceDuration;
      
      // Update state menit lembur hanya jika belum diubah manual atau saat pertama kali
      // Cek apakah ini adalah perubahan layanan pertama atau perubahan status overtimeEnabled
      const isInitialCalculation = selectedServices.length > 0 && overtimeMinutes === 0;
      
      if (isInitialCalculation) {
        setOvertimeMinutes(overtimeMinutesCalculated);
        // Hitung kompensasi lembur (Rp 100 per menit)
        const overtimeAmountCalculated = overtimeMinutesCalculated * 100;
        setOvertimeAmount(overtimeAmountCalculated);
      }
    } else {
      // Reset jika tidak ada layanan atau terapis yang dipilih atau lembur tidak diaktifkan
      setOvertimeMinutes(0);
      setOvertimeAmount(0);
    }
  }, [selectedServices, selectedTherapist, overtimeEnabled]);
  
  // Reset lembur ketika overtimeEnabled dinonaktifkan
  useEffect(() => {
    if (!overtimeEnabled) {
      setOvertimeMinutes(0);
      setOvertimeAmount(0);
    }
  }, [overtimeEnabled]);
  
  // Handler untuk input menit lembur (dipertahankan untuk potensi pengembangan di masa depan)
  const handleOvertimeMinutesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const minutes = parseInt(e.target.value) || 0;
    setOvertimeMinutes(minutes);
    
    // Hitung kompensasi lembur (Rp 100 per menit)
    const amount = minutes * 100;
    setOvertimeAmount(amount);
  };

  // Handler untuk input jumlah kompensasi lembur (dipertahankan untuk potensi pengembangan di masa depan)
  const handleOvertimeAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const amount = parseFloat(e.target.value) || 0;
    setOvertimeAmount(amount);
  };

  // Handler untuk input jumlah tunai
  const handleCashAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    const validValue = isNaN(value) || value < 0 ? 0 : value;
    setCashAmount(validValue);

    // Hitung total yang diterima dan kembalian
    const received = validValue;
    setTotalReceived(received);
    setChangeAmount(Math.max(0, received - total));
  };

  // --- Logika Fungsi untuk menambah layanan atau menambah kuantitas ---
  const handleAddService = (service: ApiService) => {
    setSelectedServices(prev => {
      // Cek apakah layanan sudah ada
      const existingItemIndex = prev.findIndex(item => item.service.id === service.id);

      if (existingItemIndex >= 0) {
        // Jika sudah ada, tambah kuantitasnya
        const updatedItems = [...prev];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + 1
        };
        return updatedItems;
      } else {
        // Jika belum ada, tambahkan sebagai item baru dengan kuantitas 1
        return [...prev, { service, quantity: 1 }];
      }
    });
  };

  const handleRemoveService = (serviceId: string) => {
    setSelectedServices(prev => {
      const existingItemIndex = prev.findIndex(item => item.service.id === serviceId);

      // Jika tidak ditemukan, jangan ubah apa-apa
      if (existingItemIndex === -1) return prev;

      const item = prev[existingItemIndex];

      // Jika kuantitas > 1, kurangi kuantitas
      if (item.quantity > 1) {
        const updatedItems = [...prev];
        updatedItems[existingItemIndex] = {
          ...item,
          quantity: item.quantity - 1
        };
        return updatedItems;
      }

      // Jika kuantitas = 1, hapus item
      return prev.filter(item => item.service.id !== serviceId);
    });
  };

  // Tambah fungsi untuk menghapus seluruh layanan
  const handleRemoveServiceCompletely = (serviceId: string) => {
    setSelectedServices(prev => prev.filter(item => item.service.id !== serviceId));
  };

  const handleSearchCustomer = useCallback(async () => {
    if (!customerSearchTerm.trim()) {
      setCustomerSearchResults([]);
      return;
    }
    setIsCustomerSearching(true);
    try {
      const response = await fetch(`/api/customers?search=${encodeURIComponent(customerSearchTerm)}`);
      if (!response.ok) {
        throw new Error('Gagal mencari pelanggan');
      }
      const data = await response.json();
      setCustomerSearchResults(data.customers || []);
    } catch (error) {
      console.error("Customer search error:", error);
      setCustomerSearchResults([]);
    } finally {
      setIsCustomerSearching(false);
    }
  }, [customerSearchTerm]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      handleSearchCustomer();
    }, 500);
    return () => clearTimeout(delayDebounceFn)
  }, [customerSearchTerm, handleSearchCustomer]);

  const handleSelectCustomer = (customer: ApiCustomer) => {
    // Kosongkan search term dan results terlebih dahulu
    setCustomerSearchTerm('');
    setCustomerSearchResults([]);

    // Cek apakah pelanggan sudah ada dalam daftar, lalu tambahkan
    if (!selectedCustomers.some(c => c.id === customer.id)) {
      setSelectedCustomers(prev => [...prev, customer]);
    }
  };

  const handleRemoveCustomer = (customerId: string) => {
    setSelectedCustomers(prev => prev.filter(c => c.id !== customerId));
  };

  const handleAddNewCustomer = async () => {
    if (!newCustomerName.trim() || !newCustomerPhone.trim()) return toast.error('Nama dan No HP wajib diisi.');

    setIsAddingCustomer(true);
    try {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: newCustomerName,
          phone: newCustomerPhone,
          address: newCustomerAddress.trim() || undefined, // Kirim alamat jika ada, jika tidak, kirim undefined
          points: 0,
          // Tag 'Baru' akan ditambahkan otomatis di API jika tidak ada tag lain
          tags: []
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Error response from server:", errorData);

        // Cek apakah error adalah nomor telepon sudah terdaftar
        if (errorData.error && errorData.error.includes('Nomor telepon sudah terdaftar')) {
          const existingCustomer = errorData.existingCustomer || {};
          throw new Error(`Nomor HP sudah digunakan oleh pelanggan ${existingCustomer.name || 'lain'}`);
        } else if (errorData.error && errorData.error.includes('Email sudah terdaftar')) {
          const existingCustomer = errorData.existingCustomer || {};
          throw new Error(`Email sudah digunakan oleh pelanggan ${existingCustomer.name || 'lain'}`);
        } else {
          throw new Error(errorData.error || 'Gagal menambahkan pelanggan baru');
        }
      }

      const responseData = await response.json();
      console.log("Customer creation response:", responseData);

      if (!responseData.customer) {
        console.error("Invalid response format - missing customer data:", responseData);
        throw new Error("Format respons tidak valid - data pelanggan tidak ditemukan");
      }

      const newCustomer: ApiCustomer = responseData.customer;
      handleSelectCustomer(newCustomer);
      setShowAddCustomerModal(false);
      setNewCustomerName('');
      setNewCustomerPhone('');
      setNewCustomerAddress('');
      toast.success('Pelanggan baru berhasil ditambahkan!');

    } catch (error) {
      console.error("Add customer error:", error);

      // Cek apakah error adalah nomor telepon sudah terdaftar
      if (error instanceof Error) {
        const errorMessage = error.message;
        if (errorMessage.includes('Nomor HP sudah digunakan')) {
          toast.warning(errorMessage, {
            description: "Silakan gunakan nomor HP yang berbeda"
          });
        } else if (errorMessage.includes('Email sudah digunakan')) {
          toast.warning(errorMessage, {
            description: "Silakan gunakan email yang berbeda"
          });
        } else {
          toast.error(`Gagal menambahkan pelanggan: ${errorMessage}`);
        }
      } else {
        toast.error(`Gagal menambahkan pelanggan: Error tidak diketahui`);
      }
    } finally {
        setIsAddingCustomer(false);
    }
  };

  // --- State Baru untuk Proses Transaksi ---
  const [isProcessingTransaction, setIsProcessingTransaction] = useState(false);
  const [processingError, setProcessingError] = useState<string | null>(null);
  // --- End State Proses Transaksi ---

  // --- State Baru untuk Pilih Booking ---
  const [selectedBookingId, setSelectedBookingId] = useState<string | null>(null);

  // State UI Booking
  const [bookingSearchTerm, setBookingSearchTerm] = useState('');
  const [bookingSearchResults, setBookingSearchResults] = useState<ApiBookingSummary[]>([]);
  const [isBookingSearching, setIsBookingSearching] = useState(false);
  const [showSelectBookingModal, setShowSelectBookingModal] = useState(false);
  const [bookingLoadingError, setBookingLoadingError] = useState<string | null>(null);

  // State untuk booking
  const [bookings, setBookings] = useState<ApiBookingSummary[]>([]);
  const [isBookingLoading, setIsBookingLoading] = useState(false);
  const [bookingError, setBookingError] = useState<string | null>(null);

  // --- Fungsi Pencarian Booking ---
  const handleSearchBooking = useCallback(async () => {
    if (!selectedOutletId) return; // Pastikan outlet dipilih
    // Tidak perlu search term jika ingin menampilkan semua booking terkonfirmasi?
    // Atau tambahkan validasi: if (!bookingSearchTerm.trim()) { setBookingSearchResults([]); return; }

    setIsBookingSearching(true);
    setBookingLoadingError(null);
    try {
      // Ambil hanya booking yang CONFIRMED untuk outlet ini
      // Tambahkan search jika diperlukan: `&search=${encodeURIComponent(bookingSearchTerm)}`
      const response = await fetch(`/api/bookings?outletId=${selectedOutletId}&status=CONFIRMED&limit=20`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Gagal mencari booking');
      }
      const data = await response.json();
      setBookingSearchResults(data.bookings || []);
    } catch (error) {
      console.error("Booking search error:", error);
      setBookingLoadingError(error instanceof Error ? error.message : 'Terjadi kesalahan');
      setBookingSearchResults([]);
    } finally {
      setIsBookingSearching(false);
    }
  }, [selectedOutletId]);

  // Trigger pencarian saat modal dibuka (opsional, atau saat user mengetik)
  useEffect(() => {
    if (showSelectBookingModal) {
      handleSearchBooking(); // Langsung cari booking CONFIRMED saat modal dibuka
    }
  }, [showSelectBookingModal, handleSearchBooking]);

  // --- Fungsi Pilih Booking (Implementasi Lengkap) ---
  const handleSelectBooking = async (booking: ApiBookingSummary) => {
    console.log("Selected booking summary:", booking);
    toast.info(`Memuat detail booking ${booking.id}...`);
    setBookingLoadingError(null); // Reset error sebelumnya
    setIsBookingSearching(true); // Tampilkan loading indicator sementara

    try {
      // Panggil API untuk mendapatkan detail lengkap booking
      // Sesuaikan include jika perlu relasi lain
      const response = await fetch(`/api/bookings/${booking.id}?include=customer,therapist,bookingServices.service`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Gagal memuat detail booking (Status: ${response.status})`);
      }

      const bookingDetail: ApiBookingDetail = await response.json();
      console.log("Booking detail fetched:", bookingDetail);

      // Validasi data yang diterima (opsional tapi bagus)
      if (!bookingDetail.customer || !bookingDetail.bookingServices || bookingDetail.bookingServices.length === 0) {
        throw new Error("Data booking detail tidak lengkap dari API.");
      }

      // Pre-fill form transaksi
      setSelectedBookingId(bookingDetail.id); // Simpan ID booking yang dipilih
      setSelectedCustomers([bookingDetail.customer]); // Set pelanggan (asumsi 1 pelanggan per booking)
      setSelectedTherapist(bookingDetail.therapist || null); // Set terapis (jika ada)

      // Set layanan dari booking dengan mengelompokkan layanan yang sama
      const serviceMap = new Map();

      // Kelompokkan layanan yang sama dan hitung quantity
      bookingDetail.bookingServices
        .filter(bs => bs.service) // Pastikan service ada
        .forEach(bs => {
          const serviceId = bs.service.id;
          if (serviceMap.has(serviceId)) {
            // Jika layanan sudah ada, tambahkan quantity
            const existingService = serviceMap.get(serviceId);
            existingService.quantity += 1;
          } else {
            // Jika layanan belum ada, tambahkan dengan quantity 1
            serviceMap.set(serviceId, {
              service: bs.service,
              quantity: 1
            });
          }
        });

      // Konversi Map ke array untuk state
      setSelectedServices(Array.from(serviceMap.values()));

      // Reset state lain jika perlu (misal: diskon, catatan)
      setTransactionNote(bookingDetail.notes || ''); // Ambil catatan dari booking jika ada
      setDiscountType('none');
      setDiscountValue(0);
      setAdditionalCharge(0);

      toast.success(`Data dari booking ${bookingDetail.id} berhasil dimuat.`);
      setShowSelectBookingModal(false); // Tutup modal

    } catch (error) {
      console.error("Failed to load booking detail:", error);
      const message = error instanceof Error ? error.message : 'Terjadi kesalahan tidak diketahui';
      setBookingLoadingError(`Gagal memuat detail: ${message}`);
      toast.error(`Gagal memuat detail booking: ${message}`);
      // Jangan tutup modal jika gagal memuat detail
      // setShowSelectBookingModal(false);
    } finally {
       setIsBookingSearching(false); // Sembunyikan loading indicator
    }
  };

  // --- Fungsi Proses Transaksi (Update bookingId) ---
  const handleProcessTransaction = async () => {
    // Validasi dasar
    if (selectedCustomers.length === 0) {
      toast.error('Pelanggan belum dipilih.');
      return;
    }
    if (!selectedTherapist) {
      toast.error('Terapis belum dipilih.');
      return;
    }
    if (selectedServices.length === 0 && !selectedBookingId) { // Izinkan jika dari booking
      toast.error('Belum ada layanan yang dipilih.');
      return;
    }
    if (!user) {
      toast.error('Sesi pengguna tidak valid. Silakan login ulang.');
      return;
    }
    if (!selectedOutletId) {
      toast.error('Outlet belum dipilih.');
      return;
    }

    setIsProcessingTransaction(true);
    setProcessingError(null);
    setTransactionComplete(false);
    setTransactionDetails(null);

    try {
      // Persiapkan data items untuk semua transaksi (walk-in dan booking)
      const itemsPayload = selectedServices.map(item => ({
        serviceId: item.service.id,
        quantity: item.quantity,
      }));

      console.log("Preparing items payload:", JSON.stringify(itemsPayload, null, 2));

      // Siapkan data transaksi utama
      const mainCustomerId = selectedCustomers[0]?.id;
      const customerIds = selectedCustomers.map(c => c.id);

      if (!mainCustomerId) {
         toast.error('ID Pelanggan utama tidak ditemukan.');
         setIsProcessingTransaction(false);
         return;
      }

      const transactionData = {
        outletId: selectedOutletId,
        customerId: mainCustomerId,
        customerIds: customerIds,
        therapistId: selectedTherapist.id,
        bookingId: selectedBookingId || '',
        totalAmount: total,
        paymentMethod: isSplitBill ? 'SPLIT' : paymentMethod.toUpperCase(),
        paymentStatus: 'PAID',
        notes: transactionNote,
        createdById: user.id,
        // Selalu kirim items untuk semua transaksi (walk-in dan booking)
        items: itemsPayload,
        // Tambahkan data diskon jika ada
        discountType: discountType,
        discountValue: discountValue,
        discountAmount: discountAmount,
        // Tambahkan data biaya tambahan jika ada
        additionalCharge: additionalCharge,
      // Tambahkan data lembur hanya jika fitur lembur diaktifkan
      overtimeMinutes: overtimeEnabled ? overtimeMinutes : 0,
      overtimeAmount: overtimeEnabled ? overtimeAmount : 0,
      // Tambahkan data split bill jika diaktifkan
        ...(isSplitBill ? {
          splitPayment: {
            firstMethod: paymentMethod.toUpperCase(),
            secondMethod: secondPaymentMethod.toUpperCase(),
            cashAmount: paymentMethod === 'cash' ? cashAmount : (secondPaymentMethod === 'cash' ? cashAmount : 0),
            changeAmount: changeAmount
          }
        } : {}),
      };

      console.log("Sending transaction data:", JSON.stringify(transactionData, null, 2));

      const response = await fetch('/api/transactions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(transactionData),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error("Transaction API Error:", result);
        throw new Error(result.error || 'Gagal memproses transaksi');
      }

      console.log("Transaction successful:", result);
      console.log("Transaction response structure:", JSON.stringify(result, null, 2));

      // Siapkan data struk
      const receiptData: TransactionReceiptData = {
        outletName: outletName || 'Nama Outlet Tidak Diketahui',
        transactionId: result.transaction?.displayId || 'N/A',
        dateTime: new Date().toLocaleString('id-ID'),
        customerName: selectedCustomers[0]?.name || 'Walk-in',
        customers: selectedCustomers,
        therapistName: selectedTherapist.name,
        items: selectedServices.map(item => ({
          name: item.service.name,
          price: item.service.price,
          quantity: item.quantity,
        })),
        subtotal: subtotal,
        // Hanya sertakan diskon jika ada dan nilainya lebih dari 0
        discountType: discountAmount > 0 ? discountType : 'none',
        discountValue: discountAmount > 0 ? discountValue : 0,
        discountAmount: discountAmount > 0 ? discountAmount : 0,
        // Hanya sertakan biaya tambahan jika ada dan nilainya lebih dari 0
        additionalCharge: additionalCharge > 0 ? additionalCharge : 0,
        tax: tax,
        total: total,
        paymentMethod: isSplitBill ? 'SPLIT' : paymentMethod.toUpperCase(),
        splitPayment: isSplitBill ? {
          firstMethod: paymentMethod.toUpperCase(),
          secondMethod: secondPaymentMethod.toUpperCase(),
          cashAmount: paymentMethod === 'cash' ? cashAmount : (secondPaymentMethod === 'cash' ? cashAmount : 0),
          changeAmount: changeAmount
        } : undefined,
        note: transactionNote,
      };

      console.log("Receipt data prepared:", JSON.stringify(receiptData, null, 2));

      // Buat sesi terapis aktif untuk setiap layanan
      try {
        // Ambil layanan dengan durasi terlama
        const longestService = selectedServices.reduce((longest, current) => {
          return (current.service.duration > longest.service.duration) ? current : longest;
        }, selectedServices[0]);

        // Buat sesi terapis aktif
        if (longestService) {
          const sessionResponse = await fetch('/api/therapist-sessions', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              therapistId: selectedTherapist.id,
              serviceId: longestService.service.id,
              transactionId: result.transaction.id,
              duration: longestService.service.duration
            }),
          });

          if (!sessionResponse.ok) {
            console.warn("Gagal membuat sesi terapis aktif:", await sessionResponse.text());
            // Tidak perlu menampilkan error ke user, karena transaksi sudah berhasil
          } else {
            console.log("Sesi terapis aktif berhasil dibuat");
          }
        }
      } catch (sessionError) {
        console.error("Error saat membuat sesi terapis aktif:", sessionError);
        // Tidak perlu menampilkan error ke user, karena transaksi sudah berhasil
      }

      setTransactionDetails(receiptData);
      setTransactionComplete(true);
      toast.success('Transaksi berhasil diproses!');

    } catch (error) {
      console.error('Error processing transaction:', error);
      const errorMessage = (error instanceof Error) ? error.message : 'Terjadi kesalahan saat memproses transaksi.';
      setProcessingError(errorMessage);
      toast.error(`Gagal: ${errorMessage}`);
    } finally {
      setIsProcessingTransaction(false);
    }
  };
  // --- End Fungsi Proses Transaksi ---

  // Handler untuk menampilkan modal konfirmasi penyelesaian sesi terapis
  const handleEndTherapistSession = (therapist: ApiTherapist) => {
    setSelectedTherapistSession(therapist);
    setShowEndSessionModal(true);
  };

  // Handler untuk menyelesaikan sesi terapis
  const handleConfirmEndSession = async () => {
    if (!selectedTherapistSession || !selectedTherapistSession.activeSessionId) {
      toast.error('Tidak dapat menemukan sesi terapis');
      setShowEndSessionModal(false);
      return;
    }

    setIsEndingSession(true);
    try {
      const response = await fetch(`/api/therapist-sessions/${selectedTherapistSession.activeSessionId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Gagal menyelesaikan sesi terapis');
      }

      // Perbarui data terapis
      const updatedTherapists = therapists.map(t => {
        if (t.id === selectedTherapistSession.id) {
          return { ...t, isServing: false, activeSessionId: undefined, availableAt: undefined };
        }
        return t;
      });

      setTherapists(updatedTherapists);
      toast.success(`Sesi terapis ${selectedTherapistSession.name} berhasil diselesaikan`);

      // Jika terapis yang dipilih adalah terapis yang sesinya diselesaikan, perbarui selectedTherapist
      if (selectedTherapist && selectedTherapist.id === selectedTherapistSession.id) {
        setSelectedTherapist({ ...selectedTherapist, isServing: false });
      }

    } catch (error) {
      console.error('Error ending therapist session:', error);
      toast.error(`Gagal menyelesaikan sesi: ${error instanceof Error ? error.message : 'Terjadi kesalahan'}`);
    } finally {
      setIsEndingSession(false);
      setShowEndSessionModal(false);
      setSelectedTherapistSession(null);
    }
  };

  // Handler untuk transaksi baru
  const handleNewTransaction = () => {
     setSelectedServices([]);
     setSelectedTherapist(null);
     setSelectedCustomers([]);
     setPaymentMethod(paymentMethods.length > 0 ? paymentMethods[0].key : 'cash');
     setIsSplitBill(false);
     setSecondPaymentMethod('transfer');
     setCashAmount(0);
     setTotalReceived(0);
     setChangeAmount(0);
     setTransactionNote('');
     setDiscountType('none');
     setDiscountValue(0);
     setAdditionalCharge(0);
     setOvertimeMinutes(0);
     setOvertimeAmount(0);
     setTransactionComplete(false);
     setTransactionDetails(null);
  }

  // State untuk metode pembayaran dari pengaturan
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    { key: 'cash', label: 'Tunai', icon: FiDollarSign },
    { key: 'transfer', label: 'Transfer Bank', icon: FiCreditCard },
    { key: 'qris', label: 'QRIS', icon: FiSmartphone },
  ]);

  // State untuk mengelola sesi terapis
  const [selectedTherapistSession, setSelectedTherapistSession] = useState<ApiTherapist | null>(null);
  const [showEndSessionModal, setShowEndSessionModal] = useState(false);
  const [isEndingSession, setIsEndingSession] = useState(false);

  // Fetch Data Awal (Services, Therapists, Outlet Name)
  useEffect(() => {
    if (selectedOutletId) {
      // ... (Logika fetch tetap sama seperti sebelumnya) ...
      const fetchData = async () => {
          try {
             const [outletData, servicesData, therapistsData, paymentTypesData, queueData] = await Promise.allSettled([
                  fetch(`/api/outlets/${selectedOutletId}`).then(res => res.ok ? res.json() : Promise.reject('Gagal memuat data outlet')),
                  fetch(`/api/services?outletId=${selectedOutletId}&isActive=true`).then(res => res.ok ? res.json() : Promise.reject('Gagal memuat layanan')),
                  fetch(`/api/therapists?outletId=${selectedOutletId}&isActive=true&includeActiveSessions=true`).then(res => res.ok ? res.json() : Promise.reject('Gagal memuat terapis')),
                  fetch(`/api/settings/payment-types`).then(res => res.ok ? res.json() : Promise.reject('Gagal memuat jenis pembayaran')),
                  fetch(`/api/attendance/queue?outletId=${selectedOutletId}`).then(res => res.ok ? res.json() : Promise.reject('Gagal memuat data antrian')) // <-- Fetch data antrian
              ]);
              // Set state outletName, services, therapists, dataError
              if (outletData.status === 'fulfilled') setOutletName(outletData.value.outlet?.name || 'Outlet Tidak Diketahui'); else { console.error(outletData.reason); setOutletName('Error'); setDataError(prev => prev ? `${prev}, Outlet` : 'Gagal memuat: Outlet'); }
              if (servicesData.status === 'fulfilled') setServices(servicesData.value.services || []); else { console.error(servicesData.reason); setServices([]); setDataError(prev => prev ? `${prev}, Layanan` : 'Gagal memuat: Layanan'); }
              if (therapistsData.status === 'fulfilled') setTherapists(therapistsData.value.therapists || []); else { console.error(therapistsData.reason); setTherapists([]); setDataError(prev => prev ? `${prev}, Terapis` : 'Gagal memuat: Terapis'); }
              // Gunakan setRawTherapistQueue
              if (queueData.status === 'fulfilled') setRawTherapistQueue(queueData.value.data || []); else { console.error(queueData.reason); setRawTherapistQueue([]); /* Tidak set dataError agar tidak fatal jika antrian gagal */ toast.error('Gagal memuat data antrian terapis.'); }

              // Proses data jenis pembayaran
              if (paymentTypesData.status === 'fulfilled') {
                const settings = paymentTypesData.value.settings || [];
                const enabledPaymentTypes: PaymentMethod[] = [];

                // Proses setiap setting untuk jenis pembayaran
                settings.forEach((setting: SettingValue) => {
                  if (setting.value === 'true') {
                    switch (setting.key) {
                      case 'payment_cash':
                        enabledPaymentTypes.push({ key: 'cash', label: 'Tunai', icon: FiDollarSign });
                        break;
                      case 'payment_transfer':
                        enabledPaymentTypes.push({ key: 'transfer', label: 'Transfer Bank', icon: FiCreditCard });
                        break;
                      case 'payment_credit_card':
                        enabledPaymentTypes.push({ key: 'credit_card', label: 'Kartu Kredit', icon: FiCreditCard });
                        break;
                      case 'payment_debit_card':
                        enabledPaymentTypes.push({ key: 'debit_card', label: 'Kartu Debit', icon: FiCreditCard });
                        break;
                      case 'payment_qris':
                        enabledPaymentTypes.push({ key: 'qris', label: 'QRIS', icon: FiSmartphone });
                        break;
                      case 'payment_ewallet':
                        enabledPaymentTypes.push({ key: 'digital_wallet', label: 'E-Wallet', icon: FiSmartphone });
                        break;
                    }
                  }
                });

                // Jika ada metode pembayaran yang diaktifkan, gunakan itu
                if (enabledPaymentTypes.length > 0) {
                  setPaymentMethods(enabledPaymentTypes);
                  // Reset metode pembayaran yang dipilih jika tidak tersedia lagi
                  if (!enabledPaymentTypes.some(p => p.key === paymentMethod)) {
                    setPaymentMethod(enabledPaymentTypes[0].key);
                  }
                }
              } else {
                console.error(paymentTypesData.reason);
              }

           } catch (err) {
              console.error("Failed to fetch initial data:", err);
              setDataError('Tidak dapat terhubung ke server.');
              setServices([]); setTherapists([]); setOutletName('');
           } finally {
             setIsDataLoading(false);
           }
       };
       fetchData();
    } else {
       // Reset state
       setIsDataLoading(false); setDataError('Pilih outlet.'); setServices([]); setTherapists([]); setOutletName('');
    }
  }, [selectedOutletId, paymentMethod]);

  // Definisikan kembali formatCurrency
  const formatCurrency = (value: number): string => {
    if (isNaN(value) || value === undefined || value === null) return 'Rp 0';
    return new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(value);
  };

  if (isDataLoading) {
      return <div className="flex justify-center items-center h-full"><span className="loading loading-spinner loading-lg text-primary"></span></div>;
  }
  if (!selectedOutletId) {
       return <div>Pilih outlet terlebih dahulu.</div>; // Handle jika tidak ada outlet ID
  }

  if (dataError) {
     return (
       <div className="flex flex-col justify-center items-center h-screen text-center px-4">
         <FiAlertTriangle className="w-16 h-16 text-error mb-4" />
         <h2 className="text-xl font-semibold text-error mb-2">Gagal Memuat Data</h2>
         <p className="text-gray-600 mb-6">Terjadi kesalahan saat memuat data penting: {dataError}.</p>
         <button onClick={() => window.location.reload()} className="btn btn-primary" type="button">Coba Muat Ulang</button>
       </div>
     );
   }

  return (
    <motion.div
      className="container mx-auto p-4 md:p-6" // Adjust padding
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      <motion.h1 variants={fadeInUp} className="text-2xl md:text-3xl font-bold text-base-content mb-6">Transaksi Baru - Outlet {outletName}</motion.h1>

      {/* Tampilkan error proses transaksi jika ada */}
      {processingError && (
          <motion.div variants={fadeInUp} className="alert alert-error shadow-sm text-sm">
              <FiAlertTriangle/>
              <span>{processingError}</span>
               <button
                 onClick={() => setProcessingError(null)}
                 className="btn btn-xs btn-ghost"
                 type="button"
               >Tutup</button>
          </motion.div>
      )}

      {/* Tampilan Form Transaksi (Selalu ada kecuali modal terbuka) */}
      {!transactionComplete && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-start"> {/* items-start agar kolom kanan tidak stretch */}

          {/* === Kolom Kiri: Input === */}
          <motion.div key="left-column" variants={staggerContainer} className="lg:col-span-2 space-y-6">

            {/* Tombol Load dari Booking */}
            <motion.div key="load-booking-button-container" variants={fadeInUp}>
              <button
                type="button"
                className="btn btn-outline border-primary text-primary hover:bg-primary/10 hover:text-primary w-full"
                onClick={() => setShowSelectBookingModal(true)}
              >
                 <FiCalendar className="mr-2"/> Load Transaksi dari Booking
              </button>
            </motion.div>

            {/* 1. Pilih Layanan (Grid Kartu) */}
            <motion.div key="service-selection-card" variants={fadeInUp} className="card bg-base-100 shadow border border-base-300">
              <div className="card-body p-3 sm:p-6">
                <h2 className="card-title text-lg flex items-center text-base-content mb-4">
                  <FiTag className="mr-2 text-primary"/> Pilih Layanan
                </h2>

                {/* Input Pencarian Layanan */}
                <div className="relative mb-4">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <FiSearch className="text-gray-500" />
                  </div>
                  <input
                    type="text"
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                    placeholder="Cari layanan..."
                    value={serviceSearchTerm}
                    onChange={(e) => setServiceSearchTerm(e.target.value)}
                  />
                </div>

                <div className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-3 gap-3">
                  {filteredServices.map((service, i) => (
                    <div key={`service-card-${service.id}-${i}`} className="card card-compact bg-base-200/50 p-3 relative group">
                      {/* Konten Kartu Layanan */}
                      <p className="font-semibold text-sm text-base-content mb-1 pr-7">{service.name}</p>
                      <p className="text-xs text-base-content/80">{formatCurrency(service.price)} ({service.duration}mnt)</p>
                      {/* Tombol Tambah (+) di pojok */}
                      <button
                         className="btn btn-primary btn-xs btn-square absolute top-2 right-2 opacity-80 group-hover:opacity-100 transition-opacity"
                         onClick={() => handleAddService(service)}
                         title={`Tambah ${service.name}`}
                         type="button"
                      >
                         <FiPlus />
                      </button>
                    </div>
                  ))}
                  {isDataLoading && services.length === 0 && <span key="loading-services" className="loading loading-dots loading-sm col-span-full text-center py-4"></span>}
                  {!isDataLoading && filteredServices.length === 0 && services.length > 0 && <p key="no-filtered-services" className="col-span-full text-center text-gray-500 text-sm py-4">Tidak ada layanan yang sesuai dengan pencarian. <button onClick={() => setServiceSearchTerm('')} className="btn btn-xs btn-outline mt-2" type="button">Reset Pencarian</button></p>}
                  {!isDataLoading && services.length === 0 && <p key="no-services" className="col-span-full text-center text-gray-500 text-sm py-4">Tidak ada layanan aktif.</p>}
                </div>
              </div>
            </motion.div>

            {/* Pemilihan Terapis (Hanya Tombol) */}
            <motion.div key="therapist-selection-card" variants={fadeInUp} className="card bg-base-100 shadow-sm border border-gray-200">
              <div className="card-body p-3 sm:p-6">
                <h2 className="card-title text-lg font-semibold mb-3"><FiUser className="text-secondary mr-2"/> Pilih Terapis</h2>

                {/* Input Pencarian Terapis */}
                <div className="relative mb-4">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <FiSearch className="text-gray-500" />
                  </div>
                  <input
                    type="text"
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-secondary focus:border-secondary"
                    placeholder="Cari terapis..."
                    value={therapistSearchTerm}
                    onChange={(e) => setTherapistSearchTerm(e.target.value)}
                  />
                </div>

                <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                  {filteredTherapists.map((therapist, i) => {
                    // Gunakan sortedTherapistQueue untuk mendapatkan info antrian
                    const queueEntry = sortedTherapistQueue.find(q => q.therapistId === therapist.id);
                    const queueNumber = queueEntry ? sortedTherapistQueue.indexOf(queueEntry) + 1 : null;
                    const isInQueue = !!queueEntry;

                    return (
                      <div key={`therapist-${therapist.id}-${i}`} className="relative flex flex-col items-stretch">
                      <button
                        onClick={() => setSelectedTherapist(therapist)}
                          className={`btn btn-sm w-full flex-grow justify-start text-left overflow-hidden py-3 pl-3 pr-10 min-h-[3rem] h-auto relative group
                            ${
                          selectedTherapist?.id === therapist.id
                                ? 'btn-secondary ring-2 ring-offset-1 ring-secondary' // Lebih jelas jika terpilih
                            : therapist.isServing
                                  ? 'btn-outline btn-disabled text-base-content/50 border-base-content/20 cursor-not-allowed' // Lebih netral jika sibuk
                                  : isInQueue
                                    ? 'btn-primary btn-outline border-primary/70 hover:bg-primary/10' // Warna primer untuk antrian
                                    : 'btn-outline border-base-content/30 text-base-content hover:bg-base-200 hover:text-base-content' // Default
                        }`}
                          disabled={therapist.isServing && selectedTherapist?.id !== therapist.id}
                          title={therapist.isServing
                            ? `${therapist.name} sedang melayani (hingga ${new Date(therapist.availableAt || Date.now()).toLocaleTimeString('id-ID', { hour: '2-digit', minute:'2-digit' })})`
                            : isInQueue
                              ? `${therapist.name} - Antrian #${queueNumber}`
                              : therapist.name
                          }
                        type="button"
                      >
                          <div className="flex items-center w-full min-h-[2rem] py-1">
                            {isInQueue && (
                              <div className="flex items-center justify-center bg-primary/20 text-primary h-6 w-6 rounded-full mr-2 shrink-0">
                                <span className="text-xs font-bold">
                                  {queueNumber}
                                </span>
                              </div>
                            )}
                            <span className="flex-1 text-left text-sm leading-tight break-words whitespace-normal mr-2">{therapist.name}</span>
                            {/* {therapist.isServing && (
                              <span className="ml-auto badge badge-xs badge-warning shrink-0">Sibuk</span>
                            )} */}
                          </div>
                    </button>

                    {/* Tombol Selesaikan Sesi (di luar button terapis) */}
                    {therapist.isServing && (
                          <div className="absolute top-1/2 -translate-y-1/2 right-2 z-20">
                        <button
                          onClick={(e) => {
                                e.stopPropagation(); // Hindari trigger klik pada tombol terapis
                            handleEndTherapistSession(therapist);
                          }}
                              className="btn btn-xs btn-circle btn-ghost text-success hover:bg-success/20 p-0.5"
                          title={`Selesaikan sesi ${therapist.name}`}
                          type="button"
                        >
                              <FiCheckCircle className="w-3 h-3" />
                        </button>
                      </div>
                    )}
                    </div>
                    );
                  })}
                  {isDataLoading && therapists.length === 0 && <span key="loading-therapists" className="loading loading-dots loading-sm col-span-full text-center py-4"></span>}
                  {!isDataLoading && filteredTherapists.length === 0 && therapists.length > 0 && <p key="no-filtered-therapists" className="col-span-full text-center text-gray-500 text-sm py-4">Tidak ada terapis yang sesuai dengan pencarian. <button onClick={() => setTherapistSearchTerm('')} className="btn btn-xs btn-outline mt-2" type="button">Reset Pencarian</button></p>}
                  {!isDataLoading && therapists.length === 0 && <p key="no-therapists" className="col-span-full text-center text-gray-500 text-sm py-4">Tidak ada terapis aktif.</p>}
                </div>
              </div>
            </motion.div>

            {/* 3. Pelanggan (Card) - RESTRUCTURED */}
            <motion.div key="customer-section-card" variants={fadeInUp} className="card bg-base-100 shadow border border-base-300">
               <div className="card-body p-3 sm:p-6">
                 <h2 className="card-title text-lg flex items-center text-base-content">
                   <FiUsers className="mr-2 text-info"/> Pelanggan
                 </h2>

                 {/* Bagian 1: Selalu tampilkan daftar pelanggan terpilih jika ada */}
                 {selectedCustomers.length > 0 && (
                    <div key="selected-customers-list" className="space-y-2 mb-4 border-b border-base-300 pb-4">
                        {selectedCustomers.map(customer => (
                            <div key={customer.id} className="alert bg-info/10 text-info-content p-2 sm:p-4">
                        <FiCheckCircle className="shrink-0" />
                        <div className="overflow-hidden">
                                   <h3 className="font-bold text-sm sm:text-base truncate">{customer.name}</h3>
                                   <div className="flex flex-col xs:flex-row xs:items-center gap-1">
                                     <div className="text-xs truncate">{customer.phone}</div>
                                     {customer.points !== undefined && (
                                       <>
                                         <span className="hidden xs:inline text-xs">•</span>
                                         <div className="flex items-center text-xs">
                                           <FiStar className="text-amber-500 mr-1 h-3 w-3 shrink-0" />
                                           <span>{customer.points} Poin</span>
                                         </div>
                                       </>
                                     )}
                                   </div>
                        </div>
                                <button
                                  onClick={() => handleRemoveCustomer(customer.id)}
                                  className="btn btn-xs btn-ghost shrink-0"
                                  type="button"
                                  title="Hapus pelanggan"
                                  aria-label="Hapus pelanggan"
                                >
                                  <FiXCircle/>
                                </button>
                    </div>
                        ))}
                    </div>
                 )}

                 {/* Bagian 2: Selalu tampilkan form pencarian/tambah */}
                 <div key="customer-search-add-section" className="space-y-2">
                    {/* Input Pencarian */}
                        <div className="form-control relative">
                           <div className="join w-full">
                              <input
                                type="text"
                            placeholder={selectedCustomers.length > 0 ? "Cari / Tambah Pelanggan Lain..." : "Cari Nama/No. HP Pelanggan..."}
                                className="input input-bordered join-item w-full placeholder:text-base-content/60 text-base-content pr-10"
                                value={customerSearchTerm}
                                onChange={(e) => setCustomerSearchTerm(e.target.value)}
                                disabled={isCustomerSearching}
                              />
                           </div>
                       {isCustomerSearching ? (
                          <span key="search-spinner" className="absolute top-1/2 right-3 -translate-y-1/2 loading loading-spinner loading-sm text-gray-400"></span>
                       ) : null}
                        </div>
                    {/* Hasil Pencarian */}
                        {customerSearchResults.length > 0 && (
                           <ul className="menu bg-base-200 rounded-box max-h-48 overflow-y-auto p-2">
                              {/* Tampilkan hasil yang cocok dengan kata kunci pencarian di bagian atas */}
                              {customerSearchResults
                                .sort((a, b) => {
                                  // Prioritaskan hasil yang mengandung kata kunci di nama
                                  const searchTermLower = customerSearchTerm.toLowerCase();
                                  const aNameMatch = a.name.toLowerCase().includes(searchTermLower);
                                  const bNameMatch = b.name.toLowerCase().includes(searchTermLower);

                                  if (aNameMatch && !bNameMatch) return -1;
                                  if (!aNameMatch && bNameMatch) return 1;

                                  // Jika keduanya cocok atau tidak cocok dengan nama, bandingkan berdasarkan nomor telepon
                                  const aPhoneMatch = a.phone.includes(customerSearchTerm);
                                  const bPhoneMatch = b.phone.includes(customerSearchTerm);

                                  if (aPhoneMatch && !bPhoneMatch) return -1;
                                  if (!aPhoneMatch && bPhoneMatch) return 1;

                                  // Jika masih sama, urutkan berdasarkan nama
                                  return a.name.localeCompare(b.name);
                                })
                                .map(cust => {
                                  // Highlight bagian yang cocok dengan kata kunci
                                  const searchTermLower = customerSearchTerm.toLowerCase();
                                  const nameMatch = cust.name.toLowerCase().includes(searchTermLower);
                                  const phoneMatch = cust.phone.includes(customerSearchTerm);

                                  // Tambahkan indikator kecocokan
                                  const matchIndicator = nameMatch ? '✓ Nama' : (phoneMatch ? '✓ No HP' : '');

                                  return (
                                    <li key={cust.id}>
                                      <a
                                        onClick={() => handleSelectCustomer(cust)}
                                        className={`text-sm text-base-content hover:bg-base-300 ${selectedCustomers.some(c => c.id === cust.id) ? 'opacity-50 cursor-not-allowed customer-item-disabled' : ''}`}
                                      >
                                        <div className="flex flex-col">
                                          <div className="flex items-center justify-between w-full">
                                            <span className="font-medium">{cust.name}</span>
                                            {matchIndicator && <span className="badge badge-xs badge-info text-[10px]">{matchIndicator}</span>}
                                          </div>
                                          <span className="text-xs text-gray-500">{cust.phone}</span>
                                        </div>
                                        {selectedCustomers.some(c => c.id === cust.id) && ' (Terpilih)'}
                                      </a>
                                    </li>
                                  );
                                })
                              }
                           </ul>
                        )}
                     {/* Tombol Tambah Baru */}
                         <button
                            className="btn btn-sm btn-outline w-full mt-2 text-base-content border-base-content/40"
                            onClick={() => setShowAddCustomerModal(true)}
                        type="button"
                         >
                            <FiUserPlus className="mr-1"/> Tambah Pelanggan Baru
                         </button>
                    </div>
               </div>
             </motion.div>
          </motion.div>

          {/* === Kolom Kanan: Ringkasan & Checkout === */}
          <motion.div key="right-column" variants={fadeInUp} className="lg:col-span-1 space-y-6">

             {/* 1. Card Ringkasan Pesanan (MODIFIED) */}
             <div key="order-summary-card" className="card bg-base-100 shadow border border-base-300 sticky top-20">
                <div className="card-body p-3 sm:p-6">
                   <h2 className="card-title text-lg text-base-content mb-4">Ringkasan Pesanan</h2>

                   {/* Daftar Layanan Terpilih */}
                   <div className="space-y-3 max-h-60 overflow-y-auto pr-2 mb-4 border-b border-base-300 pb-4">
                     {selectedServices.length > 0 ? (
                         selectedServices.map((item, index) => (
                            <div key={`service-item-${item.service.id}-${index}`} className="flex flex-col sm:flex-row sm:justify-between sm:items-center text-sm gap-2">
                               <div className="flex flex-col">
                                 <span className="text-base-content font-medium">{item.service.name}</span>
                                 <span className="text-xs text-base-content/70">
                                   {formatCurrency(item.service.price)} x {item.quantity}
                                 </span>
                               </div>
                               <div className="flex items-center justify-between sm:justify-end gap-2">
                                 <span className="text-base-content/90">{formatCurrency(item.service.price * item.quantity)}</span>
                                 <div className="flex items-center border border-base-300 rounded-md">
                                   <button
                                     onClick={() => handleRemoveService(item.service.id)}
                                     className="btn btn-xs btn-ghost text-base-content p-1 min-h-0 h-6 w-6"
                                     type="button"
                                     aria-label="Kurangi jumlah"
                                   >
                                     -
                                   </button>
                                   <span className="w-6 text-center">{item.quantity}</span>
                                   <button
                                     onClick={() => handleAddService(item.service)}
                                     className="btn btn-xs btn-ghost text-base-content p-1 min-h-0 h-6 w-6"
                                     type="button"
                                     aria-label="Tambah jumlah"
                                   >
                                     +
                                   </button>
                                 </div>
                                 <button
                                   onClick={() => handleRemoveServiceCompletely(item.service.id)}
                                   className="btn btn-xs btn-ghost text-error p-1 min-h-0 h-6 w-6"
                                   type="button"
                                   aria-label="Hapus layanan"
                                 >
                                   <FiTrash2 className='w-3 h-3'/>
                                 </button>
                               </div>
                            </div>
                         ))
                      ) : (
                         <p className="text-sm text-base-content/70 text-center py-4">Belum ada layanan dipilih.</p>
                      )}
                   </div>

                   {/* Kalkulasi Subtotal, Diskon, Charge, Pajak */}
                   <div className="space-y-2 text-sm mb-4 border-b border-base-300 pb-4">
                     <div className="flex justify-between">
                       <span className="text-base-content/80">Subtotal</span>
                       <span className="font-medium text-base-content">{formatCurrency(subtotal)}</span>
                     </div>

                     {/* --- Input Diskon --- */}
                     <div className="form-control pt-1">
                       <label className="label pb-1 pt-0">
                         <span className="label-text text-xs">Diskon</span>
                       </label>
                       <div className="join w-full">
                         <input
                           type="number"
                           placeholder="0"
                           className="input input-sm input-bordered join-item w-full [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base-content"
                           value={discountValue || ''} // Show empty string for 0
                           onChange={handleDiscountValueChange}
                           min="0"
                           step={discountType === 'percentage' ? '0.01' : '1000'}
                           disabled={subtotal === 0} // Disable if no items
                         />
                         <button
                           type="button"
                           className={`btn btn-sm join-item w-10 ${discountType === 'percentage'
                             ? 'btn-active btn-accent text-accent-content'
                             : 'btn-ghost text-base-content/80'}`}
                           onClick={() => setDiscountType('percentage')}
                           disabled={discountValue === 0 || subtotal === 0}
                         > % </button>
                         <button
                           type="button"
                           className={`btn btn-sm join-item w-10 ${discountType === 'fixed'
                             ? 'btn-active btn-accent text-accent-content'
                             : 'btn-ghost text-base-content/80'}`}
                           onClick={() => setDiscountType('fixed')}
                           disabled={discountValue === 0 || subtotal === 0}
                         > Rp </button>
                       </div>
                     </div>
                     {/* --- End Input Diskon --- */}

                     {/* Tampilkan Jumlah Diskon Aktual */}
                     {discountAmount > 0 && (
                       <div key="discount-display" className="flex justify-between text-success mt-1">
                         <span className="text-xs"> Diskon Diterapkan {discountType === 'percentage' && `(${discountValue}%)`} </span>
                         <span className="font-medium">- {formatCurrency(discountAmount)}</span>
                       </div>
                     )}

                     {/* --- Input Biaya Tambahan --- */}
                     <div className="form-control pt-1">
                        <label className="label pb-1 pt-0">
                           <span className="label-text text-xs">Biaya Tambahan (Rp)</span>
                        </label>
                        <input
                           type="number"
                           placeholder="0"
                           className="input input-sm input-bordered w-full [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base-content"
                           value={additionalCharge || ''}
                           onChange={handleChargeChange}
                           min="0"
                           step="1000"
                           disabled={subtotal === 0} // Disable jika belum ada item
                        />
                     </div>
                     {/* --- End Input Biaya Tambahan --- */}

                     {/* Tampilkan Jumlah Biaya Tambahan Aktual */}
                     {additionalCharge > 0 && (
                        <div key="charge-display" className="flex justify-between text-warning mt-1"> {/* Ganti text color jika perlu */}
                           <span className="text-xs">Biaya Tambahan</span>
                           <span className="font-medium">+ {formatCurrency(additionalCharge)}</span>
                        </div>
                     )}

                     {/* --- Tombol Lembur Terapis --- */}
                     <div className="form-control pt-1">
                        <div className="flex justify-between items-center">
                           <label className="label pb-1 pt-0">
                              <span className="label-text text-sm font-medium">Lembur Terapis</span>
                           </label>
                           <button
                              type="button"
                              className={`btn btn-sm ${overtimeEnabled ? 'btn-primary' : 'btn-outline'}`}
                              onClick={() => setOvertimeEnabled(!overtimeEnabled)}
                              disabled={!selectedTherapist || selectedServices.length === 0}
                           >
                              {overtimeEnabled ? 'Lembur Aktif' : 'Aktifkan Lembur'}
                           </button>
                        </div>
                        
                        {overtimeEnabled && (
                           <>
                              {/* Menit Lembur (Read-only) */}
                              <div className="mt-2">
                                 <div className="flex justify-between items-center">
                                    <label className="label pb-1 pt-0">
                                       <span className="label-text text-xs">Menit Lembur</span>
                                    </label>
                                    <span className="badge badge-info badge-sm">Otomatis</span>
                                 </div>
                                 <div className="relative">
                                    <input
                                       type="number"
                                       placeholder="0"
                                       className="input input-sm input-bordered w-full [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base-content"
                                       value={overtimeMinutes || ''}
                                       onChange={handleOvertimeMinutesChange}
                                    />
                                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                       <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-info" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                       </svg>
                                    </div>
                                 </div>
                                 <p className="text-xs text-info mt-1">Total durasi dari layanan yang dipilih</p>
                                 <p className="text-xs text-warning mt-1">Catatan: Biaya lembur tidak ditambahkan ke total transaksi dan tidak ditampilkan di struk</p>
                              </div>

                              {/* Kompensasi Lembur (Read-only) */}
                              <div className="mt-2">
                                 <div className="flex justify-between items-center">
                                    <label className="label pb-1 pt-0">
                                       <span className="label-text text-xs">Kompensasi Lembur</span>
                                    </label>
                                    <span className="badge badge-info badge-sm">Rp 100/menit</span>
                                 </div>
                                 <div className="relative">
                                    <input
                                       type="number"
                                       placeholder="0"
                                       className="input input-sm input-bordered w-full [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base-content bg-base-200"
                                       value={overtimeAmount || ''}
                                       readOnly
                                    />
                                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                       <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-info" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                       </svg>
                                    </div>
                                 </div>
                              </div>
                           </>
                        )}
                     </div>
                     {/* --- End Lembur Terapis --- */}

                     {/* Lembur tidak ditampilkan di ringkasan transaksi sesuai permintaan */}

                     {/* Placeholder Pajak */}
                     {tax > 0 && (
                       <div key="tax-display" className="flex justify-between mt-1">
                         <span className="text-base-content/80">Pajak</span>
                         <span className="font-medium text-base-content">{formatCurrency(tax)}</span>
                       </div>
                     )}

                     {/* Garis pemisah sebelum TOTAL jika ada diskon/charge/pajak */}
                     {(discountAmount > 0 || additionalCharge > 0 || tax > 0) && (
                        <div key="summary-separator" className="border-t border-dotted border-black my-1"></div>
                     )}
                   </div>
                   <div className="flex justify-between text-lg font-bold text-base-content border-t border-base-300 pt-3">
                         <span>Total</span>
                         <span>Rp {formatCurrency(total)}</span>
                      </div>
                </div>
             </div>

             {/* 2. Card Metode Pembayaran & Catatan */}
             <div key="payment-notes-card" className="card bg-base-100 shadow border border-base-300">
                <div className="card-body p-3 sm:p-6">
                    <div className="flex justify-between items-center mb-2 sm:mb-3">
                      <h3 className="text-sm sm:text-md font-semibold text-base-content">Metode Pembayaran</h3>
                      <div className="form-control">
                        <label className="label cursor-pointer gap-1.5 p-0">
                          <span className="label-text text-xs sm:text-sm">Split Bill</span>
                          <input
                            type="checkbox"
                            className="toggle toggle-sm toggle-primary"
                            checked={isSplitBill}
                            onChange={(e) => setIsSplitBill(e.target.checked)}
                          />
                        </label>
                      </div>
                    </div>

                    {/* Metode Pembayaran Pertama */}
                    <div className="grid grid-cols-2 gap-2 sm:gap-3">
                      {paymentMethods.map((pm, i) => (
                         <button
                           type="button"
                           key={`payment-method-${pm.key}-${i}`}
                           className={`btn btn-sm h-auto py-1.5 sm:py-2 flex flex-col items-center
                             ${paymentMethod === pm.key
                               ? 'btn-primary'
                               : 'btn-outline border-base-content/20 text-base-content'}`}
                           onClick={() => setPaymentMethod(pm.key)}
                         >
                           <pm.icon className="w-3 h-3 sm:w-4 sm:h-4 mb-0.5 sm:mb-1"/>
                           <span className="text-[9px] sm:text-xs leading-tight whitespace-normal text-center">{pm.label}</span>
                         </button>
                      ))}
                    </div>

                    {/* Metode Pembayaran Kedua (jika split bill) */}
                    {isSplitBill && (
                      <div className="mt-3">
                        <h4 className="text-xs font-medium mb-2 text-base-content">Metode Pembayaran Kedua</h4>
                        <div className="grid grid-cols-2 gap-2 sm:gap-3">
                          {paymentMethods
                            .filter(pm => pm.key !== paymentMethod) // Exclude first payment method
                            .map((pm, i) => (
                              <button
                                type="button"
                                key={`second-payment-method-${pm.key}-${i}`}
                                className={`btn btn-sm h-auto py-1.5 sm:py-2 flex flex-col items-center
                                  ${secondPaymentMethod === pm.key
                                    ? 'btn-secondary'
                                    : 'btn-outline border-base-content/20 text-base-content'}`}
                                onClick={() => setSecondPaymentMethod(pm.key)}
                              >
                                <pm.icon className="w-3 h-3 sm:w-4 sm:h-4 mb-0.5 sm:mb-1"/>
                                <span className="text-[9px] sm:text-xs leading-tight whitespace-normal text-center">{pm.label}</span>
                              </button>
                            ))}
                        </div>
                      </div>
                    )}

                    {/* Input Jumlah Tunai (jika salah satu metode adalah tunai) */}
                    {(paymentMethod === 'cash' || (isSplitBill && secondPaymentMethod === 'cash')) && (
                      <div className="form-control mt-3">
                        <label className="label pb-1 pt-0">
                          <span className="label-text text-xs">Jumlah Tunai (Rp)</span>
                        </label>
                        <input
                          type="number"
                          placeholder="0"
                          className="input input-sm input-bordered w-full [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base-content"
                          value={cashAmount || ''}
                          onChange={handleCashAmountChange}
                          min="0"
                          step="1000"
                        />
                      </div>
                    )}

                    {/* Tampilkan Total Diterima dan Kembalian (jika ada input tunai) */}
                    {cashAmount > 0 && (
                      <div className="mt-3 space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="text-base-content/80">Total Diterima</span>
                          <span className="font-medium text-base-content">{formatCurrency(totalReceived)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-base-content/80">Kembalian</span>
                          <span className="font-medium text-success">{formatCurrency(changeAmount)}</span>
                        </div>
                      </div>
                    )}

                    <div className="form-control mt-3 sm:mt-4">
                      <label className="label py-0 sm:pb-1"><span className="label-text text-xs">Catatan (Opsional)</span></label>
                      <textarea
                         className="textarea textarea-bordered text-xs sm:text-sm placeholder:text-base-content/60 h-16 sm:h-20 min-h-[4rem]"
                         placeholder="Info tambahan..."
                         value={transactionNote}
                         onChange={(e) => setTransactionNote(e.target.value)}
                      ></textarea>
                    </div>
                </div>
             </div>

             {/* 3. Tombol Proses Transaksi (Total diperbarui) */}
             <button
                key="process-transaction-button"
                type="button"
                className={`btn btn-primary w-full mt-4 sm:mt-6 py-3 h-auto ${isProcessingTransaction ? 'btn-disabled' : ''}`}
                onClick={handleProcessTransaction}
                disabled={selectedServices.length === 0 || !selectedTherapist || selectedCustomers.length === 0 || isProcessingTransaction}
             >
                {isProcessingTransaction ?
                   <span key="processing-text">
                     <span className="loading loading-spinner loading-xs"></span> Memproses...
                   </span> :
                   <span key="transaction-text">{`Proses Transaksi (${formatCurrency(total)})`}</span>
                }
             </button>
          </motion.div>
        </div>
      )}

      {/* Struk (jika transaksi selesai) */}
       {transactionComplete && transactionDetails && (
        <div key="transaction-result">
        <motion.div variants={fadeInUp} className="text-center mt-6">
           <h2 className="text-xl font-semibold text-success mb-2">Transaksi Berhasil!</h2>
             <button
               onClick={() => handlePrint()}
               className="btn btn-primary mr-2"
               type="button"
             ><FiPrinter className="mr-1"/> Cetak Struk</button>
             <button
               onClick={handleNewTransaction}
               className="btn btn-outline"
               type="button"
             >Buat Transaksi Baru</button>
         </motion.div>
        <div className="hidden receipt-container">
           <ReceiptComponent ref={receiptRef} {...transactionDetails} />
         </div>
        </div>
      )}

      {/* Modal Tambah Pelanggan */}
      <dialog id="add_customer_modal" className={`modal ${showAddCustomerModal ? 'modal-open' : ''}`}>
        <div className="modal-box p-3 sm:p-6">
          <h3 className="font-bold text-lg mb-4">Tambah Pelanggan Baru</h3>
          <form method="dialog" onSubmit={(e) => { e.preventDefault(); handleAddNewCustomer(); }} className="space-y-4">
            <div className="form-control">
              <label className="label"><span className="label-text">Nama Pelanggan</span></label>
              <input type="text" placeholder="Nama Lengkap" className="input input-bordered" value={newCustomerName} onChange={(e) => setNewCustomerName(e.target.value)} required/>
            </div>
            <div className="form-control">
              <label className="label"><span className="label-text">Nomor HP</span></label>
              <input type="tel" placeholder="08xxxxxxxxxx" className="input input-bordered" value={newCustomerPhone} onChange={(e) => setNewCustomerPhone(e.target.value)} required/>
            </div>
            <div className="form-control">
              <label className="label"><span className="label-text">Alamat (Opsional)</span></label>
              <textarea placeholder="Alamat lengkap" className="textarea textarea-bordered" value={newCustomerAddress} onChange={(e) => setNewCustomerAddress(e.target.value)}/>
            </div>
            <div className="modal-action mt-6">
              <button type="button" className="btn" onClick={() => setShowAddCustomerModal(false)} disabled={isAddingCustomer}>Batal</button>
              <button type="submit" className={`btn btn-primary ${isAddingCustomer ? 'btn-disabled' : ''}`} disabled={isAddingCustomer}>
                 {isAddingCustomer ? (
                   <span key="saving-text">
                     <span className="loading loading-spinner loading-xs"></span> Menyimpan...
                   </span>
                 ) : <span key="save-text">Simpan Pelanggan</span>}
              </button>
            </div>
          </form>
        </div>
         <form method="dialog" className="modal-backdrop" onClick={() => setShowAddCustomerModal(false)}>
           <button type="button">close</button>
         </form>
      </dialog>

      {/* Modal Pilih Booking */}
      <dialog id="select_booking_modal" className={`modal ${showSelectBookingModal ? 'modal-open' : ''}`}>
        <div className="modal-box w-11/12 max-w-2xl p-3 sm:p-6">
          <h3 className="font-bold text-lg mb-4 flex items-center">
            <FiCalendar className="mr-2"/> Pilih Booking
          </h3>
          {/* TODO: Tambahkan input search jika diperlukan */}
          {/* <input type="text" placeholder="Cari Nama Pelanggan/Tanggal..." ... /> */}

          {/* Daftar Hasil Pencarian Booking */}
          <div className="mt-4 space-y-2 max-h-96 overflow-y-auto">
            {isBookingSearching && (
              <div className="flex justify-center items-center py-6">
                <span className="loading loading-spinner loading-lg text-primary"></span>
              </div>
            )}
            {!isBookingSearching && bookingLoadingError && (
              <div className="alert alert-error text-sm">
                <FiAlertTriangle />
                <span>Gagal memuat booking: {bookingLoadingError}</span>
                <button className="btn btn-xs btn-ghost" onClick={handleSearchBooking} type="button">Coba Lagi</button>
              </div>
            )}
            {!isBookingSearching && !bookingLoadingError && bookingSearchResults.length === 0 && (
              <p className="text-center text-base-content/70 py-6">Tidak ada booking berstatus CONFIRMED ditemukan untuk outlet ini.</p>
            )}
            {!isBookingSearching && !bookingLoadingError && bookingSearchResults.map(booking => (
              <div key={booking.id} className="card card-compact bg-base-200/60 border border-base-300">
                <div className="card-body flex-row justify-between items-center gap-4 p-3">
                  <div>
                    <p className="font-semibold text-base-content">{booking.customer.name}</p>
                    <p className="text-xs text-base-content/80">
                      {new Date(booking.bookingDate).toLocaleDateString('id-ID', { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' })} - {booking.bookingTime}
                    </p>
                    {booking.therapist && <p className="text-xs text-base-content/70">Terapis: {booking.therapist.name}</p>}
                  </div>
                  <button
                    className="btn btn-sm btn-primary btn-outline"
                    onClick={() => handleSelectBooking(booking)}
                    type="button"
                  >
                    Pilih
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="modal-action mt-6">
            <button type="button" className="btn" onClick={() => setShowSelectBookingModal(false)}>Tutup</button>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop" onClick={() => setShowSelectBookingModal(false)}>
          <button type="button">close</button>
        </form>
      </dialog>

      {/* Modal Konfirmasi Selesaikan Sesi Terapis */}
      <dialog id="end_therapist_session_modal" className={`modal ${showEndSessionModal ? 'modal-open' : ''}`}>
        <div className="modal-box p-3 sm:p-6">
          <h3 className="font-bold text-lg mb-4">Selesaikan Sesi Terapis</h3>
          {selectedTherapistSession && (
            <div className="py-2">
              <p className="mb-4">Apakah Anda yakin ingin menyelesaikan sesi terapis <span className="font-semibold">{selectedTherapistSession.name}</span>?</p>
              <p className="text-sm text-gray-500 mb-4">Tindakan ini akan menandai terapis sebagai tersedia untuk melayani tamu lain.</p>
            </div>
          )}
          <div className="modal-action mt-6">
            <button
              type="button"
              className="btn"
              onClick={() => {
                setShowEndSessionModal(false);
                setSelectedTherapistSession(null);
              }}
              disabled={isEndingSession}
            >
              Batal
            </button>
            <button
              type="button"
              className={`btn btn-primary ${isEndingSession ? 'btn-disabled' : ''}`}
              onClick={handleConfirmEndSession}
              disabled={isEndingSession}
            >
              {isEndingSession ? (
                <span key="ending-session-text">
                  <span className="loading loading-spinner loading-xs"></span> Menyelesaikan...
                </span>
              ) : (
                <span key="end-session-text">Selesaikan Sesi</span>
              )}
            </button>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop" onClick={() => setShowEndSessionModal(false)}>
          <button type="button">close</button>
        </form>
      </dialog>
    </motion.div>
  );
}