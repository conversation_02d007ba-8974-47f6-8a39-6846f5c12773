import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Role, Prisma } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { logUser } from '@/lib/logger';
import { getUserIdFromToken } from '@/lib/auth-utils';

// Interface untuk Outlet Option (minimal)
interface OutletOption {
  id: string;
  name: string;
}

// GET user berdasarkan ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Await params untuk menghindari error
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: 'ID user diperlukan' },
        { status: 400 }
      );
    }

    // Ambil detail user berdasarkan ID, sertakan permissions
    const user = await prisma.user.findUnique({
      where: {
        id
      },
      include: { // Gunakan include untuk permissions dan allowedOutlets
        permissions: {
          select: {
            module: true,
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true,
          },
          orderBy: { module: 'asc' } // Urutkan berdasarkan nama modul
        },
        allowedOutlets: { // Tambahkan include untuk allowedOutlets
          select: {
            outlet: { // Pilih data outlet yang terkait
              select: {
                id: true,
                name: true,
              }
            }
          }
        }
      }
    });

    // Jika user tidak ditemukan
    if (!user) {
      return NextResponse.json(
        { error: 'User tidak ditemukan' },
        { status: 404 }
      );
    }

    // Exclude password dan format allowedOutlets
    // TODO: Linter error terkait 'allowedOutlets' kemungkinan karena Prisma Client belum di-generate ulang.
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password: _, allowedOutlets: accessData, ...userWithoutPassword } = user;
    // Definisikan tipe eksplisit untuk 'access'
    type OutletAccess = { outlet: OutletOption };
    const formattedAllowedOutlets = Array.isArray(accessData) ? accessData.map((access: OutletAccess) => access.outlet) : [];

    // Kembalikan data user
    return NextResponse.json({
      message: 'Data user berhasil diambil',
      user: { ...userWithoutPassword, allowedOutlets: formattedAllowedOutlets } // Sertakan formatted outlets
    });
  } catch (error) {
    console.error('Error fetching user details:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil detail user' },
      { status: 500 }
    );
  }
}

// PUT untuk mengupdate user (termasuk permissions)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Pastikan params.id di-await
    const { id } = await params;

    // Log request untuk debugging
    console.log(`PUT /api/users/${id} - Memproses request`);

    const body = await request.json();
    console.log('Request body:', {
      ...body,
      password: body.password ? '[REDACTED]' : undefined
    });

    // Destructuring body - field ini sekarang opsional
    const { name, email, password, role: newRole, isActive, isCaptain, allowedOutletIds } = body;

    // Dapatkan data user yang ada dulu
    const existingUser = await prisma.user.findUnique({
      where: { id },
      include: { allowedOutlets: true }, // Include relasi untuk logika role investor
    });

    if (!existingUser) {
      return NextResponse.json({ error: 'User tidak ditemukan.' }, { status: 404 });
    }

    // Siapkan objek updateData kosong
    const updateData: Prisma.UserUpdateInput = {};

    // === Isi updateData secara kondisional ===
    if (name !== undefined && name.trim() !== '') {
      updateData.name = name;
    } else if (name !== undefined && name.trim() === '') {
      // Jangan biarkan nama dikosongkan jika fieldnya ada tapi string kosong
      return NextResponse.json({ error: 'Nama tidak boleh kosong.' }, { status: 400 });
    }

    if (email !== undefined && email.trim() !== '') {
       // Validasi format email jika ada
       if (!/\S+@\S+\.\S+/.test(email)) {
           return NextResponse.json({ error: 'Format email tidak valid.' }, { status: 400 });
       }
       // Cek keunikan email jika email diubah
       if (email !== existingUser.email) {
           const emailExists = await prisma.user.findFirst({ where: { email, id: { not: id } } });
           if (emailExists) {
               return NextResponse.json({ error: 'Email sudah digunakan pengguna lain.' }, { status: 409 }); // Conflict
           }
       }
       updateData.email = email;
    } else if (email !== undefined && email.trim() === '') {
      return NextResponse.json({ error: 'Email tidak boleh kosong.' }, { status: 400 });
    }

    if (password) {
      if (password.length < 6) {
         return NextResponse.json({ error: 'Password minimal 6 karakter.' }, { status: 400 });
      }
      const hashedPassword = await bcrypt.hash(password, 10);
      updateData.password = hashedPassword;
    }

    if (newRole) {
        // Pastikan Role enum tersedia dan role valid
        const validRoles = ['ADMIN', 'MANAGER', 'STAFF', 'INVESTOR', 'CUSTOMER', 'ASSISTANT'];
        if (!validRoles.includes(newRole)) {
           return NextResponse.json({ error: 'Role tidak valid.' }, { status: 400 });
        }
        updateData.role = newRole as Role;
    }

    // Handle isActive jika ada di body
    if (isActive !== undefined && typeof isActive === 'boolean') {
        updateData.isActive = isActive;
    }

    // Tentukan role final setelah potensi perubahan
    const finalRole = newRole || existingUser.role;

    // Handle isCaptain jika ada di body dan role adalah STAFF
    if (isCaptain !== undefined && typeof isCaptain === 'boolean') {
        // Hanya user dengan role STAFF yang bisa menjadi kapten
        if (finalRole === 'STAFF') {
            updateData.isCaptain = isCaptain;
        } else if (isCaptain === true) {
            // Jika user bukan STAFF tapi mencoba diset sebagai kapten, berikan pesan error
            return NextResponse.json({
                error: 'Hanya user dengan role STAFF yang dapat menjadi kapten.'
            }, { status: 400 });
        }
    }

    // Handle allowedOutletIds jika role adalah Investor atau diubah jadi Investor
    if (finalRole === 'INVESTOR') {
        // Cek apakah allowedOutletIds dikirimkan di body
        if (allowedOutletIds !== undefined) {
            if (!Array.isArray(allowedOutletIds) || allowedOutletIds.length === 0) {
                return NextResponse.json({ error: 'Investor harus memilih minimal satu outlet (allowedOutletIds).' }, { status: 400 });
            }

            // Log untuk debugging
            console.log('Processing allowedOutletIds for INVESTOR:', allowedOutletIds);

            try {
                // Hapus relasi yang ada terlebih dahulu
                await prisma.userOutletAccess.deleteMany({
                    where: { userId: id }
                });

                // Buat relasi baru untuk setiap outlet yang dipilih
                for (const outletId of allowedOutletIds) {
                    await prisma.userOutletAccess.create({
                        data: {
                            userId: id,
                            outletId: outletId
                        }
                    });
                }
            } catch (error) {
                console.error('Error updating outlet access:', error);
                return NextResponse.json({ error: 'Gagal memperbarui akses outlet' }, { status: 500 });
            }
        } else if (newRole === 'INVESTOR') {
             // Jika role BARU diubah jadi investor tapi allowedOutletIds TIDAK dikirim
             return NextResponse.json({ error: 'Daftar outlet yang diizinkan (allowedOutletIds) wajib ada saat mengubah role menjadi Investor.' }, { status: 400 });
        }
    } else if (existingUser.role === 'INVESTOR' && finalRole !== 'INVESTOR') {
         // Jika role diubah DARI Investor ke role lain
         try {
             // Hapus semua relasi outlet
             await prisma.userOutletAccess.deleteMany({
                 where: { userId: id }
             });
         } catch (error) {
             console.error('Error removing outlet access:', error);
             return NextResponse.json({ error: 'Gagal menghapus akses outlet' }, { status: 500 });
         }
    }

    // Lakukan update hanya jika ada data yang perlu diubah
    if (Object.keys(updateData).length === 0) {
        return NextResponse.json({ message: 'Tidak ada data yang perlu diperbarui.', user: existingUser }, { status: 200 });
    }

    // Update user tanpa menggunakan updateData yang menyebabkan masalah tipe
    const updatedUser = await prisma.user.update({
        where: { id },
        data: {
            ...(updateData.name !== undefined && { name: updateData.name }),
            ...(updateData.email !== undefined && { email: updateData.email }),
            ...(updateData.password !== undefined && { password: updateData.password }),
            ...(updateData.role !== undefined && { role: updateData.role }),
            ...(updateData.isActive !== undefined && { isActive: updateData.isActive }),
            ...(updateData.isCaptain !== undefined && { isCaptain: updateData.isCaptain })
        },
        include: { allowedOutlets: true } // Include relasi untuk response
    });

    // Exclude password hash from the response
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password: _, ...userWithoutPassword } = updatedUser;

    // Ambil user ID dari token
    const adminUserId = getUserIdFromToken(request);

    // Log update user
    await logUser(
      'update',
      id,
      {
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        isActive: updatedUser.isActive,
        updatedFields: Object.keys(updateData)
      },
      undefined, // Tidak ada outletId spesifik
      adminUserId ? adminUserId : undefined // Gunakan userId dari token
    );

    return NextResponse.json({ user: userWithoutPassword });

  } catch (error: unknown) {
    // Log error asli untuk inspeksi lebih detail di log server
    console.error('[API PUT /api/users/[id]] Detailed Error:', error);

    let errorMessage = 'Gagal memperbarui pengguna.';
    let statusCode = 500;

    // Coba periksa properti 'code' daripada instanceof
    if (error && typeof error === 'object' && 'code' in error) {
        // Asumsikan ini error Prisma jika punya property 'code'
        const prismaError = error as { code: string; message?: string }; // Type assertion sederhana

        console.log(`[API PUT /api/users/[id]] Detected Prisma error code: ${prismaError.code}`);

        if (prismaError.code === 'P2002') {
            errorMessage = 'Email atau username mungkin sudah digunakan.';
            statusCode = 409; // Conflict
        }
         else if (prismaError.code === 'P2025') {
            errorMessage = 'Gagal memperbarui relasi: salah satu data terkait tidak ditemukan (misal: outlet ID tidak valid).';
            statusCode = 404;
        }
        // Tambahkan penanganan kode error Prisma lain jika perlu
        // else if (prismaError.code === 'Pxxxx') { ... }
        else {
             // Jika kode error Prisma tidak dikenali secara spesifik
             errorMessage = `Terjadi masalah database (Code: ${prismaError.code}). Coba lagi nanti.`;
        }
    } else if (error instanceof Error) {
        // Jika ini error JavaScript standar
        errorMessage = error.message;
    }
    // Jika error tidak dikenali sama sekali, pesan default akan digunakan

    return NextResponse.json({ error: errorMessage }, { status: statusCode });
  }
}

// DELETE user (hard delete, termasuk permissions terkait karena onDelete: Cascade)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Pastikan params.id di-await
  const { id } = await params;
  // Validasi ID user (asumsi UUID string)
  if (typeof id !== 'string' || id.length !== 36) { // Sesuaikan panjang jika format ID berbeda
    return NextResponse.json({ error: 'Invalid user ID format' }, { status: 400 });
  }

  console.log(`Attempting to delete user with ID: ${id}`);
  try {
    const existingUser = await prisma.user.findUnique({ where: { id } });
    if (!existingUser) {
      return NextResponse.json({ error: `User dengan ID ${id} tidak ditemukan.` }, { status: 404 });
    }

    // Validasi agar tidak menghapus admin terakhir
    if (existingUser.role === 'ADMIN') {
      const adminCount = await prisma.user.count({ where: { role: 'ADMIN' } });
      if (adminCount <= 1) {
        return NextResponse.json({ error: 'Tidak dapat menghapus admin terakhir' }, { status: 400 });
      }
    }

    // Ambil data user sebelum dihapus untuk logging
    const userToDelete = await prisma.user.findUnique({
      where: { id }
    });

    await prisma.user.delete({
      where: { id: id },
    });
    console.log(`Successfully deleted user with ID: ${id}`);

    // Ambil user ID dari token
    const adminUserId = getUserIdFromToken(request);

    // Log penghapusan user
    if (userToDelete) {
      await logUser(
        'delete',
        id,
        {
          name: userToDelete.name,
          email: userToDelete.email,
          role: userToDelete.role
        },
        undefined, // Tidak ada outletId spesifik
        adminUserId ? adminUserId : undefined // Gunakan userId dari token
      );
    }

    return NextResponse.json({ message: `User ${id} deleted successfully.` });
  } catch (error: unknown) {
    console.error(`Failed to delete user ${id}:`, error);
     let errorMessage = 'Gagal menghapus pengguna.';
     let statusCode = 500;
     if (error && typeof error === 'object' && 'code' in error) {
       const prismaError = error as { code: string; message: string };
       if (prismaError.code === 'P2025') {
          errorMessage = `User dengan ID ${id} tidak ditemukan.`;
          statusCode = 404;
       }
       if (prismaError.code === 'P2003' || prismaError.code === 'P2014') {
           errorMessage = 'Tidak dapat menghapus pengguna karena masih terhubung dengan data lain (misal: transaksi, booking).';
           statusCode = 409; // Conflict
       }
     }
     return NextResponse.json({ error: errorMessage }, { status: statusCode });
  }
}