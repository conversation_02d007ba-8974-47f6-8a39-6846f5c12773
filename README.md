# Break Time Dashboard 💆‍♀️

Dashboard untuk manajemen spa dan massage Break Time dengan fitur lengkap untuk booking, transaksi, dan manajemen pelanggan.

## 🆕 Fitur Terbaru - Edit Transaksi

### Edit Komisi dan <PERSON><PERSON><PERSON>
- **Lokasi**: Halaman Riwayat Transaksi (`/dashboard/riwayat`)
- **Tombol Edit**: Tersedia di setiap baris transaksi dengan ikon pensil (🖊️)
- **Fitur**:
  - <PERSON>bah komisi terapis
  - Atur biaya tambahan
  - Preview total baru secara real-time
  - Kalkulasi otomatis total transaksi
  - Permission guard untuk akses edit

### API Endpoint Baru
```
PUT /api/transactions/[id]
```
**Body:**
```json
{
  "commission": 50000,
  "additionalCharge": 10000
}
```

**Response:**
```json
{
  "message": "Transaksi berhasil diperbarui",
  "transaction": { ... }
}
```

### UI Improvements
- <PERSON><PERSON>m "Biaya Tambahan" ditambahkan di tabel riwayat
- Modal edit dengan format currency yang user-friendly
- Preview total baru sebelum menyimpan
- Responsive design untuk mobile dan desktop
- Validasi input untuk mencegah nilai negatif

### Permissions
Fitur edit memerlukan permission:
- Module: `transaksi`
- Action: `update`

## 🚀 Teknologi yang Digunakan

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL
- **Authentication**: Custom JWT-based auth
- **UI Components**: DaisyUI, Framer Motion

## 📱 Fitur Utama

- ✅ Manajemen Booking & Transaksi
- ✅ Sistem Pelanggan dengan Poin Loyalitas  
- ✅ Manajemen Terapis & Komisi
- ✅ Multiple Outlet Support
- ✅ Laporan & Analytics
- ✅ Progressive Web App (PWA)
- ✅ Role-based Access Control
- ✅ Real-time Dashboard
- 🆕 **Edit Transaksi (Komisi & Biaya Tambahan)**

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
