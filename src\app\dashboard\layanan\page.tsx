'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { useOutletContext } from '@/contexts/OutletContext';
import {
  FiPlus, FiSearch, FiEdit, FiTrash2, FiInbox, FiCheck, FiX, FiAlertTriangle,
  FiUsers, FiDollarSign, FiPercent, FiInfo, FiEyeOff, FiEye
} from 'react-icons/fi';
import { toast } from 'sonner';
import PermissionGuard from '@/components/permission/PermissionGuard';

// --- Interface Service (Diperbarui) ---
interface Service {
  id: string;
  name: string;
  description?: string; // Deskripsi opsional
  duration: number;     // Durasi dalam menit
  price: number;        // Harga dalam Rupiah
  commission: number;   // Komisi dalam Rupiah (BARU)
  isActive: boolean;    // Status aktif/nonaktif
  createdAt: string;    // Tanggal dibuat
  outlets?: {
    id: string;
    outlet: {
      id: string;
      name: string;
    }
  }[];
}

interface Therapist {
  id: string;
  name: string;
  outletId: string;
  outlet: {
    id: string;
    name: string;
  };
}

interface TherapistCommission {
  id: string;
  therapistId: string;
  serviceId: string;
  commission: number;
  therapist: Therapist;
}
// --- End Interface ---

// Varian Animasi
const fadeInUp = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.5 } } };
const staggerContainer = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1 } } };

export default function ServicePage() {
  const { selectedOutletId } = useOutletContext();
  const [services, setServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showInactiveOnly, setShowInactiveOnly] = useState(false); // State untuk menampilkan layanan nonaktif saja

  // State Modal Add/Edit
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedService, setSelectedService] = useState<Service | null>(null); // null = Add mode
  const [formData, setFormData] = useState<Omit<Service, 'id' | 'createdAt' | 'outlets'>>({
    name: '',
    description: '',
    duration: 60,
    price: 100000,
    commission: 0, // Default komisi (BARU)
    isActive: true,
    selectedOutletIds: [] as string[],
  });
  const [outlets, setOutlets] = useState<{id: string, name: string}[]>([]);

  // --> State untuk Modal Konfirmasi Hapus <--
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState<Service | null>(null);

  // --> State untuk Modal Komisi Terapis Khusus <--
  const [isTherapistCommissionModalOpen, setIsTherapistCommissionModalOpen] = useState(false);
  const [selectedServiceForCommission, setSelectedServiceForCommission] = useState<Service | null>(null);
  const [therapistCommissions, setTherapistCommissions] = useState<TherapistCommission[]>([]);
  const [availableTherapists, setAvailableTherapists] = useState<Therapist[]>([]);
  const [selectedTherapistId, setSelectedTherapistId] = useState<string>('');
  const [therapistCommissionValue, setTherapistCommissionValue] = useState<string>('0');
  const [defaultCommission, setDefaultCommission] = useState<number>(0);
  const [isLoadingCommissions, setIsLoadingCommissions] = useState(false);
  const [therapistSearchTerm, setTherapistSearchTerm] = useState('');

  // --> State untuk Modal Konfirmasi Hapus Komisi <--
  const [isDeleteCommissionModalOpen, setIsDeleteCommissionModalOpen] = useState(false);
  const [commissionToDelete, setCommissionToDelete] = useState<string>('');

  // Load Service Data
  useEffect(() => {
    const fetchServices = async () => {
      setIsLoading(true);
      setError(null);
      try {
        let apiUrl = '/api/services';
        const params = new URLSearchParams();

        // Tambahkan filter outletId jika ada
        if (selectedOutletId) {
          params.append('outletId', selectedOutletId);
        }
        
        // Tambahkan parameter includeInactive jika showInactiveOnly true
        if (showInactiveOnly) {
          params.append('includeInactive', 'true');
        }
        
        // Tambahkan parameter ke URL jika ada
        if (params.toString()) {
          apiUrl += `?${params.toString()}`;
        }

        const response = await fetch(apiUrl);

        // Jika status 404, anggap sebagai array kosong
        if (response.status === 404) {
          return setServices([]);
        }

        // Untuk error lainnya
        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        setServices(data.services || []);
      } catch (err) {
        console.error("Gagal memuat data layanan:", err);
        setError("Gagal memuat data layanan. Silakan coba lagi nanti.");
        setServices([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchServices();
  }, [selectedOutletId, showInactiveOnly]);

  // Load Outlets untuk form
  useEffect(() => {
    const fetchOutlets = async () => {
      try {
        const response = await fetch('/api/outlets');

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        setOutlets(data.outlets || []);

        // Set default selectedOutletIds jika ada selectedOutletId
        if (selectedOutletId && formData.selectedOutletIds.length === 0) {
          setFormData(prev => ({
            ...prev,
            selectedOutletIds: [selectedOutletId.toString()]
          }));
        }
      } catch (err) {
        console.error("Gagal memuat data outlet:", err);
      }
    };

    fetchOutlets();
  }, [selectedOutletId, formData.selectedOutletIds]);

  // Filter services berdasarkan searchTerm dan status aktif/nonaktif
  const filteredServices = useMemo(() => {
    let result = services;
    
    // Filter berdasarkan status aktif/nonaktif
    if (showInactiveOnly) {
      result = result.filter(s => !s.isActive);
    }
    
    // Filter berdasarkan kata kunci pencarian
    if (searchTerm) {
      const lowerSearch = searchTerm.toLowerCase();
      result = result.filter(s =>
        s.name.toLowerCase().includes(lowerSearch) ||
        s.description?.toLowerCase().includes(lowerSearch)
      );
    }
    
    return result;
  }, [services, searchTerm, showInactiveOnly]);

  // --- Handlers Modal & Form ---
  const handleOpenModal = (service: Service | null = null) => {
    setSelectedService(service);
    if (service) {
      // Edit mode: isi form dengan data service terpilih
      const serviceOutletIds = service.outlets?.map(o => o.outlet.id) || [];
      setFormData({
        name: service.name,
        description: service.description || '',
        duration: service.duration,
        price: service.price,
        commission: service.commission || 0, // Ambil komisi atau default 0 (BARU)
        isActive: service.isActive,
        selectedOutletIds: serviceOutletIds,
      });
    } else {
      // Add mode: reset form ke default
      setFormData({
        name: '',
        description: '',
        duration: 60,
        price: 100000,
        commission: 0, // Reset komisi ke 0 (BARU)
        isActive: true,
        selectedOutletIds: selectedOutletId ? [selectedOutletId.toString()] : [],
      });
    }
    setIsModalOpen(true);

    // Berikan waktu untuk modal muncul sebelum memformat input
    setTimeout(() => {
      // Format input harga dan komisi setelah modal muncul
      const priceInput = document.querySelector('input[name="price"]') as HTMLInputElement;
      const commissionInput = document.querySelector('input[name="commission"]') as HTMLInputElement;

      if (priceInput) {
        priceInput.value = formatRupiah(service?.price || 100000);
      }

      if (commissionInput) {
        commissionInput.value = formatRupiah(service?.commission || 0);
      }
    }, 100);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedService(null);
  };

  // Format angka dengan titik sebagai pemisah ribuan
  const formatRupiah = (angka: number): string => {
    return angka.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
  };

  // Parse string dengan format Rupiah menjadi number
  const parseRupiah = (rupiahString: string): number => {
    // Hapus semua titik dan karakter non-numerik kecuali tanda minus
    return parseFloat(rupiahString.replace(/\./g, '').replace(/[^0-9-]/g, '')) || 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      // Handle checkbox untuk isActive toggle
      if (name === 'isActive') {
        const { checked } = e.target as HTMLInputElement;
        setFormData(prev => ({ ...prev, [name]: checked }));
      }
      // Handle checkbox untuk outlet selection
      else if (name.startsWith('outlet-')) {
        const outletId = name.replace('outlet-', '');
        const { checked } = e.target as HTMLInputElement;

        setFormData(prev => {
          const currentOutlets = [...prev.selectedOutletIds];

          if (checked && !currentOutlets.includes(outletId)) {
            // Tambahkan outlet jika dicentang dan belum ada
            return { ...prev, selectedOutletIds: [...currentOutlets, outletId] };
          } else if (!checked && currentOutlets.includes(outletId)) {
            // Hapus outlet jika tidak dicentang dan sudah ada
            return { ...prev, selectedOutletIds: currentOutlets.filter(id => id !== outletId) };
          }

          return prev;
        });
      }
    } else if (name === 'price') {
      // Khusus untuk input harga, format dengan titik otomatis
      const numericValue = parseRupiah(value);
      setFormData(prev => ({ ...prev, [name]: numericValue }));

      // Update nilai input dengan format Rupiah (hanya untuk tampilan)
      const input = e.target as HTMLInputElement;
      const cursorPosition = input.selectionStart || 0;
      const previousLength = input.value.length;

      // Tunda pemformatan agar tidak mengganggu pengetikan
      setTimeout(() => {
        input.value = formatRupiah(numericValue);

        // Hitung posisi kursor baru
        const newCursorPosition = cursorPosition + (input.value.length - previousLength);
        input.setSelectionRange(newCursorPosition, newCursorPosition);
      }, 10);
    } else if (name === 'commission') {
      // Untuk komisi, format dengan titik otomatis seperti harga
      const numericValue = parseRupiah(value);
      setFormData(prev => ({ ...prev, [name]: numericValue }));

      // Update nilai input dengan format Rupiah (hanya untuk tampilan)
      const input = e.target as HTMLInputElement;
      const cursorPosition = input.selectionStart || 0;
      const previousLength = input.value.length;

      // Tunda pemformatan agar tidak mengganggu pengetikan
      setTimeout(() => {
        input.value = formatRupiah(numericValue);

        // Hitung posisi kursor baru
        const newCursorPosition = cursorPosition + (input.value.length - previousLength);
        input.setSelectionRange(newCursorPosition, newCursorPosition);
      }, 10);
    } else if (name === 'duration') {
      // Untuk durasi, tetap gunakan parseFloat
      const numericValue = parseFloat(value) || 0;
      setFormData(prev => ({ ...prev, [name]: numericValue }));
    } else {
      // Handle text, textarea, select
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSaveService = async () => {
    // Validasi sederhana
    if (!formData.name || formData.duration <= 0 || formData.price < 0 || formData.commission < 0 || formData.selectedOutletIds.length === 0) { // Izinkan harga 0
      toast.error('Nama Layanan, Durasi (>0), Harga (>=0), Komisi (>=0), dan minimal satu Outlet wajib diisi.');
      return;
    }

    setIsLoading(true);

    try {
      // Data untuk API
      const serviceData = {
        name: formData.name,
        description: formData.description || null,
        duration: parseInt(formData.duration.toString()) || 0,
        price: parseFloat(formData.price.toString()) || 0,
        commission: parseFloat(formData.commission.toString()) || 0,
        outletIds: formData.selectedOutletIds,
        isActive: formData.isActive,
      };

      let response;

      if (selectedService) {
        // ----- EDIT MODE -----
        response = await fetch(`/api/services/${selectedService.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(serviceData),
        });
      } else {
        // ----- ADD MODE -----
        response = await fetch('/api/services', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(serviceData),
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal menyimpan data');
      }

      // Refresh data layanan
      let apiUrl = '/api/services';
      if (selectedOutletId) {
        apiUrl += `?outletId=${selectedOutletId}`;
      }
      const updatedResponse = await fetch(apiUrl);
      const updatedData = await updatedResponse.json();
      setServices(updatedData.services || []);

      toast.success(selectedService
        ? `Layanan "${formData.name}" berhasil diperbarui.`
        : `Layanan "${formData.name}" berhasil ditambahkan.`);

      handleCloseModal();
    } catch (err: unknown) {
      console.error('Error saving service:', err);
      toast.error(`Gagal menyimpan layanan: ${err instanceof Error ? err.message : 'Terjadi kesalahan'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // --- Handlers untuk Modal Hapus ---
  const handleOpenDeleteModal = (service: Service) => {
    setServiceToDelete(service);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setServiceToDelete(null);
    setIsDeleteModalOpen(false);
  };

  const confirmDeleteService = async () => {
    if (!serviceToDelete) return;

    setIsLoading(true);
    const serviceId = serviceToDelete.id;
    const serviceName = serviceToDelete.name;
    handleCloseDeleteModal(); // Tutup modal segera

    try {
      const response = await fetch(`/api/services/${serviceId}`, {
        method: 'DELETE',
      });

      // Untuk menangani respons error jika service tidak dapat dihapus dan harus dinonaktifkan
      if (response.status === 409) {
        const errorData = await response.json();
        
        // Jika ada saran untuk menonaktifkan layanan
        if (errorData.suggestion === 'deactivate') {
          toast.error('Layanan tidak dapat dihapus karena sudah digunakan dalam transaksi. Layanan akan dinonaktifkan.');
          
          // Lakukan request untuk menonaktifkan layanan
          const deactivateResponse = await fetch(`/api/services/${serviceId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ isActive: false }),
          });
          
          if (!deactivateResponse.ok) {
            throw new Error('Gagal menonaktifkan layanan');
          }
          
          toast.success(`Layanan "${serviceName}" berhasil dinonaktifkan.`);
        } else {
          toast.error(errorData.error || 'Gagal menghapus layanan');
        }
      } 
      else if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal menghapus layanan');
      }
      else {
        toast.success(`Layanan "${serviceName}" berhasil dihapus.`);
      }

      // Refresh data layanan
      let apiUrl = '/api/services';
      const params = new URLSearchParams();

      // Tambahkan filter outletId jika ada
      if (selectedOutletId) {
        params.append('outletId', selectedOutletId);
      }
      
      // Tambahkan parameter includeInactive jika showInactiveOnly true
      if (showInactiveOnly) {
        params.append('includeInactive', 'true');
      }
      
      // Tambahkan parameter ke URL jika ada
      if (params.toString()) {
        apiUrl += `?${params.toString()}`;
      }

      const updatedResponse = await fetch(apiUrl);
      const updatedData = await updatedResponse.json();
      setServices(updatedData.services || []);
    } catch (err: unknown) {
      console.error('Error deleting service:', err);
      toast.error(`Gagal menghapus layanan: ${err instanceof Error ? err.message : 'Terjadi kesalahan'}`);
    } finally {
      setIsLoading(false);
    }
  };
  // --- Akhir Handlers Modal Hapus ---

  // Modifikasi handleDeleteService: Hanya buka modal
  const handleDeleteService = (service: Service) => {
    handleOpenDeleteModal(service);
  };

  // --- Handlers untuk Modal Komisi Terapis Khusus ---
  const handleOpenTherapistCommissionModal = async (service: Service) => {
    setSelectedServiceForCommission(service);
    setIsTherapistCommissionModalOpen(true);
    setIsLoadingCommissions(true);
    setTherapistSearchTerm('');

    try {
      const response = await fetch(`/api/services/${service.id}/therapist-commissions`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setTherapistCommissions(data.therapistCommissions || []);
      setAvailableTherapists(data.availableTherapists || []);
      setDefaultCommission(data.defaultCommission || 0);
      setSelectedTherapistId('');
      setTherapistCommissionValue(formatRupiah(data.defaultCommission || 0));
    } catch (error) {
      console.error('Error fetching therapist commissions:', error);
      toast.error('Gagal memuat data komisi terapis');
    } finally {
      setIsLoadingCommissions(false);
    }
  };

  const handleCloseTherapistCommissionModal = () => {
    setIsTherapistCommissionModalOpen(false);
    setSelectedServiceForCommission(null);
    setTherapistCommissions([]);
    setAvailableTherapists([]);
    setSelectedTherapistId('');
    setTherapistCommissionValue('0');
  };

  const handleAddTherapistCommission = async () => {
    if (!selectedServiceForCommission || !selectedTherapistId) {
      toast.error('Pilih terapis terlebih dahulu');
      return;
    }

    const commission = parseRupiah(therapistCommissionValue);
    if (commission < 0) {
      toast.error('Komisi tidak boleh negatif');
      return;
    }

    setIsLoadingCommissions(true);

    try {
      const response = await fetch(`/api/services/${selectedServiceForCommission.id}/therapist-commissions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          therapistId: selectedTherapistId,
          commission
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal menambahkan komisi terapis');
      }

      const data = await response.json();
      toast.success('Komisi terapis khusus berhasil ditambahkan');

      // Refresh data
      const refreshResponse = await fetch(`/api/services/${selectedServiceForCommission.id}/therapist-commissions`);
      const refreshData = await refreshResponse.json();
      setTherapistCommissions(refreshData.therapistCommissions || []);
      setAvailableTherapists(refreshData.availableTherapists || []);
      setSelectedTherapistId('');
      setTherapistSearchTerm('');
      setTherapistCommissionValue(formatRupiah(defaultCommission));
    } catch (error) {
      console.error('Error adding therapist commission:', error);
      toast.error(`Gagal menambahkan komisi terapis: ${error instanceof Error ? error.message : 'Terjadi kesalahan'}`);
    } finally {
      setIsLoadingCommissions(false);
    }
  };

  const handleUpdateTherapistCommission = async (commissionId: string) => {
    if (!selectedServiceForCommission) return;

    const commission = parseRupiah(therapistCommissionValue);
    if (commission < 0) {
      toast.error('Komisi tidak boleh negatif');
      return;
    }

    setIsLoadingCommissions(true);

    try {
      const response = await fetch(`/api/services/${selectedServiceForCommission.id}/therapist-commissions/${commissionId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ commission })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal mengupdate komisi terapis');
      }

      const data = await response.json();
      toast.success('Komisi terapis khusus berhasil diupdate');

      // Refresh data
      const refreshResponse = await fetch(`/api/services/${selectedServiceForCommission.id}/therapist-commissions`);
      const refreshData = await refreshResponse.json();
      setTherapistCommissions(refreshData.therapistCommissions || []);
      setAvailableTherapists(refreshData.availableTherapists || []);
    } catch (error) {
      console.error('Error updating therapist commission:', error);
      toast.error(`Gagal mengupdate komisi terapis: ${error instanceof Error ? error.message : 'Terjadi kesalahan'}`);
    } finally {
      setIsLoadingCommissions(false);
    }
  };

  // Handlers untuk Modal Konfirmasi Hapus Komisi
  const handleOpenDeleteCommissionModal = (commissionId: string) => {
    setCommissionToDelete(commissionId);
    setIsDeleteCommissionModalOpen(true);
  };

  const handleCloseDeleteCommissionModal = () => {
    setIsDeleteCommissionModalOpen(false);
    setCommissionToDelete('');
  };

  const handleDeleteTherapistCommission = async () => {
    if (!selectedServiceForCommission || !commissionToDelete) return;

    setIsLoadingCommissions(true);

    try {
      const response = await fetch(`/api/services/${selectedServiceForCommission.id}/therapist-commissions/${commissionToDelete}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal menghapus komisi terapis');
      }

      toast.success('Komisi terapis khusus berhasil dihapus');

      // Refresh data
      const refreshResponse = await fetch(`/api/services/${selectedServiceForCommission.id}/therapist-commissions`);
      const refreshData = await refreshResponse.json();
      setTherapistCommissions(refreshData.therapistCommissions || []);
      setAvailableTherapists(refreshData.availableTherapists || []);

      // Tutup modal konfirmasi
      handleCloseDeleteCommissionModal();
    } catch (error) {
      console.error('Error deleting therapist commission:', error);
      toast.error(`Gagal menghapus komisi terapis: ${error instanceof Error ? error.message : 'Terjadi kesalahan'}`);
    } finally {
      setIsLoadingCommissions(false);
    }
  };

  // Filter terapis berdasarkan kata kunci pencarian
  const filteredTherapists = useMemo(() => {
    if (!therapistSearchTerm.trim()) return availableTherapists;
    const lowerSearch = therapistSearchTerm.toLowerCase();
    return availableTherapists.filter(therapist =>
      therapist.name.toLowerCase().includes(lowerSearch) ||
      therapist.outlet.name.toLowerCase().includes(lowerSearch)
    );
  }, [availableTherapists, therapistSearchTerm]);
  // --- Akhir Handlers Modal Komisi Terapis Khusus ---

  const handleToggleActiveStatus = async (service: Service) => {
    try {
      setIsLoading(true);

      const response = await fetch(`/api/services/${service.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive: !service.isActive }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal mengubah status');
      }

      // Refresh data layanan
      let apiUrl = '/api/services';
      if (selectedOutletId) {
        apiUrl += `?outletId=${selectedOutletId}`;
      }
      const updatedResponse = await fetch(apiUrl);
      const updatedData = await updatedResponse.json();
      setServices(updatedData.services || []);

      toast.success(`Layanan "${service.name}" berhasil ${!service.isActive ? 'diaktifkan' : 'dinonaktifkan'}.`);
    } catch (err: unknown) {
      console.error('Error toggling service status:', err);
      toast.error(`Gagal mengubah status: ${err instanceof Error ? err.message : 'Terjadi kesalahan'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // --- End Handlers ---

  if (isLoading && services.length === 0) {
    return <div className="flex justify-center items-center h-screen"><span className="loading loading-spinner loading-lg text-primary"></span></div>;
  }

  return (
    <motion.div
      className="container mx-auto p-4 md:p-6"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {/* Header & Tools */}
      <motion.div variants={fadeInUp} className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Manajemen Layanan</h1>
          <p className="text-gray-600">Kelola layanan yang tersedia di outlet Anda</p>
        </div>
        <PermissionGuard module="services" action="create">
          <button
            onClick={() => handleOpenModal()}
            className="btn btn-primary mt-4 md:mt-0 gap-2 w-full sm:w-auto"
          >
            <FiPlus /> <span className="inline-block">Tambah Layanan Baru</span>
          </button>
        </PermissionGuard>
      </motion.div>

      {/* Menampilkan error jika ada */}
      {error && (
        <motion.div variants={fadeInUp} className="alert alert-error mb-6">
          <FiAlertTriangle className="w-6 h-6" />
          <span>{error}</span>
        </motion.div>
      )}

      {/* Search & Filter */}
      <motion.div variants={fadeInUp} className="relative mb-6">
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex items-center w-full border rounded-lg overflow-hidden focus-within:ring-2 focus-within:ring-primary">
            <FiSearch className="mx-3 text-gray-500" />
            <input
              type="text"
              placeholder="Cari layanan..."
              className="input input-ghost w-full focus:outline-none"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <button
              className="px-3 text-gray-400 hover:text-gray-600"
              onClick={() => setSearchTerm('')}
              title="Hapus kata kunci pencarian"
            >
              <FiX />
            </button>
          </div>
          
          <button 
            className={`btn ${showInactiveOnly ? 'btn-primary' : 'btn-outline'} gap-2`}
            onClick={() => setShowInactiveOnly(!showInactiveOnly)}
            title={showInactiveOnly ? "Tampilkan semua layanan" : "Tampilkan hanya layanan nonaktif"}
          >
            {showInactiveOnly ? <FiEye className="h-4 w-4" /> : <FiEyeOff className="h-4 w-4" />}
            <span className="hidden sm:inline">
              {showInactiveOnly ? "Tampilkan Semua" : "Layanan Nonaktif"}
            </span>
          </button>
        </div>
        
        {/* Badge menampilkan status filter */}
        {showInactiveOnly && (
          <div className="mt-2">
            <span className="badge badge-info gap-1">
              <FiEyeOff size={12} /> Menampilkan layanan nonaktif
              <button className="ml-1" onClick={() => setShowInactiveOnly(false)} title="Hapus filter layanan nonaktif">
                <FiX size={12} />
              </button>
            </span>
          </div>
        )}
      </motion.div>

      {/* Service List */}
      {filteredServices.length > 0 ? (
        <motion.div variants={fadeInUp}>
          <div className="bg-white rounded-lg shadow">
            {/* Tabel untuk layar medium dan di atasnya */}
            <div className="hidden md:block overflow-x-auto">
              <table className="table w-full">
                <thead>
                  <tr>
                    <th className="p-3 text-left">Nama Layanan</th>
                    <th className="p-3 text-left">Outlet</th>
                    <th className="p-3 text-left">Durasi (Menit)</th>
                    <th className="p-3 text-right">Harga</th>
                    <th className="p-3 text-right">Komisi</th>
                    <th className="p-3 text-center">Status</th>
                    <th className="p-3 text-center">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredServices.map((service) => (
                    <tr key={service.id} className={`${!service.isActive ? 'bg-gray-50 border-l-4 border-l-error opacity-70' : ''}`}>
                      <td>
                        <div className="font-medium">{service.name}</div>
                        <div className="text-sm text-gray-500 truncate max-w-[200px]">{service.description || '-'}</div>
                        {!service.isActive && (
                          <span className="badge badge-error badge-sm mt-1">Nonaktif</span>
                        )}
                      </td>
                      <td>
                        {service.outlets && service.outlets.length > 0
                          ? service.outlets.map(o => o.outlet.name).join(', ')
                          : 'N/A'}
                      </td>
                      <td>{service.duration} menit</td>
                      <td className="p-3 text-right">{new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(service.price)}</td>
                      <td className="p-3 text-right">{new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(service.commission || 0)}</td>
                      <td>
                        <span className={`badge ${service.isActive ? 'badge-success' : 'badge-error'}`}>
                          {service.isActive ? 'Aktif' : 'Nonaktif'}
                        </span>
                      </td>
                      <td>
                        <div className="flex gap-2 justify-center">
                          <PermissionGuard module="services" action="update">
                            <button
                              onClick={() => handleOpenModal(service)}
                              className="btn btn-sm btn-ghost text-blue-600"
                              title="Edit"
                            >
                              <FiEdit />
                            </button>
                          </PermissionGuard>
                          <PermissionGuard module="services" action="update">
                            <button
                              onClick={() => handleOpenTherapistCommissionModal(service)}
                              className="btn btn-sm btn-ghost text-purple-600"
                              title="Komisi Terapis Khusus"
                            >
                              <FiUsers />
                            </button>
                          </PermissionGuard>
                          <PermissionGuard module="services" action="update">
                            <button
                              onClick={() => handleToggleActiveStatus(service)}
                              className={`btn btn-sm btn-ghost ${service.isActive ? 'text-orange-600' : 'text-green-600'}`}
                              title={service.isActive ? 'Nonaktifkan' : 'Aktifkan'}
                            >
                              {service.isActive ? <FiX /> : <FiCheck />}
                            </button>
                          </PermissionGuard>
                          <PermissionGuard module="services" action="delete">
                            <button
                              onClick={() => handleDeleteService(service)}
                              className="btn btn-sm btn-ghost text-red-600"
                              title="Hapus"
                            >
                              <FiTrash2 />
                            </button>
                          </PermissionGuard>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Tampilan kartu untuk layar kecil */}
            <div className="md:hidden">
              <div className="grid grid-cols-1 gap-4 p-4">
                {filteredServices.map((service) => {
                  // Format harga dan komisi
                  const formattedPrice = new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(service.price);
                  const formattedCommission = new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(service.commission || 0);

                  // Format outlet
                  const outletNames = service.outlets && service.outlets.length > 0
                    ? service.outlets.map(o => o.outlet.name).join(', ')
                    : 'N/A';

                  return (
                    <div key={service.id} className={`card bg-base-100 shadow-sm border border-gray-200 ${!service.isActive ? 'opacity-70 border-l-4 border-l-error' : ''}`}>
                      <div className="card-body p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="card-title text-base">{service.name}</h3>
                          <span className={`badge ${service.isActive ? 'badge-success' : 'badge-error'}`}>
                            {service.isActive ? 'Aktif' : 'Nonaktif'}
                          </span>
                        </div>

                        {service.description && (
                          <p className="text-sm text-gray-600 mb-2">{service.description}</p>
                        )}

                        <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-sm mb-3">
                          <div className="text-gray-500">Outlet:</div>
                          <div className="text-gray-700 truncate">{outletNames}</div>

                          <div className="text-gray-500">Durasi:</div>
                          <div className="text-gray-700">{service.duration} menit</div>

                          <div className="text-gray-500">Harga:</div>
                          <div className="text-gray-700 font-medium">{formattedPrice}</div>

                          <div className="text-gray-500">Komisi:</div>
                          <div className="text-gray-700">{formattedCommission}</div>
                        </div>

                        <div className="card-actions justify-end border-t border-gray-100 pt-3">
                          <PermissionGuard module="services" action="update">
                            <button
                              onClick={() => handleOpenModal(service)}
                              className="btn btn-sm btn-ghost text-blue-600"
                              title="Edit"
                            >
                              <FiEdit className="mr-1" /> Edit
                            </button>
                          </PermissionGuard>
                          <PermissionGuard module="services" action="update">
                            <button
                              onClick={() => handleOpenTherapistCommissionModal(service)}
                              className="btn btn-sm btn-ghost text-purple-600"
                              title="Komisi Terapis Khusus"
                            >
                              <FiUsers className="mr-1" /> Komisi
                            </button>
                          </PermissionGuard>
                          <PermissionGuard module="services" action="update">
                            <button
                              onClick={() => handleToggleActiveStatus(service)}
                              className={`btn btn-sm btn-ghost ${service.isActive ? 'text-orange-600' : 'text-green-600'}`}
                              title={service.isActive ? 'Nonaktifkan' : 'Aktifkan'}
                            >
                              {service.isActive ? <FiX className="mr-1" /> : <FiCheck className="mr-1" />}
                              {service.isActive ? 'Nonaktifkan' : 'Aktifkan'}
                            </button>
                          </PermissionGuard>
                          <PermissionGuard module="services" action="delete">
                            <button
                              onClick={() => handleDeleteService(service)}
                              className="btn btn-sm btn-ghost text-red-600"
                              title="Hapus"
                            >
                              <FiTrash2 className="mr-1" /> Hapus
                            </button>
                          </PermissionGuard>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </motion.div>
      ) : (
        <motion.div variants={fadeInUp} className="text-center py-8 px-4 bg-white rounded-lg shadow">
          <FiInbox className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-lg font-medium text-gray-900">Tidak ada layanan ditemukan</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm 
              ? 'Coba cari dengan kata kunci lain' 
              : showInactiveOnly 
                ? 'Tidak ada layanan yang dinonaktifkan saat ini'
                : 'Mulai dengan menambahkan layanan baru'
            }
          </p>
          {!searchTerm && !showInactiveOnly && (
            <div className="mt-6">
              <PermissionGuard module="services" action="create">
                <button
                  onClick={() => handleOpenModal()}
                  className="btn btn-primary gap-2"
                >
                  <FiPlus /> <span className="inline-block">Tambah Layanan</span>
                </button>
              </PermissionGuard>
            </div>
          )}
          {showInactiveOnly && (
            <div className="mt-6">
              <button
                onClick={() => setShowInactiveOnly(false)}
                className="btn btn-outline gap-2"
              >
                <FiEye /> <span className="inline-block">Tampilkan Semua Layanan</span>
              </button>
            </div>
          )}
        </motion.div>
      )}

      {/* Add/Edit Service Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <h3 className="text-lg font-medium leading-6 text-gray-900 mb-4">
                  {selectedService ? 'Edit Layanan' : 'Tambah Layanan Baru'}
                </h3>
                <div className="space-y-4">
                  {/* Nama Layanan */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Nama Layanan <span className="text-red-500">*</span></label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="mt-1 input input-bordered w-full"
                      required
                      placeholder="Masukkan nama layanan"
                      title="Nama Layanan"
                    />
                  </div>

                  {/* Outlet Checkboxes */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Outlet <span className="text-red-500">*</span></label>
                    <div className="mt-2 grid grid-cols-2 gap-2">
                      {outlets.map(outlet => (
                        <div key={outlet.id} className="form-control">
                          <label className="label cursor-pointer justify-start gap-2">
                            <input
                              type="checkbox"
                              name={`outlet-${outlet.id}`}
                              checked={formData.selectedOutletIds.includes(outlet.id)}
                              onChange={handleInputChange}
                              className="checkbox checkbox-primary checkbox-sm"
                            />
                            <span className="label-text">{outlet.name}</span>
                          </label>
                        </div>
                      ))}
                    </div>
                    {formData.selectedOutletIds.length === 0 && (
                      <p className="text-xs text-error mt-1">Pilih minimal satu outlet</p>
                    )}
                  </div>

                  {/* Deskripsi */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Deskripsi</label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      className="mt-1 textarea textarea-bordered w-full"
                      rows={3}
                      placeholder="Deskripsi layanan (opsional)"
                      title="Deskripsi Layanan"
                    />
                  </div>

                  {/* Durasi */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Durasi (menit) <span className="text-red-500">*</span></label>
                    <input
                      type="number"
                      name="duration"
                      value={formData.duration}
                      onChange={handleInputChange}
                      className="mt-1 input input-bordered w-full"
                      min="1"
                      required
                      placeholder="Contoh: 60"
                      title="Durasi layanan dalam menit"
                    />
                  </div>

                  {/* Harga */}
                  <div>
                    <label htmlFor="price" className="block text-sm font-medium text-gray-700">Harga (Rp) <span className="text-red-500">*</span></label>
                    <input
                      id="price"
                      type="text"
                      name="price"
                      defaultValue={formatRupiah(formData.price)}
                      onChange={handleInputChange}
                      className="mt-1 input input-bordered w-full"
                      required
                      placeholder="Contoh: 100.000 atau 0"
                      aria-label="Harga Layanan"
                      min="0"
                    />
                    <p className="text-xs text-gray-500 mt-1">Harga dapat diatur menjadi 0 (gratis)</p>
                  </div>

                  {/* Komisi */}
                  <div className="form-control">
                    <label htmlFor="commission" className="label">
                      <span className="label-text">Komisi (Rp)</span>
                    </label>
                    <input
                      id="commission"
                      type="text"
                      name="commission"
                      defaultValue={formatRupiah(formData.commission)}
                      onChange={handleInputChange}
                      className="input input-bordered w-full"
                      placeholder="Contoh: 25.000"
                      aria-label="Komisi Layanan"
                      min="0"
                    />
                  </div>

                  {/* Status */}
                  <div className="flex items-center">
                    <input
                      id="isActive"
                      type="checkbox"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleInputChange}
                      className="checkbox checkbox-primary"
                      aria-label="Status Layanan Aktif"
                    />
                    <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">Layanan Aktif</label>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="btn btn-primary sm:ml-3"
                  onClick={handleSaveService}
                  disabled={isLoading}
                >
                  {isLoading && <span className="loading loading-spinner loading-xs"></span>}
                  {selectedService ? 'Perbarui' : 'Simpan'}
                </button>
                <button
                  type="button"
                  className="btn btn-ghost mt-3 sm:mt-0"
                  onClick={handleCloseModal}
                  disabled={isLoading}
                >
                  Batal
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* --> Modal Konfirmasi Hapus (BARU) <-- */}
      <dialog id="delete_service_modal" className={`modal ${isDeleteModalOpen ? 'modal-open' : ''}`}>
        <div className="modal-box max-w-sm border border-base-300">
          <button onClick={handleCloseDeleteModal} className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2 z-10" title="Tutup">✕</button>
          <h3 className="font-bold text-lg text-error">Konfirmasi Hapus Layanan</h3>
          <p className="py-4 text-base-content/80">
            Anda yakin ingin menghapus layanan &quot;<span className="font-semibold">{serviceToDelete?.name}</span>&quot;?
            <br/> Tindakan ini tidak dapat dibatalkan.
          </p>
          
          {/* Pesan tambahan untuk layanan nonaktif */}
          {serviceToDelete && !serviceToDelete.isActive && (
            <div className="bg-blue-50 p-3 rounded-lg mb-3 text-sm border border-blue-100">
              <div className="flex gap-2">
                <FiInfo className="text-blue-500 mt-0.5" />
                <div>
                  <p className="text-blue-700">
                    Layanan ini sedang dalam status <span className="font-semibold">nonaktif</span>.
                  </p>
                  <p className="text-blue-600 mt-1">
                    Menghapus layanan yang pernah digunakan dalam transaksi/booking tidak akan menghapus 
                    riwayat penggunaannya di data historis.
                  </p>
                  <p className="text-blue-700 mt-1">
                    Layanan nonaktif dapat langsung dihapus selama tidak ada booking aktif yang menggunakannya.
                  </p>
                </div>
              </div>
            </div>
          )}
          
          <div className="modal-action">
            <button className="btn btn-sm btn-ghost" onClick={handleCloseDeleteModal} disabled={isLoading} title="Batal">
              Batal
            </button>
            <button 
              className={`btn btn-sm ${serviceToDelete && !serviceToDelete.isActive ? 'btn-error' : 'btn-error'}`} 
              onClick={confirmDeleteService} 
              disabled={isLoading}
              title="Konfirmasi hapus"
            >
              {isLoading && <span className="loading loading-spinner loading-xs"></span>}
              Ya, Hapus
            </button>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop"> <button onClick={handleCloseDeleteModal}>close</button> </form>
      </dialog>

      {/* --> Modal Komisi Terapis Khusus <-- */}
      <dialog id="therapist_commission_modal" className={`modal ${isTherapistCommissionModalOpen ? 'modal-open' : ''}`}>
        <div className="modal-box max-w-3xl border border-base-300">
          <button onClick={handleCloseTherapistCommissionModal} className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2 z-10">✕</button>
          <h3 className="font-bold text-lg flex items-center gap-2">
            <FiUsers className="text-purple-600" />
            Komisi Terapis Khusus: <span className="text-purple-600">{selectedServiceForCommission?.name}</span>
          </h3>
          <div className="divider my-2"></div>

          {isLoadingCommissions ? (
            <div className="flex justify-center items-center py-8">
              <span className="loading loading-spinner loading-md text-primary"></span>
            </div>
          ) : (
            <div className="py-2">
              {/* Informasi Komisi Default */}
              <div className="bg-base-200 p-3 rounded-lg mb-4">
                <div className="flex items-center gap-2 text-sm">
                  <FiInfo className="text-info" />
                  <span>Komisi default untuk layanan ini adalah <span className="font-semibold">{new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(defaultCommission)}</span></span>
                </div>
              </div>

              {/* Daftar Komisi Terapis Khusus */}
              <div className="mb-6">
                <h4 className="font-medium mb-2 text-gray-700">Daftar Komisi Terapis Khusus</h4>
                {therapistCommissions.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="table table-sm w-full">
                      <thead>
                        <tr>
                          <th>Terapis</th>
                          <th>Outlet</th>
                          <th className="text-right">Komisi</th>
                          <th className="text-center">Aksi</th>
                        </tr>
                      </thead>
                      <tbody>
                        {therapistCommissions.map(commission => (
                          <tr key={commission.id}>
                            <td>{commission.therapist.name}</td>
                            <td>{commission.therapist.outlet.name}</td>
                            <td className="text-right font-medium">
                              {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(commission.commission)}
                            </td>
                            <td>
                              <div className="flex justify-center gap-1">
                                <button
                                  className="btn btn-xs btn-ghost text-blue-600"
                                  onClick={() => {
                                    // Set nilai komisi untuk diedit
                                    setTherapistCommissionValue(formatRupiah(commission.commission));
                                    // Fokus ke input
                                    setTimeout(() => {
                                      const input = document.getElementById('edit-commission-input') as HTMLInputElement;
                                      if (input) {
                                        input.focus();
                                      }
                                    }, 100);
                                  }}
                                >
                                  <FiEdit size={14} />
                                </button>
                                <button
                                  className="btn btn-xs btn-ghost text-green-600"
                                  onClick={() => handleUpdateTherapistCommission(commission.id)}
                                  title="Simpan perubahan"
                                >
                                  <FiCheck size={14} />
                                </button>
                                <button
                                  className="btn btn-xs btn-ghost text-red-600"
                                  onClick={() => handleOpenDeleteCommissionModal(commission.id)}
                                >
                                  <FiTrash2 size={14} />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-4 bg-base-100 border border-base-200 rounded-lg">
                    <p className="text-gray-500">Belum ada komisi terapis khusus</p>
                  </div>
                )}
              </div>

              {/* Form Tambah Komisi Terapis Khusus */}
              <div className="bg-base-100 p-4 border border-base-200 rounded-lg">
                <h4 className="font-medium mb-3 text-gray-700">Tambah Komisi Terapis Khusus</h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  {/* Pilih Terapis */}
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Pilih Terapis</span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Cari terapis..."
                        className="input input-bordered w-full pr-10"
                        value={therapistSearchTerm}
                        onChange={(e) => setTherapistSearchTerm(e.target.value)}
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <FiSearch className="text-gray-400" />
                      </div>
                    </div>
                    {therapistSearchTerm && (
                      <div className="mt-1 bg-white border border-base-300 rounded-md shadow-sm max-h-48 overflow-y-auto">
                        {filteredTherapists.length > 0 ? (
                          <ul className="menu menu-compact">
                            {filteredTherapists.map(therapist => (
                              <li key={therapist.id}>
                                <button
                                  className={`py-2 ${selectedTherapistId === therapist.id ? 'bg-primary/10 text-primary' : ''}`}
                                  onClick={() => {
                                    setSelectedTherapistId(therapist.id);
                                    setTherapistSearchTerm(therapist.name);
                                  }}
                                >
                                  <div>
                                    <div className="font-medium">{therapist.name}</div>
                                    <div className="text-xs text-gray-500">{therapist.outlet.name}</div>
                                  </div>
                                </button>
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <div className="p-3 text-center text-gray-500">
                            Tidak ada terapis yang sesuai
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Input Komisi */}
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Komisi (Rp)</span>
                    </label>
                    <input
                      id="edit-commission-input"
                      type="text"
                      className="input input-bordered w-full"
                      placeholder="Contoh: 25.000"
                      value={therapistCommissionValue}
                      onChange={(e) => {
                        const numericValue = parseRupiah(e.target.value);
                        setTherapistCommissionValue(formatRupiah(numericValue));
                      }}
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    className="btn btn-primary"
                    onClick={handleAddTherapistCommission}
                    disabled={!selectedTherapistId || isLoadingCommissions}
                  >
                    {isLoadingCommissions && <span className="loading loading-spinner loading-xs"></span>}
                    Tambah Komisi Khusus
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
        <form method="dialog" className="modal-backdrop"> <button onClick={handleCloseTherapistCommissionModal}>close</button> </form>
      </dialog>

      {/* --> Modal Konfirmasi Hapus Komisi Terapis Khusus <-- */}
      <dialog id="delete_commission_modal" className={`modal ${isDeleteCommissionModalOpen ? 'modal-open' : ''}`}>
        <div className="modal-box max-w-sm border border-base-300">
          <button onClick={handleCloseDeleteCommissionModal} className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2 z-10">✕</button>
          <h3 className="font-bold text-lg text-error">Konfirmasi Hapus Komisi Khusus</h3>
          <p className="py-4 text-base-content/80">
            Apakah Anda yakin ingin menghapus komisi terapis khusus ini?
            <br/> Tindakan ini tidak dapat dibatalkan.
          </p>
          <div className="modal-action">
            <button className="btn btn-sm btn-ghost" onClick={handleCloseDeleteCommissionModal} disabled={isLoadingCommissions}>
              Batal
            </button>
            <button className="btn btn-sm btn-error" onClick={handleDeleteTherapistCommission} disabled={isLoadingCommissions}>
              {isLoadingCommissions && <span className="loading loading-spinner loading-xs"></span>}
              Ya, Hapus
            </button>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop"> <button onClick={handleCloseDeleteCommissionModal}>close</button> </form>
      </dialog>
    </motion.div>
  );
}