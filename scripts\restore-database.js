// Script untuk restore database dari file backup JSON
const { PrismaClient } = require('../prisma/generated/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function restoreDatabase(backupFilePath) {
  try {
    console.log('🔄 <PERSON><PERSON><PERSON> proses restore database...');
    
    // Validasi path file backup
    if (!backupFilePath) {
      throw new Error('Path file backup harus disediakan!');
    }
    
    // Jika path relatif, konversi ke absolute path
    if (!path.isAbsolute(backupFilePath)) {
      backupFilePath = path.join(process.cwd(), backupFilePath);
    }
    
    // Cek apakah file backup ada
    if (!fs.existsSync(backupFilePath)) {
      throw new Error(`File backup tidak ditemukan: ${backupFilePath}`);
    }
    
    console.log(`📂 Membaca file backup: ${backupFilePath}`);
    
    // Baca file backup
    const backupData = JSON.parse(fs.readFileSync(backupFilePath, 'utf8'));
    
    if (!Array.isArray(backupData)) {
      throw new Error('Format file backup tidak valid!');
    }
    
    console.log(`📋 Ditemukan ${backupData.length} model untuk di-restore`);
    
    // Konfirmasi dari pengguna sebelum melanjutkan
    console.log('⚠️ PERINGATAN: Proses restore akan menimpa data yang ada di database.');
    console.log('⚠️ Pastikan Anda telah membuat backup terlebih dahulu!');
    console.log('⚠️ Tekan Ctrl+C untuk membatalkan, atau tunggu 5 detik untuk melanjutkan...');
    
    // Tunggu 5 detik sebelum melanjutkan
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Restore data untuk setiap model
    for (const modelData of backupData) {
      const { name, data } = modelData;
      
      // Skip jika model tidak ada atau data kosong
      if (!name || !data || !Array.isArray(data) || data.length === 0) {
        console.log(`⚠️ Skipping model ${name}: Tidak ada data atau format tidak valid`);
        continue;
      }
      
      // Cek apakah model ada di Prisma
      if (!prisma[name] || typeof prisma[name].createMany !== 'function') {
        console.log(`⚠️ Skipping model ${name}: Model tidak ditemukan di Prisma`);
        continue;
      }
      
      console.log(`🔍 Restoring data untuk model: ${name} (${data.length} records)`);
      
      try {
        // Gunakan transaksi untuk memastikan atomicity
        await prisma.$transaction(async (tx) => {
          // Hapus data yang ada (opsional, tergantung kebutuhan)
          // PERINGATAN: Ini akan menghapus SEMUA data di model tersebut!
          // Uncomment jika ingin menghapus data yang ada sebelum restore
          // await tx[name].deleteMany({});
          
          // Restore data batch per batch untuk menghindari timeout
          const batchSize = 100;
          for (let i = 0; i < data.length; i += batchSize) {
            const batch = data.slice(i, i + batchSize);
            
            // Gunakan createMany untuk efisiensi
            // Catatan: createMany tidak mendukung nested writes/relations
            await tx[name].createMany({
              data: batch,
              skipDuplicates: true, // Skip jika ada duplikasi (berdasarkan unique fields)
            });
            
            console.log(`✅ Restored batch ${i/batchSize + 1}/${Math.ceil(data.length/batchSize)} untuk model ${name}`);
          }
        });
        
        console.log(`✅ Berhasil restore ${data.length} data untuk model ${name}`);
      } catch (error) {
        console.error(`❌ Error saat restore data untuk model ${name}:`, error.message);
      }
    }
    
    console.log('✅ Proses restore database selesai!');
  } catch (error) {
    console.error('❌ Error saat melakukan restore database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Ambil path file backup dari command line arguments
const backupFilePath = process.argv[2];

if (!backupFilePath) {
  console.error('❌ Error: Path file backup harus disediakan!');
  console.log('Penggunaan: node restore-database.js <path-to-backup-file>');
  console.log('Contoh: node restore-database.js ./database-backups/backup-2025-06-20T10-00-16.024Z.json');
  process.exit(1);
}

// Jalankan fungsi restore
restoreDatabase(backupFilePath)
  .then(() => {
    console.log('🎉 Proses restore selesai!');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Terjadi kesalahan saat restore database:', error);
    process.exit(1);
  });