'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { 
  QrCodeIcon, 
  IdentificationIcon,
  ClockIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline';

const menuItems = [
  {
    title: 'ID Card',
    description: 'Generate dan cetak ID card dengan QR code',
    icon: <IdentificationIcon className="w-10 h-10 text-emerald-500" />,
    href: '/dashboard/absensi/id-card',
    color: 'bg-emerald-50'
  },
  {
    title: 'Scan QR',
    description: 'Scan QR code untuk absensi masuk/keluar',
    icon: <QrCodeIcon className="w-10 h-10 text-blue-500" />,
    href: '/dashboard/absensi/scan',
    color: 'bg-blue-50'
  },
  {
    title: 'Riwayat Absensi',
    description: 'Lihat riwayat absensi terapis dan staff',
    icon: <ClockIcon className="w-10 h-10 text-purple-500" />,
    href: '/dashboard/absensi/riwayat',
    color: 'bg-purple-50'
  },
  {
    title: 'Antrian',
    description: 'Kelola antrian terapis yang hadir',
    icon: <UserGroupIcon className="w-10 h-10 text-amber-500" />,
    href: '/dashboard/absensi/antrian',
    color: 'bg-amber-50'
  }
];

// Animation variants untuk staggered animation
const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const item = {
  hidden: { y: 20, opacity: 0 },
  show: { y: 0, opacity: 1 }
};

export default function AbsensiPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-800">Sistem Absensi</h1>
        <p className="text-gray-600">Kelola absensi terapis dan staff dengan QR code</p>
      </div>

      {/* Logo dan Header */}
      <div className="relative mb-10 rounded-xl overflow-hidden shadow-xl">
        <div style={{ background: 'linear-gradient(to right, #22c55e, #eab308)' }} className="px-8 py-16 flex items-center">
          <div className="w-1/2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <h2 className="text-3xl font-bold text-white mb-4">Absensi QR Code</h2>
              <p className="text-white text-opacity-90 mb-6">
                Sistem absensi modern dengan QR code untuk terapis dan staff Breaktime
              </p>
              <Link
                href="/dashboard/absensi/scan"
                className="inline-flex items-center px-4 py-2 bg-white text-emerald-600 rounded-lg font-medium hover:bg-gray-50"
              >
                <QrCodeIcon className="w-5 h-5 mr-2" />
                Mulai Scan
              </Link>
            </motion.div>
          </div>
          <div className="w-1/2 flex justify-end">
            <motion.div 
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ type: "spring", stiffness: 100, delay: 0.3 }}
              className="relative"
            >
              <div className="bg-white p-3 rounded-full shadow-lg">
                <Image
                  src="/images/logo.png"
                  alt="Breaktime Logo"
                  width={130}
                  height={130}
                  className="object-contain"
                  priority
                />
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Menu Grid - gunakan hex color untuk mengantisipasi masalah dengan oklch */}
      <motion.div 
        variants={container}
        initial="hidden"
        animate="show"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        {menuItems.map((menuItem, index) => (
          <motion.div key={index} variants={item}>
            <Link href={menuItem.href} className="block">
              <div className={`rounded-xl shadow-md p-6 ${menuItem.color} hover:shadow-lg transition-shadow`}>
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    {menuItem.icon}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-900">{menuItem.title}</h3>
                    <p className="mt-1 text-sm text-gray-600">{menuItem.description}</p>
                  </div>
                </div>
              </div>
            </Link>
          </motion.div>
        ))}
      </motion.div>

      {/* Tips Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8 }}
        className="mt-12 bg-white p-6 rounded-lg shadow-md border border-gray-100"
      >
        <h3 className="text-lg font-semibold text-gray-800 mb-3">Tips Penggunaan</h3>
        <ul className="space-y-2 text-gray-600">
          <li className="flex items-start">
            <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-emerald-100 text-emerald-500 mr-2">•</span>
            <span>Generate ID card untuk terapis baru di menu <b>ID Card</b>.</span>
          </li>
          <li className="flex items-start">
            <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-emerald-100 text-emerald-500 mr-2">•</span>
            <span>Scan QR code di menu <b>Scan QR</b> untuk absensi masuk dan keluar.</span>
          </li>
          <li className="flex items-start">
            <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-emerald-100 text-emerald-500 mr-2">•</span>
            <span>Pantau antrian terapis yang telah absen masuk di menu <b>Antrian</b>.</span>
          </li>
        </ul>
      </motion.div>
    </div>
  );
} 