import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import * as jwt from 'jsonwebtoken';

// Gunakan variabel environment dan pastikan ada nilai default yang aman
const JWT_SECRET = process.env.JWT_SECRET || 'breaktime_session_secret_key_for_jwt_auth';

export async function GET(request: NextRequest) {
  try {
    // Verifikasi autentikasi dari token
    const authHeader = request.headers.get('authorization');
    let token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    // Jika tidak ada token di header, cek cookie
    if (!token) {
      const cookieHeader = request.headers.get('cookie');
      if (cookieHeader) {
        const cookies = cookieHeader.split(';').map(c => c.trim());
        const userTokenCookie = cookies.find(c => c.startsWith('user_token='));
        if (userTokenCookie) {
          token = userTokenCookie.split('=')[1];
        }
      }
    }

    // Jika tidak ada token sama sekali
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verifikasi token
    const decoded = jwt.verify(token, JWT_SECRET) as {
      id: string;
      name: string;
      email: string;
      role: string;
    };

    if (!decoded || !decoded.id) {
      return NextResponse.json({ error: 'Token tidak valid' }, { status: 401 });
    }

    // Hanya admin yang bisa melihat log
    if (decoded.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Ambil parameter query
    const searchParams = request.nextUrl.searchParams;
    const outletId = searchParams.get('outletId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const type = searchParams.get('type');
    const skip = (page - 1) * limit;

    // Buat filter berdasarkan parameter
    const filter: any = {};

    if (outletId && outletId !== 'all') {
      filter.outletId = outletId;
    }

    if (type) {
      filter.type = type;
    }

    // Filter berdasarkan tanggal
    if (startDate || endDate) {
      filter.createdAt = {};

      if (startDate) {
        filter.createdAt.gte = new Date(startDate);
      }

      if (endDate) {
        // Set endDate to end of day
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        filter.createdAt.lte = endDateTime;
      }
    }

    // Ambil total log untuk pagination
    const totalLogs = await prisma.systemLog.count({
      where: filter
    });

    // Ambil log dengan pagination
    const logs = await prisma.systemLog.findMany({
      where: filter,
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit,
      include: {
        outlet: {
          select: {
            id: true,
            name: true
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Ambil semua outlet untuk filter
    const outlets = await prisma.outlet.findMany({
      select: {
        id: true,
        name: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    // Ambil semua tipe log yang ada
    const logTypes = await prisma.systemLog.findMany({
      select: {
        type: true
      },
      distinct: ['type']
    });

    return NextResponse.json({
      logs,
      pagination: {
        total: totalLogs,
        page,
        limit,
        totalPages: Math.ceil(totalLogs / limit)
      },
      outlets,
      logTypes: logTypes.map(lt => lt.type)
    });
  } catch (error) {
    console.error('Error fetching logs:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data log' },
      { status: 500 }
    );
  }
}
