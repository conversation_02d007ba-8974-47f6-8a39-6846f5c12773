import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Middleware untuk autentikasi dan otorisasi
// Melakukan decode JWT token tanpa verifikasi signature

// Paths yang tidak memerlukan authentication
const publicPaths = [
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password',
  '/terapis-komisi', // Halaman cek komisi terapis tidak memerlukan login
  '/reservasi-online', // Halaman reservasi online tidak memerlukan login
];

// API endpoints yang tidak memerlukan authentication
const publicApiEndpoints = [
  '/api/auth',
  '/api/therapists/commissions',
  '/api/outlets', // API outlet untuk reservasi online
  '/api/services', // API layanan untuk reservasi online
  '/api/therapists', // API terapis untuk reservasi online
  '/api/customers', // API customer untuk reservasi online
  '/api/customers/by-phone', // API pencarian customer berdasarkan nomor telepon
  '/api/bookings', // API booking untuk reservasi online
];

// Role-based access
const roleBasedAccess: Record<string, string[]> = {
  // Path-path yang hanya bisa diakses oleh ADMIN
  '/dashboard/users': ['ADMIN', 'STAFF'],
  '/dashboard/outlet': ['ADMIN', 'MANAGER'],
  // Path yang bisa diakses semua role
  '/dashboard': ['ADMIN', 'MANAGER', 'STAFF', 'INVESTOR'],
  '/dashboard/booking': ['ADMIN', 'MANAGER', 'STAFF', 'INVESTOR'],
  '/dashboard/transaksi': ['ADMIN', 'MANAGER', 'STAFF', 'INVESTOR'],
  '/dashboard/pelanggan': ['ADMIN', 'MANAGER', 'STAFF', 'INVESTOR'],
  '/dashboard/terapis': ['ADMIN', 'MANAGER', 'STAFF', 'INVESTOR'],
  '/dashboard/layanan': ['ADMIN', 'MANAGER', 'STAFF', 'INVESTOR'],
  '/dashboard/riwayat': ['ADMIN', 'MANAGER', 'STAFF', 'INVESTOR'],
  '/dashboard/reports': ['ADMIN', 'MANAGER', 'STAFF', 'INVESTOR'],
  '/dashboard/settings': ['ADMIN', 'MANAGER', 'STAFF'],
};

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Cek apakah sedang mengakses API yang tidak memerlukan auth
  if (publicApiEndpoints.some(endpoint => pathname.startsWith(endpoint))) {
    return NextResponse.next();
  }

  // Proses path

  // Cek apakah user sudah login (dengan memeriksa session/token/cookie)
  const userToken = request.cookies.get('user_token')?.value;
  const isAuthenticated = !!userToken;

  // Parsing userToken jika ada
  let userRole = null; // Default role null, akan divalidasi nanti
  let userPermissions = []; // Untuk menyimpan permission user

  if (isAuthenticated && userToken) {
    try {
      // Basic decode jwt token tanpa validasi
      const tokenPayload = JSON.parse(atob(userToken.split('.')[1]));
      if (tokenPayload) {
        if (tokenPayload.role) {
          userRole = tokenPayload.role;
        }
        // ID user tidak digunakan
        if (tokenPayload.permissions) {
          // Pastikan permissions adalah array
          userPermissions = Array.isArray(tokenPayload.permissions) ?
            tokenPayload.permissions : [];
        }
      }
    } catch (e) {
      // Error parsing token, akan dianggap tidak memiliki permission
    }
  }

  // Redirect ke login jika mencoba mengakses halaman yang memerlukan auth
  if (!isAuthenticated) {
    // Jika mencoba mengakses halaman yang memerlukan auth, redirect ke login
    if (pathname.startsWith('/dashboard') || pathname === '/outlet-selection') {
      const loginUrl = new URL('/auth/login', request.url);
      loginUrl.searchParams.set('redirect', encodeURIComponent(pathname));

      return NextResponse.redirect(loginUrl);
    }

    // Jika mencoba mengakses API yang memerlukan auth
    if (pathname.startsWith('/api/') && 
        !publicApiEndpoints.some(endpoint => pathname.startsWith(endpoint))) {
      return new NextResponse(JSON.stringify({ message: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Jika sudah di halaman public, lanjutkan
    if (publicPaths.some(path => pathname === path || pathname.startsWith(path))) {
      return NextResponse.next();
    }
  } else {
    // User sudah login

    // Jika mencoba mengakses halaman login atau register, redirect ke dashboard
    // Tapi biarkan akses ke halaman terapis-komisi meskipun sudah login
    if ((pathname === '/auth/login' || pathname === '/auth/register' ||
         pathname === '/auth/forgot-password' || pathname === '/auth/reset-password')) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }

    // Jika mencoba mengakses halaman terapis-komisi, biarkan lanjut
    if (pathname === '/terapis-komisi' || pathname.startsWith('/terapis-komisi')) {
      return NextResponse.next();
    }

    // Validasi jika userRole tidak ditemukan
    if (!userRole) {
      // Hapus cookie dan redirect ke login
      const response = NextResponse.redirect(new URL('/auth/login', request.url));
      response.cookies.delete('user_token');
      return response;
    }

    // Mapping path ke module permission
    const pathToModule: Record<string, string> = {
      '/dashboard/users': 'users',
      '/dashboard/outlet': 'settings',
      '/dashboard/booking': 'bookings',
      '/dashboard/transaksi': 'transactions',
      '/dashboard/pelanggan': 'customers',
      '/dashboard/terapis': 'therapists',
      '/dashboard/layanan': 'services',
      '/dashboard/riwayat': 'transactions',
      '/dashboard/reports': 'reports',
      '/dashboard/settings': 'settings',
      // Tambahkan path lain yang mungkin ada
      '/dashboard': 'dashboard',
    };

    // Optimasi khusus untuk investor
    if (userRole === 'INVESTOR' && (pathname === '/dashboard/reports' || pathname.startsWith('/dashboard/reports'))) {
      // Investor selalu punya akses ke halaman reports
      return NextResponse.next();
    }

    // Cek role-based access untuk path tertentu
    for (const [path, roles] of Object.entries(roleBasedAccess)) {
      if (pathname === path || pathname.startsWith(path)) {
        // Jika user tidak memiliki role yang tepat
        if (!roles.includes(userRole)) {
          // Redirect ke dashboard dengan pesan error
          const errorUrl = new URL('/dashboard', request.url);
          errorUrl.searchParams.set('error', 'unauthorized');
          errorUrl.searchParams.set('message', `Anda tidak memiliki akses ke halaman ${pathname}`);
          return NextResponse.redirect(errorUrl);
        }

        // Jika user memiliki role yang tepat, cek juga permission
        // Kecuali untuk ADMIN yang selalu punya akses penuh
        if (userRole !== 'ADMIN') {
          const module = pathToModule[path];
          if (module) {
            // Cari permission untuk module ini
            const modulePermission = Array.isArray(userPermissions) ?
              userPermissions.find((p: any) => p.module === module) : null;

            // Jika tidak ada permission atau tidak punya akses read
            if (!modulePermission || !modulePermission.canRead) {
              // Redirect ke dashboard dengan pesan error
              const errorUrl = new URL('/dashboard', request.url);
              errorUrl.searchParams.set('error', 'unauthorized');
              errorUrl.searchParams.set('message', `Anda tidak memiliki izin untuk melihat halaman ${pathname}`);
              return NextResponse.redirect(errorUrl);
            }
          }
        }
      }
    }
  }

  return NextResponse.next();
}

// Konfigurasi path yang harus dijalankan middleware
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * 1. /api/webhooks routes (contoh webhook yang tidak perlu auth)
     * 2. /_next (Next.js internals)
     * 3. /fonts, /images (static files)
     * 4. /favicon.ico, /logo.svg, dll (browser requests)
     */
    '/((?!api/webhooks|_next|images|icons|fonts|favicon.ico|logo.svg|robots.txt).*)',
  ],
};