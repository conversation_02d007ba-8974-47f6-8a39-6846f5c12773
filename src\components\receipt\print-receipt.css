/* CSS Khusus untuk Pencetakan Struk */

@media print {
  @page {
    size: 58mm auto !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  html, body {
    width: 58mm !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: visible !important;
  }
  
  body * {
    visibility: hidden !important;
  }
  
  #receipt-to-print,
  #receipt-to-print * {
    visibility: visible !important;
  }
  
  #receipt-to-print {
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    width: 58mm !important;
    margin: 0 !important;
    padding: 0.5rem !important;
    overflow: visible !important;
    page-break-inside: avoid !important;
    page-break-after: avoid !important;
    page-break-before: avoid !important;
  }
  
  /* Pastikan semua konten terlihat */
  #receipt-to-print * {
    overflow: visible !important;
    max-width: 100% !important;
  }
  
  /* Atur ukuran font dan margin */
  #receipt-to-print h2 {
    font-size: 10pt !important;
    margin-bottom: 2pt !important;
  }
  
  #receipt-to-print p,
  #receipt-to-print div {
    font-size: 8pt !important;
    line-height: 1.2 !important;
    margin-bottom: 1pt !important;
  }
  
  /* Pastikan gambar logo tidak terlalu besar */
  #receipt-to-print img {
    max-width: 40px !important;
    height: auto !important;
  }
}
