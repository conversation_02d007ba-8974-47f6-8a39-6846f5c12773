# Commission Calculation Consistency Fix

## Problem Analysis

Sebelumnya, sistem mengalami ketidakonsistenan dalam perhitungan komisi terapis yang disebabkan oleh:

1. **Perbedaan Penanganan Data Booking vs Walk-in**: Logika berbeda untuk mengambil data layanan dari booking dan transaksi walk-in
2. **Prioritas Komisi yang Salah**: <PERSON><PERSON><PERSON> masih mengikuti data booking meskipun kasir menambahkan layanan lain di transaksi
3. **Logika Fallback yang Kompleks**: Mekanisme fallback yang tidak konsisten saat membuat TransactionItem
4. **Inkonsistensi Sumber Harga**: Penggunaan harga real-time vs harga tersimpan di booking
5. **Kurangnya Validasi Komisi**: Tidak ada validasi konsistensi perhitungan komisi
6. **Penanganan Edge Cases**: Kuantitas negatif dan zero tidak ditangani dengan benar

## Solutions Implemented

### 2. Standardisasi Perhitungan Komisi

**File**: `src/lib/commission-utils.ts`

- Fungsi `calculateTherapistCommission()` yang konsisten untuk semua perhitungan
- Interface `ServiceCommissionData` untuk standardisasi data input
- Penanganan komisi khusus terapis dengan prioritas yang benar
- Validasi input dan penanganan edge cases

```typescript
// Contoh penggunaan yang konsisten
const commission = calculateTherapistCommission(
  serviceCommissionData,
  therapistSpecialCommissions
);
```

### 3. Perbaikan Logika Fallback

**File**: `src/app/api/transactions/route.ts`

- Prioritas yang jelas: Frontend items → Booking data → Error
- Validasi yang lebih ketat untuk service availability
- Konsistensi penggunaan harga dari booking untuk transaksi booking

```typescript
// Prioritas 1: Gunakan items dari frontend jika tersedia dan valid (untuk walk-in)
if (items && Array.isArray(items) && items.length > 0) {
  // Validasi bahwa semua service tersedia di outlet
  // ...
}
// Prioritas 2: Gunakan data dari booking jika tidak ada items dari frontend
else if (bookingId && typeof bookingId === 'string' && bookingId.trim() !== '') {
  // Konsisten dengan logika komisi: gunakan harga dari booking untuk konsistensi
  // ...
}
// Error jika tidak ada data yang valid
else {
  return NextResponse.json({ 
    error: 'Tidak ada data layanan yang valid. Harap sediakan items untuk walk-in atau bookingId untuk booking.' 
  }, { status: 400 });
}
```

### 3. Enhanced Commission Utilities

**File**: `src/lib/commission-utils.ts`

- Menambahkan penanganan kuantitas negatif dan zero
- Memperbaiki fungsi `validateCommissionConsistency` dengan signature baru
- Menambahkan fungsi legacy untuk backward compatibility

```typescript
// Penanganan kuantitas negatif/zero
services.forEach(service => {
  // Skip services with negative or zero quantities
  if (service.quantity <= 0) {
    return;
  }
  // ...
});

// Validasi konsistensi yang lebih robust
export function validateCommissionConsistency(
  services: ServiceCommissionData[],
  therapistSpecialCommissions: TherapistSpecialCommission[] = [],
  expectedCommission: number,
  tolerance: number = 0.01
): boolean {
  const recalculatedCommission = calculateTherapistCommission(services, therapistSpecialCommissions);
  return Math.abs(recalculatedCommission - expectedCommission) <= tolerance;
}
```

### 4. Enhanced Logging and Monitoring

**File**: `src/app/api/transactions/route.ts`

- Logging yang lebih detail untuk debugging
- Verifikasi manual perhitungan komisi
- Deteksi ketidakonsistenan real-time

```typescript
console.log(`Commission calculation details for therapist ${therapistId}:`);
console.log(`Data source: ${bookingId ? 'Booking' : 'Walk-in items'}`);

let totalCommissionCheck = 0;
serviceCommissionData.forEach(service => {
  const specialCommission = specialCommissions.find(sc => sc.serviceId === service.serviceId);
  const commissionPerUnit = specialCommission ? specialCommission.commission : service.defaultCommission;
  const serviceCommission = commissionPerUnit * service.quantity;
  totalCommissionCheck += serviceCommission;
  
  console.log(`Service ${service.serviceId}: ${commissionPerUnit} (${specialCommission ? 'special' : 'default'}) x ${service.quantity} = ${serviceCommission}`);
});

if (Math.abs(calculatedCommission - totalCommissionCheck) > 0.01) {
  console.error(`Commission calculation mismatch! Calculated: ${calculatedCommission}, Manual: ${totalCommissionCheck}`);
}
```

### 5. Comprehensive Testing

**File**: `src/__tests__/commission-consistency.test.ts`

Menambahkan test suite baru yang mencakup:

- Konsistensi booking vs walk-in
- Edge cases (kuantitas negatif, zero, fractional)
- Skenario dunia nyata
- Validasi konsistensi
- **NEW**: Test case prioritas komisi untuk memverifikasi:
  - Komisi dihitung berdasarkan items frontend vs booking
  - Penanganan layanan tambahan yang ditambahkan kasir
  - Validasi konsistensi dengan layanan yang benar-benar diberikan

**File**: `src/lib/__tests__/commission-utils.test.ts`

Memperbaiki test yang ada:

- Memisahkan test untuk fungsi baru dan legacy
- Menambahkan test untuk penanganan kuantitas negatif/zero
- Memperbaiki signature function calls

## Key Benefits

1. **Konsistensi**: Semua perhitungan komisi menggunakan utility function yang sama
2. **Reliability**: Validasi konsistensi mencegah ketidakonsistenan
3. **Maintainability**: Kode yang lebih terstruktur dan mudah dipahami
4. **Debugging**: Logging yang lebih detail untuk troubleshooting
5. **Testing**: Test coverage yang komprehensif untuk berbagai skenario

## Migration Notes

- Fungsi `validateCommissionConsistency` lama masih tersedia sebagai `validateCommissionConsistencyLegacy`
- Semua test case telah diupdate untuk menggunakan signature baru
- Backward compatibility dijaga untuk kode yang sudah ada

## Monitoring

Untuk memantau efektivitas perbaikan:

1. Monitor log untuk "Commission calculation inconsistency detected!"
2. Monitor error rate untuk endpoint `/api/transactions`
3. Jalankan test suite secara berkala: `npm test`
4. Review commission calculation logs untuk anomali

## Future Improvements

1. Implementasi caching untuk special commission lookup
2. Audit trail untuk perubahan komisi
3. Dashboard monitoring untuk commission consistency
4. Automated alerts untuk ketidakonsistenan