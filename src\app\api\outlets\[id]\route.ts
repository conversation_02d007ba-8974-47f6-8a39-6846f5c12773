import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { logOutlet } from '@/lib/logger';
import { getUserIdFromToken } from '@/lib/auth-utils';

// GET outlet berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Await params untuk menghindari error
    const { id } = await Promise.resolve(params);

    if (!id) {
      return NextResponse.json(
        { error: 'ID outlet diperlukan' },
        { status: 400 }
      );
    }

    // Ambil detail outlet berdasarkan ID
    const outlet = await prisma.outlet.findUnique({
      where: {
        id: id
      },
      select: {
        id: true,
        name: true,
        address: true,
        city: true,
        phone: true,
        operationalHours: true,
        isOpen: true,
        isMain: true,
        // Include therapists dan services for this outlet
        therapists: {
          where: {
            isActive: true
          },
          select: {
            id: true,
            name: true,
            experience: true,
            specialization: true
          }
        },
        services: {
          where: {
            service: {
              isActive: true
            }
          },
          select: {
            service: {
              select: {
                id: true,
                name: true,
                description: true,
                duration: true,
                price: true
              }
            }
          }
        }
      }
    });

    // Jika outlet tidak ditemukan
    if (!outlet) {
      return NextResponse.json(
        { error: 'Outlet tidak ditemukan' },
        { status: 404 }
      );
    }

    // Jika outlet tidak aktif
    if (!outlet.isOpen) {
      return NextResponse.json(
        { error: 'Outlet ini sedang tidak beroperasi' },
        { status: 403 }
      );
    }

    // Kembalikan data outlet
    return NextResponse.json({
      message: 'Data outlet berhasil diambil',
      outlet
    });
  } catch (error) {
    console.error('Error fetching outlet details:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil detail outlet' },
      { status: 500 }
    );
  }
}

// PUT untuk mengupdate outlet
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Await params untuk menghindari error
    const { id } = await Promise.resolve(params);
    const body = await request.json();
    const { name, address, city, phone, operationalHours, isOpen, isMain } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID outlet diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah outlet ada
    const existingOutlet = await prisma.outlet.findUnique({
      where: {
        id
      }
    });

    if (!existingOutlet) {
      return NextResponse.json(
        { error: 'Outlet tidak ditemukan' },
        { status: 404 }
      );
    }

    // Jika isMain akan diupdate menjadi true, update outlet lain jadi false
    if (isMain && !existingOutlet.isMain) {
      await prisma.outlet.updateMany({
        where: {
          isMain: true,
          id: {
            not: id
          }
        },
        data: {
          isMain: false
        }
      });
    }

    // Update outlet
    const updatedOutlet = await prisma.outlet.update({
      where: {
        id
      },
      data: {
        ...(name && { name }),
        ...(address && { address }),
        ...(city !== undefined && { city }),
        ...(phone && { phone }),
        ...(operationalHours && { operationalHours }),
        ...(isOpen !== undefined && { isOpen }),
        ...(isMain !== undefined && { isMain })
      }
    });

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Log update outlet
    await logOutlet(
      'update',
      id,
      {
        name: updatedOutlet.name,
        address: updatedOutlet.address,
        city: updatedOutlet.city,
        phone: updatedOutlet.phone,
        operationalHours: updatedOutlet.operationalHours,
        isOpen: updatedOutlet.isOpen,
        isMain: updatedOutlet.isMain,
        updatedFields: Object.keys({
          ...(name && { name }),
          ...(address && { address }),
          ...(city !== undefined && { city }),
          ...(phone && { phone }),
          ...(operationalHours && { operationalHours }),
          ...(isOpen !== undefined && { isOpen }),
          ...(isMain !== undefined && { isMain })
        })
      },
      userId // Gunakan userId dari token
    );

    return NextResponse.json({
      message: 'Outlet berhasil diupdate',
      outlet: updatedOutlet
    });
  } catch (error) {
    console.error('Error updating outlet:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengupdate outlet' },
      { status: 500 }
    );
  }
}

// DELETE outlet (soft delete dengan mengubah isOpen menjadi false)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await Promise.resolve(params);

    if (!id) {
      return NextResponse.json(
        { error: 'ID outlet diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah outlet ada
    const existingOutlet = await prisma.outlet.findUnique({
      where: {
        id
      }
    });

    if (!existingOutlet) {
      return NextResponse.json(
        { error: 'Outlet tidak ditemukan' },
        { status: 404 }
      );
    }

    // Jika outlet adalah outlet utama, tidak bisa dihapus
    if (existingOutlet.isMain) {
      return NextResponse.json(
        { error: 'Outlet utama tidak dapat dihapus' },
        { status: 400 }
      );
    }

    // Soft delete dengan mengubah isOpen menjadi false
    const deactivatedOutlet = await prisma.outlet.update({
      where: {
        id
      },
      data: {
        isOpen: false
      }
    });

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Log deaktivasi outlet
    await logOutlet(
      'close',
      id,
      {
        name: deactivatedOutlet.name,
        address: deactivatedOutlet.address,
        city: deactivatedOutlet.city,
        phone: deactivatedOutlet.phone,
        operationalHours: deactivatedOutlet.operationalHours,
        isMain: deactivatedOutlet.isMain
      },
      userId // Gunakan userId dari token
    );

    return NextResponse.json({
      message: 'Outlet berhasil dinonaktifkan',
      outlet: deactivatedOutlet
    });
  } catch (error) {
    console.error('Error deactivating outlet:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menonaktifkan outlet' },
      { status: 500 }
    );
  }
}