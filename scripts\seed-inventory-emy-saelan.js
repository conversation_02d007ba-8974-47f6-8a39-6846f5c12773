const { PrismaClient } = require('../prisma/generated/client');

const prisma = new PrismaClient();

async function seedInventoryEmySaelan() {
  const outletId = 'palu-emisaelan'; // ID outlet Emy Saelan
  
  console.log('🏪 Memulai seeding inventori untuk outlet Emy Saelan...');
  console.log(`📍 Outlet ID: ${outletId}`);
  
  // Cari admin user untuk createdById
  const adminUser = await prisma.user.findFirst({
    where: { role: 'ADMIN' }
  });
  
  if (!adminUser) {
    throw new Error('Admin user tidak ditemukan. Pastikan seeding user sudah dijalankan.');
  }
  
  console.log(`👤 Menggunakan admin user: ${adminUser.email}`);
  
  let totalCategories = 0;
  let totalItems = 0;

  try {
    // Buat kategori inventori berdasarkan area
    const categories = [
      { name: 'Halaman Depan', description: 'Item-item yang berada di area halaman depan outlet' },
      { name: '<PERSON><PERSON> Tunggu dan Kasir', description: 'Item-item yang berada di area ruang tunggu dan kasir' },
      { name: '<PERSON><PERSON> Cowo', description: 'Item-item yang berada di ruang treatment pria' },
      { name: 'Ruang Cewek', description: 'Item-item yang berada di ruang treatment wanita' },
      { name: 'Ruang AO', description: 'Item-item yang berada di ruang AO (Area Operasional)' },
      { name: 'Ruang Dapur', description: 'Item-item yang berada di area dapur' },
      { name: 'Kamar Mandi', description: 'Item-item yang berada di area kamar mandi' }
    ];

    console.log('Membuat kategori inventori...');
    const createdCategories = {};
    
    for (const category of categories) {
      const createdCategory = await prisma.inventoryCategory.upsert({
        where: { name: category.name },
        update: { description: category.description },
        create: {
          name: category.name,
          description: category.description,
          isActive: true
        }
      });
      createdCategories[category.name] = createdCategory;
      console.log(`✅ Kategori "${category.name}" berhasil dibuat`);
    }

    // Data inventori berdasarkan area
    const inventoryData = {
      'Halaman Depan': [
        { name: 'BANNER', quantity: 2 },
        { name: 'LAMPU', quantity: 3 },
        { name: 'COK CABANG', quantity: 1 },
        { name: 'SAPU LIDI', quantity: 1 },
        { name: 'SKOP SAMPAH', quantity: 1 }
      ],
      'Ruang Tunggu dan Kasir': [
        { name: 'KURSI', quantity: 8 },
        { name: 'MEJA MINUMAN', quantity: 1 },
        { name: 'RAK HELM', quantity: 1 },
        { name: 'VAS BUNGA', quantity: 4 },
        { name: 'MEJA KASIR', quantity: 1 },
        { name: 'TEMPAT TISSU', quantity: 1 },
        { name: 'BEL', quantity: 1 },
        { name: 'PARFUM RUANGAN', quantity: 1 },
        { name: 'LACI KASIR', quantity: 2 },
        { name: 'TEMPAT PULPEN', quantity: 1 },
        { name: 'SPEAKER', quantity: 1 },
        { name: 'MESIN DEBIT', quantity: 1 },
        { name: 'MESIN UANG PALSU', quantity: 1 },
        { name: 'PAPAN BANNER', quantity: 1 },
        { name: 'TAB', quantity: 1 },
        { name: 'CCTV', quantity: 2 },
        { name: 'LEMARI', quantity: 1 },
        { name: 'TEMPAT SAMPAH', quantity: 2 },
        { name: 'COK CABANG', quantity: 3 },
        { name: 'LAMPU', quantity: 1 },
        { name: 'LAMPU GANTUNG', quantity: 1 },
        { name: 'TEKO LISTRIK', quantity: 1 },
        { name: 'KERANJANG MINUMAN', quantity: 1 },
        { name: 'TALENAN', quantity: 2 },
        { name: 'CERMIN', quantity: 1 },
        { name: 'AC', quantity: 1 }
      ],
      'Ruang Cowo': [
        { name: 'BAD', quantity: 6 },
        { name: 'KURSI', quantity: 6 },
        { name: 'KERANJANG', quantity: 6 },
        { name: 'GANTUNGAN BAJU', quantity: 6 },
        { name: 'SPEAKER', quantity: 1 },
        { name: 'HIT ELECTRIC', quantity: 1 },
        { name: 'HORDENG', quantity: 4 },
        { name: 'LAMPU GANTUNG', quantity: 1 },
        { name: 'LAMPU LED', quantity: 1 },
        { name: 'JAM', quantity: 1 },
        { name: 'AC', quantity: 1 },
        { name: 'GORDEN BAD', quantity: 4 },
        { name: 'HANGER', quantity: 6 },
        { name: 'PLASTIK BAJU TAMU', quantity: 6 }
      ],
      'Ruang Cewek': [
        { name: 'BAD', quantity: 5 },
        { name: 'KURSI', quantity: 5 },
        { name: 'KERANJANG', quantity: 5 },
        { name: 'GANTUNGAN BAJU', quantity: 5 },
        { name: 'SPEAKER', quantity: 1 },
        { name: 'HIT ELECTRIC', quantity: 1 },
        { name: 'HORDEN', quantity: 3 },
        { name: 'LAMPU', quantity: 2 },
        { name: 'LAMPU GANTUNG', quantity: 2 },
        { name: 'COK CABANG', quantity: 2 },
        { name: 'JAM', quantity: 1 },
        { name: 'AC', quantity: 1 },
        { name: 'CERMIN', quantity: 1 },
        { name: 'GORDEN BAD', quantity: 3 },
        { name: 'HANGER', quantity: 5 },
        { name: 'PLASTIK BAJU TAMU', quantity: 5 }
      ],
      'Ruang AO': [
        { name: 'LEMARI', quantity: 2 },
        { name: 'BRANKAS', quantity: 1 },
        { name: 'KIPAS ANGIN', quantity: 1 },
        { name: 'BOTOL PARFUM', quantity: 1 },
        { name: 'BOTOL SHAMPO', quantity: 1 },
        { name: 'JAM', quantity: 1 },
        { name: 'TEMPAT CREAM', quantity: 1 },
        { name: 'MANGKUK CREAM', quantity: 11 },
        { name: 'TEMPAT LULUR', quantity: 4 },
        { name: 'KERANJANG', quantity: 2 },
        { name: 'COK CABANG', quantity: 1 },
        { name: 'SEPREI BAD', quantity: 16 },
        { name: 'KAIN LOBANG BAD', quantity: 16 },
        { name: 'SARUNG BANTAL', quantity: 33 },
        { name: 'SARUNG BANTAL KECIL', quantity: 3 },
        { name: 'HANDUK KRETEK', quantity: 5 },
        { name: 'HANDUK PIJAT', quantity: 119 },
        { name: 'KAIN BALI', quantity: 44 },
        { name: 'KEMBEN', quantity: 21 },
        { name: 'WASLAP', quantity: 7 },
        { name: 'BANTAL', quantity: 15 },
        { name: 'TEMPAT SAMPAH', quantity: 1 },
        { name: 'LAMPU', quantity: 2 },
        { name: 'TAS HS', quantity: 4 },
        { name: 'CUP MINYAK', quantity: 49 },
        { name: 'HT', quantity: 2 }
      ],
      'Ruang Dapur': [
        { name: 'RENDAMAN KAKI', quantity: 7 },
        { name: 'LOYANG PUTIH', quantity: 16 },
        { name: 'MESIN CUCI', quantity: 1 },
        { name: 'TABUNG GAS', quantity: 1 },
        { name: 'KOMPOR', quantity: 1 },
        { name: 'TERMOS', quantity: 4 },
        { name: 'GALON', quantity: 7 },
        { name: 'RAK SANDAL', quantity: 1 },
        { name: 'LOYANG BESAR', quantity: 2 },
        { name: 'KERANJANG', quantity: 3 },
        { name: 'LAMPU', quantity: 5 },
        { name: 'COK CABANG', quantity: 4 },
        { name: 'CEREK', quantity: 1 },
        { name: 'KAIN PEL', quantity: 1 },
        { name: 'SAPU IJUK', quantity: 1 },
        { name: 'DISPENSER', quantity: 1 },
        { name: 'JAS HUJAN', quantity: 2 }
      ],
      'Kamar Mandi': [
        { name: 'GAYUNG', quantity: 3 },
        { name: 'BOTOL SABUN', quantity: 1 },
        { name: 'KESET', quantity: 3 },
        { name: 'TEMPAT SAMPAH', quantity: 1 },
        { name: 'SIKAT WC', quantity: 3 },
        { name: 'LAMPU', quantity: 2 },
        { name: 'GANTUNGAN HAIRDRAYER', quantity: 1 }
      ]
    };

    // Buat item inventori untuk setiap kategori
    console.log('Membuat item inventori...');
    let totalItems = 0;

    for (const [categoryName, items] of Object.entries(inventoryData)) {
      const category = createdCategories[categoryName];
      console.log(`\n📦 Membuat item untuk kategori: ${categoryName}`);

      for (const item of items) {
        // Buat item inventori

        const createdItem = await prisma.inventoryItem.create({
          data: {
            name: item.name,
            description: `${item.name} yang berada di area ${categoryName}`,
            categoryId: category.id,
            outletId: outletId,
            totalQuantity: item.quantity,
            goodCondition: item.quantity, // Asumsikan semua dalam kondisi baik
            damagedCondition: 0,
            lostCondition: 0,
            remainingQuantity: item.quantity,
            minStockLevel: Math.max(1, Math.floor(item.quantity * 0.2)), // 20% dari quantity sebagai minimum stock
            maxStockLevel: item.quantity * 2,
            unitPrice: 0,
            notes: `Item inventori di area ${categoryName}`,
            createdById: adminUser.id // Menggunakan admin sebagai creator
          }
        });

        // Buat movement record untuk stock awal
        await prisma.inventoryMovement.create({
          data: {
            itemId: createdItem.id,
            movementType: 'IN',
            quantity: item.quantity,
            reason: 'INITIAL_STOCK',
            notes: `Stock awal - ${item.name}`,
            createdById: adminUser.id
          }
        });

        console.log(`  ✅ ${item.name} (${item.quantity} pcs)`);
        totalItems++;
      }
    }

    console.log(`\n🎉 Seeding inventori berhasil!`);
    console.log(`📊 Total kategori dibuat: ${Object.keys(createdCategories).length}`);
    console.log(`📦 Total item dibuat: ${totalItems}`);
    console.log(`🏢 Outlet: Breaktime Palu Emisaelan (${outletId})`);

  } catch (error) {
    console.error('❌ Error saat seeding inventori:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Jalankan seeding
seedInventoryEmySaelan()
  .catch((e) => {
    console.error('❌ Seeding gagal:', e);
    process.exit(1);
  });