'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  FiArrowLeft, 
  FiCheck, 
  FiPlay, 
  FiEdit, 
  FiSave,
  FiCalendar,
  FiUser,
  FiMapPin,
  FiFileText,
  FiClock,
  FiPackage,
  FiCheckCircle,
  FiAlertCircle
} from 'react-icons/fi';
import { toast } from 'react-hot-toast';

interface AuditItem {
  id: string;
  expectedGood: number;
  expectedDamaged: number;
  expectedLost: number;
  actualGood: number | null;
  actualDamaged: number | null;
  actualLost: number | null;
  variance: number | null;
  notes: string | null;
  isChecked: boolean;
  checkedAt: string | null;
  item: {
    id: string;
    name: string;
    description: string | null;
    goodCondition: number;
    damagedCondition: number;
    lostCondition: number;
    category: {
      id: string;
      name: string;
    };
  };
  checkedBy: {
    id: string;
    name: string;
  } | null;
}

interface Audit {
  id: string;
  title: string;
  description: string | null;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  scheduledDate: string;
  startedAt: string | null;
  completedAt: string | null;
  notes: string | null;
  createdAt: string;
  outlet: {
    id: string;
    name: string;
  };
  createdBy: {
    id: string;
    name: string;
  };
  assignedTo: {
    id: string;
    name: string;
  } | null;
  auditItems: AuditItem[];
}

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.5 }
};

const AuditDetailPage = () => {
  const params = useParams();
  const router = useRouter();
  const auditId = params.id as string;

  const [audit, setAudit] = useState<Audit | null>(null);
  const [loading, setLoading] = useState(true);
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState({
    actualGood: '',
    actualDamaged: '',
    actualLost: '',
    notes: ''
  });

  useEffect(() => {
    fetchAuditDetail();
  }, [auditId]);

  useEffect(() => {
    if (audit) {
      startAuditIfNeeded();
    }
  }, [audit]);

  const fetchAuditDetail = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/inventory/audits/${auditId}`);

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = 'Gagal mengambil detail audit';
        
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch {
          console.error('Non-JSON error response:', errorText);
        }
        
        throw new Error(errorMessage);
      }

      const data = await response.json();
      setAudit(data.audit);
    } catch (error) {
      console.error('Error fetching audit detail:', error);
      toast.error(error instanceof Error ? error.message : 'Terjadi kesalahan');
    } finally {
      setLoading(false);
    }
  };

  const handleStartAudit = async () => {
    try {
      const response = await fetch(`/api/inventory/audits/${auditId}/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = 'Gagal memulai audit';
        
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch {
          console.error('Non-JSON error response:', errorText);
        }
        
        throw new Error(errorMessage);
      }

      const data = await response.json();
      toast.success('Audit berhasil dimulai');
      fetchAuditDetail();
    } catch (error) {
      console.error('Error starting audit:', error);
      toast.error(error instanceof Error ? error.message : 'Terjadi kesalahan');
    }
  };

  const handleEditItem = (item: AuditItem) => {
    setEditingItemId(item.id);
    setEditForm({
      actualGood: item.actualGood?.toString() || item.expectedGood.toString(),
      actualDamaged: item.actualDamaged?.toString() || item.expectedDamaged.toString(),
      actualLost: item.actualLost?.toString() || item.expectedLost.toString(),
      notes: item.notes || ''
    });
  };

  const handleSaveItem = async (itemId: string) => {
    try {
      const response = await fetch(`/api/inventory/audits/${auditId}/items/${itemId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          actualGood: parseInt(editForm.actualGood) || 0,
          actualDamaged: parseInt(editForm.actualDamaged) || 0,
          actualLost: parseInt(editForm.actualLost) || 0,
          notes: editForm.notes.trim() || null
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = 'Gagal menyimpan item audit';
        
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch {
          console.error('Non-JSON error response:', errorText);
        }
        
        throw new Error(errorMessage);
      }

      const data = await response.json();
      toast.success('Item audit berhasil disimpan');
      setEditingItemId(null);
      fetchAuditDetail();
    } catch (error) {
      console.error('Error saving audit item:', error);
      toast.error(error instanceof Error ? error.message : 'Terjadi kesalahan');
    }
  };

  const handleCompleteAudit = async () => {
    try {
      const response = await fetch(`/api/inventory/audits/${auditId}/complete`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          updateStock: true
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Gagal menyelesaikan audit');
      }

      toast.success('Audit berhasil diselesaikan');
      fetchAuditDetail();
    } catch (error) {
      console.error('Error completing audit:', error);
      toast.error(error instanceof Error ? error.message : 'Terjadi kesalahan');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'COMPLETED': return 'bg-green-100 text-green-800 border-green-200';
      case 'CANCELLED': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING': return 'Menunggu';
      case 'IN_PROGRESS': return 'Sedang Berlangsung';
      case 'COMPLETED': return 'Selesai';
      case 'CANCELLED': return 'Dibatalkan';
      default: return status;
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Makassar'
    });
  };

  const calculateVariance = (expected: number, actual: number | null) => {
    if (actual === null) return null;
    return actual - expected;
  };

  const getVarianceColor = (variance: number | null) => {
    if (variance === null) return 'text-gray-500';
    if (variance > 0) return 'text-green-600';
    if (variance < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const startAuditIfNeeded = async () => {
    if (!audit || audit.status !== 'PENDING') return;
    
    // Cek apakah audit items sudah ada
    if (audit.auditItems && audit.auditItems.length > 0) return;
    
    try {
      const response = await fetch(`/api/inventory/audits/${auditId}/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error starting audit:', errorText);
        return;
      }

      const data = await response.json();
      toast.success(`Audit dimulai dengan ${data.audit?.auditItems?.length || 0} item`);
      fetchAuditDetail(); // Refresh data
    } catch (error) {
      console.error('Error starting audit:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="loading loading-spinner loading-lg text-primary"></div>
      </div>
    );
  }

  if (!audit) {
    return (
      <div className="text-center py-12">
        <FiAlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium mb-2">Audit tidak ditemukan</h3>
        <button
          onClick={() => router.back()}
          className="btn btn-primary"
        >
          <FiArrowLeft className="w-4 h-4" />
          Kembali
        </button>
      </div>
    );
  }

  const checkedItemsCount = audit.auditItems.filter(item => item.isChecked).length;
  const totalItemsCount = audit.auditItems.length;
  const progress = totalItemsCount > 0 ? (checkedItemsCount / totalItemsCount) * 100 : 0;

  return (
    <motion.div 
      className="space-y-6"
      variants={fadeInUp}
      initial="initial"
      animate="animate"
    >
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.back()}
            className="btn btn-ghost btn-sm"
          >
            <FiArrowLeft className="w-4 h-4" />
            Kembali
          </button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Detail Audit Inventori</h1>
            <p className="text-gray-600 text-sm sm:text-base">Kelola dan periksa item audit</p>
          </div>
        </div>

        <div className="flex items-center gap-3 self-start sm:self-auto">
          {audit.status === 'PENDING' && (
            <button
              onClick={handleStartAudit}
              className="btn btn-success gap-2"
            >
              <FiPlay className="w-4 h-4" />
              Mulai Audit
            </button>
          )}
          
          {audit.status === 'IN_PROGRESS' && checkedItemsCount === totalItemsCount && (
            <button
              onClick={handleCompleteAudit}
              className="btn btn-success gap-2"
            >
              <FiCheckCircle className="w-4 h-4" />
              Selesaikan Audit
            </button>
          )}
        </div>
      </div>

      {/* Audit Info Card */}
      <motion.div variants={fadeInUp} className="card bg-white shadow-lg">
        <div className="card-body">
          <div className="flex items-start justify-between mb-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{audit.title}</h2>
              {audit.description && (
                <p className="text-gray-600 mt-1">{audit.description}</p>
              )}
            </div>
            <div className={`px-3 py-1 rounded-full border text-sm font-medium ${getStatusColor(audit.status)}`}>
              {getStatusText(audit.status)}
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
            <div className="flex items-center gap-3">
              <FiMapPin className="w-5 h-5 text-gray-400" />
              <div>
                <p className="text-sm text-gray-500">Outlet</p>
                <p className="font-medium">{audit.outlet.name}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <FiCalendar className="w-5 h-5 text-gray-400" />
              <div>
                <p className="text-sm text-gray-500">Dijadwalkan</p>
                <p className="font-medium">{formatDateTime(audit.scheduledDate)}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <FiUser className="w-5 h-5 text-gray-400" />
              <div>
                <p className="text-sm text-gray-500">Dibuat oleh</p>
                <p className="font-medium">{audit.createdBy.name}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <FiPackage className="w-5 h-5 text-gray-400" />
              <div>
                <p className="text-sm text-gray-500">Total Item</p>
                <p className="font-medium">{totalItemsCount} item</p>
              </div>
            </div>
          </div>

          {audit.status === 'IN_PROGRESS' && (
            <div className="mt-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Progress Audit</span>
                <span className="text-sm text-gray-500">{checkedItemsCount}/{totalItemsCount} item</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          )}

          {audit.startedAt && (
            <div className="flex items-center gap-3 mt-4 text-sm text-gray-600">
              <FiClock className="w-4 h-4" />
              <span>Dimulai: {formatDateTime(audit.startedAt)}</span>
            </div>
          )}

          {audit.completedAt && (
            <div className="flex items-center gap-3 mt-2 text-sm text-gray-600">
              <FiCheckCircle className="w-4 h-4" />
              <span>Selesai: {formatDateTime(audit.completedAt)}</span>
            </div>
          )}
        </div>
      </motion.div>

      {/* Audit Items */}
      <motion.div variants={fadeInUp} className="card bg-white shadow-lg">
        <div className="card-body">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Daftar Item Audit</h3>
            
            {audit.status === 'PENDING' && audit.auditItems.length === 0 && (
              <button 
                onClick={handleStartAudit}
                className="btn btn-primary gap-2"
              >
                <FiPlay className="w-4 h-4" />
                Mulai Audit
              </button>
            )}
          </div>
          
          {audit.auditItems.length === 0 ? (
            <div className="text-center py-12">
              <FiPackage className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">
                {audit.status === 'PENDING' ? 'Audit Belum Dimulai' : 'Tidak Ada Item'}
              </h4>
              <p className="text-gray-500 mb-6">
                {audit.status === 'PENDING' 
                  ? 'Klik tombol "Mulai Audit" untuk memulai proses audit inventori'
                  : 'Tidak ada item inventori yang perlu diaudit di outlet ini'
                }
              </p>
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden lg:block overflow-x-auto">
            <table className="table table-zebra w-full">
              <thead>
                <tr>
                  <th className="min-w-[200px]">Item</th>
                  <th className="min-w-[120px]">Kategori</th>
                  <th className="min-w-[140px]">Expected</th>
                  <th className="min-w-[140px]">Actual</th>
                  <th className="min-w-[120px]">Variance</th>
                  <th className="min-w-[120px]">Status</th>
                  <th className="min-w-[100px]">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {audit.auditItems.map((auditItem) => (
                  <tr key={auditItem.id}>
                    <td>
                      <div className="max-w-[200px]">
                        <div className="font-medium text-gray-900 break-words">{auditItem.item.name}</div>
                        {auditItem.item.description && (
                          <div className="text-sm text-gray-500 break-words line-clamp-2">{auditItem.item.description}</div>
                        )}
                      </div>
                    </td>
                    <td>
                      <div className="flex flex-wrap">
                        <span className="badge badge-primary badge-sm whitespace-nowrap">
                          📦 {auditItem.item.category.name}
                        </span>
                      </div>
                    </td>
                    <td>
                      <div className="text-sm">
                        <div>Baik: {auditItem.expectedGood}</div>
                        <div>Rusak: {auditItem.expectedDamaged}</div>
                        <div>Hilang: {auditItem.expectedLost}</div>
                      </div>
                    </td>
                    <td>
                      {editingItemId === auditItem.id ? (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <span className="text-xs w-12">Baik:</span>
                            <input
                              type="number"
                              value={editForm.actualGood}
                              onChange={(e) => setEditForm(prev => ({ ...prev, actualGood: e.target.value }))}
                              className="input input-bordered input-xs w-16"
                              min="0"
                            />
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-xs w-12">Rusak:</span>
                            <input
                              type="number"
                              value={editForm.actualDamaged}
                              onChange={(e) => setEditForm(prev => ({ ...prev, actualDamaged: e.target.value }))}
                              className="input input-bordered input-xs w-16"
                              min="0"
                            />
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-xs w-12">Hilang:</span>
                            <input
                              type="number"
                              value={editForm.actualLost}
                              onChange={(e) => setEditForm(prev => ({ ...prev, actualLost: e.target.value }))}
                              className="input input-bordered input-xs w-16"
                              min="0"
                            />
                          </div>
                        </div>
                      ) : (
                        <div className="text-sm">
                          <div>Baik: {auditItem.actualGood ?? '-'}</div>
                          <div>Rusak: {auditItem.actualDamaged ?? '-'}</div>
                          <div>Hilang: {auditItem.actualLost ?? '-'}</div>
                        </div>
                      )}
                    </td>
                    <td>
                      <div className="text-sm">
                        <div className={getVarianceColor(calculateVariance(auditItem.expectedGood, auditItem.actualGood))}>
                          Baik: {calculateVariance(auditItem.expectedGood, auditItem.actualGood) ?? '-'}
                        </div>
                        <div className={getVarianceColor(calculateVariance(auditItem.expectedDamaged, auditItem.actualDamaged))}>
                          Rusak: {calculateVariance(auditItem.expectedDamaged, auditItem.actualDamaged) ?? '-'}
                        </div>
                        <div className={getVarianceColor(calculateVariance(auditItem.expectedLost, auditItem.actualLost))}>
                          Hilang: {calculateVariance(auditItem.expectedLost, auditItem.actualLost) ?? '-'}
                        </div>
                      </div>
                    </td>
                    <td>
                      {auditItem.isChecked ? (
                        <div className="flex items-center gap-2">
                          <FiCheckCircle className="w-4 h-4 text-green-600" />
                          <span className="text-green-600 font-medium text-sm">Sudah dicek</span>
                        </div>
                      ) : (
                        <span className="text-gray-500 text-sm">Belum dicek</span>
                      )}
                    </td>
                    <td>
                      {audit.status === 'IN_PROGRESS' && (
                        <div className="flex items-center gap-2">
                          {editingItemId === auditItem.id ? (
                            <>
                              <button
                                onClick={() => handleSaveItem(auditItem.item.id)}
                                className="btn btn-success btn-xs"
                              >
                                <FiSave className="w-3 h-3" />
                              </button>
                              <button
                                onClick={() => setEditingItemId(null)}
                                className="btn btn-ghost btn-xs"
                              >
                                Batal
                              </button>
                            </>
                          ) : (
                            <button
                              onClick={() => handleEditItem(auditItem)}
                              className="btn btn-primary btn-xs"
                            >
                              <FiEdit className="w-3 h-3" />
                              Edit
                            </button>
                          )}
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Mobile Card View */}
          <div className="block lg:hidden space-y-4">
            {audit.auditItems.map((auditItem) => (
              <div key={`mobile-${auditItem.id}`} className="card bg-base-100 border border-gray-200 shadow-sm">
                <div className="card-body p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-900 break-words">{auditItem.item.name}</h4>
                      {auditItem.item.description && (
                        <p className="text-sm text-gray-500 mt-1 break-words">{auditItem.item.description}</p>
                      )}
                    </div>
                    <div className="ml-3 flex-shrink-0">
                      <span className="badge badge-primary badge-sm whitespace-nowrap">
                        📦 {auditItem.item.category.name}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <h5 className="font-medium text-gray-700 mb-2">Expected</h5>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span>Baik:</span>
                          <span className="font-medium">{auditItem.expectedGood}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Rusak:</span>
                          <span className="font-medium">{auditItem.expectedDamaged}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Hilang:</span>
                          <span className="font-medium">{auditItem.expectedLost}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h5 className="font-medium text-gray-700 mb-2">Actual</h5>
                      {editingItemId === auditItem.id ? (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span>Baik:</span>
                            <input
                              type="number"
                              value={editForm.actualGood}
                              onChange={(e) => setEditForm(prev => ({ ...prev, actualGood: e.target.value }))}
                              className="input input-bordered input-xs w-16"
                              min="0"
                            />
                          </div>
                          <div className="flex items-center justify-between">
                            <span>Rusak:</span>
                            <input
                              type="number"
                              value={editForm.actualDamaged}
                              onChange={(e) => setEditForm(prev => ({ ...prev, actualDamaged: e.target.value }))}
                              className="input input-bordered input-xs w-16"
                              min="0"
                            />
                          </div>
                          <div className="flex items-center justify-between">
                            <span>Hilang:</span>
                            <input
                              type="number"
                              value={editForm.actualLost}
                              onChange={(e) => setEditForm(prev => ({ ...prev, actualLost: e.target.value }))}
                              className="input input-bordered input-xs w-16"
                              min="0"
                            />
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-1">
                          <div className="flex justify-between">
                            <span>Baik:</span>
                            <span className="font-medium">{auditItem.actualGood ?? '-'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Rusak:</span>
                            <span className="font-medium">{auditItem.actualDamaged ?? '-'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Hilang:</span>
                            <span className="font-medium">{auditItem.actualLost ?? '-'}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="mt-4 pt-3 border-t border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {auditItem.isChecked ? (
                          <>
                            <FiCheckCircle className="w-4 h-4 text-green-600" />
                            <span className="text-green-600 font-medium text-sm">Sudah dicek</span>
                          </>
                        ) : (
                          <span className="text-gray-500 text-sm">Belum dicek</span>
                        )}
                      </div>
                      
                      {audit.status === 'IN_PROGRESS' && (
                        <div className="flex items-center gap-2">
                          {editingItemId === auditItem.id ? (
                            <>
                              <button
                                onClick={() => handleSaveItem(auditItem.item.id)}
                                className="btn btn-success btn-xs"
                              >
                                <FiSave className="w-3 h-3" />
                                Simpan
                              </button>
                              <button
                                onClick={() => setEditingItemId(null)}
                                className="btn btn-ghost btn-xs"
                              >
                                Batal
                              </button>
                            </>
                          ) : (
                            <button
                              onClick={() => handleEditItem(auditItem)}
                              className="btn btn-primary btn-xs"
                            >
                              <FiEdit className="w-3 h-3" />
                              Edit
                            </button>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Variance Display for Mobile */}
                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div className="text-center">
                        <span className="block text-gray-500">Variance Baik</span>
                        <span className={`font-medium ${getVarianceColor(calculateVariance(auditItem.expectedGood, auditItem.actualGood))}`}>
                          {calculateVariance(auditItem.expectedGood, auditItem.actualGood) ?? '-'}
                        </span>
                      </div>
                      <div className="text-center">
                        <span className="block text-gray-500">Variance Rusak</span>
                        <span className={`font-medium ${getVarianceColor(calculateVariance(auditItem.expectedDamaged, auditItem.actualDamaged))}`}>
                          {calculateVariance(auditItem.expectedDamaged, auditItem.actualDamaged) ?? '-'}
                        </span>
                      </div>
                      <div className="text-center">
                        <span className="block text-gray-500">Variance Hilang</span>
                        <span className={`font-medium ${getVarianceColor(calculateVariance(auditItem.expectedLost, auditItem.actualLost))}`}>
                          {calculateVariance(auditItem.expectedLost, auditItem.actualLost) ?? '-'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
            </>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default AuditDetailPage; 