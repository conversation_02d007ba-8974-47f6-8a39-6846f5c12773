import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserIdFromToken } from '@/lib/auth-utils';
import { logTherapist } from '@/lib/logger';

// POST untuk menggabungkan dua terapis
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { primaryTherapistId, secondaryTherapistId } = body;

    // Validasi input
    if (!primaryTherapistId || !secondaryTherapistId) {
      return NextResponse.json(
        { error: 'ID terapis utama dan ID terapis yang akan digabungkan diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah terapis sama
    if (primaryTherapistId === secondaryTherapistId) {
      return NextResponse.json(
        { error: 'Tidak dapat menggabungkan terapis yang sama' },
        { status: 400 }
      );
    }

    // Cek apakah kedua terapis ada
    const primaryTherapist = await prisma.therapist.findUnique({
      where: { id: primaryTherapistId },
      include: { outlet: true }
    });

    const secondaryTherapist = await prisma.therapist.findUnique({
      where: { id: secondaryTherapistId },
      include: { outlet: true }
    });

    if (!primaryTherapist || !secondaryTherapist) {
      return NextResponse.json(
        { error: 'Salah satu atau kedua terapis tidak ditemukan' },
        { status: 404 }
      );
    }

    // Cek apakah kedua terapis berasal dari outlet yang sama
    if (primaryTherapist.outletId !== secondaryTherapist.outletId) {
      return NextResponse.json(
        { 
          error: 'Terapis harus berasal dari outlet yang sama untuk digabungkan',
          primaryOutlet: primaryTherapist.outlet.name,
          secondaryOutlet: secondaryTherapist.outlet.name
        },
        { status: 400 }
      );
    }

    // Cek apakah terapis sekunder memiliki sesi aktif
    const activeSession = await prisma.activeTherapistSession.findFirst({
      where: {
        therapistId: secondaryTherapistId,
        endTime: { gt: new Date() }
      }
    });

    if (activeSession) {
      return NextResponse.json(
        { error: 'Terapis yang akan digabungkan masih memiliki sesi aktif' },
        { status: 400 }
      );
    }

    // Hitung jumlah data yang akan dipindahkan untuk konfirmasi
    const transactionsCount = await prisma.transaction.count({
      where: { therapistId: secondaryTherapistId }
    });

    const bookingsCount = await prisma.booking.count({
      where: { therapistId: secondaryTherapistId }
    });

    const commissionsCount = await prisma.therapistServiceCommission.count({
      where: { therapistId: secondaryTherapistId }
    });

    const attendancesCount = await prisma.therapistAttendance.count({
      where: { therapistId: secondaryTherapistId }
    });

    // Mulai transaksi database untuk memastikan semua operasi berhasil atau gagal bersama
    const result = await prisma.$transaction(async (tx) => {
      // 1. Pindahkan semua transaksi
      await tx.transaction.updateMany({
        where: { therapistId: secondaryTherapistId },
        data: { therapistId: primaryTherapistId }
      });

      // 2. Pindahkan semua booking
      await tx.booking.updateMany({
        where: { therapistId: secondaryTherapistId },
        data: { therapistId: primaryTherapistId }
      });

      // 3. Pindahkan semua sesi aktif (jika ada)
      await tx.activeTherapistSession.updateMany({
        where: { therapistId: secondaryTherapistId },
        data: { therapistId: primaryTherapistId }
      });

      // 4. Pindahkan semua komisi khusus
      // Pertama, hapus komisi yang konflik (jika terapis utama sudah memiliki komisi untuk layanan yang sama)
      const secondaryCommissions = await tx.therapistServiceCommission.findMany({
        where: { therapistId: secondaryTherapistId },
        select: { serviceId: true }
      });

      const serviceIds = secondaryCommissions.map(c => c.serviceId);

      // Hapus komisi yang konflik di terapis utama
      await tx.therapistServiceCommission.deleteMany({
        where: {
          therapistId: primaryTherapistId,
          serviceId: { in: serviceIds }
        }
      });

      // Pindahkan komisi dari terapis sekunder ke terapis utama
      await tx.therapistServiceCommission.updateMany({
        where: { therapistId: secondaryTherapistId },
        data: { therapistId: primaryTherapistId }
      });

      // 5. Pindahkan semua data absensi
      await tx.therapistAttendance.updateMany({
        where: { therapistId: secondaryTherapistId },
        data: { therapistId: primaryTherapistId }
      });

      // 6. Nonaktifkan terapis sekunder dan tambahkan catatan bahwa telah digabungkan
      const updatedSecondaryTherapist = await tx.therapist.update({
        where: { id: secondaryTherapistId },
        data: {
          isActive: false,
          specialization: `[Digabungkan dengan ${primaryTherapist.name}] ${secondaryTherapist.specialization || ''}`
        }
      });

      return {
        primaryTherapist,
        secondaryTherapist: updatedSecondaryTherapist,
        transactionsCount,
        bookingsCount,
        commissionsCount,
        attendancesCount
      };
    });

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Log penggabungan terapis
    await logTherapist(
      'merge',
      primaryTherapistId,
      {
        mergedWith: secondaryTherapistId,
        transactionsCount,
        bookingsCount,
        commissionsCount,
        attendancesCount
      },
      primaryTherapist.outletId,
      userId
    );

    return NextResponse.json({
      message: 'Terapis berhasil digabungkan',
      primaryTherapist: result.primaryTherapist,
      secondaryTherapist: result.secondaryTherapist,
      stats: {
        transactionsCount,
        bookingsCount,
        commissionsCount,
        attendancesCount
      }
    }, { status: 200 });
  } catch (error) {
    console.error('Error merging therapists:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menggabungkan terapis' },
      { status: 500 }
    );
  }
}
