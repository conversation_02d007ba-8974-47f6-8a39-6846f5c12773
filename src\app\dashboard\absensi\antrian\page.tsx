'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import Link from 'next/link';
import { 
  ChevronLeftIcon, 
  ArrowPathIcon,
  UserGroupIcon,
  ClockIcon,
  QrCodeIcon,
  BuildingOfficeIcon,
  StarIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import QueueFilter from '@/components/attendance/QueueFilter';
import WaitingTime from '@/components/attendance/WaitingTime';
import { format } from 'date-fns';
import { id as localeId } from 'date-fns/locale';
import { useOutletContext } from '@/contexts/OutletContext';

interface TherapistQueue {
  id: string;
  therapistId: string;
  name: string;
  outletId: string;
  outletName: string;
  checkInTime: string;
  waitingTime: number;
}

interface Outlet {
  id: string;
  name: string;
}

interface Filter {
  outletId?: string;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { 
    opacity: 1,
    transition: { 
      when: "beforeChildren",
      staggerChildren: 0.15,
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { 
    y: 0, 
    opacity: 1,
    transition: { type: 'spring', stiffness: 100, damping: 12 }
  }
};

// Fungsi untuk mengambil waktu lokal (format jam:menit)
const formatTimeOnly = (dateString: string) => {
  const date = new Date(dateString);
  return format(date, 'HH:mm', { locale: localeId });
};

export default function AntrianPage() {
  const { selectedOutletId } = useOutletContext();
  const [queue, setQueue] = useState<TherapistQueue[]>([]);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState<Filter>({ outletId: selectedOutletId || '' });
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState<boolean>(true);
  
  // Effect untuk mengupdate filter saat outlet berubah
  useEffect(() => {
    if (selectedOutletId) {
      setFilters(prev => ({ ...prev, outletId: selectedOutletId }));
    }
  }, [selectedOutletId]);
  
  // Mengambil data antrian
  const fetchQueue = async () => {
    try {
      setLoading(true);
      setError('');
      
      // Buat URL dengan parameter filter
      let url = `/api/attendance/queue`;
      
      if (filters.outletId) {
        url += `?outletId=${filters.outletId}`;
      }
      
      const response = await fetch(url);
      const result = await response.json();
      
      if (response.ok) {
        setQueue(result.data);
        setOutlets(result.filters.outlets);
        setLastRefresh(new Date());
      } else {
        setError(result.error || 'Gagal mengambil data antrian');
        toast.error(result.error || 'Gagal mengambil data antrian');
      }
    } catch (err) {
      console.error('Error fetching queue data:', err);
      setError('Terjadi kesalahan saat mengambil data antrian');
      toast.error('Terjadi kesalahan saat mengambil data antrian');
    } finally {
      setLoading(false);
    }
  };
  
  // Setup auto refresh
  useEffect(() => {
    fetchQueue();
    
    // Refresh data setiap 30 detik jika autoRefresh aktif
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchQueue();
      }, 30000); // 30 detik
      
      setRefreshInterval(interval);
    }
    
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [filters, autoRefresh]);
  
  // Handle filter change
  const handleFilter = (newFilters: Filter) => {
    setFilters(newFilters);
  };
  
  // Toggle auto refresh
  const toggleAutoRefresh = () => {
    if (autoRefresh) {
      // Matikan auto refresh
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
      setAutoRefresh(false);
      toast.success('Auto refresh dinonaktifkan');
    } else {
      // Aktifkan auto refresh
      fetchQueue();
      const interval = setInterval(() => {
        fetchQueue();
      }, 30000); // 30 detik
      
      setRefreshInterval(interval);
      setAutoRefresh(true);
      toast.success('Auto refresh diaktifkan - setiap 30 detik');
    }
  };
  
  // Manual refresh
  const handleManualRefresh = () => {
    fetchQueue();
    toast.success('Data berhasil diperbarui');
  };
  
  return (
    <motion.div
      className="container mx-auto px-4 py-6 min-h-screen bg-gradient-to-br from-slate-50 to-sky-100"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header dengan Tombol Kembali */}
      <motion.div
        variants={itemVariants}
        className="mb-8 bg-gradient-to-r from-[#4EA799] to-[#3d8a7e] rounded-xl shadow-lg p-6 text-white relative overflow-hidden"
      >
        {/* Dekorasi Blob */}
        <div className="absolute -top-10 -left-10 w-32 h-32 bg-white/10 rounded-full filter blur-xl opacity-70"></div>
        <div className="absolute -bottom-12 -right-12 w-40 h-40 bg-white/10 rounded-full filter blur-xl opacity-50"></div>

        <Link href="/dashboard/absensi" className="absolute left-6 top-6 inline-flex items-center text-white hover:text-yellow-300 transition-colors z-10">
          <ChevronLeftIcon className="w-5 h-5 mr-1" />
          <span>Kembali</span>
        </Link>
        
        <div className="text-center relative z-10">
          <UserGroupIcon className="w-16 h-16 mx-auto mb-2 text-white/80" />
          <h1 className="text-3xl font-bold">Antrian Terapis</h1>
          <p className="text-teal-100 mt-1">Pantau dan kelola terapis yang sedang menunggu</p>
        </div>
      </motion.div>
      
      {/* Filter */}
      <motion.div variants={itemVariants} className="mb-6">
        <QueueFilter 
          onFilter={handleFilter} 
          outlets={outlets}
          loading={loading}
          initialFilters={filters}
        />
      </motion.div>
      
      {/* Toolbar */}
      <motion.div 
        variants={itemVariants}
        className="bg-white/80 backdrop-blur-md rounded-xl shadow-lg mb-6 p-4 flex flex-col sm:flex-row gap-4 justify-between items-center"
      >
        <div className="text-sm text-slate-600 flex items-center">
          <ClockIcon className="w-4 h-4 mr-1.5 text-sky-600" />
          Terakhir diperbarui: <span className="font-semibold ml-1">{format(lastRefresh, 'HH:mm:ss', { locale: localeId })}</span>
        </div>
        
        <div className="flex items-center gap-3">
          <motion.button
            whileHover={{ scale: 1.05, boxShadow: "0px 5px 15px rgba(0,0,0,0.1)" }}
            whileTap={{ scale: 0.95 }}
            onClick={toggleAutoRefresh}
            className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 shadow-sm hover:shadow-md ${
              autoRefresh 
                ? 'bg-sky-500 text-white border border-sky-600' 
                : 'bg-slate-100 text-slate-700 border border-slate-300 hover:bg-slate-200'
            }`}
          >
            <ClockIcon className={`w-4 h-4 mr-1.5 ${autoRefresh ? 'animate-pulse' : ''}`} />
            {autoRefresh ? "Auto Refresh On" : "Auto Refresh Off"}
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.05, boxShadow: "0px 5px 15px rgba(0,0,0,0.1)" }}
            whileTap={{ scale: 0.95 }}
            onClick={handleManualRefresh}
            disabled={loading}
            className="flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-60 transition-all duration-300"
          >
            {loading ? (
              <ArrowPathIcon className="h-4 w-4 animate-spin" />
            ) : (
              <ArrowPathIcon className="h-4 w-4" />
            )}
            <span className="ml-1.5">Refresh</span>
          </motion.button>
          
          <Link
            href="/dashboard/absensi/scan"
            className="flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 transition-all duration-300"
          >
            <QrCodeIcon className="h-4 w-4 mr-1.5" />
            Scan QR
          </Link>
        </div>
      </motion.div>
      
      {/* Data Display */}
      {loading && queue.length === 0 ? (
        <motion.div variants={itemVariants} className="flex flex-col items-center justify-center py-16 text-center">
          <ArrowPathIcon className="h-16 w-16 animate-spin text-sky-600 mb-6" />
          <p className="text-xl font-semibold text-slate-700">Memuat data antrian...</p>
          <p className="text-slate-500">Mohon tunggu sebentar.</p>
        </motion.div>
      ) : error ? (
        <motion.div variants={itemVariants} className="bg-red-50 border-l-4 border-red-500 p-6 rounded-xl shadow-md text-center">
          <ExclamationCircleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-lg font-semibold text-red-700 mb-2">{error}</p>
          <p className="text-red-600 text-sm mb-4">Gagal memuat data antrian. Silakan coba lagi.</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="mt-4 inline-flex items-center px-6 py-2.5 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            onClick={handleManualRefresh}
          >
            <ArrowPathIcon className="h-5 w-5 mr-2" />
            Coba Lagi
          </motion.button>
        </motion.div>
      ) : queue.length === 0 ? (
        <motion.div 
          variants={itemVariants} 
          className="text-center py-16 bg-white/70 backdrop-blur-md rounded-xl shadow-lg"
        >
          <UserGroupIcon className="h-20 w-20 text-slate-400 mx-auto mb-6" />
          <h3 className="text-2xl font-semibold text-slate-700 mb-2">Antrian Kosong</h3>
          <p className="text-slate-500 mb-6">Belum ada terapis yang masuk antrian saat ini.</p>
          <Link
            href="/dashboard/absensi/scan"
            className="inline-flex items-center px-6 py-2.5 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-300"
          >
            <QrCodeIcon className="h-5 w-5 mr-2" />
            Mulai Scan Absensi
          </Link>
        </motion.div>
      ) : (
        // Grid layout untuk kartu terapis
        <motion.div 
          variants={containerVariants}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {queue.map((therapist, index) => (
            <motion.div 
              key={therapist.id}
              variants={itemVariants}
              className="bg-white/90 backdrop-blur-md rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-2xl hover:-translate-y-1"
            >
              <div className="p-5">
                <div className="flex items-center mb-4">
                  <div className={`flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-br ${index % 3 === 0 ? 'from-sky-400 to-sky-600' : index % 3 === 1 ? 'from-teal-400 to-teal-600' : 'from-indigo-400 to-indigo-600'} flex items-center justify-center text-white text-xl font-bold shadow-md`}>
                    {therapist.name.substring(0, 1).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-slate-800">{therapist.name}</h3>
                    <p className="text-xs text-slate-500 flex items-center">
                      <BuildingOfficeIcon className="w-3 h-3 mr-1 text-slate-400"/>
                      {therapist.outletName}
                    </p>
                  </div>
                  <div className="ml-auto text-xs font-semibold px-2 py-0.5 rounded-full bg-slate-100 text-slate-600 border border-slate-200">
                    # {index + 1}
                  </div>
                </div>

                <div className="space-y-2.5 text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-500 flex items-center"><ClockIcon className="w-4 h-4 mr-1.5 text-sky-500"/>Waktu Check-in:</span>
                    <span className="font-medium text-slate-700">{formatTimeOnly(therapist.checkInTime)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-500 flex items-center"><ArrowPathIcon className="w-4 h-4 mr-1.5 text-teal-500"/>Waktu Tunggu:</span>
                    <WaitingTime minutes={therapist.waitingTime} iconSize="w-4 h-4" textSize="text-sm" />
                  </div>
                </div>
              </div>
              
              {/* Indikator Waktu Tunggu (opsional) */}
              <div className="h-1.5 bg-slate-200">
                <div
                  className={`h-full transition-all duration-500 ease-out
                    ${therapist.waitingTime < 15 ? 'bg-green-500' : therapist.waitingTime < 30 ? 'bg-yellow-500' : 'bg-red-500'}`}
                  style={{ width: `${Math.min(100, (therapist.waitingTime / 60) * 100)}%` }} // Max 60 menit = 100%
                ></div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      )}
      
      {/* Total Antrian & Scan Out (jika ada antrian) */}
      {queue.length > 0 && (
        <motion.div 
          variants={itemVariants}
          className="mt-8 bg-white/80 backdrop-blur-md rounded-xl shadow-lg p-4 flex flex-col sm:flex-row justify-between items-center"
        >
          <div className="text-sm text-slate-600">
            Total: <span className="font-semibold text-sky-700">{queue.length}</span> terapis dalam antrian.
          </div>
          <Link
            href="/dashboard/absensi/scan" // Asumsi ini untuk scan out juga
            className="mt-3 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-400 transition-all duration-300"
          >
            <QrCodeIcon className="h-4 w-4 mr-1.5" />
            Scan Keluar
          </Link>
        </motion.div>
      )}
      
      {/* Tips Card - Didesain ulang */}
      <motion.div
        variants={itemVariants}
        className="mt-8 bg-gradient-to-r from-sky-50 to-indigo-50 rounded-xl shadow-lg p-6 border border-slate-200/70"
      >
        <div className="flex items-start">
          <div className="flex-shrink-0 mt-0.5 p-2 bg-sky-500/10 rounded-full">
            <StarIcon className="h-6 w-6 text-sky-600" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-semibold text-slate-800">Panduan Penggunaan</h3>
            <div className="mt-2 text-sm text-slate-600 space-y-1.5">
              <p className="flex items-start"><span className="text-sky-600 mr-2 mt-0.5">&#8226;</span>Halaman ini menampilkan terapis yang sudah absensi masuk dan belum keluar.</p>
              <p className="flex items-start"><span className="text-sky-600 mr-2 mt-0.5">&#8226;</span>Waktu tunggu dihitung sejak terapis melakukan absensi masuk.</p>
              <p className="flex items-start"><span className="text-sky-600 mr-2 mt-0.5">&#8226;</span>Filter outlet secara otomatis menggunakan outlet aktif Anda saat ini.</p>
              <p className="flex items-start"><span className="text-sky-600 mr-2 mt-0.5">&#8226;</span>Data diperbarui otomatis setiap 30 detik (jika "Auto Refresh" aktif).</p>
              <p className="flex items-start"><span className="text-sky-600 mr-2 mt-0.5">&#8226;</span>Gunakan tombol "Scan QR" atau "Scan Keluar" untuk absensi.</p>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
} 