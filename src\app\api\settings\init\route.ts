import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// Data pengaturan default
const defaultSettings = [
  // Landing Page
  {
    key: 'landing_hero_title',
    value: 'Selamat Datang di Breaktime Spa',
    category: 'landing-page',
    label: 'Judul Hero',
    type: 'TEXT',
    isSystem: false
  },
  {
    key: 'landing_hero_subtitle',
    value: 'Temukan ketenangan dan kesegaran melalui layanan spa premium kami',
    category: 'landing-page',
    label: 'Subjudul Hero',
    type: 'TEXT',
    isSystem: false
  },
  {
    key: 'landing_about_title',
    value: 'Tentang Kami',
    category: 'landing-page',
    label: 'Judul Tentang Kami',
    type: 'TEXT',
    isSystem: false
  },
  {
    key: 'landing_about_desc',
    value: 'Breaktime Spa adalah tempat relaksasi premium yang menawarkan berbagai perawatan spa dan kesehatan untuk menyegarkan tubuh dan pikiran And<PERSON>.',
    category: 'landing-page',
    label: 'Deskripsi Tentang Kami',
    type: 'RICHTEXT',
    isSystem: false
  },
  {
    key: 'landing_services_count',
    value: '4',
    category: 'landing-page',
    label: 'Jumlah Layanan yang Ditampilkan',
    type: 'NUMBER',
    isSystem: false
  },
  {
    key: 'landing_testimonials_count',
    value: '3',
    category: 'landing-page',
    label: 'Jumlah Testimoni yang Ditampilkan',
    type: 'NUMBER',
    isSystem: false
  },
  {
    key: 'landing_primary_color',
    value: '#0D9488',
    category: 'landing-page',
    label: 'Warna Utama',
    type: 'COLOR',
    isSystem: false
  },
  {
    key: 'landing_secondary_color',
    value: '#F59E0B',
    category: 'landing-page',
    label: 'Warna Sekunder',
    type: 'COLOR',
    isSystem: false
  },
  
  // CTA (Call to Action)
  {
    key: 'cta_booking_text',
    value: 'Reservasi Sekarang',
    category: 'cta',
    label: 'Teks Tombol Booking',
    type: 'TEXT',
    isSystem: false
  },
  {
    key: 'cta_whatsapp_enabled',
    value: 'true',
    category: 'cta',
    label: 'Aktifkan WhatsApp',
    type: 'BOOLEAN',
    isSystem: false
  },
  {
    key: 'cta_whatsapp_number',
    value: '628123456789',
    category: 'cta',
    label: 'Nomor WhatsApp',
    type: 'TEXT',
    isSystem: false
  },
  {
    key: 'cta_whatsapp_text',
    value: 'Halo, saya ingin bertanya tentang layanan Breaktime Spa.',
    category: 'cta',
    label: 'Teks Pesan WhatsApp',
    type: 'TEXT',
    isSystem: false
  },
  {
    key: 'cta_phone_enabled',
    value: 'true',
    category: 'cta',
    label: 'Aktifkan Telepon',
    type: 'BOOLEAN',
    isSystem: false
  },
  {
    key: 'cta_phone_number',
    value: '628123456789',
    category: 'cta',
    label: 'Nomor Telepon',
    type: 'TEXT',
    isSystem: false
  },
  
  // Jenis Pembayaran
  {
    key: 'payment_cash',
    value: 'true',
    category: 'payment-types',
    label: 'Tunai',
    type: 'BOOLEAN',
    isSystem: false
  },
  {
    key: 'payment_credit_card',
    value: 'true',
    category: 'payment-types',
    label: 'Kartu Kredit',
    type: 'BOOLEAN',
    isSystem: false
  },
  {
    key: 'payment_debit_card',
    value: 'true',
    category: 'payment-types',
    label: 'Kartu Debit',
    type: 'BOOLEAN',
    isSystem: false
  },
  {
    key: 'payment_transfer',
    value: 'true',
    category: 'payment-types',
    label: 'Transfer Bank',
    type: 'BOOLEAN',
    isSystem: false
  },
  {
    key: 'payment_qris',
    value: 'true',
    category: 'payment-types',
    label: 'QRIS',
    type: 'BOOLEAN',
    isSystem: false
  },
  {
    key: 'payment_ewallet',
    value: 'true',
    category: 'payment-types',
    label: 'E-Wallet',
    type: 'BOOLEAN',
    isSystem: false
  },
  {
    key: 'payment_ewallet_types',
    value: JSON.stringify(['GoPay', 'OVO', 'DANA', 'LinkAja', 'ShopeePay']),
    category: 'payment-types',
    label: 'Jenis E-Wallet',
    type: 'TEXT',
    isSystem: false
  }
];

// POST untuk inisialisasi pengaturan default
export async function GET() {
  try {
    // Buat semua pengaturan dalam transaksi
    const results = await prisma.$transaction(
      defaultSettings.map(setting => {
        return prisma.setting.upsert({
          where: { key: setting.key },
          create: setting,
          update: {} // Jangan update jika sudah ada
        });
      })
    );
    
    return NextResponse.json({
      message: 'Pengaturan awal berhasil diinisialisasi',
      count: results.length,
      settings: results
    });
  } catch (error) {
    console.error('Error initializing settings:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menginisialisasi pengaturan' },
      { status: 500 }
    );
  }
} 