# 📝 Panduan Implementasi Fitur Lembur Terapis

## ✨ Ringkasan

Fitur lembur terapis telah diimplementasikan untuk melacak dan menghitung kompensasi lembur terapis. Fitur ini memungkinkan pencatatan menit lembur dan jumlah kompensasi pada setiap transaksi, serta melacak total menit lembur dan pendapatan lembur untuk setiap terapis.

## 🗃️ Perubahan Database

Berikut adalah perubahan yang telah diterapkan ke database:

### Model Transaction

- Ditambahkan kolom `overtimeMinutes` (Integer) - Jumlah menit lembur terapis pada transaksi ini
- Ditambahkan kolom `overtimeAmount` (Decimal) - Ju<PERSON>lah kompensasi lembur terapis pada transaksi ini (Rp 100 per menit)
- Ditambahkan indeks `Transaction_overtimeMinutes_idx` untuk mempercepat query yang melibatkan lembur

### Model Therapist

- Ditambahkan kolom `totalOvertimeMinutes` (Integer) - Total menit lembur terapis sepanjang waktu
- Ditambahkan kolom `totalOvertimeEarnings` (Decimal) - Total pendapatan lembur terapis sepanjang waktu

## 🚀 Cara Menggunakan Fitur

### 1. Mencatat Lembur pada Transaksi

Saat membuat atau mengedit transaksi, Anda dapat mencatat jumlah menit lembur terapis dan sistem akan otomatis menghitung jumlah kompensasi berdasarkan tarif Rp 100 per menit.

```typescript
// Contoh penggunaan di API atau service
async function addOvertimeToTransaction(transactionId: number, overtimeMinutes: number) {
  const overtimeAmount = overtimeMinutes * 100; // Rp 100 per menit
  
  const transaction = await prisma.transaction.update({
    where: { id: transactionId },
    data: {
      overtimeMinutes,
      overtimeAmount,
    },
  });
  
  // Update total lembur terapis
  await prisma.therapist.update({
    where: { id: transaction.therapistId },
    data: {
      totalOvertimeMinutes: {
        increment: overtimeMinutes,
      },
      totalOvertimeEarnings: {
        increment: overtimeAmount,
      },
    },
  });
  
  return transaction;
}
```

### 2. Melihat Total Lembur Terapis

Anda dapat melihat total menit lembur dan pendapatan lembur untuk setiap terapis melalui halaman detail terapis atau laporan.

```typescript
// Contoh penggunaan di API atau service
async function getTherapistOvertimeSummary(therapistId: string) {
  const therapist = await prisma.therapist.findUnique({
    where: { id: therapistId },
    select: {
      name: true,
      totalOvertimeMinutes: true,
      totalOvertimeEarnings: true,
    },
  });
  
  return therapist;
}
```

## 📊 Laporan dan Analitik

Anda dapat membuat laporan lembur terapis berdasarkan periode tertentu dengan query berikut:

```typescript
// Contoh query untuk laporan lembur terapis per periode
async function getOvertimeReportByPeriod(startDate: Date, endDate: Date) {
  const overtimeReport = await prisma.transaction.groupBy({
    by: ['therapistId'],
    where: {
      transactionDate: {
        gte: startDate,
        lte: endDate,
      },
      overtimeMinutes: {
        gt: 0,
      },
    },
    _sum: {
      overtimeMinutes: true,
      overtimeAmount: true,
    },
  });
  
  // Dapatkan informasi terapis
  const therapistIds = overtimeReport.map(report => report.therapistId);
  const therapists = await prisma.therapist.findMany({
    where: {
      id: {
        in: therapistIds,
      },
    },
    select: {
      id: true,
      name: true,
    },
  });
  
  // Gabungkan data
  return overtimeReport.map(report => {
    const therapist = therapists.find(t => t.id === report.therapistId);
    return {
      therapistId: report.therapistId,
      therapistName: therapist?.name,
      totalOvertimeMinutes: report._sum.overtimeMinutes,
      totalOvertimeAmount: report._sum.overtimeAmount,
    };
  });
}
```

## 🧪 Pengujian

Untuk memastikan fitur lembur terapis berfungsi dengan baik, lakukan pengujian berikut:

1. Buat transaksi baru dengan mencatat menit lembur
2. Verifikasi bahwa jumlah kompensasi lembur dihitung dengan benar (Rp 100 per menit)
3. Verifikasi bahwa total menit lembur dan pendapatan lembur terapis diperbarui dengan benar
4. Buat beberapa transaksi dengan lembur untuk terapis yang sama dan verifikasi bahwa total lembur terakumulasi dengan benar

## 📝 Catatan Penting

- Tarif lembur saat ini ditetapkan pada Rp 100 per menit. Jika tarif ini perlu diubah di masa mendatang, perlu dilakukan perubahan pada kode yang menghitung `overtimeAmount`.
- Pastikan untuk memperbarui total lembur terapis setiap kali transaksi dengan lembur dibuat, diperbarui, atau dihapus.
- Jika transaksi dengan lembur dihapus, pastikan untuk mengurangi total lembur terapis yang sesuai.

## 🔄 Rencana Pengembangan Selanjutnya

- Implementasi fitur untuk mengatur tarif lembur yang dapat dikonfigurasi
- Penambahan laporan lembur terapis yang lebih detail
- Integrasi dengan sistem penggajian terapis

---

Dokumen ini dibuat pada 20 Juni 2025 dan mencerminkan implementasi fitur lembur terapis saat ini. Jika ada perubahan atau pengembangan lebih lanjut, dokumen ini akan diperbarui.