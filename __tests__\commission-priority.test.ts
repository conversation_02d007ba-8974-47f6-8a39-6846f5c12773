import { calculateTherapistCommission, validateCommissionConsistency } from '../src/lib/commission-utils';

// Define interfaces locally for testing
interface ServiceCommissionData {
  serviceId: string;
  quantity: number;
  defaultCommission: number;
}

interface TherapistSpecialCommission {
  serviceId: string;
  commission: number;
}

describe('Commission Priority Logic', () => {
  describe('Frontend Items vs Booking Priority', () => {
    it('should prioritize frontend items over booking for commission calculation', () => {
      // Simulasi: Booking memiliki layanan A, tapi kasir menambah layanan B
      const bookingServices: ServiceCommissionData[] = [
        {
          serviceId: 'service-a',
          quantity: 1,
          defaultCommission: 50000 // Komisi dari booking
        }
      ];

      const frontendItems: ServiceCommissionData[] = [
        {
          serviceId: 'service-a',
          quantity: 1,
          defaultCommission: 50000 // Layanan dari booking
        },
        {
          serviceId: 'service-b',
          quantity: 1,
          defaultCommission: 75000 // Layanan tambahan dari kasir
        }
      ];

      // Komisi harus dihitung berdasarkan frontend items (A + B)
      const commissionFromFrontend = calculateTherapistCommission(frontendItems, []);
      const commissionFromBookingOnly = calculateTherapistCommission(bookingServices, []);

      expect(commissionFromFrontend).toBe(125000); // 50k + 75k
      expect(commissionFromBookingOnly).toBe(50000); // Hanya 50k
      expect(commissionFromFrontend).toBeGreaterThan(commissionFromBookingOnly);
    });

    it('should handle case where frontend items completely replace booking services', () => {
      const bookingServices: ServiceCommissionData[] = [
        {
          serviceId: 'service-original',
          quantity: 2,
          defaultCommission: 40000
        }
      ];

      const frontendItems: ServiceCommissionData[] = [
        {
          serviceId: 'service-replacement',
          quantity: 1,
          defaultCommission: 100000
        }
      ];

      const commissionFromFrontend = calculateTherapistCommission(frontendItems, []);
      const commissionFromBooking = calculateTherapistCommission(bookingServices, []);

      expect(commissionFromFrontend).toBe(100000);
      expect(commissionFromBooking).toBe(80000); // 40k * 2
      expect(commissionFromFrontend).not.toBe(commissionFromBooking);
    });

    it('should validate commission consistency with frontend items', () => {
      const services: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 1,
          defaultCommission: 60000
        },
        {
          serviceId: 'service-2',
          quantity: 2,
          defaultCommission: 30000
        }
      ];

      const customCommissions: TherapistSpecialCommission[] = [];
      const expectedCommission = 120000; // 60k + (30k * 2)

      const isConsistent = validateCommissionConsistency(services, customCommissions, expectedCommission);
      expect(isConsistent).toBe(true);

      // Test dengan komisi yang salah
      const wrongCommission = 80000; // Komisi dari booking saja
      const isInconsistent = validateCommissionConsistency(services, customCommissions, wrongCommission);
      expect(isInconsistent).toBe(false);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty frontend items gracefully', () => {
      const emptyItems: ServiceCommissionData[] = [];
      const commission = calculateTherapistCommission(emptyItems, []);
      expect(commission).toBe(0);
    });

    it('should handle services with zero commission', () => {
      const services: ServiceCommissionData[] = [
        {
          serviceId: 'free-service',
          quantity: 1,
          defaultCommission: 0
        },
        {
          serviceId: 'paid-service',
          quantity: 1,
          defaultCommission: 50000
        }
      ];

      const commission = calculateTherapistCommission(services, []);
      expect(commission).toBe(50000); // Hanya dari paid service
    });

    it('should handle mixed quantity scenarios', () => {
      const services: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 3,
          defaultCommission: 25000
        },
        {
          serviceId: 'service-2',
          quantity: 1,
          defaultCommission: 75000
        }
      ];

      const commission = calculateTherapistCommission(services, []);
      expect(commission).toBe(150000); // (25k * 3) + (75k * 1)
    });
  });

  describe('Custom Commission Override', () => {
    it('should apply custom commission when specified', () => {
      const services: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 1,
          defaultCommission: 50000
        }
      ];

      const customCommissions: TherapistSpecialCommission[] = [
        {
          serviceId: 'service-1',
          commission: 80000
        }
      ];

      const commission = calculateTherapistCommission(services, customCommissions);
      expect(commission).toBe(80000); // Menggunakan custom commission
    });

    it('should mix default and custom commissions correctly', () => {
      const services: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 1,
          defaultCommission: 50000
        },
        {
          serviceId: 'service-2',
          quantity: 1,
          defaultCommission: 40000
        }
      ];

      const customCommissions: TherapistSpecialCommission[] = [
        {
          serviceId: 'service-1',
          commission: 70000
        }
      ];

      const commission = calculateTherapistCommission(services, customCommissions);
      expect(commission).toBe(110000); // 70k (custom) + 40k (default)
    });
  });
});