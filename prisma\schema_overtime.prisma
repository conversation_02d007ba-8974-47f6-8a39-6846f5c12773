// Skema Prisma untuk implementasi fitur lembur terapis

// Tambahkan kolom overtimeMinutes dan overtimeAmount ke model Transaction
model Transaction {
  // <PERSON><PERSON><PERSON> yang sudah ada
  transactionDate           DateTime                 @default(now())
  displayId                 String?                  @unique
  totalAmount               Float
  paymentMethod             PaymentMethod            @default(CASH)
  paymentStatus             PaymentStatus            @default(PAID)
  notes                     String?
  createdAt                 DateTime                 @default(now())
  updatedAt                 DateTime                 @updatedAt
  bookingId                 String?                  @unique
  createdById               String
  customerId                String
  outletId                  String
  therapistId               String
  therapistCommissionEarned Float?                   @default(0)
  discountType              String?
  discountValue             Float?                   @default(0)
  discountAmount            Float?                   @default(0)
  additionalCharge          Float?                   @default(0)
  id                        Int                      @id @default(autoincrement())
  
  // Kolom baru untuk fitur lembur
  overtimeMinutes           Int?                     @default(0)
  overtimeAmount            Decimal?                 @default(0) @db.Decimal(10, 2)
  
  // Relasi yang sudah ada
  activeSessions            ActiveTherapistSession[]
  splitPayment              SplitPayment?
  booking                   Booking?                 @relation(fields: [bookingId], references: [id])
  createdBy                 User                     @relation("CreatedBy", fields: [createdById], references: [id])
  customer                  Customer                 @relation(fields: [customerId], references: [id])
  outlet                    Outlet                   @relation(fields: [outletId], references: [id])
  therapist                 Therapist                @relation("TherapistRelation", fields: [therapistId], references: [id])
  transactionItems          TransactionItem[]

  @@index([customerId])
  @@index([outletId])
  @@index([therapistId])
  @@index([createdById])
  @@index([createdAt])
  @@index([overtimeMinutes]) // Indeks baru untuk kolom overtimeMinutes
}

// Tambahkan kolom totalOvertimeMinutes dan totalOvertimeEarnings ke model Therapist
model Therapist {
  // Kolom yang sudah ada
  id                 String                       @id @default(uuid())
  name               String
  phone              String?
  experience         Int?
  specialization     String?
  isActive           Boolean                      @default(true)
  createdAt          DateTime                     @default(now())
  updatedAt          DateTime                     @updatedAt
  outletId           String
  captainUserId      String?
  
  // Kolom baru untuk fitur lembur
  totalOvertimeMinutes Int?                       @default(0)
  totalOvertimeEarnings Decimal?                 @default(0) @db.Decimal(10, 2)
  
  // Relasi yang sudah ada
  activeSessions     ActiveTherapistSession[]
  bookings           Booking[]
  captain            User?                        @relation("CaptainTeam", fields: [captainUserId], references: [id])
  outlet             Outlet                       @relation(fields: [outletId], references: [id])
  serviceCommissions TherapistServiceCommission[]
  transactions       Transaction[]               @relation("TherapistRelation")
  attendances        TherapistAttendance[]       @relation("TherapistAttendances")
  salaries           TherapistSalary[]           @relation("TherapistSalaries") // Relasi baru ke TherapistSalary
}

// Model baru untuk gaji terapis
model TherapistSalary {
  id                  String    @id @default(uuid())
  therapistId         String
  periodStart         DateTime
  periodEnd           DateTime
  baseSalary          Decimal   @default(0) @db.Decimal(10, 2)
  commissionEarned    Decimal   @default(0) @db.Decimal(10, 2)
  totalOvertimeMinutes Int?     @default(0)
  totalOvertimeAmount  Decimal? @default(0) @db.Decimal(10, 2)
  bonusAmount         Decimal?  @default(0) @db.Decimal(10, 2)
  deductionAmount     Decimal?  @default(0) @db.Decimal(10, 2)
  totalAmount         Decimal   @default(0) @db.Decimal(10, 2)
  paymentStatus       PaymentStatus @default(PENDING)
  paymentDate         DateTime?
  notes               String?
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt
  
  // Relasi
  therapist           Therapist @relation("TherapistSalaries", fields: [therapistId], references: [id], onDelete: Cascade)
  
  @@index([therapistId])
  @@index([periodStart, periodEnd])
  @@index([paymentStatus])
  @@index([totalOvertimeMinutes]) // Indeks untuk kolom totalOvertimeMinutes
}