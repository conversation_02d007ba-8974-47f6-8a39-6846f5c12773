import React from 'react';
import { ClockIcon } from '@heroicons/react/24/outline';

interface WaitingTimeProps {
  minutes: number;
  className?: string;
}

const WaitingTime: React.FC<WaitingTimeProps> = ({ minutes, className = '' }) => {
  // Format waktu tunggu menjadi jam:menit
  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours === 0) {
      return `${mins} menit`;
    } else {
      return `${hours} jam ${mins} menit`;
    }
  };
  
  // Tentukan warna berdasarkan waktu tunggu
  const getColorClass = (minutes: number): string => {
    if (minutes < 30) {
      return 'text-green-600 bg-green-50';
    } else if (minutes < 60) {
      return 'text-yellow-600 bg-yellow-50';
    } else {
      return 'text-red-600 bg-red-50';
    }
  };
  
  // Tentukan ikon berdasarkan waktu tunggu
  const getTimeDisplay = () => {
    const colorClass = getColorClass(minutes);
    
    return (
      <div className={`inline-flex items-center rounded-full px-2.5 py-0.5 ${colorClass} ${className}`}>
        <ClockIcon className="w-3.5 h-3.5 mr-1" />
        <span className="text-xs font-medium">
          {formatTime(minutes)}
        </span>
      </div>
    );
  };
  
  return getTimeDisplay();
};

export default WaitingTime; 