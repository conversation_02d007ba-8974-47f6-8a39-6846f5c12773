'use client';

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useOutletContext } from '@/contexts/OutletContext';
import ReceiptComponent, { ReceiptProps } from '@/components/receipt/ReceiptComponent';
import { useReactToPrint } from 'react-to-print';
import { toast } from 'sonner';
import { FiEye, FiPrinter, FiTrash2, FiInbox, FiSearch, FiAlertTriangle, FiCalendar, FiUser, FiDownload, FiEdit2 } from 'react-icons/fi';
import PermissionGuard from '@/components/permission/PermissionGuard';
import { parseISO, format, startOfDay, endOfDay } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';

// Tipe data transaksi - Pastikan ini sesuai dengan respons API
interface TransactionReceiptData {
  id: string | number; // ID bisa string (UUID) atau number (autoincrement)
  dateTime: string;
  customers?: { id: string; name: string; phone: string }[];
  items: { name: string; price: number; quantity: number }[];
  subtotal: number;
  discountType: 'percentage' | 'fixed' | 'none';
  discountValue: number;
  discountAmount: number;
  additionalCharge: number;
  tax: number;
  total: number;
  paymentMethod: string;
  note: string;
  createdAt: string;
  totalAmount: number;
  displayId?: string; // Ini akan berisi ID TRXXXXX
  customer?: { id: string; name: string; phone?: string }; // Tambahkan ID dan phone jika perlu
  outlet?: {
    id: string;
    name: string;
    address?: string;
    phone?: string;
  };
  therapist?: { id: string; name: string };
  therapistCommissionEarned?: number; // Tambahkan field komisi terapis
  createdBy?: { id: string; name: string }; // Tambahkan field pembuat transaksi
  transactionItems?: {
    id: string;
    quantity: number;
    price?: number;
    service?: {
      id: string;
      name: string;
      price: number;
    };
  }[];
}

// Kunci localStorage (Tidak digunakan lagi untuk fetch utama)
// const TRANSACTION_HISTORY_KEY = 'transactionHistory';

// Helper functions untuk konsistensi dengan halaman terapis
const getOriginalServiceTotal = (trx: TransactionReceiptData): number => {
  // Prioritaskan transactionItems dari API
  if (Array.isArray(trx.transactionItems) && trx.transactionItems.length > 0) {
    return trx.transactionItems.reduce((total, item) => {
      const price = item.service?.price || item.price || 0;
      const quantity = item.quantity || 1;
      return total + (price * quantity);
    }, 0);
  }
  
  // Fallback ke items structure lama
  if (Array.isArray(trx.items) && trx.items.length > 0) {
    return trx.items.reduce((total, item) => {
      const price = item.price || 0;
      const quantity = item.quantity || 1;
      return total + (price * quantity);
    }, 0);
  }
  
  // Fallback terakhir ke totalAmount
  return trx.totalAmount || 0;
};

// Menghitung total penjualan terapis (harga asli layanan + biaya tambahan)
const getTherapistSalesTotal = (trx: TransactionReceiptData): number => {
  let total = getOriginalServiceTotal(trx);
  
  // Tambahkan biaya tambahan jika ada (biaya tambahan masuk ke penjualan terapis)
  if (trx.additionalCharge && trx.additionalCharge > 0) {
    total += trx.additionalCharge;
  }
  
  return total;
};

// Varian Animasi
const fadeInUp = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.5 } } };
const staggerContainer = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1 } } };

export default function TransactionHistoryPage() {
  const { selectedOutletId } = useOutletContext();
  const [history, setHistory] = useState<TransactionReceiptData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedReceiptData, setSelectedReceiptData] = useState<ReceiptProps | null>(null);
  const [isReceiptModalOpen, setIsReceiptModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [startDateFilter, setStartDateFilter] = useState('');
  const [endDateFilter, setEndDateFilter] = useState('');
  const [therapistFilter, setTherapistFilter] = useState('');
  const [therapistsList, setTherapistsList] = useState<{id: string, name: string}[]>([]);
  const [therapistSearchTerm, setTherapistSearchTerm] = useState('');
  const therapistDropdownRef = useRef<HTMLDivElement>(null);

  // --- State untuk Pagination ---
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  // --- End State Pagination ---

  // --- State Baru untuk Modal Hapus ---
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [transactionToDelete, setTransactionToDelete] = useState<TransactionReceiptData | null>(null);
  const [isDeleting, setIsDeleting] = useState(false); // Loading state untuk proses hapus
  // --- End State Modal Hapus ---

  // --- State Baru untuk Modal Edit ---
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [transactionToEdit, setTransactionToEdit] = useState<TransactionReceiptData | null>(null);
  const [editCommission, setEditCommission] = useState<number>(0);
  const [editAdditionalCharge, setEditAdditionalCharge] = useState<number>(0);
  const [editItems, setEditItems] = useState<{ serviceId?: string | null; name: string; price: number; quantity: number }[]>([]);
  const [editDiscountType, setEditDiscountType] = useState<'percentage' | 'fixed' | 'none'>('none');
  const [editDiscountValue, setEditDiscountValue] = useState<number>(0);
  const [editTherapistId, setEditTherapistId] = useState<string>('');
  const [isUpdating, setIsUpdating] = useState(false); // Loading state untuk proses edit
  const [servicesList, setServicesList] = useState<{id: string, name: string, price: number}[]>([]);
  const [selectedServiceId, setSelectedServiceId] = useState<string>('');
  // --- End State Modal Edit ---

  const receiptRef = useRef<HTMLDivElement>(null);

  // Fungsi Cetak Struk dengan membuka halaman baru
  const handlePrintReceipt = () => {
    if (selectedReceiptData?.transactionId) {
      // Ekstrak ID transaksi dari transactionId (format: TRXXXXXXX)
      const idMatch = selectedReceiptData.transactionId.match(/TR(\d+)/);
      const transactionId = idMatch ? idMatch[1] : selectedReceiptData.transactionId;

      console.log('Mencetak struk dengan ID:', transactionId);

      // Buka halaman cetak dalam jendela baru
      const printWindow = window.open(`/print/receipt?id=${transactionId}`, '_blank', 'width=400,height=600');

      // Pastikan jendela berhasil dibuka
      if (!printWindow) {
        toast.error('Popup diblokir oleh browser. Mohon izinkan popup untuk mencetak struk.');
      }
    } else {
      toast.error('ID transaksi tidak ditemukan');
    }
  };

  // Fungsi untuk fetch data terapis
  const fetchTherapists = useCallback(async () => {
    try {
      const url = selectedOutletId ? `/api/therapists?outletId=${selectedOutletId}` : '/api/therapists';
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const therapistsArray = data?.therapists || [];

      if (Array.isArray(therapistsArray)) {
        setTherapistsList(therapistsArray.map(t => ({ id: t.id, name: t.name })));
      } else {
        console.error("Properti 'therapists' dalam data API bukan array:", therapistsArray);
      }
    } catch (error) {
      console.error("Gagal memuat data terapis:", error);
      setTherapistsList([]);
    }
  }, [selectedOutletId]);

  // Fungsi untuk fetch data layanan
  const fetchServices = useCallback(async () => {
    try {
      const url = selectedOutletId ? `/api/services?outletId=${selectedOutletId}` : '/api/services';
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const servicesArray = data?.services || [];

      if (Array.isArray(servicesArray)) {
        setServicesList(servicesArray.map(s => ({ id: s.id, name: s.name, price: s.price })));
      } else {
        console.error("Properti 'services' dalam data API bukan array:", servicesArray);
      }
    } catch (error) {
      console.error("Gagal memuat data layanan:", error);
      setServicesList([]);
    }
  }, [selectedOutletId]);

  // Fungsi untuk fetch data dari API
  const fetchTransactionHistory = useCallback(async () => {
    setIsLoading(true);
    try {
      // Buat URL dengan semua filter yang diperlukan
      let url = selectedOutletId ? `/api/transactions?outletId=${selectedOutletId}` : '/api/transactions';

      // Tambahkan filter tanggal jika ada
      if (startDateFilter) {
        url += `&startDate=${startDateFilter}`;
      }
      if (endDateFilter) {
        url += `&endDate=${endDateFilter}`;
      }

      // Tambahkan filter terapis jika ada
      if (therapistFilter) {
        url += `&therapistId=${therapistFilter}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Akses array transaksi dengan pengecekan null/undefined
      const transactionsArray = data?.transactions || [];

      // Pastikan transactionsArray adalah array sebelum mencoba sort dan set state
      if (Array.isArray(transactionsArray)) {
        // Urutkan jika API belum mengurutkan
        // Gunakan createdAt dari Prisma untuk sorting
        transactionsArray.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        setHistory(transactionsArray); // Set state dengan array transaksi
        // Nonaktifkan toast sukses fetch agar tidak terlalu ramai
        // toast.success(data.message || "Riwayat transaksi berhasil dimuat.");
      } else {
        console.error("Properti 'transactions' dalam data API bukan array:", transactionsArray);
        throw new Error("Format data transaksi dari API tidak sesuai."); // Lemparkan error
      }

    } catch (error) {
      console.error("Gagal memuat riwayat transaksi dari API:", error);
      toast.error("Gagal memuat riwayat transaksi. Coba lagi nanti.");
      setHistory([]); // Reset jika error
    } finally {
      setIsLoading(false);
    }
  }, [selectedOutletId, startDateFilter, endDateFilter, therapistFilter]);

  // Load data dari API saat komponen mount atau outlet berubah
  useEffect(() => {
    fetchTransactionHistory();
  }, [fetchTransactionHistory]);

  // Load data terapis saat komponen mount atau outlet berubah
  useEffect(() => {
    fetchTherapists();
  }, [fetchTherapists]);

  // Load data layanan saat komponen mount atau outlet berubah
  useEffect(() => {
    fetchServices();
  }, [fetchServices]);

  // Effect untuk menutup dropdown terapis saat mengklik di luar
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (therapistDropdownRef.current && !therapistDropdownRef.current.contains(event.target as Node)) {
        // Menutup dropdown dengan menghapus class dropdown-open
        const dropdown = therapistDropdownRef.current.querySelector('.dropdown') as HTMLElement;
        if (dropdown) dropdown.classList.remove('dropdown-open');
      }
    }

    // Tambahkan event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Cleanup event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Filter history berdasarkan searchTerm, date range, dan therapist
  const filteredHistory = useMemo(() => {
    // Pastikan history tidak undefined
    if (!history || history.length === 0) {
       return [];
    }

    let filtered = [...history];

    // Filter berdasarkan searchTerm
    if (searchTerm && searchTerm.trim()) {
      const lowerCaseSearchTerm = searchTerm.toLowerCase();
      filtered = filtered.filter(trx => {
        // Cari di ID transaksi
        const matchesId = trx.displayId?.toLowerCase().includes(lowerCaseSearchTerm);
        
        // Cari di nama pelanggan
        const matchesCustomer = trx.customer?.name && trx.customer.name.toLowerCase().includes(lowerCaseSearchTerm);
        
        // Cari di nama terapis
        const matchesTherapist = trx.therapist?.name && trx.therapist.name.toLowerCase().includes(lowerCaseSearchTerm);
        
        // Cari di nama layanan/items
        const matchesService = trx.items && trx.items.some(item => 
          item.name && item.name.toLowerCase().includes(lowerCaseSearchTerm)
        );
        
        // Cari di transactionItems jika ada
        const matchesTransactionItems = trx.transactionItems && trx.transactionItems.some(item => 
          item.service?.name && item.service.name.toLowerCase().includes(lowerCaseSearchTerm)
        );
        
        // Cari di tanggal (format yang user-friendly)
        const matchesDate = (() => {
          try {
            const dateObj = parseISO(trx.createdAt);
            const formattedDate = format(dateObj, 'dd MMM yyyy HH:mm').toLowerCase();
            return formattedDate.includes(lowerCaseSearchTerm);
          } catch {
            return trx.createdAt.toLowerCase().includes(lowerCaseSearchTerm);
          }
        })();
        
        // Cari di total amount
        const matchesTotal = (trx.totalAmount?.toString() ?? '').includes(lowerCaseSearchTerm);
        
        return matchesId || matchesCustomer || matchesTherapist || matchesService || matchesTransactionItems || matchesDate || matchesTotal;
      });
    }

    // Filter berdasarkan tanggal mulai (client-side backup filter)
    if (startDateFilter) {
      const startDate = startOfDay(parseISO(startDateFilter));
      filtered = filtered.filter(trx => {
        try {
          const trxDate = parseISO(trx.createdAt);
          return trxDate >= startDate;
        } catch {
          return true; // Jika parsing gagal, tetap tampilkan
        }
      });
    }

    // Filter berdasarkan tanggal akhir (client-side backup filter)
    if (endDateFilter) {
      const endDate = endOfDay(parseISO(endDateFilter));
      filtered = filtered.filter(trx => {
        try {
          const trxDate = parseISO(trx.createdAt);
          return trxDate <= endDate;
        } catch {
          return true; // Jika parsing gagal, tetap tampilkan
        }
      });
    }

    // Filter berdasarkan terapis (client-side backup filter)
    if (therapistFilter) {
      filtered = filtered.filter(trx => trx.therapist?.id === therapistFilter);
    }

    return filtered;
  }, [history, searchTerm, startDateFilter, endDateFilter, therapistFilter]);

  // Hitung total halaman berdasarkan data yang difilter
  useEffect(() => {
    if (filteredHistory.length === 0) {
      setTotalPages(1);
    } else {
      setTotalPages(Math.ceil(filteredHistory.length / itemsPerPage));
    }
    // Reset ke halaman pertama saat filter berubah
    setCurrentPage(1);
  }, [filteredHistory, itemsPerPage]);

  // Paginate data yang sudah difilter
  const paginatedHistory = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredHistory.slice(startIndex, endIndex);
  }, [filteredHistory, currentPage, itemsPerPage]);

  const handleViewReceipt = async (trxData: TransactionReceiptData) => {
    try {
      // Ambil data transaksi lengkap dari API untuk mendapatkan diskon dan biaya tambahan
      const response = await fetch(`/api/transactions/${trxData.id}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const transactionDetail = await response.json();
      console.log('Transaction details from API:', transactionDetail);

      // Konversi TransactionReceiptData ke ReceiptProps dengan data lengkap dari API
      const propsForReceipt: ReceiptProps = {
        outletName: trxData.outlet?.name || "Break Time Spa & Massage",
        outletAddress: trxData.outlet?.address,
        outletPhone: trxData.outlet?.phone,
        transactionId: trxData.displayId || (typeof trxData.id === 'number' ? `TR${String(trxData.id).padStart(7, '0')}` : String(trxData.id)),
        dateTime: new Date(trxData.createdAt).toLocaleString('id-ID', {
          year: 'numeric', month: 'short', day: 'numeric',
          hour: '2-digit', minute: '2-digit'
        }),
        customerName: trxData.customer?.name || 'Pelanggan',
        customers: trxData.customer ? [{
          id: trxData.customer.id,
          name: trxData.customer.name,
          phone: trxData.customer.phone || ''
        }] : [],
        therapistName: trxData.therapist?.name || 'Tidak Diketahui',
        items: transactionDetail.items || trxData.items || [
          { name: "Layanan/Produk Umum", price: trxData.totalAmount || 0, quantity: 1 }
        ],
        subtotal: transactionDetail.subtotal || trxData.subtotal || trxData.totalAmount || 0,
        // Gunakan data diskon dari API
        discountType: transactionDetail.discountType || 'none',
        discountValue: transactionDetail.discountValue || 0,
        discountAmount: transactionDetail.discountAmount || 0,
        // Gunakan data biaya tambahan dari API
        additionalCharge: transactionDetail.additionalCharge || 0,
        tax: transactionDetail.tax || 0,
        total: trxData.totalAmount || 0,
        paymentMethod: trxData.paymentMethod || "CASH",
        note: trxData.note || "Terima kasih atas kunjungan Anda!",
        createdByName: trxData.createdBy?.name
      };

      setSelectedReceiptData(propsForReceipt);
      setIsReceiptModalOpen(true);
    } catch (error) {
      console.error('Error fetching transaction details:', error);
      toast.error('Gagal memuat detail transaksi');

      // Fallback ke data yang ada jika gagal mengambil dari API
      const propsForReceipt: ReceiptProps = {
        outletName: trxData.outlet?.name || "Break Time Spa & Massage",
        outletAddress: trxData.outlet?.address,
        outletPhone: trxData.outlet?.phone,
        transactionId: trxData.displayId || (typeof trxData.id === 'number' ? `TR${String(trxData.id).padStart(7, '0')}` : String(trxData.id)),
        dateTime: new Date(trxData.createdAt).toLocaleString('id-ID', {
          year: 'numeric', month: 'short', day: 'numeric',
          hour: '2-digit', minute: '2-digit'
        }),
        customerName: trxData.customer?.name || 'Pelanggan',
        customers: trxData.customer ? [{
          id: trxData.customer.id,
          name: trxData.customer.name,
          phone: trxData.customer.phone || ''
        }] : [],
        therapistName: trxData.therapist?.name || 'Tidak Diketahui',
        items: trxData.items || [
          { name: "Layanan/Produk Umum", price: trxData.totalAmount || 0, quantity: 1 }
        ],
        subtotal: trxData.subtotal ?? trxData.totalAmount ?? 0,
        discountType: 'none',
        discountValue: 0,
        discountAmount: 0,
        additionalCharge: 0,
        tax: 0,
        total: trxData.totalAmount || 0,
        paymentMethod: trxData.paymentMethod || "CASH",
        note: trxData.note || "Terima kasih atas kunjungan Anda!",
        createdByName: trxData.createdBy?.name
      };

      setSelectedReceiptData(propsForReceipt);
      setIsReceiptModalOpen(true);
    }
  };

  const handleCloseModal = () => {
    setIsReceiptModalOpen(false);
    setSelectedReceiptData(null);
  };

  // --- Fungsi Baru untuk Membuka Modal Konfirmasi Hapus ---
  const openDeleteConfirmationModal = (transaction: TransactionReceiptData) => {
    setTransactionToDelete(transaction); // Simpan seluruh data transaksi
    setIsDeleteModalOpen(true);
  };

  // --- Fungsi Baru untuk Menutup Modal Konfirmasi Hapus ---
  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setTransactionToDelete(null);
    setIsDeleting(false); // Reset loading state juga
  };

  // --- Fungsi untuk Navigasi Pagination ---
  const goToPage = (page: number) => {
    if (page < 1) page = 1;
    if (page > totalPages) page = totalPages;
    setCurrentPage(page);
    // Scroll ke atas tabel
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const goToPreviousPage = () => goToPage(currentPage - 1);
  const goToNextPage = () => goToPage(currentPage + 1);
  const goToFirstPage = () => goToPage(1);
  const goToLastPage = () => goToPage(totalPages);

  // Format tanggal untuk tampilan
  const formatDateDisplay = (dateStr: string): string => {
    if (!dateStr) return '';
    try {
      const date = parseISO(dateStr);
      return format(date, 'dd MMMM yyyy', { locale: idLocale });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateStr;
    }
  };

  // Fungsi untuk mendapatkan tanggal hari ini dengan format YYYY-MM-DD
  const getTodayDate = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Fungsi untuk menangani perubahan filter tanggal
  const handleDateFilterChange = () => {
    setCurrentPage(1); // Reset ke halaman pertama
    fetchTransactionHistory();
  };

  // Fungsi untuk menangani perubahan filter terapis
  const handleTherapistFilterChange = (therapistId: string) => {
    setTherapistFilter(therapistId);
    setCurrentPage(1); // Reset ke halaman pertama
    fetchTransactionHistory();
  };

  // Filter terapis berdasarkan kata kunci pencarian
  const filteredTherapists = useMemo(() => {
    if (!therapistSearchTerm.trim()) return therapistsList;
    return therapistsList.filter(therapist =>
      therapist.name.toLowerCase().includes(therapistSearchTerm.toLowerCase())
    );
  }, [therapistsList, therapistSearchTerm]);

  // Fungsi untuk mereset semua filter
  const resetFilters = () => {
    setStartDateFilter('');
    setEndDateFilter('');
    setTherapistFilter('');
    setTherapistSearchTerm('');
    setSearchTerm('');
    setCurrentPage(1);
    fetchTransactionHistory();
  };

  // Fungsi untuk mengunduh data transaksi dalam format Excel
  const handleDownloadExcel = async () => {
    try {
      // Tampilkan toast loading
      toast.loading('Menyiapkan file Excel...');

      // Buat URL dengan semua filter yang diperlukan
      let url = '/api/transactions/export-excel';
      const params = new URLSearchParams();

      // Tambahkan filter outlet jika ada
      if (selectedOutletId) {
        params.append('outletId', selectedOutletId);
      }

      // Tambahkan filter tanggal jika ada
      if (startDateFilter) {
        params.append('startDate', startDateFilter);
      }
      if (endDateFilter) {
        params.append('endDate', endDateFilter);
      }

      // Tambahkan filter terapis jika ada
      if (therapistFilter) {
        params.append('therapistId', therapistFilter);
      }

      // Buat URL lengkap
      const fullUrl = `${url}?${params.toString()}`;
      console.log(`Downloading Excel with URL: ${fullUrl}`);

      // Buat tanggal untuk nama file
      const dateStr = format(new Date(), 'yyyyMMdd_HHmmss');

      // Lakukan fetch dengan responseType blob
      const response = await fetch(fullUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Konversi response ke blob
      const blob = await response.blob();

      // Buat URL objek dari blob
      const downloadUrl = window.URL.createObjectURL(blob);

      // Buat elemen anchor untuk download
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = `Riwayat_Transaksi_${dateStr}.xlsx`;
      document.body.appendChild(a);
      a.click();

      // Cleanup
      window.URL.revokeObjectURL(downloadUrl);
      document.body.removeChild(a);

      // Tampilkan toast sukses
      toast.success('File Excel berhasil diunduh');
    } catch (error) {
      console.error('Error downloading Excel:', error);
      toast.error('Gagal mengunduh file Excel. Silakan coba lagi.');
    } finally {
      // Dismiss toast loading
      toast.dismiss();
    }
  };

  // --- Fungsi untuk Eksekusi Hapus (Dipanggil dari Modal) ---
  const executeDeleteTransaction = async () => {
    if (!transactionToDelete) return; // Pastikan ada transaksi yang dipilih

    setIsDeleting(true); // Mulai loading
    try {
      const transactionApiId = transactionToDelete.id; // Ambil ID asli
      const displayId = transactionToDelete.displayId || (typeof transactionApiId === 'number' ? `TR${String(transactionApiId).padStart(7, '0')}` : transactionApiId);

      const response = await fetch(`/api/transactions/${transactionApiId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Gagal menghapus transaksi.' }));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      toast.success(`Transaksi ${displayId} berhasil dihapus.`);
      closeDeleteModal(); // Tutup modal setelah sukses
      await fetchTransactionHistory(); // Refresh data

    } catch (error: unknown) {
      console.error("Gagal menghapus transaksi:", error);
      const errorMessage = error instanceof Error ? error.message : 'Kesalahan tidak diketahui';
      toast.error(`Gagal menghapus transaksi: ${errorMessage}`);
      // Jangan tutup modal jika error agar user tahu
    } finally {
      setIsDeleting(false); // Hentikan loading
    }
  };

  // --- Fungsi untuk membuka modal edit ---
  const openEditModal = (transaction: TransactionReceiptData) => {
    setTransactionToEdit(transaction);
    setEditCommission(transaction.therapistCommissionEarned || 0);
    setEditAdditionalCharge(transaction.additionalCharge || 0);
    setEditItems(transaction.items || []);
    setEditDiscountType(transaction.discountType || 'none');
    setEditDiscountValue(transaction.discountValue || 0);
    setEditTherapistId(transaction.therapist?.id || '');
    setIsEditModalOpen(true);
  };

  // --- Fungsi untuk menutup modal edit ---
  const closeEditModal = () => {
    setIsEditModalOpen(false);
    setTransactionToEdit(null);
    setEditCommission(0);
    setEditAdditionalCharge(0);
    setEditItems([]);
    setEditDiscountType('none');
    setEditDiscountValue(0);
    setEditTherapistId('');
    setIsUpdating(false);
  };

  // --- Fungsi untuk eksekusi update transaksi ---
  const executeUpdateTransaction = async () => {
    if (!transactionToEdit) return;

    // Validasi input
    if (editCommission < 0) {
      toast.error('Komisi tidak boleh negatif');
      return;
    }

    if (editAdditionalCharge < 0) {
      toast.error('Biaya tambahan tidak boleh negatif');
      return;
    }

    // Validasi terapis jika dipilih
    if (editTherapistId && editTherapistId !== transactionToEdit.therapist?.id) {
      const selectedTherapist = therapistsList.find(t => t.id === editTherapistId);
      if (!selectedTherapist) {
        toast.error('Terapis yang dipilih tidak valid');
        return;
      }
    }

    setIsUpdating(true);
    try {
      const transactionApiId = transactionToEdit.id;
      const displayId = transactionToEdit.displayId || (typeof transactionApiId === 'number' ? `TR${String(transactionApiId).padStart(7, '0')}` : transactionApiId);

      // Hitung discount amount berdasarkan type dan value
      const itemsTotal = editItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      let discountAmount = 0;
      if (editDiscountType === 'percentage') {
        discountAmount = (itemsTotal * editDiscountValue) / 100;
      } else if (editDiscountType === 'fixed') {
        discountAmount = editDiscountValue;
      }

      const response = await fetch(`/api/transactions/${transactionApiId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          commission: editCommission,
          additionalCharge: editAdditionalCharge,
          items: editItems,
          discountType: editDiscountType,
          discountValue: editDiscountValue,
          discountAmount: discountAmount,
          therapistId: editTherapistId || undefined
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Gagal memperbarui transaksi.' }));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const responseData = await response.json();
      
      // Buat pesan sukses yang lebih informatif
      let successMessage = `Transaksi ${displayId} berhasil diperbarui`;
      const changes = [];
      
      if (editTherapistId && editTherapistId !== transactionToEdit.therapist?.id) {
        const newTherapist = therapistsList.find(t => t.id === editTherapistId);
        if (newTherapist) {
          changes.push(`terapis → ${newTherapist.name}`);
        }
      }
      
      if (changes.length > 0) {
        successMessage += `: ${changes.join(', ')}`;
      }
      
      toast.success(successMessage);
      closeEditModal();
      await fetchTransactionHistory(); // Refresh data

    } catch (error: unknown) {
      console.error("Gagal memperbarui transaksi:", error);
      const errorMessage = error instanceof Error ? error.message : 'Kesalahan tidak diketahui';
      toast.error(`Gagal memperbarui transaksi: ${errorMessage}`);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <motion.div
      className="container mx-auto p-4 md:p-6"
      data-theme="breaktime"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      <motion.div variants={fadeInUp} className="flex flex-col md:flex-row justify-between items-start md:items-center gap-3 md:gap-0 mb-4 md:mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-gray-800">Riwayat Transaksi</h1>
        <div className="flex flex-wrap gap-2 w-full md:w-auto">
          <PermissionGuard module="transaksi" action="read">
            <button
              type="button"
              className="btn btn-success btn-xs md:btn-sm shadow-md flex-1 md:flex-none text-xs md:text-sm"
              onClick={handleDownloadExcel}
            >
              <FiDownload className="mr-1"/>
              <span className="hidden xs:inline">Download Excel</span>
              <span className="inline xs:hidden">Excel</span>
            </button>
          </PermissionGuard>
        </div>
      </motion.div>

       {/* Filter Bar */}
       <motion.div variants={fadeInUp} className="mb-6 space-y-4">
          {/* Search Bar */}
          <div className="form-control">
             <div className="join w-full lg:w-1/2">
                <input
                   type="text"
                   placeholder="Cari ID, Nama Pelanggan, Tanggal, Total..."
                   className="input input-bordered join-item w-full placeholder:text-gray-400 text-gray-700"
                   value={searchTerm}
                   onChange={(e) => setSearchTerm(e.target.value)}
                />
                <button className="btn btn-ghost join-item">
                  <FiSearch className="text-gray-500"/>
                </button>
             </div>
          </div>

          {/* Advanced Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Date Range Filter - Start Date */}
            <div className="form-control">
              <label className="label">
                <span className="label-text flex items-center gap-1 text-gray-600"><FiCalendar /> Tanggal Mulai</span>
              </label>
              <div className="dropdown dropdown-end w-full">
                <div tabIndex={0} role="button" className="btn btn-outline w-full justify-between border-gray-300 text-gray-700">
                  {startDateFilter ? formatDateDisplay(startDateFilter) : 'Pilih tanggal mulai'}
                  <FiCalendar className="ml-2" />
                </div>
                <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-64">
                  <div className="card-body p-2">
                    <div className="w-full max-w-[300px] mx-auto">
                      <div className="flex justify-between items-center py-2 mb-2">
                        <button
                          className="btn btn-sm btn-ghost"
                          onClick={(e) => {
                            e.preventDefault();
                            const currentDate = startDateFilter ? new Date(startDateFilter) : new Date();
                            currentDate.setMonth(currentDate.getMonth() - 1);
                            setStartDateFilter(format(currentDate, 'yyyy-MM-dd'));
                            handleDateFilterChange();
                          }}
                        >
                          «
                        </button>
                        <div className="text-sm font-medium">
                          {startDateFilter ? format(parseISO(startDateFilter), 'MMMM yyyy', { locale: idLocale }) : format(new Date(), 'MMMM yyyy', { locale: idLocale })}
                        </div>
                        <button
                          className="btn btn-sm btn-ghost"
                          onClick={(e) => {
                            e.preventDefault();
                            const currentDate = startDateFilter ? new Date(startDateFilter) : new Date();
                            currentDate.setMonth(currentDate.getMonth() + 1);
                            setStartDateFilter(format(currentDate, 'yyyy-MM-dd'));
                            handleDateFilterChange();
                          }}
                        >
                          »
                        </button>
                      </div>
                      <div className="grid grid-cols-7 gap-1">
                        {/* Hari dalam seminggu */}
                        {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                          <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                        ))}

                        {/* Tanggal */}
                        {Array.from({ length: 42 }, (_, i) => {
                          const currentDate = startDateFilter ? new Date(startDateFilter) : new Date();
                          const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                          const startingDayOfWeek = firstDayOfMonth.getDay();
                          const day = i - startingDayOfWeek + 1;
                          const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
                          const isCurrentMonth = date.getMonth() === currentDate.getMonth();

                          // Format tanggal untuk perbandingan
                          const dateStr = format(date, 'yyyy-MM-dd');
                          const isSelected = startDateFilter === dateStr;

                          return (
                            <button
                              key={i}
                              className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all
                                      ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isCurrentMonth && !isSelected ? 'hover:bg-base-200' : ''}`}
                              onClick={(e) => {
                                e.preventDefault();
                                if (isCurrentMonth) {
                                  setStartDateFilter(dateStr);
                                  handleDateFilterChange();
                                }
                              }}
                              disabled={!isCurrentMonth}
                            >
                              {date.getDate()}
                            </button>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Date Range Filter - End Date */}
            <div className="form-control">
              <label className="label">
                <span className="label-text flex items-center gap-1 text-gray-600"><FiCalendar /> Tanggal Akhir</span>
              </label>
              <div className="dropdown dropdown-end w-full">
                <div tabIndex={0} role="button" className="btn btn-outline w-full justify-between border-gray-300 text-gray-700">
                  {endDateFilter ? formatDateDisplay(endDateFilter) : 'Pilih tanggal akhir'}
                  <FiCalendar className="ml-2" />
                </div>
                <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-64">
                  <div className="card-body p-2">
                    <div className="w-full max-w-[300px] mx-auto">
                      <div className="flex justify-between items-center py-2 mb-2">
                        <button
                          className="btn btn-sm btn-ghost"
                          onClick={(e) => {
                            e.preventDefault();
                            const currentDate = endDateFilter ? new Date(endDateFilter) : new Date();
                            currentDate.setMonth(currentDate.getMonth() - 1);
                            setEndDateFilter(format(currentDate, 'yyyy-MM-dd'));
                            handleDateFilterChange();
                          }}
                        >
                          «
                        </button>
                        <div className="text-sm font-medium">
                          {endDateFilter ? format(parseISO(endDateFilter), 'MMMM yyyy', { locale: idLocale }) : format(new Date(), 'MMMM yyyy', { locale: idLocale })}
                        </div>
                        <button
                          className="btn btn-sm btn-ghost"
                          onClick={(e) => {
                            e.preventDefault();
                            const currentDate = endDateFilter ? new Date(endDateFilter) : new Date();
                            currentDate.setMonth(currentDate.getMonth() + 1);
                            setEndDateFilter(format(currentDate, 'yyyy-MM-dd'));
                            handleDateFilterChange();
                          }}
                        >
                          »
                        </button>
                      </div>
                      <div className="grid grid-cols-7 gap-1">
                        {/* Hari dalam seminggu */}
                        {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                          <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                        ))}

                        {/* Tanggal */}
                        {Array.from({ length: 42 }, (_, i) => {
                          const currentDate = endDateFilter ? new Date(endDateFilter) : new Date();
                          const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                          const startingDayOfWeek = firstDayOfMonth.getDay();
                          const day = i - startingDayOfWeek + 1;
                          const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
                          const isCurrentMonth = date.getMonth() === currentDate.getMonth();

                          // Format tanggal untuk perbandingan
                          const dateStr = format(date, 'yyyy-MM-dd');
                          const isSelected = endDateFilter === dateStr;

                          return (
                            <button
                              key={i}
                              className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all
                                      ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isCurrentMonth && !isSelected ? 'hover:bg-base-200' : ''}`}
                              onClick={(e) => {
                                e.preventDefault();
                                if (isCurrentMonth) {
                                  setEndDateFilter(dateStr);
                                  handleDateFilterChange();
                                }
                              }}
                              disabled={!isCurrentMonth}
                            >
                              {date.getDate()}
                            </button>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Therapist Filter with Search */}
            <div className="form-control" ref={therapistDropdownRef}>
              <label className="label">
                <span className="label-text flex items-center gap-1 text-gray-600"><FiUser /> Terapis</span>
              </label>
              <div className="dropdown dropdown-bottom w-full">
                <div className="relative w-full">
                  <input
                    type="text"
                    placeholder="Cari terapis..."
                    className="input input-bordered w-full text-gray-700 border-gray-300 pr-16"
                    value={therapistSearchTerm}
                    onChange={(e) => setTherapistSearchTerm(e.target.value)}
                    onClick={(e) => {
                      // Prevent dropdown from closing when clicking on input
                      e.stopPropagation();
                      const dropdown = e.currentTarget.closest('.dropdown') as HTMLElement;
                      if (dropdown) dropdown.classList.add('dropdown-open');
                    }}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    {therapistSearchTerm && (
                      <button
                        className="mr-4 text-gray-400 hover:text-gray-600"
                        onClick={() => setTherapistSearchTerm('')}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </button>
                    )}
                    <FiSearch className="text-gray-500 pointer-events-none" />
                  </div>
                </div>
                <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-full max-h-60 overflow-y-auto mt-1" onClick={(e) => e.stopPropagation()}>
                  <li>
                    <a
                      onClick={() => {
                        handleTherapistFilterChange('');
                        setTherapistSearchTerm('Semua Terapis');
                      }}
                      className={!therapistFilter ? 'bg-teal-50 text-teal-700' : ''}
                    >
                      Semua Terapis
                    </a>
                  </li>
                  {filteredTherapists.length > 0 ? (
                    filteredTherapists.map(t => (
                      <li key={t.id}>
                        <a
                          onClick={() => {
                            handleTherapistFilterChange(t.id);
                            setTherapistSearchTerm(t.name);
                          }}
                          className={therapistFilter === t.id ? 'bg-teal-50 text-teal-700' : ''}
                        >
                          {t.name}
                        </a>
                      </li>
                    ))
                  ) : therapistSearchTerm ? (
                    <li className="text-center py-2">
                      <span className="text-gray-500 text-sm">Tidak ada terapis yang sesuai dengan pencarian.</span>
                      <button onClick={() => setTherapistSearchTerm('')} className="btn btn-xs btn-outline mt-1">Reset Pencarian</button>
                    </li>
                  ) : (
                    <li className="text-center py-2">
                      <span className="text-gray-500 text-sm">Pilih terapis</span>
                    </li>
                  )}
                </ul>
              </div>

              {/* Menampilkan terapis yang dipilih jika tidak menampilkan "Semua Terapis" */}
              {therapistFilter && (
                <div className="mt-2 p-2 bg-teal-50 rounded-md border border-teal-100 flex justify-between items-center">
                  <div>
                    <span className="font-medium text-teal-700">
                      {therapistsList.find(t => t.id === therapistFilter)?.name || 'Terapis'}
                    </span>
                  </div>
                  <button
                    className="btn btn-xs btn-ghost text-teal-700"
                    onClick={() => {
                      handleTherapistFilterChange('');
                      setTherapistSearchTerm('');
                    }}
                  >
                    Ganti
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Reset Filters Button */}
          {(startDateFilter || endDateFilter || therapistFilter || searchTerm) && (
            <div className="flex justify-end">
              <button
                onClick={resetFilters}
                className="btn btn-sm btn-ghost text-gray-600 hover:bg-gray-100"
              >
                Reset Filter
              </button>
            </div>
          )}
       </motion.div>

      {isLoading ? (
         <div className="flex justify-center items-center h-64"><span className="loading loading-spinner loading-lg text-teal-500"></span></div>
      ) : (
        <motion.div variants={fadeInUp} className="bg-white shadow border border-gray-200 rounded-lg">
          {/* Tabel untuk layar besar (md dan di atasnya) */}
          <div className="hidden md:block overflow-x-auto">
            <table className="table table-zebra w-full">
              {/* head */}
              <thead className="bg-gray-100 text-gray-700">
                <tr>
                  <th>Tanggal & Waktu</th>
                  <th>ID Transaksi</th>
                  <th>Pelanggan</th>
                  <th>Terapis</th>
                  <th>Layanan</th>
                  <th>Komisi</th>
                  <th>Biaya Tambahan</th>
                  <th>Total</th>
                  <th className="text-center">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {/* Tampilkan pesan jika tidak ada hasil search atau history kosong */}
                {filteredHistory.length === 0 && (
                   <tr><td colSpan={9} className="text-center text-gray-500 py-10">
                      <FiInbox className="w-10 h-10 mx-auto mb-2 text-gray-300"/>
                      {searchTerm || startDateFilter || endDateFilter || therapistFilter ?
                        `Tidak ada transaksi yang sesuai dengan filter yang dipilih.` :
                        'Belum ada riwayat transaksi.'}
                   </td></tr>
                )}
                {paginatedHistory && paginatedHistory.length > 0 ? paginatedHistory.map((trx) => {
                  // Coba parse tanggal di sini untuk validasi dan reusability
                  let formattedDate = 'Invalid Date';
                  try {
                    // Gunakan createdAt dari Prisma karena lebih konsisten
                    const dateObj = parseISO(trx.createdAt);
                    formattedDate = format(dateObj, 'dd MMM yyyy HH:mm');
                  } catch (error) {
                    console.error("Error parsing date:", trx.createdAt, error);
                  }

                  // Gabungkan nama layanan (handle jika items kosong atau null)
                  const serviceNames = trx.items && trx.items.length > 0
                    ? trx.items.map(item => `${item.name} (${item.quantity}x)`).join(', ')
                    : '-'; // Tampilkan strip jika tidak ada item

                  return (
                    <tr key={trx.id} className="hover:bg-gray-50 text-gray-600 text-sm">
                      <td>{formattedDate}</td>
                      <td className="font-mono">{trx.displayId || (typeof trx.id === 'number' ? `TR${String(trx.id).padStart(7, '0')}` : trx.id)}</td>
                      <td>{trx.customer?.name || 'Pelanggan'}</td>
                      <td>{trx.therapist?.name || '-'}</td>
                      <td className="max-w-xs truncate tooltip tooltip-left" data-tip={serviceNames}>{serviceNames}</td>
                      <td className="font-medium text-green-600">
                        {trx.therapistCommissionEarned 
                          ? new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(trx.therapistCommissionEarned)
                          : '-'
                        }
                      </td>
                      <td className="font-medium text-gray-700">
                        {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(trx.additionalCharge || 0)}
                      </td>
                      <td className="font-medium text-gray-700">
                        {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(trx.totalAmount || 0)}
                      </td>
                      <td className="text-center space-x-1">
                         <button
                            className="btn btn-xs btn-ghost text-teal-600 tooltip tooltip-left"
                            data-tip="Lihat Struk"
                            onClick={() => handleViewReceipt(trx)}
                            type="button"
                         >
                            <FiEye />
                         </button>
                         <PermissionGuard module="transaksi" action="update">
                           <button
                              className="btn btn-xs btn-ghost text-blue-600 tooltip tooltip-left"
                              data-tip="Edit"
                              onClick={() => openEditModal(trx)}
                              type="button"
                           >
                              <FiEdit2 />
                           </button>
                         </PermissionGuard>
                         <PermissionGuard module="transaksi" action="delete">
                           <button
                              className="btn btn-xs btn-ghost text-red-600 tooltip tooltip-left"
                              data-tip="Hapus"
                              onClick={() => openDeleteConfirmationModal(trx)} // Panggil fungsi buka modal
                              type="button"
                           >
                              <FiTrash2 />
                           </button>
                         </PermissionGuard>
                      </td>
                    </tr>
                  );
                }) : null}
              </tbody>
            </table>
          </div>

          {/* Tampilan kartu untuk layar kecil (sm dan di bawahnya) */}
          <div className="md:hidden">
            {filteredHistory.length === 0 ? (
              <div className="text-center text-gray-500 py-10">
                <FiInbox className="w-10 h-10 mx-auto mb-2 text-gray-300"/>
                {searchTerm || startDateFilter || endDateFilter || therapistFilter ?
                  `Tidak ada transaksi yang sesuai dengan filter yang dipilih.` :
                  'Belum ada riwayat transaksi.'}
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-4 p-4">
                {paginatedHistory.map((trx) => {
                  // Format tanggal
                  let formattedDate = 'Invalid Date';
                  try {
                    const dateObj = parseISO(trx.createdAt);
                    formattedDate = format(dateObj, 'dd MMM yyyy HH:mm');
                  } catch (error) {
                    console.error("Error parsing date:", trx.createdAt, error);
                  }

                  // Format layanan
                  const serviceNames = trx.items && trx.items.length > 0
                    ? trx.items.map(item => `${item.name} (${item.quantity}x)`).join(', ')
                    : '-';

                  // Format ID transaksi
                  const transactionId = trx.displayId || (typeof trx.id === 'number' ? `TR${String(trx.id).padStart(7, '0')}` : trx.id);

                  // Format total
                  const formattedTotal = new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(trx.totalAmount || 0);

                  return (
                    <div key={trx.id} className="card bg-base-100 shadow-sm border border-gray-200">
                      <div className="card-body p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="card-title text-sm font-mono text-gray-700">{transactionId}</h3>
                          <span className="text-sm font-medium text-gray-700">{formattedTotal}</span>
                        </div>

                        <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-sm mb-3">
                          <div className="text-gray-500">Tanggal:</div>
                          <div className="text-gray-700">{formattedDate}</div>

                          <div className="text-gray-500">Pelanggan:</div>
                          <div className="text-gray-700 truncate">{trx.customer?.name || 'Pelanggan'}</div>

                          <div className="text-gray-500">Terapis:</div>
                          <div className="text-gray-700 truncate">{trx.therapist?.name || '-'}</div>

                          <div className="text-gray-500">Layanan:</div>
                          <div className="text-gray-700 truncate tooltip tooltip-left" data-tip={serviceNames}>{serviceNames}</div>

                          <div className="text-gray-500">Komisi:</div>
                          <div className="text-green-600 font-medium">
                            {trx.therapistCommissionEarned 
                              ? new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(trx.therapistCommissionEarned)
                              : '-'
                            }
                          </div>

                          <div className="text-gray-500">Biaya Tambahan:</div>
                          <div className="text-blue-600 font-medium">
                            {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(trx.additionalCharge || 0)}
                          </div>
                        </div>

                        <div className="card-actions justify-end">
                          <button
                            className="btn btn-xs btn-ghost text-teal-600"
                            onClick={() => handleViewReceipt(trx)}
                            type="button"
                          >
                            <FiEye className="mr-1" /> Lihat
                          </button>

                          <PermissionGuard module="transaksi" action="update">
                            <button
                              className="btn btn-xs btn-ghost text-blue-600"
                              onClick={() => openEditModal(trx)}
                              type="button"
                            >
                              <FiEdit2 className="mr-1" /> Edit
                            </button>
                          </PermissionGuard>

                          <PermissionGuard module="transaksi" action="delete">
                            <button
                              className="btn btn-xs btn-ghost text-red-600"
                              onClick={() => openDeleteConfirmationModal(trx)}
                              type="button"
                            >
                              <FiTrash2 className="mr-1" /> Hapus
                            </button>
                          </PermissionGuard>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Pagination */}
          {filteredHistory.length > 0 && (
            <div className="flex flex-col sm:flex-row justify-between items-center px-4 py-4 bg-gray-50 border-t border-gray-200 text-sm">
              {/* Info halaman dan dropdown items per page */}
              <div className="mb-3 sm:mb-0 flex flex-col sm:flex-row sm:items-center gap-2 text-gray-600 w-full sm:w-auto">
                <span className="text-center sm:text-left">
                  Menampilkan {Math.min(filteredHistory.length, (currentPage - 1) * itemsPerPage + 1)} - {Math.min(filteredHistory.length, currentPage * itemsPerPage)} dari {filteredHistory.length} transaksi
                </span>
                <div className="dropdown dropdown-top dropdown-end self-center">
                  <div tabIndex={0} role="button" className="btn btn-xs btn-ghost hover:bg-gray-100 text-gray-700">
                    {itemsPerPage} / halaman
                  </div>
                  <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-white rounded-box w-32 text-gray-700">
                    {[5, 10, 20, 50, 100].map(value => (
                      <li key={value}>
                        <a
                          className={itemsPerPage === value ? 'bg-gray-100 font-medium' : ''}
                          onClick={() => setItemsPerPage(value)}
                        >
                          {value} / halaman
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Pagination controls */}
              <div className="flex flex-wrap justify-center gap-1 w-full sm:w-auto">
                {/* Versi mobile: Hanya tombol prev/next */}
                <div className="flex sm:hidden gap-2 w-full justify-center">
                  <button
                    className="btn btn-sm btn-ghost hover:bg-gray-100 text-gray-700 disabled:text-gray-300"
                    onClick={goToPreviousPage}
                    disabled={currentPage === 1}
                  >
                    ‹ Sebelumnya
                  </button>
                  <span className="flex items-center px-3 py-1 bg-white border border-gray-200 rounded-md shadow-sm">
                    {currentPage} / {totalPages}
                  </span>
                  <button
                    className="btn btn-sm btn-ghost hover:bg-gray-100 text-gray-700 disabled:text-gray-300"
                    onClick={goToNextPage}
                    disabled={currentPage === totalPages}
                  >
                    Berikutnya ›
                  </button>
                </div>

                {/* Versi desktop: Tombol lengkap */}
                <div className="hidden sm:flex join bg-white shadow-sm">
                  <button
                    className="join-item btn btn-sm btn-ghost hover:bg-gray-100 text-gray-700 disabled:text-gray-300"
                    onClick={goToFirstPage}
                    disabled={currentPage === 1}
                  >
                    «
                  </button>
                  <button
                    className="join-item btn btn-sm btn-ghost hover:bg-gray-100 text-gray-700 disabled:text-gray-300"
                    onClick={goToPreviousPage}
                    disabled={currentPage === 1}
                  >
                    ‹
                  </button>

                  {/* Nomor Halaman */}
                  <div className="join-item flex items-center">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      // Logika untuk menampilkan 5 halaman dengan halaman saat ini di tengah jika memungkinkan
                      let pageToShow;
                      if (totalPages <= 5) {
                        pageToShow = i + 1;
                      } else if (currentPage <= 3) {
                        pageToShow = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageToShow = totalPages - 4 + i;
                      } else {
                        pageToShow = currentPage - 2 + i;
                      }

                      return (
                        <button
                          key={pageToShow}
                          className={`btn btn-sm ${currentPage === pageToShow ? 'btn-primary text-white' : 'btn-ghost text-gray-700 hover:bg-gray-100'} min-w-[2.5rem]`}
                          onClick={() => goToPage(pageToShow)}
                        >
                          {pageToShow}
                        </button>
                      );
                    })}
                  </div>

                  <button
                    className="join-item btn btn-sm btn-ghost hover:bg-gray-100 text-gray-700 disabled:text-gray-300"
                    onClick={goToNextPage}
                    disabled={currentPage === totalPages}
                  >
                    ›
                  </button>
                  <button
                    className="join-item btn btn-sm btn-ghost hover:bg-gray-100 text-gray-700 disabled:text-gray-300"
                    onClick={goToLastPage}
                    disabled={currentPage === totalPages}
                  >
                    »
                  </button>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      )}

      {/* Modal Struk */}
      {isReceiptModalOpen && selectedReceiptData && (
        <dialog id="receipt_modal" className="modal modal-open">
          <div className="modal-box w-11/12 max-w-sm bg-white p-0">
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
               <h3 className="font-semibold text-lg text-gray-800 text-center flex-1">Detail Struk</h3>
               <div>
                  <button onClick={() => handlePrintReceipt()} className="btn btn-sm btn-primary mr-2"><FiPrinter/> Cetak</button>
                  <button onClick={handleCloseModal} className="btn btn-sm btn-ghost">&times;</button>
               </div>
            </div>
            <div className="p-2 max-h-[70vh] overflow-y-auto flex justify-center">
               <div style={{ width: '58mm', margin: '0 auto' }}>
                 <ReceiptComponent ref={receiptRef} {...selectedReceiptData} />
               </div>
            </div>
          </div>
           <form method="dialog" className="modal-backdrop" onClick={handleCloseModal}>
             <button type="button">close</button>
           </form>
        </dialog>
      )}

      {/* === MODAL KONFIRMASI HAPUS BARU === */}
      <dialog id="delete_confirmation_modal" className={`modal ${isDeleteModalOpen ? 'modal-open' : ''}`}>
        <div className="modal-box">
          <h3 className="font-bold text-lg text-error flex items-center">
            <FiAlertTriangle className="mr-2"/> Konfirmasi Hapus
          </h3>
          <p className="py-4">
            Anda yakin ingin menghapus transaksi dengan ID:
            <strong className="font-mono ml-1">{transactionToDelete?.displayId || (typeof transactionToDelete?.id === 'number' ? `TR${String(transactionToDelete.id).padStart(7, '0')}` : transactionToDelete?.id)}</strong>?
            <br />
            Tindakan ini tidak dapat dibatalkan.
          </p>
          <div className="modal-action">
            <button className="btn" onClick={closeDeleteModal} disabled={isDeleting}>Batal</button>
            <button
              className={`btn btn-error ${isDeleting ? 'btn-disabled' : ''}`}
              onClick={executeDeleteTransaction}
              disabled={isDeleting}
            >
              {isDeleting ?
                <><span className="loading loading-spinner loading-xs"></span> Menghapus...</> :
                "Ya, Hapus Transaksi"
              }
            </button>
          </div>
        </div>
        {/* Klik backdrop untuk menutup */}
        <form method="dialog" className="modal-backdrop" onClick={closeDeleteModal}>
           <button type="button" disabled={isDeleting}>close</button>
        </form>
      </dialog>
      {/* === SELESAI MODAL KONFIRMASI HAPUS === */}

      {/* === MODAL EDIT === */}
      <dialog id="edit_modal" className={`modal ${isEditModalOpen ? 'modal-open' : ''}`}>
        <div className="modal-box">
          <h3 className="font-bold text-lg text-blue-600 flex items-center">
            <FiEdit2 className="mr-2"/> Edit Transaksi
          </h3>
          {transactionToEdit && (
            <div className="py-2 mb-4 text-sm text-gray-600">
              <div className="font-mono font-medium text-gray-800">
                {transactionToEdit.displayId || (typeof transactionToEdit.id === 'number' ? `TR${String(transactionToEdit.id).padStart(7, '0')}` : transactionToEdit.id)}
              </div>
              <div>Pelanggan: {transactionToEdit.customer?.name || 'Pelanggan'}</div>
              <div>Terapis: {transactionToEdit.therapist?.name || '-'}</div>
              <div>Total Saat Ini: {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(transactionToEdit.totalAmount || 0)}</div>
            </div>
          )}
          <div className="py-4 space-y-6">
            {/* Edit Terapis */}
            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium text-lg">👨‍⚕️ Ubah Terapis</span>
              </label>
              <select
                className="select select-bordered w-full"
                value={editTherapistId}
                onChange={(e) => setEditTherapistId(e.target.value)}
              >
                <option value="">Pilih terapis...</option>
                {therapistsList.map((therapist) => (
                  <option key={therapist.id} value={therapist.id}>
                    {therapist.name}
                  </option>
                ))}
              </select>
              <label className="label">
                <span className="label-text-alt text-gray-500">
                  Terapis saat ini: {transactionToEdit?.therapist?.name || 'Tidak ada'}
                </span>
              </label>
            </div>

            {/* Edit Items */}
            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium text-lg">📝 Edit Item Layanan</span>
              </label>
              <div className="space-y-3">
                {editItems.map((item, index) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-lg border">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      <div className="form-control">
                        <label className="label">
                          <span className="label-text text-sm">Nama Layanan</span>
                        </label>
                        <input
                          type="text"
                          className="input input-bordered input-sm"
                          value={item.name}
                          onChange={(e) => {
                            const newItems = [...editItems];
                            newItems[index].name = e.target.value;
                            setEditItems(newItems);
                          }}
                        />
                      </div>
                      <div className="form-control">
                        <label className="label">
                          <span className="label-text text-sm">Harga</span>
                        </label>
                        <input
                          type="number"
                          className="input input-bordered input-sm"
                          value={item.price}
                          onChange={(e) => {
                            const newItems = [...editItems];
                            newItems[index].price = Number(e.target.value);
                            setEditItems(newItems);
                          }}
                          min="0"
                          step="1000"
                        />
                      </div>
                      <div className="form-control">
                        <label className="label">
                          <span className="label-text text-sm">Jumlah</span>
                        </label>
                        <div className="flex gap-2">
                          <input
                            type="number"
                            className="input input-bordered input-sm w-20"
                            value={item.quantity}
                            onChange={(e) => {
                              const newItems = [...editItems];
                              newItems[index].quantity = Number(e.target.value);
                              setEditItems(newItems);
                            }}
                            min="1"
                          />
                          {editItems.length > 1 && (
                            <button
                              type="button"
                              className="btn btn-sm btn-error"
                              onClick={() => {
                                const newItems = editItems.filter((_, i) => i !== index);
                                setEditItems(newItems);
                              }}
                            >
                              🗑️
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right mt-2 text-sm font-medium text-gray-600">
                      Subtotal: {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(item.price * item.quantity)}
                    </div>
                  </div>
                ))}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">➕ Tambah Layanan</span>
                  </label>
                  <div className="flex gap-2">
                    <select
                      className="select select-bordered flex-1"
                      value={selectedServiceId}
                      onChange={(e) => setSelectedServiceId(e.target.value)}
                    >
                      <option value="">Pilih layanan...</option>
                      {servicesList.map((service) => (
                        <option key={service.id} value={service.id}>
                          {service.name} - {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(service.price)}
                        </option>
                      ))}
                      <option value="custom">Item Kustom</option>
                    </select>
                    <button
                      type="button"
                      className="btn btn-primary"
                      onClick={() => {
                        if (selectedServiceId === 'custom') {
                          // Tambah item kustom
                          setEditItems([...editItems, { serviceId: null, name: 'Layanan Baru', price: 0, quantity: 1 }]);
                        } else if (selectedServiceId) {
                          // Tambah layanan dari daftar
                          const selectedService = servicesList.find(s => s.id === selectedServiceId);
                          if (selectedService) {
                            setEditItems([...editItems, { 
                              serviceId: selectedService.id, 
                              name: selectedService.name, 
                              price: selectedService.price, 
                              quantity: 1 
                            }]);
                          }
                        }
                        setSelectedServiceId(''); // Reset dropdown
                      }}
                      disabled={!selectedServiceId}
                    >
                      Tambah
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Edit Diskon */}
            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium text-lg">💰 Pengaturan Diskon</span>
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Tipe Diskon</span>
                  </label>
                  <select
                    className="select select-bordered"
                    value={editDiscountType}
                    onChange={(e) => setEditDiscountType(e.target.value as 'percentage' | 'fixed' | 'none')}
                  >
                    <option value="none">Tidak Ada Diskon</option>
                    <option value="percentage">Persentase (%)</option>
                    <option value="fixed">Nominal Tetap (Rp)</option>
                  </select>
                </div>
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">
                      {editDiscountType === 'percentage' ? 'Persentase Diskon' : editDiscountType === 'fixed' ? 'Nominal Diskon' : 'Nilai Diskon'}
                    </span>
                  </label>
                  <input
                    type="number"
                    className="input input-bordered"
                    value={editDiscountValue}
                    onChange={(e) => setEditDiscountValue(Number(e.target.value))}
                    min="0"
                    max={editDiscountType === 'percentage' ? 100 : undefined}
                    step={editDiscountType === 'percentage' ? 1 : 1000}
                    disabled={editDiscountType === 'none'}
                    placeholder={editDiscountType === 'percentage' ? '0-100' : '0'}
                  />
                </div>
              </div>
              {editDiscountType !== 'none' && (
                <div className="mt-2 text-sm text-gray-600">
                  Diskon: {(() => {
                    const itemsTotal = editItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                    if (editDiscountType === 'percentage') {
                      const discountAmount = (itemsTotal * editDiscountValue) / 100;
                      return new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(discountAmount);
                    } else {
                      return new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(editDiscountValue);
                    }
                  })()} 
                </div>
              )}
            </div>

            <div className="divider"></div>

            {/* Komisi dan Biaya Tambahan */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium">💼 Komisi Terapis</span>
                  <span className="label-text-alt text-gray-500">Rupiah</span>
                </label>
                <input
                  type="number"
                  placeholder="0"
                  className="input input-bordered w-full focus:input-primary"
                  value={editCommission}
                  onChange={(e) => setEditCommission(Number(e.target.value))}
                  min="0"
                  step="1000"
                />
                <label className="label">
                  <span className="label-text-alt text-gray-500">
                    {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(editCommission)}
                  </span>
                </label>
              </div>
              
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium">💵 Biaya Tambahan</span>
                  <span className="label-text-alt text-gray-500">Rupiah</span>
                </label>
                <input
                  type="number"
                  placeholder="0"
                  className="input input-bordered w-full focus:input-primary"
                  value={editAdditionalCharge || ''}
                  onChange={(e) => setEditAdditionalCharge(e.target.value === '' ? 0 : Number(e.target.value))}
                  min="0"
                  step="1000"
                />
                <label className="label">
                  <span className="label-text-alt text-gray-500">
                    {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(editAdditionalCharge)}
                  </span>
                </label>
              </div>
            </div>

            {/* Preview total baru */}
            {transactionToEdit && (
              <>
                <div className="divider"></div>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-sm font-medium text-blue-800 mb-3">📊 Preview Perhitungan Baru:</div>
                  <div className="space-y-2 text-sm">
                    {(() => {
                      // Hitung total baru berdasarkan items yang diedit
                      const itemsTotal = editItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                      
                      // Hitung diskon
                      let discountAmount = 0;
                      if (editDiscountType === 'percentage') {
                        discountAmount = (itemsTotal * editDiscountValue) / 100;
                      } else if (editDiscountType === 'fixed') {
                        discountAmount = editDiscountValue;
                      }
                      
                      const subtotalAfterDiscount = itemsTotal - discountAmount;
                      const finalTotal = subtotalAfterDiscount + editAdditionalCharge;
                      
                      return (
                        <>
                          <div className="flex justify-between">
                            <span>Subtotal Item:</span>
                            <span className="font-medium">{new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(itemsTotal)}</span>
                          </div>
                          {editDiscountType !== 'none' && (
                            <div className="flex justify-between text-red-600">
                              <span>Diskon ({editDiscountType === 'percentage' ? `${editDiscountValue}%` : 'Tetap'}):</span>
                              <span className="font-medium">-{new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(discountAmount)}</span>
                            </div>
                          )}
                          <div className="flex justify-between">
                            <span>Subtotal Setelah Diskon:</span>
                            <span className="font-medium">{new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(subtotalAfterDiscount)}</span>
                          </div>
                          {editAdditionalCharge > 0 && (
                            <div className="flex justify-between text-green-600">
                              <span>Biaya Tambahan:</span>
                              <span className="font-medium">+{new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(editAdditionalCharge)}</span>
                            </div>
                          )}
                          <div className="border-t pt-2 mt-2">
                            <div className="flex justify-between text-lg font-bold text-blue-900">
                              <span>Total Akhir:</span>
                              <span>{new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(finalTotal)}</span>
                            </div>
                          </div>
                        </>
                      );
                    })()} 
                  </div>
                </div>
              </>
            )}
          </div>
          <div className="modal-action">
            <button className="btn btn-ghost" onClick={closeEditModal} disabled={isUpdating}>Batal</button>
            <button
              className={`btn btn-primary ${isUpdating ? 'btn-disabled' : ''}`}
              onClick={executeUpdateTransaction}
              disabled={isUpdating}
            >
              {isUpdating ?
                <><span className="loading loading-spinner loading-xs"></span> Menyimpan...</> :
                <>
                  <FiEdit2 className="mr-1" />
                  Simpan Perubahan
                </>
              }
            </button>
          </div>
        </div>
        {/* Klik backdrop untuk menutup */}
        <form method="dialog" className="modal-backdrop" onClick={closeEditModal}>
           <button type="button" disabled={isUpdating}>close</button>
        </form>
      </dialog>
      {/* === SELESAI MODAL EDIT === */}

    </motion.div>
  );
}