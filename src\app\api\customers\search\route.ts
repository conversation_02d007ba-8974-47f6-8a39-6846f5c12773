import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const phone = searchParams.get('phone');
    
    if (!phone) {
      return NextResponse.json(
        { error: 'Nomor telepon diperlukan' },
        { status: 400 }
      );
    }

    // Bersihkan nomor telepon (hapus spasi, tanda baca, dll)
    const cleanPhone = phone.replace(/[^0-9]/g, '');
    
    // Cari pelanggan berdasarkan nomor telepon
    const customer = await prisma.customer.findFirst({
      where: {
        phone: {
          contains: cleanPhone
        },
        isActive: true
      }
    });

    if (!customer) {
      return NextResponse.json(
        { message: 'Pelanggan tidak ditemukan' },
        { status: 404 }
      );
    }

    return NextResponse.json({ customer });
  } catch (error) {
    console.error('Error searching customer:', error);
    return NextResponse.json(
      { error: '<PERSON><PERSON><PERSON><PERSON> kesalahan saat mencari pelanggan' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
