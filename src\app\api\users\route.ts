import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { Prisma } from '@prisma/client'; // Import Prisma type
import { logUser } from '@/lib/logger';
import { getUserIdFromToken } from '@/lib/auth-utils';

// Daftar modul sistem (sinkronkan dengan frontend dan seed)
const SYSTEM_MODULES = [
  'dashboard',
  'transactions',
  'customers',
  'therapists',
  'services',
  'bookings',
  'reports',
  'settings',
  'users',
];

// Fungsi helper untuk generate default permissions
const generateDefaultPermissions = (role: string) => {
  return SYSTEM_MODULES.map(moduleName => {
    let canCreate = false, canRead = false, canUpdate = false, canDelete = false;
    switch (role) {
      case 'ADMIN':
        canCreate = canRead = canUpdate = canDelete = true;
        break;
      case 'MANAGER':
        canRead = true;
        if (!['users', 'settings'].includes(moduleName)) {
          canCreate = canUpdate = canDelete = true;
        }
        break;
      case 'STAFF':
        canRead = ['dashboard', 'transactions', 'customers', 'therapists', 'services', 'bookings'].includes(moduleName);
        canCreate = ['transactions', 'customers', 'bookings'].includes(moduleName);
        canUpdate = ['transactions', 'customers', 'bookings'].includes(moduleName);
        canDelete = false;
        break;
      case 'INVESTOR':
        canRead = ['dashboard', 'reports'].includes(moduleName);
        break;
    }
    return {
      module: moduleName,
      canCreate,
      canRead,
      canUpdate,
      canDelete,
    };
  });
};

// GET semua users (admin only)
export async function GET(request: Request) {
  try {
    // Ambil URL parameters
    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role');
    const isActive = searchParams.get('isActive');
    const isCaptain = searchParams.get('isCaptain');
    const search = searchParams.get('search');

    // Build query
    const where: Prisma.UserWhereInput = {};

    // Filter berdasarkan role
    if (role) {
      // Validasi role secara manual karena Role enum mungkin tidak tersedia
      const validRoles = ['ADMIN', 'MANAGER', 'STAFF', 'INVESTOR'];
      if (validRoles.includes(role)) {
        where.role = role as Role;
      }
    }

    // Filter berdasarkan status aktif
    if (isActive !== null && isActive !== undefined) {
      where.isActive = isActive === 'true';
    }

    // Filter berdasarkan status kapten
    if (isCaptain !== null && isCaptain !== undefined) {
      where.isCaptain = isCaptain === 'true';
    }

    // Filter berdasarkan pencarian (name, email, atau username)
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { username: { contains: search, mode: 'insensitive' } }
      ];
    }

    // Ambil users dari database, sertakan permissions dan allowedOutlets
    const users = await prisma.user.findMany({
      where,
      include: { // Gunakan include untuk mendapatkan permissions DAN allowedOutlets
        permissions: {
          select: {
            module: true,
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true,
          },
        },
        allowedOutlets: {
          select: {
            outlet: {
              select: {
                id: true,
                name: true,
              }
            }
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    // Exclude password dari response dan format allowedOutlets
    // TODO: Linter error terkait 'allowedOutlets' kemungkinan karena Prisma Client belum di-generate ulang.
    const formattedUsers = users.map(({ password: _password, allowedOutlets, ...user }) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const unusedPassword = _password; // Tandai sebagai tidak terpakai jika perlu
      // Definisikan tipe eksplisit untuk 'access'
      type OutletAccess = { outlet: { id: string; name: string } };
      return {
        ...user,
        // Ubah format allowedOutlets agar sesuai dengan interface OutletOption di frontend
        // Tambahkan pengecekan Array.isArray untuk keamanan
        allowedOutlets: Array.isArray(allowedOutlets)
                          ? allowedOutlets.map((access: OutletAccess) => access.outlet)
                          : [],
      };
    });

    return NextResponse.json({
      message: 'Data users berhasil diambil',
      users: formattedUsers
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data users' },
      { status: 500 }
    );
  }
}

// POST untuk membuat user baru (admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, username, password, role, isCaptain, allowedOutletIds } = body;
    const userRole: string = role || 'STAFF'; // Default ke STAFF jika tidak ada

    // Validasi input
    if (!name || !email || !username || !password) {
      return NextResponse.json(
        { error: 'Nama, email, username, dan password diperlukan' },
        { status: 400 }
      );
    }

    // Validasi khusus untuk role INVESTOR
    if (userRole === 'INVESTOR') {
      if (!allowedOutletIds || !Array.isArray(allowedOutletIds) || allowedOutletIds.length === 0) {
        return NextResponse.json(
          { error: 'Investor harus memilih minimal satu outlet (allowedOutletIds).' },
          { status: 400 }
        );
      }
    }

    // Validasi untuk isCaptain
    const shouldBeCaptain = isCaptain === true;
    if (shouldBeCaptain && userRole !== 'STAFF') {
      return NextResponse.json(
        { error: 'Hanya user dengan role STAFF yang dapat menjadi kapten.' },
        { status: 400 }
      );
    }

    // Cek duplikat email
    const existingEmail = await prisma.user.findUnique({
      where: { email }
    });

    if (existingEmail) {
      return NextResponse.json(
        { error: 'Email sudah terdaftar' },
        { status: 400 }
      );
    }

    // Cek duplikat username
    const existingUsername = await prisma.user.findUnique({
      where: { username }
    });

    if (existingUsername) {
      return NextResponse.json(
        { error: 'Username sudah terdaftar' },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Generate default permissions untuk role ini
    const defaultPermissions = generateDefaultPermissions(userRole);

    // Buat user baru beserta permissions default dalam transaksi
    const newUser = await prisma.$transaction(async (tx) => {
      // 1. Buat user baru
      const user = await tx.user.create({
        data: {
          name,
          email,
          username,
          password: hashedPassword,
          role: userRole,
          isActive: true,
          isCaptain: shouldBeCaptain && userRole === 'STAFF' ? true : false,
          permissions: { // Buat permissions terkait
            create: defaultPermissions, // Masukkan data permission
          },
        },
        include: { // Sertakan permissions dalam response
          permissions: {
            select: {
              module: true,
              canCreate: true,
              canRead: true,
              canUpdate: true,
              canDelete: true,
            },
          },
        }
      });

      // 2. Jika role adalah INVESTOR, buat relasi dengan outlet yang dipilih
      if (userRole === 'INVESTOR' && Array.isArray(allowedOutletIds) && allowedOutletIds.length > 0) {
        console.log('Creating outlet access for investor:', allowedOutletIds);

        // Buat relasi untuk setiap outlet yang dipilih
        for (const outletId of allowedOutletIds) {
          await tx.userOutletAccess.create({
            data: {
              userId: user.id,
              outletId: outletId
            }
          });
        }
      }

      return user;
    });

    // Ambil data user lengkap dengan allowedOutlets setelah transaksi
    const userWithOutlets = await prisma.user.findUnique({
      where: { id: newUser.id },
      include: {
        permissions: {
          select: {
            module: true,
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true,
          },
        },
        allowedOutlets: {
          select: {
            outlet: {
              select: {
                id: true,
                name: true,
              }
            }
          }
        }
      }
    });

    if (!userWithOutlets) {
      throw new Error('Gagal mengambil data user setelah pembuatan');
    }

    // Exclude password dari response dan format allowedOutlets
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password: _, allowedOutlets: accessData, ...userWithoutPassword } = userWithOutlets;
    // Definisikan tipe eksplisit untuk 'access'
    type OutletAccess = { outlet: { id: string; name: string } };
    // Tambahkan pengecekan Array.isArray untuk keamanan
    const formattedAllowedOutlets = Array.isArray(accessData)
                                    ? accessData.map((access: OutletAccess) => access.outlet)
                                    : [];

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Log pembuatan user baru
    await logUser(
      'create',
      newUser.id,
      {
        name: newUser.name,
        email: newUser.email,
        username: newUser.username,
        role: newUser.role,
        isActive: newUser.isActive
      },
      undefined, // Tidak ada outletId spesifik
      userId ? userId : undefined // Gunakan userId dari token
    );

    return NextResponse.json({
      message: 'User berhasil dibuat beserta permissions default',
      user: {
        ...userWithoutPassword,
        allowedOutlets: formattedAllowedOutlets
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);

    // Cek apakah error terkait outlet ID yang tidak valid
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2003') {
        return NextResponse.json(
          { error: 'Salah satu outlet ID yang dipilih tidak valid.' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat user' },
      { status: 500 }
    );
  }
}