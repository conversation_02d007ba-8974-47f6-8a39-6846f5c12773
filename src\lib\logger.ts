import { prisma } from './prisma';

export type LogType = 'ERROR' | 'WARNING' | 'INFO' | 'SUCCESS' | 'SYSTEM' | 'AUTH' | 'CREATE' | 'UPDATE' | 'DELETE' | 'BOOKING' | 'TRANSACTION' | 'THERAPIST' | 'SERVICE' | 'OUTLET' | 'USER' | string;

interface LogOptions {
  type: LogType;
  message: string;
  details?: any;
  outletId?: string;
  userId?: string;
}

/**
 * Fungsi untuk menambahkan log ke sistem
 * @param options Opsi log
 * @returns Promise dengan log yang dibuat
 */
export async function addSystemLog(options: LogOptions) {
  try {
    const { type, message, details, outletId, userId } = options;

    const log = await prisma.systemLog.create({
      data: {
        type,
        message,
        details: details ? details : undefined,
        outletId: outletId || null,
        userId: userId || null,
      },
    });

    return log;
  } catch (error) {
    console.error('Error adding system log:', error);
    // <PERSON>an throw error di sini, karena logging seharusnya tidak mengganggu alur aplikasi
    return null;
  }
}

/**
 * Fungsi untuk menambahkan log error
 * @param message Pesan error
 * @param details Detail error
 * @param outletId ID outlet (opsional)
 * @param userId ID user (opsional)
 */
export function logError(message: string, details?: any, outletId?: string, userId?: string) {
  return addSystemLog({
    type: 'ERROR',
    message,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log warning
 * @param message Pesan warning
 * @param details Detail warning
 * @param outletId ID outlet (opsional)
 * @param userId ID user (opsional)
 */
export function logWarning(message: string, details?: any, outletId?: string, userId?: string) {
  return addSystemLog({
    type: 'WARNING',
    message,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log info
 * @param message Pesan info
 * @param details Detail info
 * @param outletId ID outlet (opsional)
 * @param userId ID user (opsional)
 */
export function logInfo(message: string, details?: any, outletId?: string, userId?: string) {
  return addSystemLog({
    type: 'INFO',
    message,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log success
 * @param message Pesan success
 * @param details Detail success
 * @param outletId ID outlet (opsional)
 * @param userId ID user (opsional)
 */
export function logSuccess(message: string, details?: any, outletId?: string, userId?: string) {
  return addSystemLog({
    type: 'SUCCESS',
    message,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log auth
 * @param message Pesan auth
 * @param details Detail auth
 * @param outletId ID outlet (opsional)
 * @param userId ID user (opsional)
 */
export function logAuth(message: string, details?: any, outletId?: string, userId?: string) {
  return addSystemLog({
    type: 'AUTH',
    message,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log system
 * @param message Pesan system
 * @param details Detail system
 * @param outletId ID outlet (opsional)
 * @param userId ID user (opsional)
 */
export function logSystem(message: string, details?: any, outletId?: string, userId?: string) {
  return addSystemLog({
    type: 'SYSTEM',
    message,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log aktivitas pembuatan data
 * @param entity Nama entitas yang dibuat (misalnya: 'customer', 'therapist', 'service')
 * @param entityId ID entitas yang dibuat
 * @param details Detail tambahan
 * @param outletId ID outlet (opsional)
 * @param userId ID user (opsional)
 */
export function logCreate(entity: string, entityId: string, details?: any, outletId?: string, userId?: string) {
  return addSystemLog({
    type: 'CREATE',
    message: `Membuat ${entity} baru: ${entityId}`,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log aktivitas pembaruan data
 * @param entity Nama entitas yang diperbarui (misalnya: 'customer', 'therapist', 'service')
 * @param entityId ID entitas yang diperbarui
 * @param details Detail perubahan
 * @param outletId ID outlet (opsional)
 * @param userId ID user (opsional)
 */
export function logUpdate(entity: string, entityId: string, details?: any, outletId?: string, userId?: string) {
  return addSystemLog({
    type: 'UPDATE',
    message: `Memperbarui ${entity}: ${entityId}`,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log aktivitas penghapusan data
 * @param entity Nama entitas yang dihapus (misalnya: 'customer', 'therapist', 'service')
 * @param entityId ID entitas yang dihapus
 * @param details Detail tambahan
 * @param outletId ID outlet (opsional)
 * @param userId ID user (opsional)
 */
export function logDelete(entity: string, entityId: string, details?: any, outletId?: string, userId?: string) {
  return addSystemLog({
    type: 'DELETE',
    message: `Menghapus ${entity}: ${entityId}`,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log aktivitas booking
 * @param action Aksi yang dilakukan (misalnya: 'create', 'confirm', 'cancel', 'complete')
 * @param bookingId ID booking
 * @param details Detail booking
 * @param outletId ID outlet (opsional)
 * @param userId ID user (opsional)
 */
export function logBooking(action: string, bookingId: string, details?: any, outletId?: string, userId?: string) {
  let message = '';
  switch (action.toLowerCase()) {
    case 'create':
      message = `Membuat booking baru: ${bookingId}`;
      break;
    case 'confirm':
      message = `Mengkonfirmasi booking: ${bookingId}`;
      break;
    case 'cancel':
      message = `Membatalkan booking: ${bookingId}`;
      break;
    case 'complete':
      message = `Menyelesaikan booking: ${bookingId}`;
      break;
    case 'no_show':
      message = `Menandai booking sebagai no-show: ${bookingId}`;
      break;
    default:
      message = `${action} booking: ${bookingId}`;
  }

  return addSystemLog({
    type: 'BOOKING',
    message,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log aktivitas transaksi
 * @param action Aksi yang dilakukan (misalnya: 'create', 'update', 'refund')
 * @param transactionId ID transaksi
 * @param details Detail transaksi
 * @param outletId ID outlet (opsional)
 * @param userId ID user (opsional)
 */
export function logTransaction(action: string, transactionId: string | number, details?: any, outletId?: string, userId?: string) {
  let message = '';
  switch (action.toLowerCase()) {
    case 'create':
      message = `Membuat transaksi baru: ${transactionId}`;
      break;
    case 'update':
      message = `Memperbarui transaksi: ${transactionId}`;
      break;
    case 'refund':
      message = `Melakukan refund transaksi: ${transactionId}`;
      break;
    case 'split':
      message = `Melakukan split payment pada transaksi: ${transactionId}`;
      break;
    default:
      message = `${action} transaksi: ${transactionId}`;
  }

  return addSystemLog({
    type: 'TRANSACTION',
    message,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log aktivitas terapis
 * @param action Aksi yang dilakukan (misalnya: 'create', 'update', 'delete')
 * @param therapistId ID terapis
 * @param details Detail terapis
 * @param outletId ID outlet (opsional)
 * @param userId ID user (opsional)
 */
export function logTherapist(action: string, therapistId: string, details?: any, outletId?: string, userId?: string) {
  let message = '';
  switch (action.toLowerCase()) {
    case 'create':
      message = `Membuat terapis baru: ${details?.name || therapistId}`;
      break;
    case 'update':
      message = `Memperbarui terapis: ${details?.name || therapistId}`;
      break;
    case 'delete':
      message = `Menghapus terapis: ${details?.name || therapistId}`;
      break;
    case 'activate':
      message = `Mengaktifkan terapis: ${details?.name || therapistId}`;
      break;
    case 'deactivate':
      message = `Menonaktifkan terapis: ${details?.name || therapistId}`;
      break;
    default:
      message = `${action} terapis: ${details?.name || therapistId}`;
  }

  return addSystemLog({
    type: 'THERAPIST',
    message,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log aktivitas layanan
 * @param action Aksi yang dilakukan (misalnya: 'create', 'update', 'delete')
 * @param serviceId ID layanan
 * @param details Detail layanan
 * @param outletId ID outlet (opsional)
 * @param userId ID user (opsional)
 */
export function logService(action: string, serviceId: string, details?: any, outletId?: string, userId?: string) {
  let message = '';
  switch (action.toLowerCase()) {
    case 'create':
      message = `Membuat layanan baru: ${details?.name || serviceId}`;
      break;
    case 'update':
      message = `Memperbarui layanan: ${details?.name || serviceId}`;
      break;
    case 'delete':
      message = `Menghapus layanan: ${details?.name || serviceId}`;
      break;
    case 'activate':
      message = `Mengaktifkan layanan: ${details?.name || serviceId}`;
      break;
    case 'deactivate':
      message = `Menonaktifkan layanan: ${details?.name || serviceId}`;
      break;
    default:
      message = `${action} layanan: ${details?.name || serviceId}`;
  }

  return addSystemLog({
    type: 'SERVICE',
    message,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log aktivitas outlet
 * @param action Aksi yang dilakukan (misalnya: 'create', 'update', 'delete')
 * @param outletId ID outlet
 * @param details Detail outlet
 * @param userId ID user (opsional)
 */
export function logOutlet(action: string, outletId: string, details?: any, userId?: string) {
  let message = '';
  switch (action.toLowerCase()) {
    case 'create':
      message = `Membuat outlet baru: ${details?.name || outletId}`;
      break;
    case 'update':
      message = `Memperbarui outlet: ${details?.name || outletId}`;
      break;
    case 'delete':
      message = `Menghapus outlet: ${details?.name || outletId}`;
      break;
    case 'open':
      message = `Membuka outlet: ${details?.name || outletId}`;
      break;
    case 'close':
      message = `Menutup outlet: ${details?.name || outletId}`;
      break;
    default:
      message = `${action} outlet: ${details?.name || outletId}`;
  }

  return addSystemLog({
    type: 'OUTLET',
    message,
    details,
    outletId,
    userId,
  });
}

/**
 * Fungsi untuk menambahkan log aktivitas user
 * @param action Aksi yang dilakukan (misalnya: 'create', 'update', 'delete')
 * @param targetUserId ID user yang menjadi target aksi
 * @param details Detail user
 * @param outletId ID outlet (opsional)
 * @param userId ID user yang melakukan aksi (opsional)
 */
export function logUser(action: string, targetUserId: string, details?: any, outletId?: string, userId?: string) {
  let message = '';
  switch (action.toLowerCase()) {
    case 'create':
      message = `Membuat user baru: ${details?.name || targetUserId}`;
      break;
    case 'update':
      message = `Memperbarui user: ${details?.name || targetUserId}`;
      break;
    case 'delete':
      message = `Menghapus user: ${details?.name || targetUserId}`;
      break;
    case 'activate':
      message = `Mengaktifkan user: ${details?.name || targetUserId}`;
      break;
    case 'deactivate':
      message = `Menonaktifkan user: ${details?.name || targetUserId}`;
      break;
    case 'login':
      message = `Login user: ${details?.name || targetUserId}`;
      break;
    case 'logout':
      message = `Logout user: ${details?.name || targetUserId}`;
      break;
    case 'password_change':
      message = `Mengubah password user: ${details?.name || targetUserId}`;
      break;
    case 'permission_change':
      message = `Mengubah hak akses user: ${details?.name || targetUserId}`;
      break;
    default:
      message = `${action} user: ${details?.name || targetUserId}`;
  }

  return addSystemLog({
    type: 'USER',
    message,
    details,
    outletId,
    userId,
  });
}
