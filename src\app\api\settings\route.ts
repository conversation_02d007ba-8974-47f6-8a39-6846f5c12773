import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET semua pengaturan
export async function GET(request: Request) {
  try {
    // Ambil URL parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    
    // Build query
    const where: { category?: string } = {};
    
    // Filter berdasarkan kategori jika ada
    if (category) {
      where.category = category;
    }
    
    // Ambil pengaturan dari database
    const settings = await prisma.setting.findMany({
      where,
      orderBy: [
        { category: 'asc' },
        { label: 'asc' }
      ]
    });

    // Kembalikan data pengaturan dikelompokkan berdasarkan kategori
    const groupedSettings = settings.reduce((acc, setting) => {
      if (!acc[setting.category]) {
        acc[setting.category] = [];
      }
      acc[setting.category].push(setting);
      return acc;
    }, {} as Record<string, typeof settings>);

    return NextResponse.json({
      message: 'Data pengaturan berhasil diambil',
      settings: groupedSettings
    });
  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data pengaturan' },
      { status: 500 }
    );
  }
}

// POST untuk membuat atau memperbarui pengaturan
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { key, value, category, label, type, options, isSystem = false } = body;

    // Validasi input
    if (!key || value === undefined || !category || !label) {
      return NextResponse.json(
        { error: 'Key, value, category, dan label diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah pengaturan sudah ada
    const existingSetting = await prisma.setting.findUnique({
      where: { key }
    });

    let setting;

    if (existingSetting) {
      // Update pengaturan yang ada
      setting = await prisma.setting.update({
        where: { key },
        data: {
          value,
          category,
          label,
          type: type || 'TEXT',
          options: options || null,
          isSystem
        }
      });
    } else {
      // Buat pengaturan baru
      setting = await prisma.setting.create({
        data: {
          key,
          value,
          category,
          label,
          type: type || 'TEXT',
          options: options || null,
          isSystem
        }
      });
    }

    return NextResponse.json({
      message: existingSetting ? 'Pengaturan berhasil diperbarui' : 'Pengaturan berhasil dibuat',
      setting
    });
  } catch (error) {
    console.error('Error creating/updating setting:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat/memperbarui pengaturan' },
      { status: 500 }
    );
  }
} 