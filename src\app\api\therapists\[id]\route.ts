import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { logTherapist } from '@/lib/logger';
import { getUserIdFromToken } from '@/lib/auth-utils';

// GET terapis berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Await params untuk menghindari error
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: 'ID terapis diperlukan' },
        { status: 400 }
      );
    }

    // Ambil detail terapis berdasarkan ID
    const therapist = await prisma.therapist.findUnique({
      where: {
        id: id
      },
      include: {
        outlet: {
          select: {
            id: true,
            name: true,
            address: true,
            city: true
          }
        },
        captain: {
          select: {
            id: true,
            name: true
          }
        },
        bookings: {
          where: {
            status: 'COMPLETED'
          },
          select: {
            id: true,
            bookingDate: true,
            bookingTime: true,
            bookingServices: {
              select: {
                service: { select: { name: true } }
              }
            },
          },
          orderBy: {
            bookingDate: 'desc'
          },
          take: 10
        }
      }
    });
    
    // Tambahkan data lembur terapis jika ada
    const overtimeData = await prisma.transaction.aggregate({
      where: {
        therapistId: id,
        overtimeMinutes: {
          gt: 0
        }
      },
      _sum: {
        overtimeMinutes: true,
        overtimeAmount: true
      }
    });

    // Jika terapis tidak ditemukan
    if (!therapist) {
      return NextResponse.json(
        { error: 'Terapis tidak ditemukan' },
        { status: 404 }
      );
    }

    // Jika terapis tidak aktif
    if (!therapist.isActive) {
      return NextResponse.json(
        { error: 'Terapis ini tidak aktif saat ini' },
        { status: 403 }
      );
    }

    // Kembalikan data terapis dengan informasi lembur
    return NextResponse.json({
      message: 'Data terapis berhasil diambil',
      therapist: {
        ...therapist,
        overtime: {
          totalMinutes: overtimeData._sum.overtimeMinutes || 0,
          totalAmount: overtimeData._sum.overtimeAmount || 0
        }
      }
    });
  } catch (error) {
    console.error('Error fetching therapist details:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil detail terapis' },
      { status: 500 }
    );
  }
}

// PUT untuk mengupdate terapis
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Await params untuk menghindari error
    const { id } = await params;
    const body = await request.json();
    const { name, outletId, specialization, experience, isActive, captainUserId } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID terapis diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah terapis ada
    const existingTherapist = await prisma.therapist.findUnique({
      where: {
        id
      }
    });

    if (!existingTherapist) {
      return NextResponse.json(
        { error: 'Terapis tidak ditemukan' },
        { status: 404 }
      );
    }

    // Jika outletId berubah, cek apakah outlet baru ada
    if (outletId && outletId !== existingTherapist.outletId) {
      const outlet = await prisma.outlet.findUnique({
        where: {
          id: outletId
        }
      });

      if (!outlet) {
        return NextResponse.json(
          { error: 'Outlet tidak ditemukan' },
          { status: 404 }
        );
      }
    }

    // Update terapis
    const updatedTherapist = await prisma.therapist.update({
      where: {
        id
      },
      data: {
        ...(name && { name }),
        ...(outletId && { outletId }),
        ...(specialization && { specialization }),
        ...(experience !== undefined && { experience }),
        ...(isActive !== undefined && { isActive }),
        ...(captainUserId !== undefined && { captainUserId })
      },
      include: {
        outlet: {
          select: {
            id: true,
            name: true
          }
        },
        captain: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Log update terapis
    await logTherapist(
      'update',
      id,
      {
        name: updatedTherapist.name,
        outletId: updatedTherapist.outletId,
        specialization: updatedTherapist.specialization,
        experience: updatedTherapist.experience,
        isActive: updatedTherapist.isActive,
        captainUserId: updatedTherapist.captainUserId,
        updatedFields: Object.keys({
          ...(name && { name }),
          ...(outletId && { outletId }),
          ...(specialization && { specialization }),
          ...(experience !== undefined && { experience }),
          ...(isActive !== undefined && { isActive }),
          ...(captainUserId !== undefined && { captainUserId })
        })
      },
      updatedTherapist.outletId,
      userId // Gunakan userId dari token
    );

    return NextResponse.json({
      message: 'Terapis berhasil diupdate',
      therapist: updatedTherapist
    });
  } catch (error) {
    console.error('Error updating therapist:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengupdate terapis' },
      { status: 500 }
    );
  }
}

// DELETE terapis (soft delete dengan mengubah isActive menjadi false)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: 'ID terapis diperlukan' },
        { status: 400 }
      );
    }

    console.log(`Attempting to delete therapist ${id}. Checking active bookings...`);

    // Cek apakah terapis memiliki booking aktif (PENDING atau CONFIRMED)
    const activeBookings = await prisma.booking.findMany({
      where: {
        therapistId: id,
        status: {
          in: ['PENDING', 'CONFIRMED']
        }
      },
      select: { id: true }
    });

    if (activeBookings.length > 0) {
      console.warn(`Therapist ${id} cannot be deleted due to active bookings: ${activeBookings.map(b => b.id).join(', ')}`);
      return NextResponse.json(
        { error: 'Terapis tidak dapat dihapus karena masih memiliki booking yang aktif (Pending/Confirmed).' },
        { status: 409 }
      );
    }

    // Cek riwayat transaksi (Optional)
    const relatedTransactions = await prisma.transaction.findFirst({
      where: { therapistId: id },
      select: { id: true }
    });
    if (relatedTransactions) {
      console.warn(`Therapist ${id} cannot be deleted because they have transaction history (ID: ${relatedTransactions.id}). Consider deactivating instead.`);
      return NextResponse.json(
        { error: 'Terapis tidak dapat dihapus karena sudah memiliki riwayat transaksi. Harap nonaktifkan saja.' },
        { status: 409 }
      );
    }

    console.log(`No active bookings or critical transaction history found for therapist ${id}. Proceeding with deletion.`);

    // Ambil data terapis sebelum dihapus untuk logging
    const therapistToDelete = await prisma.therapist.findUnique({
      where: { id }
    });

    // Hapus terapis
    await prisma.therapist.delete({ where: { id } });

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Log penghapusan terapis
    if (therapistToDelete) {
      await logTherapist(
        'delete',
        id,
        {
          name: therapistToDelete.name,
          outletId: therapistToDelete.outletId,
          specialization: therapistToDelete.specialization,
          experience: therapistToDelete.experience
        },
        therapistToDelete.outletId,
        userId // Gunakan userId dari token
      );
    }

    return NextResponse.json({ message: 'Terapis berhasil dihapus' });

  } catch (error: unknown) {
    console.error('Error deleting therapist:', error);
    let errorMessage = 'Terjadi kesalahan saat menghapus terapis';
    if (error instanceof Error && 'code' in error && error.code === 'P2025') {
      errorMessage = 'Terapis tidak ditemukan untuk dihapus.';
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}