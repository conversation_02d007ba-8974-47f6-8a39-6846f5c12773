'use client';

import React, { ReactNode } from 'react';
import { usePermission } from '@/hooks/usePermission';

interface PermissionGuardProps {
  module: string;
  action: 'create' | 'read' | 'update' | 'delete';
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Komponen untuk menampilkan atau menyembunyikan elemen berdasarkan permission
 *
 * @param module - <PERSON>a modul (misalnya 'transactions', 'customers', dll)
 * @param action - <PERSON><PERSON> aksi ('create', 'read', 'update', 'delete')
 * @param children - Elemen yang akan ditampilkan jika user memiliki permission
 * @param fallback - Elemen yang akan ditampilkan jika user tidak memiliki permission (opsional)
 */
export default function PermissionGuard({ module, action, children, fallback = null }: PermissionGuardProps) {
  const { checkPermission, isLoadingPermissions, user, hasInitialPermissions } = usePermission();

  // Admin selalu punya akses penuh tanpa menunggu loading
  if (user?.role === 'ADMIN') {
    return <>{children}</>;
  }

  // Jika sudah ada initial permissions dari cache, langsung cek
  if (hasInitialPermissions) {
    const hasPermission = checkPermission(module, action);
    return hasPermission ? <>{children}</> : <>{fallback}</>;
  }

  // Jika masih loading dan belum ada initial permissions, tampilkan loading spinner kecil
  if (isLoadingPermissions) {
    // Untuk tombol, tampilkan skeleton button
    if (children && typeof children === 'object' && 'type' in children && children.type === 'button') {
      return <button className="btn btn-ghost btn-sm opacity-30 animate-pulse" disabled>...</button>;
    }
    return <span className="loading loading-spinner loading-xs opacity-30"></span>;
  }

  // Cek apakah user memiliki permission
  const hasPermission = checkPermission(module, action);

  // Tampilkan children jika user memiliki permission, atau fallback jika tidak
  return hasPermission ? <>{children}</> : <>{fallback}</>;
}
