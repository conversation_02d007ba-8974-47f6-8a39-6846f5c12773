import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserIdFromToken } from '@/lib/auth-utils';

// GET - Ambil semua item inventori
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');
    const outletId = searchParams.get('outletId');
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const count = searchParams.get('count') === 'true';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (categoryId) {
      where.categoryId = categoryId;
    }
    
    if (outletId) {
      where.outletId = outletId;
    }
    
    // Status filter akan dihandle setelah query untuk simplicity
    // karena perlu membandingkan remainingQuantity dengan minStockLevel
    
    if (search && search.trim() !== '') {
      where.OR = [
        { name: { contains: search.trim(), mode: 'insensitive' } },
        { description: { contains: search.trim(), mode: 'insensitive' } },
        { notes: { contains: search.trim(), mode: 'insensitive' } },
        {
          category: {
            name: { contains: search.trim(), mode: 'insensitive' }
          }
        }
      ];
    }

    // If only count is requested, return count directly
    if (count) {
      const total = await prisma.inventoryItem.count({ where });
      return NextResponse.json({
        message: 'Jumlah item berhasil diambil',
        total
      });
    }

    let items = await prisma.inventoryItem.findMany({
      where,
      include: {
        category: true,
        outlet: true,
        createdBy: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            movements: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Apply status filter after query
    if (status) {
      if (status === 'low_stock') {
        items = items.filter(item => 
          item.remainingQuantity <= (item.minStockLevel || 0)
        );
      } else if (status === 'out_of_stock') {
        items = items.filter(item => item.remainingQuantity === 0);
      }
    }

    // Apply pagination after filtering
    const total = items.length;
    const totalPages = Math.ceil(total / limit);
    const paginatedItems = items.slice(skip, skip + limit);

    // Transform data to match frontend interface
    const transformedItems = paginatedItems.map(item => ({
      id: item.id,
      name: item.name,
      code: null, // Database doesn't have code field
      description: item.description,
      quantity: item.remainingQuantity,
      unit: 'pcs', // Default unit since not in database
      goodCondition: item.goodCondition,
      damaged: item.damagedCondition,
      lost: item.lostCondition,
      minimumStock: item.minStockLevel || 0,
      location: item.notes,
      category: item.category,
      outlet: item.outlet
    }));

    return NextResponse.json({
      message: 'Item inventori berhasil diambil',
      items: transformedItems,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    });
  } catch (error) {
    console.error('Error fetching inventory items:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil item inventori' },
      { status: 500 }
    );
  }
}

// POST - Buat item inventori baru
export async function POST(request: NextRequest) {
  try {
    const userId = getUserIdFromToken(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      name,
      description,
      categoryId,
      outletId,
      quantity,
      goodCondition,
      damaged,
      lost,
      minimumStock,
      location
    } = body;

    // Validasi input
    if (!name || name.trim() === '') {
      return NextResponse.json(
        { error: 'Nama item harus diisi' },
        { status: 400 }
      );
    }

    if (!categoryId) {
      return NextResponse.json(
        { error: 'Kategori harus dipilih' },
        { status: 400 }
      );
    }

    if (!outletId) {
      return NextResponse.json(
        { error: 'Outlet harus dipilih' },
        { status: 400 }
      );
    }

    // Validasi quantity
    const totalQuantity = (quantity || 0) + (goodCondition || 0) + (damaged || 0) + (lost || 0);
    if (totalQuantity < 0) {
      return NextResponse.json(
        { error: 'Total jumlah tidak boleh negatif' },
        { status: 400 }
      );
    }

    // Cek apakah nama item sudah ada di outlet ini
    const existingItem = await prisma.inventoryItem.findFirst({
      where: {
        name: name.trim(),
        outletId
      }
    });

    if (existingItem) {
      return NextResponse.json(
        { error: 'Nama item sudah digunakan di outlet ini' },
        { status: 400 }
      );
    }

    // Hitung remaining stock
    const remaining = Math.max(0, (goodCondition || 0) - (damaged || 0) - (lost || 0));

    const item = await prisma.inventoryItem.create({
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        categoryId,
        outletId,
        totalQuantity: quantity || 0,
        goodCondition: goodCondition || 0,
        damagedCondition: damaged || 0,
        lostCondition: lost || 0,
        remainingQuantity: Math.max(0, remaining),
        minStockLevel: minimumStock || 0,
        notes: location?.trim() || null,
        createdById: userId
      },
      include: {
        category: true,
        outlet: true,
        createdBy: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    // Buat movement record untuk stock awal
    if (totalQuantity > 0) {
      await prisma.inventoryMovement.create({
        data: {
          itemId: item.id,
          movementType: 'IN',
          quantity: totalQuantity,
          reason: 'Stock awal',
          createdById: userId
        }
      });
    }

    return NextResponse.json({
      message: 'Item inventori berhasil dibuat',
      item
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating inventory item:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat item inventori' },
      { status: 500 }
    );
  }
}