import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET pengaturan jenis pembayaran
export async function GET() {
  try {
    // Ambil pengaturan dengan kategori 'payment-types'
    const settings = await prisma.setting.findMany({
      where: {
        category: 'payment-types'
      },
      orderBy: {
        label: 'asc'
      }
    });

    return NextResponse.json({
      message: 'Data pengaturan jenis pembayaran berhasil diambil',
      settings
    });
  } catch (error) {
    console.error('Error fetching payment type settings:', error);
    return NextResponse.json(
      { error: '<PERSON><PERSON><PERSON><PERSON> kesalahan saat mengambil data pengaturan jenis pembayaran' },
      { status: 500 }
    );
  }
}

// POST untuk membuat atau memperbarui pengaturan jenis pembayaran
export async function POST(request: Request) {
  try {
    const body = await request.json();
    // body bisa array atau objek tunggal
    const settingsData = Array.isArray(body) ? body : [body];
    
    if (settingsData.length === 0) {
      return NextResponse.json(
        { error: 'Tidak ada data pengaturan yang diterima' },
        { status: 400 }
      );
    }
    
    // Validasi semua data
    for (const setting of settingsData) {
      const { key, value, label } = setting;
      if (!key || value === undefined || !label) {
        return NextResponse.json(
          { error: 'Key, value, dan label diperlukan untuk semua pengaturan' },
          { status: 400 }
        );
      }
    }
    
    // Ubah semua pengaturan dalam transaksi
    const results = await prisma.$transaction(
      settingsData.map(setting => {
        const { key, value, label, type = 'TEXT', options } = setting;
        
        // Pastikan kategori adalah 'payment-types'
        return prisma.setting.upsert({
          where: { key },
          create: {
            key,
            value,
            category: 'payment-types',
            label,
            type,
            options: options || null
          },
          update: {
            value,
            label,
            type,
            options: options || null
          }
        });
      })
    );
    
    return NextResponse.json({
      message: 'Pengaturan jenis pembayaran berhasil diperbarui',
      settings: results
    });
  } catch (error) {
    console.error('Error updating payment type settings:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui pengaturan jenis pembayaran' },
      { status: 500 }
    );
  }
} 