/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  output: 'standalone',
  // Hapus pengecualian untuk file Prisma agar bisa berjalan di Vercel
  outputFileTracingExcludes: {
    '**': [
      // './node_modules/@prisma/client/**/*', // <PERSON><PERSON> exclude file Prisma
      // './**/generated/prisma/**/*', // <PERSON><PERSON> exclude file Prisma
      './node_modules/.pnpm/**/*',
      '**/.yarn/**/*',
      './node_modules/@swc/core-win32-x64-msvc/**/*',
      '**/node_modules/next/dist/compiled/@napi-rs/triples/**/*',
      '**/node_modules/sharp/**/*',
      "./Application Data/**",
      "./AppData/**",
      "C:/Users/<USER>/Application Data/**",
      "C:/Users/<USER>/AppData/**",
    ],
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
}

module.exports = nextConfig
