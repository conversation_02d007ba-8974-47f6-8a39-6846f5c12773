'use client';

import { useState, useEffect } from 'react';
import { FiDownload, FiX } from 'react-icons/fi';

export default function AddToHomeScreen() {
  const [showPrompt, setShowPrompt] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);

  useEffect(() => {
    // Check if the user has already dismissed or installed the app
    const hasPromptBeenShown = localStorage.getItem('pwaPromptShown');
    
    if (hasPromptBeenShown) {
      return;
    }

    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault();
      // Stash the event so it can be triggered later
      setDeferredPrompt(e);
      // Show the prompt banner
      setShowPrompt(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = () => {
    if (!deferredPrompt) {
      return;
    }

    // Show the install prompt
    deferredPrompt.prompt();

    // Wait for the user to respond to the prompt
    deferredPrompt.userChoice.then((choiceResult: { outcome: string }) => {
      if (choiceResult.outcome === 'accepted') {
        console.log('User accepted the install prompt');
      } else {
        console.log('User dismissed the install prompt');
      }
      
      // Mark that we've shown the prompt
      localStorage.setItem('pwaPromptShown', 'true');
      
      // Clear the saved prompt
      setDeferredPrompt(null);
      setShowPrompt(false);
    });
  };

  const handleDismiss = () => {
    // Mark that we've shown the prompt
    localStorage.setItem('pwaPromptShown', 'true');
    setShowPrompt(false);
  };

  if (!showPrompt) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-80 bg-white rounded-lg shadow-lg p-4 z-50 border border-gray-200 animate-fade-in-up">
      <div className="flex items-start">
        <div className="flex-shrink-0 bg-teal-500 rounded-full p-2 mr-3">
          <FiDownload className="text-white" size={20} />
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-gray-800">Instal Aplikasi Breaktime</h3>
          <p className="text-sm text-gray-600 mt-1">Instal aplikasi untuk pengalaman yang lebih baik dan akses cepat.</p>
          <div className="mt-3 flex gap-2">
            <button 
              onClick={handleInstallClick}
              className="btn btn-sm btn-primary flex-1"
              type="button"
            >
              Instal
            </button>
            <button 
              onClick={handleDismiss}
              className="btn btn-sm btn-ghost"
              type="button"
            >
              Nanti
            </button>
          </div>
        </div>
        <button 
          onClick={handleDismiss}
          className="text-gray-400 hover:text-gray-600"
          type="button"
          aria-label="Tutup"
        >
          <FiX size={18} />
        </button>
      </div>
    </div>
  );
}
