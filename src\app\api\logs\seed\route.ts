import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import * as jwt from 'jsonwebtoken';

// Gunakan variabel environment dan pastikan ada nilai default yang aman
const JWT_SECRET = process.env.JWT_SECRET || 'breaktime_session_secret_key_for_jwt_auth';

export async function POST(request: NextRequest) {
  try {
    // Verifikasi autentikasi dari token
    const authHeader = request.headers.get('authorization');
    let token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    // Jika tidak ada token di header, cek cookie
    if (!token) {
      const cookieHeader = request.headers.get('cookie');
      if (cookieHeader) {
        const cookies = cookieHeader.split(';').map(c => c.trim());
        const userTokenCookie = cookies.find(c => c.startsWith('user_token='));
        if (userTokenCookie) {
          token = userTokenCookie.split('=')[1];
        }
      }
    }

    // Jika tidak ada token sama sekali
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verifikasi token
    const decoded = jwt.verify(token, JWT_SECRET) as {
      id: string;
      name: string;
      email: string;
      role: string;
    };

    if (!decoded || !decoded.id) {
      return NextResponse.json({ error: 'Token tidak valid' }, { status: 401 });
    }

    // Hanya admin yang bisa menambahkan log
    if (decoded.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Ambil semua outlet
    const outlets = await prisma.outlet.findMany({
      select: { id: true }
    });

    // Ambil semua user
    const users = await prisma.user.findMany({
      select: { id: true }
    });

    // Contoh pesan log
    const logMessages = [
      { type: 'INFO', message: 'Sistem dimulai' },
      { type: 'INFO', message: 'Backup database otomatis berhasil' },
      { type: 'WARNING', message: 'Penggunaan disk mendekati batas' },
      { type: 'ERROR', message: 'Gagal terhubung ke layanan pembayaran' },
      { type: 'SUCCESS', message: 'Sinkronisasi data berhasil' },
      { type: 'AUTH', message: 'Login berhasil' },
      { type: 'AUTH', message: 'Login gagal: password salah' },
      { type: 'SYSTEM', message: 'Pemeliharaan sistem dijadwalkan' },
      { type: 'INFO', message: 'Pengaturan sistem diperbarui' },
      { type: 'WARNING', message: 'Beberapa transaksi tertunda' },
    ];

    // Buat 50 log acak
    const logs = [];
    const now = new Date();

    for (let i = 0; i < 50; i++) {
      const randomOutlet = outlets[Math.floor(Math.random() * outlets.length)];
      const randomUser = users[Math.floor(Math.random() * users.length)];
      const randomMessage = logMessages[Math.floor(Math.random() * logMessages.length)];
      const randomDate = new Date(now.getTime() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000);

      logs.push({
        type: randomMessage.type,
        message: randomMessage.message,
        details: {
          source: 'seed',
          randomValue: Math.floor(Math.random() * 1000)
        },
        outletId: Math.random() > 0.3 ? randomOutlet.id : null,
        userId: Math.random() > 0.3 ? randomUser.id : null,
        createdAt: randomDate
      });
    }

    // Tambahkan log ke database
    const result = await prisma.systemLog.createMany({
      data: logs
    });

    return NextResponse.json({
      success: true,
      count: result.count,
      message: `${result.count} log berhasil ditambahkan`
    });
  } catch (error) {
    console.error('Error seeding logs:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menambahkan log' },
      { status: 500 }
    );
  }
}
