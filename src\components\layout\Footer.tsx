'use client';

import { FiMapPin, FiPhone, FiMail } from 'react-icons/fi';

export default function Footer() {
  return (
    <footer className="bg-white pt-16 pb-8 px-4 md:px-20">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-16">
        <div className="lg:col-span-2">
          <div className="flex items-center mb-4">
            <svg
              width="44"
              height="44"
              viewBox="0 0 500 500"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="mr-2"
            >
              <g transform="translate(125, 100) scale(0.55)">
                <path d="M248 45C319.36 45 439.36 142.56 439.36 203C439.36 236.64 392.68 264 355.28 264C313.96 264 168.4 264 140.72 264C103.32 264 56.64 236.64 56.64 203C56.64 142.56 176.64 45 248 45Z" fill="#F4BB45"/>
                <path d="M248 506C176.64 506 56.64 408.44 56.64 348C56.64 314.36 103.32 287 140.72 287C182.04 287 327.6 287 355.28 287C392.68 287 439.36 314.36 439.36 348C439.36 408.44 319.36 506 248 506Z" fill="#4E9E97"/>
                <circle cx="248" cy="264" r="50" fill="#4E9E97"/>
              </g>
            </svg>
            <h3 className="text-2xl font-bold">
              <span className="text-primary">Break</span>
              <span className="text-secondary">time</span>
            </h3>
          </div>
          <p className="text-gray-500 max-w-xs mb-2">
            Memberikan pengalaman relaksasi terbaik untuk kesejahteraan dan kenyamanan Anda.
          </p>
          <p className="text-primary-light mb-6">Badan Segar Urusan Lancar</p>
          <div className="flex gap-4">
            <a className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path></svg>
            </a>
            <a className="w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center text-secondary hover:bg-secondary hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"></path></svg>
            </a>
            <a className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"></path></svg>
            </a>
          </div>
        </div>
        
        <div>
          <h6 className="font-bold text-lg mb-4">Layanan</h6>
          <ul className="space-y-3">
            <li><a className="text-gray-500 hover:text-primary">Pijat Tradisional</a></li>
            <li><a className="text-gray-500 hover:text-primary">Pijat Kretek</a></li>
            <li><a className="text-gray-500 hover:text-primary">Lulur</a></li>
            <li><a className="text-gray-500 hover:text-primary">Masker Wajah</a></li>
            <li><a className="text-gray-500 hover:text-primary">Earcandle Treatment</a></li>
          </ul>
        </div>
        
        <div>
          <h6 className="font-bold text-lg mb-4">Jenis Layanan</h6>
          <ul className="space-y-3">
            <li><a className="text-gray-500 hover:text-primary">Outlet Service</a></li>
            <li><a className="text-gray-500 hover:text-primary">Home Service</a></li>
            <li><a className="text-gray-500 hover:text-primary">Layanan Tambahan</a></li>
            <li><a className="text-gray-500 hover:text-primary">Promo</a></li>
          </ul>
        </div>
        
        <div>
          <h6 className="font-bold text-lg mb-4">Kontak</h6>
          <ul className="space-y-3">
            <li className="flex items-center gap-2 text-gray-500">
              <FiMapPin className="text-primary" />
              <span>Jl. Emmy Saelan Depan 711 & Jl. Setiabudi Samping Darisa</span>
            </li>
            <li className="flex items-center gap-2 text-gray-500">
              <FiPhone className="text-primary" />
              <span>0812-4896-1009</span>
            </li>
            <li className="flex items-center gap-2 text-gray-500">
              <FiMail className="text-primary" />
              <span><EMAIL></span>
            </li>
          </ul>
        </div>
      </div>
      
      <div className="border-t pt-8 text-center">
        <p className="text-gray-500 text-sm">
          © 2024 BREAKTIME. Semua Hak Dilindungi.
        </p>
      </div>
    </footer>
  );
}
