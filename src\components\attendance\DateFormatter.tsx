import React from 'react';
import { format } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { id } from 'date-fns/locale';

interface DateFormatterProps {
  date: Date | string;
  format?: string;
  showTime?: boolean;
  className?: string;
}

const DateFormatter: React.FC<DateFormatterProps> = ({
  date,
  format: formatString,
  showTime = true,
  className = 'text-gray-700',
}) => {
  const timeZone = 'Asia/Makassar'; // WITA timezone
  
  // Konversi string ke Date object jika perlu
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // Konversi UTC ke zona waktu lokal (WITA)
  const zonedDate = toZonedTime(dateObj, timeZone);
  
  // Format default
  const defaultFormat = showTime 
    ? 'dd MMM yyyy, HH:mm:ss'
    : 'dd MMM yyyy';
  
  // Format tanggal dengan locale Indonesia
  const formattedDate = format(
    zonedDate,
    formatString || defaultFormat,
    { locale: id }
  );
  
  return (
    <span className={className}>
      {formattedDate}
    </span>
  );
};

export default DateFormatter; 