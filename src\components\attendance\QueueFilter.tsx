import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  BuildingOfficeIcon,
  ArrowPathIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

interface Outlet {
  id: string;
  name: string;
}

interface QueueFilterProps {
  onFilter: (filters: any) => void;
  outlets: Outlet[];
  loading: boolean;
  initialFilters?: {
    outletId?: string;
  };
}

const QueueFilter: React.FC<QueueFilterProps> = ({
  onFilter,
  outlets,
  loading,
  initialFilters = {}
}) => {
  // State untuk filter
  const [selectedOutlet, setSelectedOutlet] = useState(initialFilters.outletId || '');

  // Handle filter submit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    onFilter({
      outletId: selectedOutlet
    });
  };
  
  // Reset filter
  const handleReset = () => {
    setSelectedOutlet('');
    
    onFilter({
      outletId: ''
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-md mb-6 overflow-visible">
      <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
        <h3 className="text-base font-semibold text-gray-700 flex items-center">
          <FunnelIcon className="w-5 h-5 mr-2 text-[#4EA799]" />
          Filter Antrian
        </h3>
      </div>
      
      <form onSubmit={handleSubmit} className="p-4 pb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          {/* Filter Outlet */}
          <div>
            <label htmlFor="outlet" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
              <BuildingOfficeIcon className="w-4 h-4 mr-1 text-[#4EA799]" />
              Outlet
            </label>
            <select
              id="outlet"
              value={selectedOutlet}
              onChange={(e) => setSelectedOutlet(e.target.value)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#4EA799] appearance-none bg-white"
              aria-label="Pilih Outlet"
              style={{ backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`, backgroundPosition: `right 0.5rem center`, backgroundRepeat: `no-repeat`, backgroundSize: `1.5em 1.5em`, paddingRight: `2.5rem` }}
            >
              <option value="">Semua Outlet</option>
              {outlets.map((outlet) => (
                <option key={outlet.id} value={outlet.id}>
                  {outlet.name}
                </option>
              ))}
            </select>
          </div>
          
          {/* Placeholder untuk balance layout */}
          <div className="hidden md:flex md:items-end">
            <div className="flex space-x-3">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="button"
                onClick={handleReset}
                disabled={loading}
                className="px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 flex items-center shadow-sm"
              >
                <ArrowPathIcon className="w-4 h-4 mr-1" />
                Reset
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="submit"
                disabled={loading}
                className="px-5 py-2 bg-[#4EA799] text-white rounded-md hover:bg-[#3d8a7e] focus:outline-none focus:ring-2 focus:ring-[#4EA799] disabled:opacity-50 flex items-center shadow-sm"
              >
                {loading ? (
                  <>
                    <ArrowPathIcon className="w-4 h-4 mr-1 animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>
                    <FunnelIcon className="w-4 h-4 mr-1" />
                    Filter
                  </>
                )}
              </motion.button>
            </div>
          </div>
        </div>
        
        {/* Tombol untuk mobile */}
        <div className="mt-6 flex justify-end space-x-3 md:hidden">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            type="button"
            onClick={handleReset}
            disabled={loading}
            className="px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 flex items-center shadow-sm"
          >
            <ArrowPathIcon className="w-4 h-4 mr-1" />
            Reset
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            type="submit"
            disabled={loading}
            className="px-5 py-2 bg-[#4EA799] text-white rounded-md hover:bg-[#3d8a7e] focus:outline-none focus:ring-2 focus:ring-[#4EA799] disabled:opacity-50 flex items-center shadow-sm"
          >
            {loading ? (
              <>
                <ArrowPathIcon className="w-4 h-4 mr-1 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <FunnelIcon className="w-4 h-4 mr-1" />
                Filter
              </>
            )}
          </motion.button>
        </div>
      </form>
    </div>
  );
};

export default QueueFilter; 