'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { FiPlus, FiEdit, FiTrash2, FiX, FiSearch, FiFilter, FiClipboard, FiBarChart, FiTrendingUp, FiRotateCcw } from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import { useOutletContext } from '@/contexts/OutletContext';

interface Category {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  _count: {
    items: number;
  };
}

interface Item {
  id: string;
  name: string;
  code?: string;
  description?: string;
  quantity: number;
  unit: string;
  goodCondition: number;
  damaged: number;
  lost: number;
  minimumStock: number;
  location?: string;
  category: {
    id: string;
    name: string;
  };
  outlet?: {
    id: string;
    name: string;
  };
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export default function InventoryPage() {
  const router = useRouter();
  const { selectedOutletId } = useOutletContext();
  
  // State management
  const [activeTab, setActiveTab] = useState<'items' | 'categories'>('items');
  const [items, setItems] = useState<Item[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [showAllOutlets, setShowAllOutlets] = useState(false);
  const [showAllItems, setShowAllItems] = useState(false);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // Modal states
  const [showAddItemModal, setShowAddItemModal] = useState(false);
  const [showAddCategoryModal, setShowAddCategoryModal] = useState(false);
  const [showAnalyticsModal, setShowAnalyticsModal] = useState(false);
  const [editingItem, setEditingItem] = useState<Item | null>(null);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [deletingItem, setDeletingItem] = useState<Item | null>(null);
  const [deletingCategory, setDeletingCategory] = useState<Category | null>(null);

  // Modal management functions
  const closeAllModals = () => {
    setShowAddItemModal(false);
    setShowAddCategoryModal(false);
    setShowAnalyticsModal(false);
    setEditingItem(null);
    setEditingCategory(null);
    setDeletingItem(null);
    setDeletingCategory(null);
  };

  const openAddItemModal = () => {
    closeAllModals();
    setShowAddItemModal(true);
  };

  const openAddCategoryModal = () => {
    closeAllModals();
    setShowAddCategoryModal(true);
  };

  const openEditItemModal = (item: Item) => {
    closeAllModals();
    setEditingItem(item);
  };

  const openEditCategoryModal = (category: Category) => {
    closeAllModals();
    setEditingCategory(category);
  };

  const openDeleteItemModal = (item: Item) => {
    closeAllModals();
    setDeletingItem(item);
  };

  const openDeleteCategoryModal = (category: Category) => {
    closeAllModals();
    setDeletingCategory(category);
  };

  const openAnalyticsModal = () => {
    closeAllModals();
    setShowAnalyticsModal(true);
  };

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Reset search and filter functions
  const resetAllFilters = useCallback(() => {
    setSearchTerm('');
    setDebouncedSearchTerm('');
    setSelectedCategory('');
    setSelectedStatus('');
    setShowAllOutlets(false); // Kembali ke outlet terpilih 
    setShowAllItems(true); // Tampilkan semua item tanpa pagination
    toast.success('🔍 Menampilkan semua item dari outlet terpilih!');
  }, []);

  // Function to go back to paginated view
  const backToPagination = useCallback(() => {
    setShowAllItems(false);
    setPagination(prev => ({ ...prev, page: 1 }));
    toast.success('📄 Kembali ke mode pagination');
  }, []);

  // Fetch functions
  const fetchItems = async (page = 1) => {
    try {
      if (page === 1) {
        setLoading(true);
      } else {
        setSearchLoading(true);
      }
      
      const params = new URLSearchParams({
        page: showAllItems ? '1' : page.toString(),
        limit: showAllItems ? '9999' : pagination.limit.toString(), // Fetch all if showAllItems is true
        search: debouncedSearchTerm,
        categoryId: selectedCategory,
        status: selectedStatus
      });

      // Add outlet filter if outlet is selected and not showing all outlets
      if (selectedOutletId && !showAllOutlets) {
        params.append('outletId', selectedOutletId);
      }

      const response = await fetch(`/api/inventory/items?${params}`);
      const data = await response.json();

      if (response.ok) {
        setItems(data.items);
        if (showAllItems) {
          // Update pagination to show we're displaying all items
          setPagination({
            page: 1,
            limit: data.items.length,
            total: data.pagination.total,
            totalPages: 1
          });
        } else {
          setPagination(data.pagination);
        }
      } else {
        toast.error(data.error || 'Gagal memuat data item');
      }
    } catch (error) {
      console.error('Error fetching items:', error);
      toast.error('Terjadi kesalahan saat memuat data item');
    } finally {
      setLoading(false);
      setSearchLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/inventory/categories');
      const data = await response.json();

      if (response.ok) {
        setCategories(data.categories);
      } else {
        toast.error(data.error || 'Gagal memuat data kategori');
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast.error('Terjadi kesalahan saat memuat data kategori');
    }
  };

  // Effects
  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    if (activeTab === 'items') {
      fetchItems(1);
    }
  }, [activeTab, debouncedSearchTerm, selectedCategory, selectedStatus, selectedOutletId, showAllOutlets, showAllItems]);

  // Handle form submissions
  const handleAddItem = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    
    try {
      const response = await fetch('/api/inventory/items', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.get('name'),
          description: formData.get('description') || null,
          categoryId: formData.get('categoryId'),
          outletId: selectedOutletId,
          quantity: parseInt(formData.get('quantity') as string) || 0,
          goodCondition: parseInt(formData.get('goodCondition') as string) || 0,
          damaged: parseInt(formData.get('damaged') as string) || 0,
          lost: parseInt(formData.get('lost') as string) || 0,
          minimumStock: parseInt(formData.get('minimumStock') as string) || 0,
          location: formData.get('location') || null,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Item berhasil ditambahkan');
        fetchItems(pagination.page);
        closeAllModals();
      } else {
        toast.error(data.error || 'Gagal menambahkan item');
      }
    } catch (error) {
      console.error('Error adding item:', error);
      toast.error('Terjadi kesalahan saat menambahkan item');
    }
  };

  const handleEditItem = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!editingItem) return;

    const formData = new FormData(e.currentTarget);
    
    try {
      const response = await fetch(`/api/inventory/items/${editingItem.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.get('name'),
          code: formData.get('code') || null,
          description: formData.get('description') || null,
          categoryId: formData.get('categoryId'),
          quantity: parseInt(formData.get('quantity') as string) || 0,
          unit: formData.get('unit'),
          goodCondition: parseInt(formData.get('goodCondition') as string) || 0,
          damaged: parseInt(formData.get('damaged') as string) || 0,
          lost: parseInt(formData.get('lost') as string) || 0,
          minimumStock: parseInt(formData.get('minimumStock') as string) || 0,
          location: formData.get('location') || null,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Item berhasil diperbarui');
        fetchItems(pagination.page);
        closeAllModals();
      } else {
        toast.error(data.error || 'Gagal memperbarui item');
      }
    } catch (error) {
      console.error('Error updating item:', error);
      toast.error('Terjadi kesalahan saat memperbarui item');
    }
  };

  const handleAddCategory = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    
    try {
      const response = await fetch('/api/inventory/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.get('name'),
          description: formData.get('description') || null,
          isActive: formData.get('isActive') === 'on',
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Kategori berhasil ditambahkan');
        fetchCategories();
        closeAllModals();
      } else {
        toast.error(data.error || 'Gagal menambahkan kategori');
      }
    } catch (error) {
      console.error('Error adding category:', error);
      toast.error('Terjadi kesalahan saat menambahkan kategori');
    }
  };

  const handleEditCategory = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!editingCategory) return;

    const formData = new FormData(e.currentTarget);
    
    try {
      const response = await fetch(`/api/inventory/categories/${editingCategory.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.get('name'),
          description: formData.get('description') || null,
          isActive: formData.get('isActive') === 'on',
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Kategori berhasil diperbarui');
        fetchCategories();
        closeAllModals();
      } else {
        toast.error(data.error || 'Gagal memperbarui kategori');
      }
    } catch (error) {
      console.error('Error updating category:', error);
      toast.error('Terjadi kesalahan saat memperbarui kategori');
    }
  };

  const handleDeleteItem = async () => {
    if (!deletingItem) return;

    try {
      const response = await fetch(`/api/inventory/items/${deletingItem.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Item berhasil dihapus');
        fetchItems(pagination.page);
        closeAllModals();
      } else {
        toast.error(data.error || 'Gagal menghapus item');
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      toast.error('Terjadi kesalahan saat menghapus item');
    }
  };

  const handleDeleteCategory = async () => {
    if (!deletingCategory) return;

    try {
      const response = await fetch(`/api/inventory/categories/${deletingCategory.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Kategori berhasil dihapus');
        fetchCategories();
        closeAllModals();
      } else {
        toast.error(data.error || 'Gagal menghapus kategori');
      }
    } catch (error) {
      console.error('Error deleting category:', error);
      toast.error('Terjadi kesalahan saat menghapus kategori');
    }
      };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-yellow-50">
      <div className="p-6">
        {/* Modern Header with Gradient */}
        <div className="bg-gradient-to-r from-[#52a297] to-[#f2bc51] rounded-2xl p-8 mb-8 text-white shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2 flex items-center gap-3">
                <FiBarChart className="w-8 h-8" />
                Inventori Management
              </h1>
              <p className="text-green-100 text-lg">Kelola inventori dan stok barang dengan mudah</p>
              <div className="flex items-center gap-6 mt-4">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm">{items.length} Total Items</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                  <span className="text-sm">{categories.length} Categories</span>
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-3">
              <button
                onClick={() => router.push('/dashboard/inventori/audit')}
                className="btn bg-white/20 border-white/30 text-white hover:bg-white/30 backdrop-blur-sm transition-all duration-300 shadow-lg"
              >
                <FiClipboard className="w-5 h-5" />
                Audit Inventori
              </button>
              <button 
                onClick={openAnalyticsModal}
                className="btn bg-white/20 border-white/30 text-white hover:bg-white/30 backdrop-blur-sm transition-all duration-300 shadow-lg"
              >
                <FiTrendingUp className="w-5 h-5" />
                Analytics
              </button>
            </div>
          </div>
        </div>

        {/* Modern Tabs */}
        <div className="flex items-center justify-between mb-8">
          <div className="bg-white rounded-2xl p-2 shadow-lg border border-gray-100">
            <div className="flex gap-2">
              <button
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                  activeTab === 'items'
                    ? 'bg-gradient-to-r from-[#52a297] to-[#52a297] text-white shadow-lg transform scale-105'
                  : 'text-gray-600 hover:text-[#52a297] hover:bg-green-50'
                }`}
                onClick={() => setActiveTab('items')}
              >
                <FiBarChart className="w-4 h-4 inline mr-2" />
                Item Inventori
              </button>
              <button
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                  activeTab === 'categories'
                    ? 'bg-gradient-to-r from-[#f2bc51] to-[#f2bc51] text-white shadow-lg transform scale-105'
                  : 'text-gray-600 hover:text-[#f2bc51] hover:bg-yellow-50'
                }`}
                onClick={() => setActiveTab('categories')}
              >
                <FiFilter className="w-4 h-4 inline mr-2" />
                Kategori
              </button>
            </div>
          </div>

          <div className="flex gap-3">
            {activeTab === 'categories' && (
              <button
                onClick={openAddCategoryModal}
                className="btn bg-gradient-to-r from-[#f2bc51] to-[#f2bc51] text-white border-0 hover:from-[#f2bc51] hover:to-[#f2bc51] shadow-lg transition-all duration-300 hover:scale-105"
              >
                <FiPlus className="w-5 h-5" />
                Tambah Kategori
              </button>
            )}
            {activeTab === 'items' && (
              <button
                onClick={openAddItemModal}
                className="btn bg-gradient-to-r from-[#52a297] to-[#52a297] text-white border-0 hover:from-[#52a297] hover:to-[#52a297] shadow-lg transition-all duration-300 hover:scale-105"
              >
                <FiPlus className="w-5 h-5" />
                Tambah Item
              </button>
            )}
          </div>
        </div>

        {/* Items Tab */}
        {activeTab === 'items' && (
          <div>
            {/* Modern Filters */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-100 p-6 mb-8">
              {/* Outlet Filter Toggle */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-800">🏪 Filter Outlet</h3>
                  <label className="flex items-center gap-3 cursor-pointer">
                    <span className="text-sm font-medium text-gray-700">
                      {showAllOutlets ? '🌐 Semua Outlet' : '🏢 Outlet Terpilih'}
                    </span>
                    <input
                      type="checkbox"
                      className="toggle toggle-success"
                      checked={showAllOutlets}
                      onChange={(e) => setShowAllOutlets(e.target.checked)}
                    />
                    <span className="text-xs text-gray-500">
                      {showAllOutlets ? 'Menampilkan inventori dari semua outlet' : 'Menampilkan inventori outlet terpilih'}
                    </span>
                  </label>
                </div>
                
                {/* Warning untuk mode semua outlet */}
                {showAllOutlets && (
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                    <div className="flex items-center gap-2">
                      <span className="text-amber-600">⚠️</span>
                      <span className="text-sm font-medium text-amber-800">Mode Semua Outlet Aktif</span>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-sm text-amber-700">
                        Anda sedang melihat item dari SEMUA outlet ({pagination.total} item total). 
                        Matikan toggle di atas untuk melihat hanya item dari outlet yang dipilih di sidebar.
                      </p>
                      <button 
                        onClick={() => setShowAllOutlets(false)}
                        className="btn btn-sm bg-amber-100 hover:bg-amber-200 text-amber-800 border-amber-300 gap-1"
                      >
                        🏢 Beralih ke Outlet Terpilih
                      </button>
                    </div>
                  </div>
                )}
                
                {/* Info untuk mode outlet terpilih */}
                {!showAllOutlets && selectedOutletId && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-center gap-2">
                      <span className="text-blue-600">🏢</span>
                      <span className="text-sm font-medium text-blue-800">Filter Outlet Aktif</span>
                    </div>
                    <p className="text-sm text-blue-700 mt-1">
                      Menampilkan item dari outlet terpilih ({pagination.total} item). 
                      Nyalakan toggle di atas untuk melihat item dari semua outlet.
                    </p>
                  </div>
                )}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="relative group">
                  <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-[#52a297] transition-colors" />
                  <input
                    type="text"
                    placeholder="Cari item inventori..."
                    className="input input-bordered w-full pl-12 h-12 rounded-xl border-gray-200 focus:border-[#52a297] focus:ring-2 focus:ring-green-200 transition-all duration-300"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  {searchLoading && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <span className="loading loading-spinner loading-sm text-[#52a297]"></span>
                    </div>
                  )}
                </div>
                <select
                  className="select select-bordered w-full h-12 rounded-xl border-gray-200 focus:border-[#f2bc51] focus:ring-2 focus:ring-yellow-200 transition-all duration-300"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  <option value="" className="text-gray-500">🏷️ Semua Kategori</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id} className="flex items-center gap-2">
                      📦 {category.name} ({category._count?.items || 0} item)
                    </option>
                  ))}
                </select>
                <select
                  className="select select-bordered w-full h-12 rounded-xl border-gray-200 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-300"
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                >
                  <option value="">📊 Semua Status</option>
                  <option value="low_stock">⚠️ Stok Rendah</option>
                  <option value="out_of_stock">❌ Stok Habis</option>
                </select>
                <button
                  onClick={resetAllFilters}
                  className="btn bg-gradient-to-r from-gray-500 to-gray-600 text-white border-0 hover:from-gray-600 hover:to-gray-700 h-12 rounded-xl shadow-lg transition-all duration-300 hover:scale-105"
                  title="Reset semua filter"
                >
                  <FiRotateCcw className="w-5 h-5" />
                  Lihat Semua
                </button>
              </div>
            </div>

            {/* Search Results Info & Show All Button */}
            {!loading && (
              <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200 p-4 mb-6">
                <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:justify-between">
                  <div className="flex flex-wrap items-center gap-2">
                    <span className="text-sm text-gray-600 flex-shrink-0">
                      {showAllItems ? '🔍 Menampilkan semua:' : '📊 Hasil pencarian:'}
                    </span>
                    <span className="font-medium text-gray-900 flex-shrink-0">
                      {showAllItems ? `${pagination.total} item` : `Menampilkan ${pagination.limit} dari ${pagination.total} item`}
                    </span>
                    {debouncedSearchTerm && (
                      <span className="badge badge-primary badge-sm gap-1 px-3 py-1 text-xs font-medium whitespace-nowrap">
                        <FiSearch className="w-3 h-3" />
                        "<span className="truncate max-w-[80px]">{debouncedSearchTerm}</span>"
                      </span>
                    )}
                    {selectedCategory && (
                      <span className="badge badge-warning badge-sm gap-1 px-3 py-1 text-xs font-medium whitespace-nowrap">
                        <FiFilter className="w-3 h-3" />
                        <span className="truncate max-w-[100px]">{categories.find(c => c.id === selectedCategory)?.name}</span>
                      </span>
                    )}
                    {selectedStatus && (
                      <span className="badge badge-info badge-sm gap-1 px-3 py-1 text-xs font-medium whitespace-nowrap">
                        📊 {selectedStatus === 'low_stock' ? 'Stok Rendah' : 'Stok Habis'}
                      </span>
                    )}
                    {showAllOutlets && (
                      <span className="badge badge-success badge-sm gap-1 px-3 py-1 text-xs font-medium whitespace-nowrap">
                        🌐 Semua Outlet
                      </span>
                    )}
                    {showAllItems && (
                      <span className="badge badge-success badge-sm gap-1 px-3 py-1 text-xs font-medium whitespace-nowrap">
                        ✨ Mode Semua Item
                      </span>
                    )}
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {showAllItems ? (
                      <button 
                        onClick={backToPagination}
                        className="btn btn-ghost btn-sm gap-2 hover:bg-blue-100 hover:text-blue-600 flex-shrink-0"
                      >
                        📄 Kembali ke Pagination
                      </button>
                    ) : (
                      <button
                        onClick={resetAllFilters}
                        className="btn btn-ghost btn-sm gap-2 hover:bg-green-100 hover:text-green-600 flex-shrink-0"
                      >
                        <FiRotateCcw className="w-4 h-4" />
                        Lihat Semua Item
                      </button>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Items Grid */}
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <div className="text-center">
                  <span className="loading loading-spinner loading-lg text-[#52a297]"></span>
                  <p className="text-gray-600 mt-4">Memuat item inventori...</p>
                </div>
              </div>
            ) : items.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                  <FiBarChart className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {debouncedSearchTerm || selectedCategory || selectedStatus ? 
                    'Tidak Ada Hasil' : 'Belum Ada Item'}
                </h3>
                <p className="text-gray-600 mb-6">
                  {debouncedSearchTerm || selectedCategory || selectedStatus ? 
                    'Coba ubah kata kunci pencarian atau filter' : 
                    'Tambahkan item inventori pertama untuk memulai'}
                </p>
                <div className="flex gap-3 justify-center">
                  {(debouncedSearchTerm || selectedCategory || selectedStatus) && (
                    <button
                      onClick={resetAllFilters}
                      className="btn btn-outline gap-2"
                    >
                      <FiRotateCcw className="w-4 h-4" />
                      Reset Filter
                    </button>
                  )}
                  <button
                    onClick={openAddItemModal}
                    className="btn bg-gradient-to-r from-[#52a297] to-[#52a297] text-white border-0 gap-2"
                  >
                    <FiPlus className="w-4 h-4" />
                    Tambah Item
                  </button>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {items.map((item) => (
                  <div key={item.id} className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-100 p-4 sm:p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300 group">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1 min-w-0 pr-2">
                        <h3 className="font-bold text-gray-900 mb-2 text-base sm:text-lg group-hover:text-[#52a297] transition-colors break-words">{item.name}</h3>
                        {item.code && (
                          <p className="text-sm text-gray-500 mb-3 font-mono bg-gray-50 px-2 py-1 rounded-lg inline-block break-all">{item.code}</p>
                        )}
                        {showAllOutlets && item.outlet && (
                          <p className="text-xs text-blue-600 mb-2 bg-blue-50 px-2 py-1 rounded-lg inline-block font-medium break-words">
                            🏪 {item.outlet.name}
                          </p>
                        )}
                        <div className="flex flex-wrap items-center gap-2">
                          <span className="badge badge-primary badge-sm bg-gradient-to-r from-[#52a297] to-[#52a297] text-white border-0 shadow-md px-2 sm:px-3 py-1 text-xs font-medium whitespace-nowrap">
                            📦 <span className="truncate max-w-[120px]">{item.category.name}</span>
                          </span>
                        </div>
                      </div>
                      <div className="flex gap-1 sm:gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex-shrink-0">
                        <button
                          onClick={() => openEditItemModal(item)}
                          className="btn btn-ghost btn-sm btn-circle hover:bg-green-100 hover:text-[#52a297] transition-all duration-300"
                          title="Edit Item"
                        >
                          <FiEdit className="w-3 h-3 sm:w-4 sm:h-4" />
                        </button>
                        <button
                          onClick={() => openDeleteItemModal(item)}
                          className="btn btn-ghost btn-sm btn-circle hover:bg-red-100 hover:text-red-600 transition-all duration-300"
                          title="Hapus Item"
                        >
                          <FiTrash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                        </button>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-gray-600 font-medium">📦 Total Stok</span>
                          <span className="font-bold text-xl text-gray-900">{item.quantity} {item.unit}</span>
                        </div>
                        <div className="grid grid-cols-3 gap-2 text-sm">
                          <div className="text-center">
                            <div className="text-green-600 font-bold">{item.goodCondition}</div>
                            <div className="text-xs text-gray-500">✅ Baik</div>
                          </div>
                          {item.damaged > 0 && (
                            <div className="text-center">
                              <div className="text-[#f2bc51] font-bold">{item.damaged}</div>
                              <div className="text-xs text-gray-500">⚠️ Rusak</div>
                            </div>
                          )}
                          {item.lost > 0 && (
                            <div className="text-center">
                              <div className="text-red-600 font-bold">{item.lost}</div>
                              <div className="text-xs text-gray-500">❌ Hilang</div>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">🎯 Min. Stok:</span>
                        <span className={`font-bold ${
                          item.quantity <= item.minimumStock ? 'text-red-600' : 'text-gray-700'
                        }`}>
                          {item.minimumStock}
                        </span>
                      </div>
                      
                      {item.location && (
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-gray-600">📍 Lokasi:</span>
                          <span className="font-medium bg-green-50 text-[#52a297] px-2 py-1 rounded-lg">{item.location}</span>
                        </div>
                      )}
                    </div>

                    {item.quantity <= item.minimumStock && (
                      <div className="mt-4 p-3 bg-gradient-to-r from-red-50 to-yellow-50 border-l-4 border-red-500 rounded-lg">
                        <div className="flex items-center gap-2">
                          <span className="text-red-600 text-lg">⚠️</span>
                          <span className="text-red-700 font-medium text-sm">Stok Rendah!</span>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Modern Pagination - Hidden when showing all items */}
            {!showAllItems && pagination.totalPages > 1 && (
              <div className="flex flex-col items-center mt-8 gap-4">
                <div className="text-sm text-gray-600 text-center">
                  Menampilkan {((pagination.page - 1) * pagination.limit) + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} dari {pagination.total} item
                </div>
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-2">
                  <div className="flex items-center gap-2">
                    <button
                      className="btn btn-ghost btn-sm rounded-xl disabled:opacity-50 hover:bg-green-50 hover:text-[#52a297] transition-all duration-300"
                      disabled={pagination.page === 1 || searchLoading}
                      onClick={() => fetchItems(pagination.page - 1)}
                    >
                      {searchLoading && pagination.page > 1 ? (
                        <span className="loading loading-spinner loading-xs"></span>
                      ) : '← Previous'}
                    </button>
                    <div className="px-4 py-2 bg-gradient-to-r from-[#52a297] to-[#52a297] text-white rounded-xl font-medium shadow-md">
                      {pagination.page} of {pagination.totalPages}
                    </div>
                    <button
                      className="btn btn-ghost btn-sm rounded-xl disabled:opacity-50 hover:bg-green-50 hover:text-[#52a297] transition-all duration-300"
                      disabled={pagination.page === pagination.totalPages || searchLoading}
                      onClick={() => fetchItems(pagination.page + 1)}
                    >
                      {searchLoading && pagination.page < pagination.totalPages ? (
                        <span className="loading loading-spinner loading-xs"></span>
                      ) : 'Next →'}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Show All Items Info */}
            {showAllItems && pagination.total > 10 && (
              <div className="text-center mt-8 p-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl border border-green-200">
                <div className="flex items-center justify-center gap-3 text-green-800">
                  <FiTrendingUp className="w-6 h-6" />
                  <span className="font-semibold text-lg">
                    Menampilkan semua {pagination.total} item inventori! 🎉
                  </span>
                </div>
                <p className="text-green-600 mt-2 text-sm">
                  Scroll untuk melihat semua item atau kembali ke mode pagination untuk navigasi yang lebih mudah
                </p>
              </div>
            )}
          </div>
        )}

        {/* Modern Categories Tab */}
        {activeTab === 'categories' && (
          <>
            {categories.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                  <FiFilter className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Belum Ada Kategori
                </h3>
                <p className="text-gray-600 mb-6">
                  Tambahkan kategori pertama untuk mengorganisir item inventori
                </p>
                <button
                  onClick={openAddCategoryModal}
                  className="btn bg-gradient-to-r from-[#f2bc51] to-[#f2bc51] text-white border-0 gap-2"
                >
                  <FiPlus className="w-4 h-4" />
                  Tambah Kategori
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {categories.map((category) => (
                  <div key={category.id} className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-100 p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300 group">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="font-bold text-gray-900 mb-2 text-xl group-hover:text-[#f2bc51] transition-colors">{category.name}</h3>
                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{category.description || 'Tidak ada deskripsi'}</p>
                        <div className="flex flex-wrap items-center gap-2">
                          <span className="badge badge-sm bg-gradient-to-r from-[#f2bc51] to-[#f2bc51] text-white border-0 shadow-md px-3 py-1 text-xs font-medium whitespace-nowrap">
                            📦 {category._count.items} items
                          </span>
                          <span className={`badge badge-sm border-0 shadow-md px-3 py-1 text-xs font-medium whitespace-nowrap ${
                            category.isActive 
                              ? 'bg-gradient-to-r from-green-500 to-green-600 text-white' 
                              : 'bg-gradient-to-r from-red-500 to-red-600 text-white'
                          }`}>
                            {category.isActive ? '✅ Aktif' : '❌ Nonaktif'}
                          </span>
                        </div>
                      </div>
                      <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <button
                          onClick={() => openEditCategoryModal(category)}
                          className="btn btn-ghost btn-sm btn-circle hover:bg-yellow-100 hover:text-[#f2bc51] transition-all duration-300"
                        >
                          <FiEdit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => openDeleteCategoryModal(category)}
                          className="btn btn-ghost btn-sm btn-circle hover:bg-red-100 hover:text-red-600 transition-all duration-300"
                          disabled={category._count.items > 0}
                        >
                          <FiTrash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600 font-medium">📊 Total Items</span>
                        <span className="font-bold text-2xl text-gray-900">{category._count.items}</span>
                      </div>
                      {category._count.items > 0 && (
                        <div className="mt-2 text-xs text-gray-500">
                          💡 Kategori ini memiliki item aktif
                        </div>
                      )}
                    </div>
                    
                    {category._count.items > 0 && (
                      <div className="mt-4 p-3 bg-gradient-to-r from-green-50 to-green-50 border-l-4 border-[#52a297] rounded-lg">
                        <div className="flex items-center gap-2">
                          <span className="text-[#52a297] text-lg">ℹ️</span>
                          <span className="text-[#52a297] font-medium text-sm">Tidak dapat dihapus (ada {category._count.items} item)</span>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>

      {/* Add Item Modal */}
      {showAddItemModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Tambah Item</h3>
              <button
                onClick={closeAllModals}
                className="btn btn-ghost btn-sm btn-square"
              >
                <FiX className="w-4 h-4" />
              </button>
            </div>

            <form onSubmit={handleAddItem} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nama Item *
                </label>
                <input
                  type="text"
                  name="name"
                  className="input input-bordered w-full"
                  placeholder="Masukkan nama item"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Deskripsi
                </label>
                <textarea
                  name="description"
                  className="textarea textarea-bordered w-full"
                  rows={3}
                  placeholder="Masukkan deskripsi item"
                ></textarea>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Kategori *
                </label>
                <select 
                  name="categoryId" 
                  className="select select-bordered w-full h-12 rounded-xl border-gray-200 focus:border-[#52a297] focus:ring-2 focus:ring-green-200 transition-all duration-300" 
                  required
                >
                  <option value="" className="text-gray-500">🏷️ Pilih kategori</option>
                  {categories.filter(cat => cat.isActive).map((category) => (
                    <option key={category.id} value={category.id} className="flex items-center gap-2">
                      📦 {category.name}
                    </option>
                  ))}
                </select>
                <div className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                  <span className="text-blue-500">💡</span>
                  Pilih kategori untuk mengklasifikasi item
                </div>
                {editingItem && (
                  <div className="text-xs text-green-600 mt-1 flex items-center gap-1">
                    <span className="text-green-500">📦</span>
                    Kategori saat ini: <span className="font-medium">{editingItem.category.name}</span>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Total Kuantitas *
                </label>
                <input
                  type="number"
                  name="quantity"
                  className="input input-bordered w-full"
                  placeholder="0"
                  min="0"
                  required
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Kondisi Baik *
                  </label>
                  <input
                    type="number"
                    name="goodCondition"
                    className="input input-bordered w-full"
                    placeholder="0"
                    min="0"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Rusak
                  </label>
                  <input
                    type="number"
                    name="damaged"
                    className="input input-bordered w-full"
                    placeholder="0"
                    min="0"
                    defaultValue="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hilang
                  </label>
                  <input
                    type="number"
                    name="lost"
                    className="input input-bordered w-full"
                    placeholder="0"
                    min="0"
                    defaultValue="0"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Stok Minimum *
                  </label>
                  <input
                    type="number"
                    name="minimumStock"
                    className="input input-bordered w-full"
                    placeholder="0"
                    min="0"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Lokasi
                  </label>
                  <input
                    type="text"
                    name="location"
                    className="input input-bordered w-full"
                    placeholder="Masukkan lokasi item"
                  />
                </div>
              </div>

              <div className="flex items-center gap-3 pt-4">
                <button
                  type="button"
                  onClick={closeAllModals}
                  className="btn btn-ghost flex-1"
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="btn btn-primary flex-1"
                >
                  Simpan
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Item Modal */}
      {editingItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Edit Item</h3>
              <button
                onClick={closeAllModals}
                className="btn btn-ghost btn-sm btn-square"
              >
                <FiX className="w-4 h-4" />
              </button>
            </div>

            <form onSubmit={handleEditItem} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nama Item *
                  </label>
                  <input
                    type="text"
                    name="name"
                    className="input input-bordered w-full"
                    defaultValue={editingItem.name}
                    placeholder="Masukkan nama item"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Kode Item
                  </label>
                  <input
                    type="text"
                    name="code"
                    className="input input-bordered w-full"
                    defaultValue={editingItem.code || ''}
                    placeholder="Masukkan kode item"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Deskripsi
                </label>
                <textarea
                  name="description"
                  className="textarea textarea-bordered w-full"
                  rows={3}
                  defaultValue={editingItem.description || ''}
                  placeholder="Masukkan deskripsi item"
                ></textarea>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Kategori *
                </label>
                <select 
                  name="categoryId" 
                  className="select select-bordered w-full h-12 rounded-xl border-gray-200 focus:border-[#52a297] focus:ring-2 focus:ring-green-200 transition-all duration-300" 
                  defaultValue={editingItem.category.id} 
                  required
                >
                  <option value="" className="text-gray-500">🏷️ Pilih kategori</option>
                  {categories.filter(cat => cat.isActive).map((category) => (
                    <option key={category.id} value={category.id} className="flex items-center gap-2">
                      📦 {category.name}
                    </option>
                  ))}
                </select>
                <div className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                  <span className="text-blue-500">💡</span>
                  Pilih kategori untuk mengklasifikasi item
                </div>
                {editingItem && (
                  <div className="text-xs text-green-600 mt-1 flex items-center gap-1">
                    <span className="text-green-500">📦</span>
                    Kategori saat ini: <span className="font-medium">{editingItem.category.name}</span>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Total Kuantitas *
                  </label>
                  <input
                    type="number"
                    name="quantity"
                    className="input input-bordered w-full"
                    defaultValue={editingItem.quantity}
                    placeholder="0"
                    min="0"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Satuan *
                  </label>
                  <input
                    type="text"
                    name="unit"
                    className="input input-bordered w-full"
                    defaultValue={editingItem.unit}
                    placeholder="pcs, kg, liter, dll"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Kondisi Baik *
                  </label>
                  <input
                    type="number"
                    name="goodCondition"
                    className="input input-bordered w-full"
                    defaultValue={editingItem.goodCondition}
                    placeholder="0"
                    min="0"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Rusak
                  </label>
                  <input
                    type="number"
                    name="damaged"
                    className="input input-bordered w-full"
                    defaultValue={editingItem.damaged}
                    placeholder="0"
                    min="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hilang
                  </label>
                  <input
                    type="number"
                    name="lost"
                    className="input input-bordered w-full"
                    defaultValue={editingItem.lost}
                    placeholder="0"
                    min="0"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Stok Minimum *
                  </label>
                  <input
                    type="number"
                    name="minimumStock"
                    className="input input-bordered w-full"
                    defaultValue={editingItem.minimumStock}
                    placeholder="0"
                    min="0"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Lokasi
                  </label>
                  <input
                    type="text"
                    name="location"
                    className="input input-bordered w-full"
                    defaultValue={editingItem.location || ''}
                    placeholder="Masukkan lokasi item"
                  />
                </div>
              </div>

              <div className="flex items-center gap-3 pt-4">
                <button
                  type="button"
                  onClick={closeAllModals}
                  className="btn btn-ghost flex-1"
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="btn btn-primary flex-1"
                >
                  Simpan
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Add Category Modal */}
      {showAddCategoryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Tambah Kategori</h3>
              <button
                onClick={closeAllModals}
                className="btn btn-ghost btn-sm btn-square"
              >
                <FiX className="w-4 h-4" />
              </button>
            </div>

            <form onSubmit={handleAddCategory} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nama Kategori *
                </label>
                <input
                  type="text"
                  name="name"
                  className="input input-bordered w-full"
                  placeholder="Masukkan nama kategori"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Deskripsi
                </label>
                <textarea
                  name="description"
                  className="textarea textarea-bordered w-full"
                  rows={3}
                  placeholder="Masukkan deskripsi kategori"
                ></textarea>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  name="isActive"
                  className="checkbox checkbox-primary"
                  defaultChecked
                />
                <label className="text-sm text-gray-700">
                  Aktif
                </label>
              </div>

              <div className="flex items-center gap-3 pt-4">
                <button
                  type="button"
                  onClick={closeAllModals}
                  className="btn btn-ghost flex-1"
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="btn btn-primary flex-1"
                >
                  Simpan
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Category Modal */}
      {editingCategory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Edit Kategori</h3>
              <button
                onClick={closeAllModals}
                className="btn btn-ghost btn-sm btn-square"
              >
                <FiX className="w-4 h-4" />
              </button>
            </div>

            <form onSubmit={handleEditCategory} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nama Kategori *
                </label>
                <input
                  type="text"
                  name="name"
                  className="input input-bordered w-full"
                  defaultValue={editingCategory.name}
                  placeholder="Masukkan nama kategori"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Deskripsi
                </label>
                <textarea
                  name="description"
                  className="textarea textarea-bordered w-full"
                  rows={3}
                  defaultValue={editingCategory.description || ''}
                  placeholder="Masukkan deskripsi kategori"
                ></textarea>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  name="isActive"
                  className="checkbox checkbox-primary"
                  defaultChecked={editingCategory.isActive}
                />
                <label className="text-sm text-gray-700">
                  Aktif
                </label>
              </div>

              <div className="flex items-center gap-3 pt-4">
                <button
                  type="button"
                  onClick={closeAllModals}
                  className="btn btn-ghost flex-1"
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="btn btn-primary flex-1"
                >
                  Simpan
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Item Modal */}
      {deletingItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Konfirmasi Hapus</h3>
              <button
                onClick={closeAllModals}
                className="btn btn-ghost btn-sm btn-square"
              >
                <FiX className="w-4 h-4" />
              </button>
            </div>

            <div className="mb-6">
              <p className="text-gray-600 mb-2">
                Apakah Anda yakin ingin menghapus item <strong>"{deletingItem.name}"</strong>?
              </p>
              <p className="text-sm text-red-600">
                ⚠️ Item yang dihapus tidak dapat dikembalikan.
              </p>
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={closeAllModals}
                className="btn btn-ghost flex-1"
              >
                Batal
              </button>
              <button
                onClick={handleDeleteItem}
                className="btn btn-error flex-1"
              >
                Hapus
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Analytics Modal */}
      {showAnalyticsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <FiTrendingUp className="w-6 h-6 text-[#52a297]" />
                Analytics Inventori
              </h3>
              <button
                onClick={closeAllModals}
                className="btn btn-ghost btn-sm btn-circle hover:bg-gray-100"
              >
                <FiX className="w-5 h-5" />
              </button>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {/* Total Items */}
              <div className="bg-gradient-to-br from-[#52a297] to-[#52a297]/80 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm font-medium">Total Items</p>
                    <p className="text-3xl font-bold">{items?.length || 0}</p>
                  </div>
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <FiBarChart className="w-6 h-6" />
                  </div>
                </div>
              </div>

              {/* Total Categories */}
              <div className="bg-gradient-to-br from-[#f2bc51] to-[#f2bc51]/80 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-yellow-100 text-sm font-medium">Total Kategori</p>
                    <p className="text-3xl font-bold">{categories?.length || 0}</p>
                  </div>
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <FiFilter className="w-6 h-6" />
                  </div>
                </div>
              </div>

              {/* Low Stock Items */}
              <div className="bg-gradient-to-br from-red-500 to-red-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-red-100 text-sm font-medium">Stok Rendah</p>
                    <p className="text-3xl font-bold">
                      {items?.filter(item => (item.quantity || 0) <= (item.minimumStock || 0)).length || 0}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <span className="text-xl">⚠️</span>
                  </div>
                </div>
              </div>

              {/* Total Stock Value */}
              <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm font-medium">Total Stok</p>
                    <p className="text-3xl font-bold">
                      {items?.reduce((total, item) => total + (item.quantity || 0), 0) || 0}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <span className="text-xl">📦</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Category Analysis */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* Items per Category */}
              <div className="bg-white border border-gray-200 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <span className="text-[#52a297]">📊</span>
                  Items per Kategori
                </h4>
                <div className="space-y-3">
                  {categories.map(category => (
                    <div key={category.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium text-gray-700">{category.name}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-gradient-to-r from-[#52a297] to-[#52a297] h-2 rounded-full transition-all duration-300"
                            style={{ 
                              width: `${categories.length > 0 && categories.some(c => c._count?.items > 0) ? 
                                ((category._count?.items || 0) / Math.max(...categories.map(c => c._count?.items || 0))) * 100 : 0}%` 
                            }}
                          ></div>
                        </div>
                        <span className="text-sm font-bold text-gray-900 min-w-[2rem] text-right">
                          {category._count?.items || 0}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Stock Status */}
              <div className="bg-white border border-gray-200 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <span className="text-[#f2bc51]">📈</span>
                  Status Stok
                </h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center gap-2">
                      <span className="text-blue-600">💎</span>
                      <span className="font-medium text-blue-700">Kondisi Baik</span>
                    </div>
                    <span className="text-lg font-bold text-blue-600">
                      {items.filter(item => (item.damaged || 0) === 0 && (item.lost || 0) === 0).length || 0}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center gap-2">
                      <span className="text-green-600">✅</span>
                      <span className="font-medium text-green-700">Stok Normal</span>
                    </div>
                    <span className="text-lg font-bold text-green-600">
                      {items.filter(item => (item.quantity || 0) > (item.minimumStock || 0)).length || 0}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                    <div className="flex items-center gap-2">
                      <span className="text-red-600">⚠️</span>
                      <span className="font-medium text-red-700">Stok Rendah</span>
                    </div>
                    <span className="text-lg font-bold text-red-600">
                      {items.filter(item => (item.quantity || 0) <= (item.minimumStock || 0)).length || 0}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex items-center gap-2">
                      <span className="text-gray-600">📦</span>
                      <span className="font-medium text-gray-700">Total Barang Rusak</span>
                    </div>
                    <span className="text-lg font-bold text-gray-600">
                      {items.reduce((total, item) => total + (item.damaged || 0), 0) || 0}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="flex items-center gap-2">
                      <span className="text-orange-600">❌</span>
                      <span className="font-medium text-orange-700">Total Barang Hilang</span>
                    </div>
                    <span className="text-lg font-bold text-orange-600">
                      {items.reduce((total, item) => total + (item.lost || 0), 0) || 0}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Low Stock Alert */}
            {items.filter(item => item.quantity <= item.minimumStock).length > 0 && (
              <div className="bg-gradient-to-r from-red-50 to-orange-50 border-l-4 border-red-500 rounded-lg p-6 mb-6">
                <h4 className="text-lg font-semibold text-red-700 mb-3 flex items-center gap-2">
                  <span className="text-red-600">🚨</span>
                  Peringatan Stok Rendah
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {items
                    .filter(item => item.quantity <= item.minimumStock)
                    .slice(0, 6)
                    .map(item => (
                    <div key={item.id} className="flex items-center justify-between p-3 bg-white rounded-lg border border-red-200">
                      <div>
                        <p className="font-medium text-gray-900">{item.name}</p>
                        <p className="text-sm text-gray-600">{item.category.name}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-red-600 font-medium">
                          {item.quantity}/{item.minimumStock}
                        </p>
                        <p className="text-xs text-gray-500">Sisa/Min</p>
                      </div>
                    </div>
                  ))}
                </div>
                {items.filter(item => item.quantity <= item.minimumStock).length > 6 && (
                  <p className="text-sm text-red-600 mt-3 text-center">
                    +{items.filter(item => item.quantity <= item.minimumStock).length - 6} item lainnya memerlukan perhatian
                  </p>
                )}
              </div>
            )}

            <div className="flex justify-end">
              <button
                onClick={closeAllModals}
                className="btn bg-gradient-to-r from-[#52a297] to-[#52a297] text-white border-0 hover:from-[#52a297] hover:to-[#52a297] px-8"
              >
                Tutup
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Category Modal */}
      {deletingCategory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Konfirmasi Hapus</h3>
              <button
                onClick={closeAllModals}
                className="btn btn-ghost btn-sm btn-square"
              >
                <FiX className="w-4 h-4" />
              </button>
            </div>

            <div className="mb-6">
              <p className="text-gray-600 mb-2">
                Apakah Anda yakin ingin menghapus kategori <strong>"{deletingCategory.name}"</strong>?
              </p>
              <p className="text-sm text-red-600">
                ⚠️ Kategori yang dihapus tidak dapat dikembalikan.
              </p>
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={closeAllModals}
                className="btn btn-ghost flex-1"
              >
                Batal
              </button>
              <button
                onClick={handleDeleteCategory}
                className="btn btn-error flex-1"
              >
                Hapus
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}