import { NextRequest } from 'next/server';
import * as jwt from 'jsonwebtoken';

// Gunakan variabel environment dan pastikan ada nilai default yang aman
const JWT_SECRET = process.env.JWT_SECRET || 'breaktime_session_secret_key_for_jwt_auth';

/**
 * Fungsi untuk mengambil user ID dari token di cookie
 * @param request NextRequest object
 * @returns User ID atau null jika tidak ditemukan
 */
export function getUserIdFromToken(request: NextRequest): string | null {
  try {
    // Ambil token dari cookie
    const cookieHeader = request.headers.get('cookie');
    if (!cookieHeader) return null;

    const cookies = cookieHeader.split(';').map(c => c.trim());
    const userTokenCookie = cookies.find(c => c.startsWith('user_token='));
    if (!userTokenCookie) return null;

    const token = userTokenCookie.split('=')[1];
    
    // Verifikasi token
    const decoded = jwt.verify(token, JWT_SECRET) as {
      id: string;
      name: string;
      email: string;
      role: string;
    };

    if (!decoded || !decoded.id) return null;
    
    return decoded.id;
  } catch (error) {
    console.error('Error getting user ID from token:', error);
    return null;
  }
}
