import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import { BookingStatus } from '@/lib/types/prisma';
import { logBooking } from '@/lib/logger';
import { NextRequest } from 'next/server';
import { generateBookingId } from '@/lib/idGenerator';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-utils';
import { getUserIdFromToken } from '@/lib/auth-utils';

// GET semua booking
export async function GET(request: Request) {
  try {
    // Ambil URL parameters
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');
    const customerId = searchParams.get('customerId');
    const therapistId = searchParams.get('therapistId');
    // const serviceId = searchParams.get('serviceId'); // Filter by service needs adjustment if required
    const status = searchParams.get('status');
    const specificDate = searchParams.get('date'); // Parameter for a specific date
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const debug = searchParams.get('debug') === 'true'; // Parameter untuk mengaktifkan logging tambahan

    if (debug) {
      console.log('API Booking - Debug mode enabled');
      console.log('API Booking - Request URL:', request.url);
      console.log('API Booking - Environment:', process.env.NODE_ENV);
      console.log('API Booking - Parameters:', {
        outletId,
        customerId,
        therapistId,
        status,
        specificDate,
        startDate,
        endDate
      });

      // Log Prisma client info
      console.log('API Booking - Prisma client info:', {
        clientVersion: prisma._clientVersion,
        activeProvider: prisma._activeProvider,
        engineConfig: prisma._engineConfig ? {
          datamodel: '(omitted)',
          datasources: prisma._engineConfig.overrideDatasources,
          env: Object.keys(prisma._engineConfig.env || {}),
        } : 'Not available'
      });
    }

    // Build query dengan tipe yang benar
    const where: Prisma.BookingWhereInput = {};

    // Filter berdasarkan outlet
    if (outletId) {
      where.outletId = outletId;
    }

    // Filter berdasarkan customer
    if (customerId) {
      where.customerId = customerId;
    }

    // Filter berdasarkan terapis
    if (therapistId) {
      where.therapistId = therapistId;
    }

    // Filter berdasarkan status
    if (status) {
      // Pastikan status adalah salah satu dari enum BookingStatus
      if (Object.values(BookingStatus).includes(status as BookingStatus)) {
          where.status = status as BookingStatus;
      }
    }

    // Filter berdasarkan tanggal spesifik ATAU rentang tanggal
    console.log('API Booking - Filter parameters:', { specificDate, startDate, endDate });

    // Inisialisasi filter tanggal
    where.bookingDate = {};

    // Jika ada specificDate, gunakan sebagai filter
    if (specificDate) {
      const startOfDay = new Date(specificDate);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(specificDate);
      endOfDay.setHours(23, 59, 59, 999);

      console.log('API Booking - Using specificDate filter:', {
        specificDate,
        startOfDay: startOfDay.toISOString(),
        endOfDay: endOfDay.toISOString()
      });

      where.bookingDate.gte = startOfDay;
      where.bookingDate.lte = endOfDay;
    }

    // Jika ada startDate atau endDate, gunakan sebagai filter tambahan atau pengganti
    if (startDate || endDate) {
      console.log('API Booking - Using startDate/endDate filter');

      if (startDate) {
        const start = new Date(startDate);
        start.setHours(0, 0, 0, 0);
        where.bookingDate.gte = start;
        console.log('API Booking - startDate filter:', { startDate, start: start.toISOString() });
      }

      if (endDate) {
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        where.bookingDate.lte = end;
        console.log('API Booking - endDate filter:', { endDate, end: end.toISOString() });
      }

      console.log('API Booking - Final date filter:', where.bookingDate);
    }

    // Log query yang akan dijalankan
    if (debug) {
      console.log('API Booking - Final query filter:', JSON.stringify(where, null, 2));

      // Coba dapatkan query SQL (hanya untuk debugging)
      try {
        const queryLog = await prisma.$queryRaw`SELECT 1`;
        console.log('API Booking - Prisma connection test:', queryLog);
      } catch (e) {
        console.error('API Booking - Prisma connection test failed:', e);
      }
    }

    // Ambil semua booking dari database
    const bookings = await prisma.booking.findMany({
      where,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            phone: true
          }
        },
        outlet: { // Include outlet data
          select: {
            id: true,
            name: true
          }
        },
        therapist: {
          select: {
            id: true,
            name: true,
            specialization: true
          }
        },
        bookingServices: { // Include BookingService
          select: {
            service: { // Include Service through BookingService
              select: {
                id: true,
                name: true,
                duration: true,
                price: true,
              }
            }
          }
        }
      },
      orderBy: [
        { bookingDate: 'desc' }, // Ganti dari date
        { bookingTime: 'asc' }  // Tambah urutan waktu
      ]
    });

    // Log jumlah booking yang ditemukan
    console.log(`API Booking - Found ${bookings.length} bookings with filter:`, where);

    if (bookings.length > 0) {
      console.log('API Booking - Sample booking dates:', bookings.slice(0, 3).map(b => ({
        id: b.id.substring(0, 8),
        bookingDate: b.bookingDate,
        bookingTime: b.bookingTime
      })));
    }

    // Jika debug mode dan tidak ada booking ditemukan, coba query raw
    if (debug && bookings.length === 0) {
      try {
        // Query untuk memeriksa apakah ada booking di tanggal tersebut
        const rawBookings = await prisma.$queryRaw`
          SELECT id, "bookingDate", "bookingTime", "outletId"
          FROM "Booking"
          WHERE "outletId" = ${outletId}
          AND "bookingDate" >= ${new Date(startDate + 'T00:00:00Z')}
          AND "bookingDate" <= ${new Date(endDate + 'T23:59:59Z')}
          LIMIT 5
        `;

        console.log('API Booking - Raw query results:', rawBookings);

        // Query untuk memeriksa total booking di database
        const totalBookings = await prisma.$queryRaw`SELECT COUNT(*) FROM "Booking"`;
        console.log('API Booking - Total bookings in database:', totalBookings);

        // Query untuk memeriksa booking di outlet tertentu
        const outletBookings = await prisma.$queryRaw`SELECT COUNT(*) FROM "Booking" WHERE "outletId" = ${outletId}`;
        console.log(`API Booking - Total bookings for outlet ${outletId}:`, outletBookings);
      } catch (e) {
        console.error('API Booking - Raw query failed:', e);
      }
    }

    // Format ulang hasil untuk menyederhanakan akses layanan
    const formattedBookings = bookings.map(booking => ({
      ...booking,
      services: booking.bookingServices.map(bs => bs.service) // Ubah bookingServices menjadi services
    }));

    // Kembalikan data booking
    return NextResponse.json({
      message: 'Data booking berhasil diambil',
      bookings: formattedBookings
    });
  } catch (error) {
    console.error('Error fetching bookings:', error);

    // Log detail error untuk debugging
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    } else {
      console.error('Unknown error type:', typeof error);
    }

    return NextResponse.json(
      {
        error: 'Terjadi kesalahan saat mengambil data booking',
        details: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// POST untuk membuat booking baru
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    // Allow creating bookings from online reservation without authentication
    const userId = getUserIdFromToken(req);
    const userAuthHeader = req.headers.get('x-user-id');
    const userNameHeader = req.headers.get('x-user-name');

    // Izinkan pembuatan booking tanpa otentikasi jika request berasal dari formulir online
    const isOnlineReservation = (!userId && userAuthHeader && userNameHeader) ||
                              (body.customerName && body.customerPhone);

    if (!userId && !isOnlineReservation) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Validate required fields
    const {
      customerId,
      customerIds,
      serviceIds,
      services,
      therapistId,
      outletId,
      date,
      time,
      customerName,
      customerPhone,
      customerEmail,
      notes,
      status = "PENDING",
    } = body;

    // For online reservations, check if customer exists or create new one
    let actualCustomerId = customerId;

    if (isOnlineReservation && customerName && customerPhone) {
      // Find customer by phone number
      const existingCustomer = await prisma.customer.findFirst({
        where: { phone: customerPhone }
      });

      if (existingCustomer) {
        actualCustomerId = existingCustomer.id;
      } else {
        // Create new customer
        const newCustomer = await prisma.customer.create({
          data: {
            name: customerName,
            phone: customerPhone,
            email: customerEmail || null,
            points: 0,
            createdById: null, // Created through online system
            outletId: outletId,
          }
        });
        actualCustomerId = newCustomer.id;
      }
    }

    // Check for required fields
    if (!outletId || (!actualCustomerId && !customerIds?.length) || !date || !time) {
      return NextResponse.json(
        { message: "Missing required fields" },
        { status: 400 }
      );
    }

    // At least one service is required
    const hasServices =
      (serviceIds && serviceIds.length > 0) ||
      (services && services.length > 0);

    if (!hasServices) {
      return NextResponse.json(
        { message: "At least one service is required" },
        { status: 400 }
      );
    }

    // Generate unique booking ID
    const displayId = await generateBookingId();

    // KONVERSI: Pada frontend kita terima date dan time sebagai string terpisah,
    // namun pada model Prisma kita memerlukan bookingDate (DateTime) dan bookingTime (String)

    // 1. Konversi string date (YYYY-MM-DD) dan time (HH:MM) menjadi bookingDate (JavaScript Date)
    const [year, month, day] = date.split('-').map(Number);
    const [hours, minutes] = time.split(':').map(Number);
    const bookingDate = new Date(year, month - 1, day, hours, minutes);

    // 2. Simpan time string langsung sebagai bookingTime
    const bookingTime = time;

    // 3. Buat objek data yang sesuai dengan model Prisma (tidak menyertakan date & time)
    const bookingData = {
      status,
      therapistId: therapistId || null,
      outletId,
      notes: notes || null,
      createdById: userId || userAuthHeader || null,
      customerId: actualCustomerId || null,
      displayId,
      bookingDate,
      bookingTime,
    };

    // Create the booking
    const booking = await prisma.booking.create({
      data: bookingData,
    });

    // Handle additional customers (if any)
    if (customerIds && customerIds.length) {
      const additionalCustomers = customerIds.map((id: string) => ({
        bookingId: booking.id,
        customerId: id,
      }));

      if (additionalCustomers.length > 0) {
        await prisma.bookingCustomer.createMany({
          data: additionalCustomers,
        });
      }
    }

    // Handle selected services (new format)
    if (services && services.length > 0) {
      // PENTING: BookingService memerlukan field price yang diambil dari database
      // Frontend hanya mengirimkan id dan quantity, kita harus mengambil price dari database

      // 1. Ambil service IDs dari request
      const serviceIds = services.map((svc: any) => svc.id);

      // 2. Query database untuk mendapatkan data layanan termasuk harga
      const servicesData = await prisma.service.findMany({
        where: {
          id: {
            in: serviceIds
          }
        },
        select: {
          id: true,
          price: true
        }
      });

      // 3. Buat BookingService dengan harga dari database (sesuai model Prisma)
      const bookingServices = services.map((svc: any) => {
        const serviceData = servicesData.find(s => s.id === svc.id);
        if (!serviceData) {
          throw new Error(`Service with ID ${svc.id} not found`);
        }

        return {
          bookingId: booking.id,
          serviceId: svc.id,
          quantity: svc.quantity || 1,
          price: serviceData.price || 0, // Field price wajib ada dalam model BookingService
        };
      });

      await prisma.bookingService.createMany({
        data: bookingServices,
      });
    }
    // Handle legacy format (serviceIds array)
    else if (serviceIds && serviceIds.length > 0) {
      // PENTING: Sama seperti di atas, kita perlu mengambil price dari database
      const servicesData = await prisma.service.findMany({
        where: {
          id: {
            in: serviceIds
          }
        },
        select: {
          id: true,
          price: true
        }
      });

      const bookingServices = serviceIds.map((serviceId: string) => {
        const serviceData = servicesData.find(s => s.id === serviceId);
        if (!serviceData) {
          throw new Error(`Service with ID ${serviceId} not found`);
        }

        return {
          bookingId: booking.id,
          serviceId,
          quantity: 1,
          price: serviceData.price || 0, // Field price wajib ada dalam model BookingService
        };
      });

      await prisma.bookingService.createMany({
        data: bookingServices,
      });
    }

    // Ambil data booking lengkap termasuk customer, therapist, dan services
    const completeBooking = await prisma.booking.findUnique({
      where: { id: booking.id },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            phone: true,
            points: true
          }
        },
        therapist: {
          select: {
            id: true,
            name: true,
            specialization: true
          }
        },
        bookingServices: {
          include: {
            service: true
          }
        },
        outlet: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    return NextResponse.json({
      message: "Booking created successfully",
      booking: completeBooking || {
        id: booking.id,
        displayId: booking.displayId,
        createdAt: booking.createdAt,
        status: booking.status,
      },
    });
  } catch (error) {
    console.error("Error creating booking:", error);
    return NextResponse.json(
      { message: "Failed to create booking", error: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}