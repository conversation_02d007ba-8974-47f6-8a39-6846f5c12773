'use client';

import React, { useRef } from 'react';
import {
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>,
  <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>key, Tooltip as RechartsTooltip
} from 'recharts';

// Helper <PERSON><PERSON> (Gold & Teal)
const LOGO_COLORS = ['#FDBA74', '#2DD4BF', '#FCD34D', '#5EEAD4', '#FB923C', '#0D9488']; 

// Helper untuk label Pie Chart
const RADIAN = Math.PI / 180;
const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }: any) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central" fontSize={10}>
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

interface BookingConversionData {
  date: string;
  totalBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  noShowBookings: number;
  conversionRate: number;
}

interface BookingConversionChartProps {
  data: BookingConversionData[];
  type?: 'bar' | 'pie' | 'line';
  title?: string;
  height?: number;
  className?: string;
}

const BookingConversionChart: React.FC<BookingConversionChartProps> = ({
  data,
  type = 'bar',
  title = 'Konversi Booking',
  height = 300,
  className = ''
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  // Prepare data for pie chart
  const pieData = [
    { name: 'Selesai', value: data.reduce((sum, item) => sum + item.completedBookings, 0) },
    { name: 'Dibatalkan', value: data.reduce((sum, item) => sum + item.cancelledBookings, 0) },
    { name: 'Tidak Hadir', value: data.reduce((sum, item) => sum + item.noShowBookings, 0) }
  ];

  // Calculate overall conversion rate
  const totalBookings = data.reduce((sum, item) => sum + item.totalBookings, 0);
  const completedBookings = data.reduce((sum, item) => sum + item.completedBookings, 0);
  const overallConversionRate = totalBookings > 0 ? (completedBookings / totalBookings) * 100 : 0;

  return (
    <div className={`w-full ${className}`}>
      {title && (
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-semibold">{title}</h3>
          <div className="text-sm">
            <span className="font-medium">Tingkat Konversi: </span>
            <span className={`${overallConversionRate >= 70 ? 'text-green-600' : overallConversionRate >= 50 ? 'text-amber-600' : 'text-red-600'}`}>
              {overallConversionRate.toFixed(1)}%
            </span>
          </div>
        </div>
      )}
      <div ref={chartRef} style={{ width: '100%', height: `${height}px` }}>
        <ResponsiveContainer>
          {type === 'bar' ? (
            <BarChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
              <XAxis dataKey="date" tick={{ fill: '#6b7280' }} />
              <YAxis tick={{ fill: '#6b7280' }} />
              <Tooltip />
              <Legend />
              <Bar dataKey="completedBookings" name="Selesai" fill="#10b981" />
              <Bar dataKey="cancelledBookings" name="Dibatalkan" fill="#f43f5e" />
              <Bar dataKey="noShowBookings" name="Tidak Hadir" fill="#f59e0b" />
            </BarChart>
          ) : (
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius="80%"
                innerRadius="40%"
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                paddingAngle={2}
              >
                <Cell key="cell-0" fill="#10b981" /> {/* Selesai - Hijau */}
                <Cell key="cell-1" fill="#f43f5e" /> {/* Dibatalkan - Merah */}
                <Cell key="cell-2" fill="#f59e0b" /> {/* Tidak Hadir - Kuning */}
              </Pie>
              <Tooltip formatter={(value: number) => [`${value} booking`, '']} />
              <Legend />
            </PieChart>
          )}
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default BookingConversionChart;
