-- CreateTable
CREATE TABLE "TherapistServiceCommission" (
    "id" TEXT NOT NULL,
    "therapistId" TEXT NOT NULL,
    "serviceId" TEXT NOT NULL,
    "commission" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TherapistServiceCommission_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "TherapistServiceCommission_therapistId_idx" ON "TherapistServiceCommission"("therapistId");

-- CreateIndex
CREATE INDEX "TherapistServiceCommission_serviceId_idx" ON "TherapistServiceCommission"("serviceId");

-- CreateIndex
CREATE UNIQUE INDEX "TherapistServiceCommission_therapistId_serviceId_key" ON "TherapistServiceCommission"("therapistId", "serviceId");

-- AddForeignKey
ALTER TABLE "TherapistServiceCommission" ADD CONSTRAINT "TherapistServiceCommission_therapistId_fkey" FOREIGN KEY ("therapistId") REFERENCES "Therapist"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TherapistServiceCommission" ADD CONSTRAINT "TherapistServiceCommission_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "Service"("id") ON DELETE CASCADE ON UPDATE CASCADE;
