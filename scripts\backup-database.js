// Script untuk backup database ke file JSON
const { PrismaClient } = require('../prisma/generated/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function backupDatabase() {
  try {
    console.log('🔄 Memulai proses backup database...');
    
    // Dapatkan semua model dari Prisma
    const models = Object.keys(prisma).filter(
      key => typeof prisma[key] === 'object' && 
      prisma[key] !== null && 
      Object.keys(prisma[key]).includes('findMany')
    );
    
    console.log(`📋 Ditemukan ${models.length} model untuk di-backup`);
    
    // Buat array untuk menyimpan semua data
    const backupData = [];
    
    // Ambil data dari setiap model
    for (const model of models) {
      console.log(`🔍 Mengambil data dari model: ${model}`);
      
      try {
        // Ambil semua data dari model
        const data = await prisma[model].findMany();
        
        // Tambahkan ke array backup
        backupData.push({
          name: model,
          data: data
        });
        
        console.log(`✅ Berhasil mengambil ${data.length} data dari model ${model}`);
      } catch (error) {
        console.error(`❌ Error saat mengambil data dari model ${model}:`, error.message);
      }
    }
    
    // Buat direktori backup jika belum ada
    const backupDir = path.join(process.cwd(), 'database-backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir);
    }
    
    // Buat nama file dengan timestamp
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const backupFilePath = path.join(backupDir, `backup-${timestamp}.json`);
    
    // Tulis data ke file
    fs.writeFileSync(backupFilePath, JSON.stringify(backupData, null, 2));
    
    console.log(`✅ Backup database berhasil disimpan ke: ${backupFilePath}`);
    return backupFilePath;
  } catch (error) {
    console.error('❌ Error saat melakukan backup database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Jalankan fungsi backup
backupDatabase()
  .then(filePath => {
    console.log(`🎉 Proses backup selesai! File tersimpan di: ${filePath}`);
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Terjadi kesalahan saat backup database:', error);
    process.exit(1);
  });