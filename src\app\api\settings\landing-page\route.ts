import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET pengaturan landing page
export async function GET() {
  try {
    // Ambil pengaturan dengan kategori 'landing-page'
    const settings = await prisma.setting.findMany({
      where: {
        category: 'landing-page'
      },
      orderBy: {
        label: 'asc'
      }
    });

    return NextResponse.json({
      message: 'Data pengaturan landing page berhasil diambil',
      settings
    });
  } catch (error) {
    console.error('Error fetching landing page settings:', error);
    return NextResponse.json(
      { error: 'Ter<PERSON>di kesalahan saat mengambil data pengaturan landing page' },
      { status: 500 }
    );
  }
}

// POST untuk membuat atau memperbarui pengaturan landing page
export async function POST(request: Request) {
  try {
    const body = await request.json();
    // body bisa array atau objek tunggal
    const settingsData = Array.isArray(body) ? body : [body];
    
    if (settingsData.length === 0) {
      return NextResponse.json(
        { error: 'Tidak ada data pengaturan yang diterima' },
        { status: 400 }
      );
    }
    
    // Validasi semua data
    for (const setting of settingsData) {
      const { key, value, label } = setting;
      if (!key || value === undefined || !label) {
        return NextResponse.json(
          { error: 'Key, value, dan label diperlukan untuk semua pengaturan' },
          { status: 400 }
        );
      }
    }
    
    // Ubah semua pengaturan dalam transaksi
    const results = await prisma.$transaction(
      settingsData.map(setting => {
        const { key, value, label, type = 'TEXT', options } = setting;
        
        // Pastikan kategori adalah 'landing-page'
        return prisma.setting.upsert({
          where: { key },
          create: {
            key,
            value,
            category: 'landing-page',
            label,
            type,
            options: options || null
          },
          update: {
            value,
            label,
            type,
            options: options || null
          }
        });
      })
    );
    
    return NextResponse.json({
      message: 'Pengaturan landing page berhasil diperbarui',
      settings: results
    });
  } catch (error) {
    console.error('Error updating landing page settings:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui pengaturan landing page' },
      { status: 500 }
    );
  }
} 