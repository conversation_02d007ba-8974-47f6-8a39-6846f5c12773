const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Direktori untuk mencari file
const rootDir = './src';

// Fungsi untuk mencari file secara rekursif
function findFiles(dir, pattern) {
  let results = [];
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Rekursif ke subdirektori
      results = results.concat(findFiles(filePath, pattern));
    } else if (pattern.test(file)) {
      results.push(filePath);
    }
  }
  
  return results;
}

// Mencari semua file TypeScript dan JavaScript
const tsFiles = findFiles(rootDir, /\.(ts|tsx|js|jsx)$/);

// Pola untuk mencari import prisma
const importPattern = /import\s+prisma\s+from\s+['"]@\/lib\/prisma['"];/g;

// Perbaikan untuk setiap file
let fixedCount = 0;

for (const file of tsFiles) {
  const content = fs.readFileSync(file, 'utf8');
  
  if (importPattern.test(content)) {
    console.log(`Fixing imports in ${file}`);
    
    // Ganti import statement
    const newContent = content.replace(
      importPattern, 
      `import { prisma } from '@/lib/prisma';`
    );
    
    // Tulis kembali file
    fs.writeFileSync(file, newContent, 'utf8');
    fixedCount++;
  }
}

console.log(`Fixed prisma imports in ${fixedCount} files.`);
