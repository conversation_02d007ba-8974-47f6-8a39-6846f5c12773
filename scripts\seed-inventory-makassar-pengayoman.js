const { PrismaClient } = require('../prisma/generated/client');

const prisma = new PrismaClient();

async function seedInventoryMakassarPengayoman() {
  try {
    console.log('🚀 Mulai seeding inventori untuk outlet Makassar Pengayoman...');

    // Cari outlet Makassar Pengayoman
    const outlet = await prisma.outlet.findFirst({
      where: {
        OR: [
          { id: 'makassar-pengayoman' },
          { name: { contains: 'Makassar Pengayoman', mode: 'insensitive' } },
          { name: { contains: 'Pengayoman', mode: 'insensitive' } }
        ]
      }
    });

    if (!outlet) {
      throw new Error('Outlet Makassar Pengayoman tidak ditemukan!');
    }

    console.log(`✅ Outlet ditemukan: ${outlet.name} (${outlet.id})`);

    // Cari admin user untuk createdById
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (!adminUser) {
      throw new Error('Admin user tidak ditemukan. Pastikan seeding user sudah dijalankan.');
    }

    console.log(`✅ Admin user ditemukan: ${adminUser.name}`);

    // Definisi kategori inventori berdasarkan area
    const categories = [
      { name: 'Area Depan/Parkiran', description: 'Item untuk area depan dan parkiran' },
      { name: 'Ruang Tunggu/Kasir', description: 'Item untuk ruang tunggu dan kasir' },
      { name: 'Ruang Terapis', description: 'Item untuk ruang terapis' },
      { name: 'Kamar Mandi', description: 'Item untuk kamar mandi' },
      { name: 'Dapur/Pantry', description: 'Item untuk dapur dan pantry' },
      { name: 'Ruang Office', description: 'Item untuk ruang kantor' },
      { name: 'Lantai 2', description: 'Item untuk lantai 2' },
      { name: 'Laundry', description: 'Item untuk laundry' }
    ];

    // Buat kategori inventori berdasarkan area
    console.log('Membuat kategori inventori...');
    const createdCategories = {};
    
    for (const category of categories) {
      const createdCategory = await prisma.inventoryCategory.upsert({
        where: { name: category.name },
        update: { description: category.description },
        create: {
          name: category.name,
          description: category.description,
          isActive: true
        }
      });
      createdCategories[category.name] = createdCategory;
      console.log(`✅ Kategori "${category.name}" berhasil dibuat`);
    }

    // Definisi item inventori dengan kategori dan jumlah
    const inventoryItems = [
      // Area Depan/Parkiran
      { name: 'Neon Box', category: 'Area Depan/Parkiran', quantity: 1, unit: 'pcs' },
      { name: 'CCTV', category: 'Area Depan/Parkiran', quantity: 4, unit: 'pcs' },
      { name: 'Keset', category: 'Area Depan/Parkiran', quantity: 3, unit: 'pcs' },
      { name: 'Lampu Kuning', category: 'Area Depan/Parkiran', quantity: 6, unit: 'pcs' },
      { name: 'Tanda Open Close', category: 'Area Depan/Parkiran', quantity: 1, unit: 'pcs' },
      { name: 'Tanda Dorong Tarik', category: 'Area Depan/Parkiran', quantity: 3, unit: 'pcs' },
      { name: 'Tanda Harap Tenang', category: 'Area Depan/Parkiran', quantity: 3, unit: 'pcs' },
      { name: 'Tanda No Smoking', category: 'Area Depan/Parkiran', quantity: 3, unit: 'pcs' },
      { name: 'Tanda CCTV', category: 'Area Depan/Parkiran', quantity: 1, unit: 'pcs' },
      { name: 'Lampu Sudut', category: 'Area Depan/Parkiran', quantity: 2, unit: 'pcs' },
      { name: 'Rak Sepatu', category: 'Area Depan/Parkiran', quantity: 1, unit: 'pcs' },
      { name: 'Sandal Tamu', category: 'Area Depan/Parkiran', quantity: 19, unit: 'pasang' },

      // Ruang Tunggu/Kasir
      { name: 'Kursi Bulat Kecil', category: 'Ruang Tunggu/Kasir', quantity: 2, unit: 'pcs' },
      { name: 'Payung', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Bunga', category: 'Ruang Tunggu/Kasir', quantity: 6, unit: 'pcs' },
      { name: 'Televisi', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Akrilik', category: 'Ruang Tunggu/Kasir', quantity: 3, unit: 'pcs' },
      { name: 'Difuser', category: 'Ruang Tunggu/Kasir', quantity: 2, unit: 'pcs' },
      { name: 'Meja Kasir', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Pot Tempat Pensil', category: 'Ruang Tunggu/Kasir', quantity: 2, unit: 'pcs' },
      { name: 'Tempat Tisu', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Bingkai Daftar Harga', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Logo Titik Pijat', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Tempat Minuman Rotan', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Standing Banner', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Tempat Sampah', category: 'Ruang Tunggu/Kasir', quantity: 4, unit: 'pcs' },
      { name: 'Kursi Kasir', category: 'Ruang Tunggu/Kasir', quantity: 2, unit: 'pcs' },
      { name: 'Hit Elektrik', category: 'Ruang Tunggu/Kasir', quantity: 2, unit: 'pcs' },
      { name: 'Speaker', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Tempat Uang', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Kemoceng', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Keranjang Tempat Minyak', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Teko Listrik', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Tempat Pulpen', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },
      { name: 'Kanebo', category: 'Ruang Tunggu/Kasir', quantity: 1, unit: 'pcs' },

      // Ruang Terapis
      { name: 'Kursi Terapis', category: 'Ruang Terapis', quantity: 17, unit: 'pcs' },
      { name: 'Tempat Barang Tamu', category: 'Ruang Terapis', quantity: 16, unit: 'pcs' },
      { name: 'Lampu Cermin', category: 'Ruang Terapis', quantity: 19, unit: 'pcs' },
      { name: 'Ambalan', category: 'Ruang Terapis', quantity: 17, unit: 'pcs' },
      { name: 'Hanger Baju', category: 'Ruang Terapis', quantity: 18, unit: 'pcs' },
      { name: 'Gantungan Baju', category: 'Ruang Terapis', quantity: 21, unit: 'pcs' },
      { name: 'Colokan', category: 'Ruang Terapis', quantity: 37, unit: 'pcs' },
      { name: 'Sisir', category: 'Ruang Terapis', quantity: 17, unit: 'pcs' },
      { name: 'Botol Parfum Tamu', category: 'Ruang Terapis', quantity: 17, unit: 'pcs' },
      { name: 'Pomade', category: 'Ruang Terapis', quantity: 9, unit: 'pcs' },
      { name: 'AC', category: 'Ruang Terapis', quantity: 6, unit: 'pcs' },
      { name: 'Lampu Sorot', category: 'Ruang Terapis', quantity: 5, unit: 'pcs' },
      { name: 'Lampu Putih', category: 'Ruang Terapis', quantity: 30, unit: 'pcs' },
      { name: 'Rendaman Air', category: 'Ruang Terapis', quantity: 17, unit: 'pcs' },
      { name: 'Tempat Cream', category: 'Ruang Terapis', quantity: 17, unit: 'pcs' },
      { name: 'Bed', category: 'Ruang Terapis', quantity: 21, unit: 'pcs' },

      // Kamar Mandi
      { name: 'Door Close', category: 'Kamar Mandi', quantity: 1, unit: 'pcs' },
      { name: 'Tanda Toilet', category: 'Kamar Mandi', quantity: 2, unit: 'pcs' },
      { name: 'Shower', category: 'Kamar Mandi', quantity: 2, unit: 'pcs' },
      { name: 'Gayung', category: 'Kamar Mandi', quantity: 5, unit: 'pcs' },
      { name: 'Botol Sabun Tamu', category: 'Kamar Mandi', quantity: 2, unit: 'pcs' },
      { name: 'Penggosok Badan', category: 'Kamar Mandi', quantity: 2, unit: 'pcs' },
      { name: 'Ember Kamar Mandi', category: 'Kamar Mandi', quantity: 5, unit: 'pcs' },

      // Dapur/Pantry
      { name: 'Termos', category: 'Dapur/Pantry', quantity: 4, unit: 'pcs' },
      { name: 'Karpet', category: 'Dapur/Pantry', quantity: 1, unit: 'pcs' },
      { name: 'Finger', category: 'Dapur/Pantry', quantity: 1, unit: 'pcs' },
      { name: 'Galon', category: 'Dapur/Pantry', quantity: 5, unit: 'pcs' },
      { name: 'WiFi BT Plus', category: 'Dapur/Pantry', quantity: 1, unit: 'pcs' },
      { name: 'Kompor', category: 'Dapur/Pantry', quantity: 1, unit: 'pcs' },
      { name: 'Gas 5 KG', category: 'Dapur/Pantry', quantity: 1, unit: 'pcs' },
      { name: 'Rak Piring', category: 'Dapur/Pantry', quantity: 1, unit: 'pcs' },
      { name: 'Teko Masak Air', category: 'Dapur/Pantry', quantity: 1, unit: 'pcs' },
      { name: 'Tas HS', category: 'Dapur/Pantry', quantity: 9, unit: 'pcs' },
      { name: 'Tempat Cream HS', category: 'Dapur/Pantry', quantity: 10, unit: 'pcs' },
      { name: 'Kulkas', category: 'Dapur/Pantry', quantity: 1, unit: 'pcs' },
      { name: 'Rice Cooker', category: 'Dapur/Pantry', quantity: 1, unit: 'pcs' },
      { name: 'Gelas 1 Lusin', category: 'Dapur/Pantry', quantity: 12, unit: 'pcs' },
      { name: 'Piring 1 Lusin', category: 'Dapur/Pantry', quantity: 9, unit: 'pcs' },
      { name: 'Ember Beras', category: 'Dapur/Pantry', quantity: 1, unit: 'pcs' },

      // Ruang Office
      { name: 'Meja Kantor', category: 'Ruang Office', quantity: 1, unit: 'pcs' },
      { name: 'Kursi Kantor', category: 'Ruang Office', quantity: 1, unit: 'pcs' },
      { name: 'Kompor 2 Mata', category: 'Ruang Office', quantity: 1, unit: 'pcs' },
      { name: 'Gas 3 KG', category: 'Ruang Office', quantity: 1, unit: 'pcs' },
      { name: 'Papan Tulis', category: 'Ruang Office', quantity: 1, unit: 'pcs' },
      { name: 'Brankas', category: 'Ruang Office', quantity: 1, unit: 'pcs' },

      // Lantai 2
      { name: 'Kasur Busa', category: 'Lantai 2', quantity: 30, unit: 'pcs' },
      { name: 'Kipas', category: 'Lantai 2', quantity: 3, unit: 'pcs' },
      { name: 'Boxer Merah', category: 'Lantai 2', quantity: 18, unit: 'pcs' },
      { name: 'Boxer Biru', category: 'Lantai 2', quantity: 12, unit: 'pcs' },
      { name: 'Boxer Hitam', category: 'Lantai 2', quantity: 20, unit: 'pcs' },
      { name: 'Keranjang Rotan', category: 'Lantai 2', quantity: 20, unit: 'pcs' },
      { name: 'Tempat Lulur', category: 'Lantai 2', quantity: 10, unit: 'pcs' },
      { name: 'Waslap', category: 'Lantai 2', quantity: 20, unit: 'pcs' },
      { name: 'Bantal', category: 'Lantai 2', quantity: 19, unit: 'pcs' },
      { name: 'Keranjang Pakaian Kotor', category: 'Lantai 2', quantity: 2, unit: 'pcs' },
      { name: 'Handuk Hijau', category: 'Lantai 2', quantity: 60, unit: 'pcs' },
      { name: 'Handuk Abu Abu', category: 'Lantai 2', quantity: 140, unit: 'pcs' },
      { name: 'Handuk Biru', category: 'Lantai 2', quantity: 200, unit: 'pcs' },
      { name: 'Sarung Bantal', category: 'Lantai 2', quantity: 17, unit: 'pcs' },
      { name: 'Cermin', category: 'Lantai 2', quantity: 2, unit: 'pcs' },
      { name: 'Kain Bali', category: 'Lantai 2', quantity: 92, unit: 'pcs' },
      { name: 'Kemben', category: 'Lantai 2', quantity: 57, unit: 'pcs' },
      { name: 'Meja AO', category: 'Lantai 2', quantity: 1, unit: 'pcs' },

      // Laundry
      { name: 'Hair Dryer', category: 'Laundry', quantity: 1, unit: 'pcs' },
      { name: 'Mesin Cuci', category: 'Laundry', quantity: 1, unit: 'pcs' },
      { name: 'DAP', category: 'Laundry', quantity: 1, unit: 'pcs' },
      { name: 'Tali Jemuran', category: 'Laundry', quantity: 1, unit: 'pcs' }
    ];

    console.log(`📦 Akan membuat ${inventoryItems.length} item inventori...`);

    // Buat item inventori
    let createdCount = 0;
    for (const item of inventoryItems) {
      const category = createdCategories[item.category];
      if (!category) {
        console.warn(`⚠️ Kategori tidak ditemukan untuk item: ${item.name}`);
        continue;
      }

      // Cek apakah item sudah ada
      const existingItem = await prisma.inventoryItem.findFirst({
        where: {
          name: item.name,
          outletId: outlet.id,
          categoryId: category.id
        }
      });

      if (!existingItem) {
        // Buat item baru
        const newItem = await prisma.inventoryItem.create({
          data: {
            name: item.name,
            description: `${item.name} yang berada di area ${item.category}`,
            categoryId: category.id,
            outletId: outlet.id,
            totalQuantity: item.quantity,
            goodCondition: item.quantity, // Asumsikan semua dalam kondisi baik
            damagedCondition: 0,
            lostCondition: 0,
            remainingQuantity: item.quantity,
            minStockLevel: Math.max(1, Math.floor(item.quantity * 0.2)), // 20% dari quantity sebagai minimum stock
            maxStockLevel: item.quantity * 2,
            unitPrice: 0,
            notes: `Item inventori di area ${item.category}`,
            createdById: adminUser.id
          }
        });

        // Buat movement record untuk stock awal
        await prisma.inventoryMovement.create({
          data: {
            itemId: newItem.id,
            movementType: 'IN',
            quantity: item.quantity,
            reason: 'INITIAL_STOCK',
            notes: `Stock awal - ${item.name}`,
            createdById: adminUser.id
          }
        });

        createdCount++;
        console.log(`✅ Item dibuat: ${item.name} (${item.quantity} pcs)`);
      } else {
        console.log(`ℹ️ Item sudah ada: ${item.name}`);
      }
    }

    console.log(`\n🎉 Seeding inventori Makassar Pengayoman selesai!`);
    console.log(`📊 Statistik:`);
    console.log(`   - Kategori: ${Object.keys(createdCategories).length}`);
    console.log(`   - Item baru dibuat: ${createdCount}`);
    console.log(`   - Total item: ${inventoryItems.length}`);
    console.log(`   - Outlet: ${outlet.name}`);

  } catch (error) {
    console.error('❌ Error saat seeding inventori:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Jalankan seeding jika file ini dieksekusi langsung
if (require.main === module) {
  seedInventoryMakassarPengayoman()
    .then(() => {
      console.log('✅ Seeding berhasil!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding gagal:', error);
      process.exit(1);
    });
}

module.exports = seedInventoryMakassarPengayoman;