#!/usr/bin/env node

/**
 * Simple test runner for commission utilities without Jest
 * This can be run directly with Node.js for quick validation
 */

const path = require('path');

// Simple test framework
class SimpleTest {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
  }

  describe(description, callback) {
    console.log(`\n📋 ${description}`);
    callback();
  }

  it(description, callback) {
    try {
      callback();
      console.log(`  ✅ ${description}`);
      this.passed++;
    } catch (error) {
      console.log(`  ❌ ${description}`);
      console.log(`     Error: ${error.message}`);
      this.failed++;
    }
  }

  expect(actual) {
    return {
      toBe: (expected) => {
        if (actual !== expected) {
          throw new Error(`Expected ${expected}, but got ${actual}`);
        }
      },
      toBeGreaterThan: (expected) => {
        if (actual <= expected) {
          throw new Error(`Expected ${actual} to be greater than ${expected}`);
        }
      },
      toBeLessThan: (expected) => {
        if (actual >= expected) {
          throw new Error(`Expected ${actual} to be less than ${expected}`);
        }
      }
    };
  }

  summary() {
    console.log(`\n📊 Test Summary:`);
    console.log(`  ✅ Passed: ${this.passed}`);
    console.log(`  ❌ Failed: ${this.failed}`);
    console.log(`  📈 Total: ${this.passed + this.failed}`);
    
    if (this.failed === 0) {
      console.log(`\n🎉 All tests passed! Commission calculation is working correctly! 🎯`);
    } else {
      console.log(`\n⚠️  Some tests failed. Please check the commission calculation logic.`);
    }
  }
}

// Mock commission utilities (simplified version for testing)
const commissionUtils = {
  calculateTherapistCommission: (services, specialCommissions = []) => {
    let totalCommission = 0;
    
    const specialCommissionMap = new Map();
    specialCommissions.forEach(sc => {
      specialCommissionMap.set(sc.serviceId, sc.commission);
    });

    services.forEach(service => {
      const hasSpecialCommission = specialCommissionMap.has(service.serviceId);
      const commissionPerItem = hasSpecialCommission
        ? specialCommissionMap.get(service.serviceId)
        : service.defaultCommission;

      const serviceCommission = commissionPerItem * service.quantity;
      totalCommission += serviceCommission;
    });

    return totalCommission;
  },

  validateCommissionConsistency: (calculated, stored, tolerance = 0.01) => {
    return Math.abs(calculated - stored) <= tolerance;
  },

  calculateTherapistSalesTotal: (subtotal, additionalCharge = 0, discountAmount = 0) => {
    return subtotal + additionalCharge;
  }
};

// Run tests
const test = new SimpleTest();

test.describe('Commission Calculation Tests', () => {
  test.it('should calculate basic commission correctly', () => {
    const serviceData = [
      {
        serviceId: 'service-1',
        quantity: 1,
        defaultCommission: 50000
      }
    ];

    const result = commissionUtils.calculateTherapistCommission(serviceData, []);
    test.expect(result).toBe(50000);
  });

  test.it('should calculate commission with multiple quantity', () => {
    const serviceData = [
      {
        serviceId: 'service-1',
        quantity: 3,
        defaultCommission: 50000
      }
    ];

    const result = commissionUtils.calculateTherapistCommission(serviceData, []);
    test.expect(result).toBe(150000);
  });

  test.it('should use special commission when available', () => {
    const serviceData = [
      {
        serviceId: 'service-1',
        quantity: 1,
        defaultCommission: 50000
      }
    ];

    const specialCommissions = [
      {
        serviceId: 'service-1',
        commission: 75000
      }
    ];

    const result = commissionUtils.calculateTherapistCommission(serviceData, specialCommissions);
    test.expect(result).toBe(75000);
  });

  test.it('should calculate commission for multiple services', () => {
    const serviceData = [
      {
        serviceId: 'service-1',
        quantity: 1,
        defaultCommission: 50000
      },
      {
        serviceId: 'service-2',
        quantity: 2,
        defaultCommission: 30000
      }
    ];

    const result = commissionUtils.calculateTherapistCommission(serviceData, []);
    test.expect(result).toBe(110000); // 50000 + (30000 * 2)
  });

  test.it('should validate commission consistency', () => {
    const calculated = 100000;
    const stored = 100000;
    const result = commissionUtils.validateCommissionConsistency(calculated, stored);
    test.expect(result).toBe(true);
  });

  test.it('should calculate therapist sales total correctly', () => {
    const result = commissionUtils.calculateTherapistSalesTotal(100000, 5000, 10000);
    test.expect(result).toBe(105000); // subtotal + additionalCharge (discount doesn't affect)
  });

  test.it('should handle performance test with large dataset', () => {
    const largeServiceData = [];
    for (let i = 0; i < 1000; i++) {
      largeServiceData.push({
        serviceId: `service-${i}`,
        quantity: Math.floor(Math.random() * 5) + 1,
        defaultCommission: Math.floor(Math.random() * 100000) + 10000
      });
    }

    const startTime = Date.now();
    const commission = commissionUtils.calculateTherapistCommission(largeServiceData, []);
    const endTime = Date.now();

    test.expect(commission).toBeGreaterThan(0);
    test.expect(endTime - startTime).toBeLessThan(100);
  });
});

test.describe('Business Rules Validation', () => {
  test.it('should follow rule: Commission = Service Commission * Quantity', () => {
    const serviceData = [
      {
        serviceId: 'service-1',
        quantity: 3,
        defaultCommission: 50000
      }
    ];

    const commission = commissionUtils.calculateTherapistCommission(serviceData, []);
    test.expect(commission).toBe(150000); // 50000 * 3
  });

  test.it('should prioritize special commission over default', () => {
    const serviceData = [
      {
        serviceId: 'service-1',
        quantity: 1,
        defaultCommission: 50000
      }
    ];

    const specialCommissions = [
      {
        serviceId: 'service-1',
        commission: 75000
      }
    ];

    const commission = commissionUtils.calculateTherapistCommission(serviceData, specialCommissions);
    test.expect(commission).toBe(75000);
  });
});

// Run the tests
console.log('🧪 Running Commission Calculation Tests...\n');
test.summary();
