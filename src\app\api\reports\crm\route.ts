import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Gender } from '@/lib/types/prisma';
import { subDays, startOfDay, endOfDay } from 'date-fns';

// CACHING SYSTEM - In-Memory Cache untuk Performance Optimization
interface CacheEntry {
  data: any;
  timestamp: number;
  expiresAt: number;
  lastTransactionDate?: Date;
}

interface CRMCacheData {
  customers: any[];
  peakHours: any[];
  customerCategories: any[];
  performance: any;
}

// Cache storage dengan Map untuk performance optimal
const crmCache = new Map<string, CacheEntry>();
const CACHE_TTL = 6 * 60 * 60 * 1000; // 6 jam cache TTL untuk production stability
const MAX_CACHE_SIZE = 50; // Maksimal 50 cache entries

// Utility functions untuk cache management
function generateCacheKey(outletId: string, startDate: string, endDate: string): string {
  return `crm:${outletId}:${startDate}:${endDate}`;
}

function cleanupExpiredCache(): void {
  const now = Date.now();
  for (const [key, entry] of crmCache.entries()) {
    if (entry.expiresAt < now) {
      crmCache.delete(key);
    }
  }
}

function limitCacheSize(): void {
  if (crmCache.size > MAX_CACHE_SIZE) {
    // Hapus cache yang paling lama
    const sortedEntries = Array.from(crmCache.entries())
      .sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    const toDelete = sortedEntries.slice(0, crmCache.size - MAX_CACHE_SIZE);
    toDelete.forEach(([key]) => crmCache.delete(key));
  }
}

async function getLatestTransactionDate(outletId: string): Promise<Date | null> {
  try {
    const latestTransaction = await prisma.transaction.findFirst({
      where: outletId !== 'all' ? { outletId } : {},
      orderBy: { transactionDate: 'desc' },
      select: { transactionDate: true }
    });
    
    return latestTransaction?.transactionDate || null;
  } catch (error) {
    console.error('Error getting latest transaction date:', error);
    return null;
  }
}

function isCacheValid(cacheEntry: CacheEntry, latestTransactionDate: Date | null): boolean {
  const now = Date.now();
  
  // Check TTL expiration
  if (cacheEntry.expiresAt < now) {
    return false;
  }
  
  // Check if there's new transaction data
  if (latestTransactionDate && cacheEntry.lastTransactionDate) {
    if (latestTransactionDate > cacheEntry.lastTransactionDate) {
      return false; // Ada data transaksi baru
    }
  }
  
  return true;
}

async function setCacheEntry(
  key: string, 
  data: CRMCacheData, 
  latestTransactionDate: Date | null
): Promise<void> {
  const now = Date.now();
  
  crmCache.set(key, {
    data,
    timestamp: now,
    expiresAt: now + CACHE_TTL,
    lastTransactionDate: latestTransactionDate
  });
  
  // Cleanup dan limit cache size
  cleanupExpiredCache();
  limitCacheSize();
  
  console.log(`📦 Cache set for key: ${key}, expires in ${CACHE_TTL/60000} minutes`);
}

// Cache untuk menyimpan hasil penentuan gender dari API
const genderCache = new Map<string, Gender>();

// Konfigurasi API Genderize.io
// PRODUCTION OPTIMIZATION: Tuned for production performance and stability
const GENDERIZE_CONFIG = {
  maxBatchSize: 5, // Reduced from 10 untuk stability
  timeout: 3000, // Reduced from 5000 untuk faster fallback
  batchTimeout: 6000, // Reduced from 10000 untuk faster processing
  maxDatasetSize: 50, // Reduced from 100 untuk menghindari timeout
  retryDelay: 200, // Increased untuk menghindari rate limiting
  enableAPI: false, // DISABLED untuk production stability - menggunakan fallback
};

// Fungsi untuk menentukan gender berdasarkan nama menggunakan Genderize.io API
async function determineGenderFromNameAPI(name: string): Promise<Gender> {
  if (!name) return Gender.OTHER;

  // Skip API jika dinonaktifkan
  if (!GENDERIZE_CONFIG.enableAPI) {
    return determineGenderFromNameFallback(name);
  }

  // Cek jika nama sudah ada di cache
  if (genderCache.has(name.toLowerCase())) {
    return genderCache.get(name.toLowerCase())!;
  }

  // Ambil nama depan saja (kata pertama)
  const firstName = name.split(' ')[0].toLowerCase();

  // Cek jika nama depan sudah ada di cache
  if (genderCache.has(firstName)) {
    return genderCache.get(firstName)!;
  }

  // Skip jika nama terlalu pendek atau mengandung karakter non-alfabet
  if (firstName.length < 2 || !/^[a-zA-Z]+$/.test(firstName)) {
    const fallbackResult = determineGenderFromNameFallback(name);
    genderCache.set(firstName, fallbackResult);
    return fallbackResult;
  }

  try {
    // Buat AbortController untuk timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), GENDERIZE_CONFIG.timeout);

    // Panggil API Genderize.io dengan timeout dan retry
    const response = await fetch(`https://api.genderize.io?name=${encodeURIComponent(firstName)}`, {
      signal: controller.signal,
      headers: {
        'User-Agent': 'BreakTime-CRM/1.0',
        'Accept': 'application/json',
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Genderize API HTTP error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Validasi respons API
    if (!data || typeof data.gender !== 'string') {
      throw new Error('Invalid response format from Genderize API');
    }

    // Tentukan gender berdasarkan hasil API
    let gender = Gender.OTHER;
    if (data.gender === 'male') {
      gender = Gender.MALE;
    } else if (data.gender === 'female') {
      gender = Gender.FEMALE;
    }

    // Simpan ke cache
    genderCache.set(firstName, gender);

    return gender;
  } catch (error: unknown) {
    // Log error untuk debugging dengan lebih detail
    const errorDetails = error instanceof Error ? {
      error: error.message,
      name: error.name,
      stack: error.stack || 'No stack trace'
    } : { error: 'Unknown error', name: 'Unknown', stack: 'No stack trace' };
    
    // Silently fail to avoid log spam - error already tracked with errorDetails
    
    // Fallback ke metode lama jika API gagal
    const fallbackResult = determineGenderFromNameFallback(name);
    
    // Cache hasil fallback untuk menghindari API call berulang untuk nama yang sama
    genderCache.set(firstName, fallbackResult);
    
    return fallbackResult;
  }
}

// Fungsi fallback menggunakan metode lama untuk menentukan gender
function determineGenderFromNameFallback(name: string): Gender {
  if (!name) return Gender.OTHER;

  // Daftar nama yang biasanya untuk laki-laki
  const maleNames = [
    'adi', 'agus', 'ahmad', 'andi', 'andri', 'anton', 'arief', 'arifin', 'aris', 'asep',
    'bambang', 'bayu', 'budi', 'dedi', 'denny', 'dian', 'dicky', 'dodi', 'dwi', 'eko',
    'fajar', 'fauzi', 'ferry', 'firman', 'hadi', 'hendra', 'herman', 'herry', 'ikhsan', 'imam',
    'indra', 'irfan', 'joko', 'kurniawan', 'lukman', 'mahmud', 'muhammad', 'mulyono', 'nana', 'nugroho',
    'purnomo', 'rahmat', 'reza', 'ridwan', 'rizal', 'rizky', 'rudy', 'santoso', 'sigit', 'slamet',
    'sugianto', 'suharto', 'sukarno', 'supriadi', 'surya', 'sutrisno', 'syaiful', 'taufik', 'teguh', 'tri',
    'udin', 'wahyu', 'wawan', 'wibowo', 'widodo', 'yanto', 'yudi', 'yusuf', 'zaenal', 'zainudin',
    // Tambahan nama laki-laki
    'adam', 'aditya', 'agung', 'akbar', 'alex', 'ali', 'anwar', 'arya', 'bagus', 'bima',
    'candra', 'dani', 'darma', 'david', 'dimas', 'edi', 'fahmi', 'faisal', 'febri', 'galih',
    'gani', 'gunawan', 'haris', 'harry', 'hendri', 'ilham', 'iwan', 'jaya', 'johan', 'koko',
    'leo', 'lutfi', 'malik', 'maman', 'mario', 'maulana', 'nanda', 'putra', 'raden', 'rama',
    'randy', 'ricky', 'rio', 'ryan', 'satria', 'tono', 'toto', 'vicky', 'willy', 'yoga'
  ];

  // Daftar nama yang biasanya untuk perempuan
  const femaleNames = [
    'aini', 'ajeng', 'amalia', 'anisa', 'annisa', 'ayu', 'bunga', 'citra', 'dewi', 'dian',
    'dina', 'dini', 'dwi', 'eka', 'endang', 'erika', 'erni', 'eva', 'farah', 'farida',
    'fatimah', 'fitri', 'fitriani', 'hana', 'hani', 'hasanah', 'ida', 'indah', 'intan', 'ira',
    'kartika', 'kiki', 'laila', 'lestari', 'lia', 'lina', 'linda', 'lisa', 'lusi', 'mawar',
    'mega', 'melati', 'mira', 'nadia', 'nia', 'nina', 'nita', 'novi', 'novita', 'nur',
    'nurul', 'putri', 'rahma', 'ratna', 'rini', 'rita', 'rosa', 'rosita', 'sari', 'sinta',
    'siti', 'sri', 'suci', 'tari', 'tika', 'tina', 'tuti', 'umi', 'vera', 'wati',
    'winda', 'wulan', 'yani', 'yanti', 'yuli', 'yulia', 'yuni', 'yunita', 'zahra', 'zubaidah',
    // Tambahan nama perempuan
    'adinda', 'agnes', 'alya', 'amanda', 'angela', 'anggi', 'ani', 'anita', 'anny', 'astri',
    'bella', 'betty', 'carla', 'clara', 'diana', 'dini', 'ella', 'elsa', 'erna', 'fanny',
    'feby', 'fina', 'gita', 'hesti', 'icha', 'ika', 'irma', 'jessica', 'julia', 'karin',
    'lala', 'lili', 'maya', 'melly', 'mila', 'nana', 'neni', 'olive', 'puti', 'ratu',
    'rena', 'rina', 'sarah', 'shinta', 'silvi', 'tania', 'tasya', 'vina', 'wina', 'yolanda'
  ];

  // Ambil nama depan saja (kata pertama)
  const firstName = name.split(' ')[0].toLowerCase();

  // Cek apakah nama depan ada di daftar nama laki-laki
  if (maleNames.includes(firstName)) {
    return Gender.MALE;
  }

  // Cek apakah nama depan ada di daftar nama perempuan
  if (femaleNames.includes(firstName)) {
    return Gender.FEMALE;
  }

  // Jika nama depan tidak cocok, coba cek substring dari nama depan
  // untuk menangani kasus seperti "Bambang" yang tidak ada di daftar tapi "bambang" ada
  for (const maleName of maleNames) {
    if (firstName.startsWith(maleName) || maleName.startsWith(firstName)) {
      return Gender.MALE;
    }
  }

  for (const femaleName of femaleNames) {
    if (firstName.startsWith(femaleName) || femaleName.startsWith(firstName)) {
      return Gender.FEMALE;
    }
  }

  // Jika tidak ada yang cocok, kembalikan MALE sebagai default (karena lebih banyak pelanggan pria)
  return Gender.MALE;
}

// Fungsi untuk menentukan gender berdasarkan nama
// Karena fungsi async tidak bisa langsung digunakan di forEach, kita gunakan fungsi wrapper
function determineGenderFromName(name: string): Gender {
  // Pertama coba gunakan cache untuk menghemat API call
  if (genderCache.has(name.toLowerCase())) {
    return genderCache.get(name.toLowerCase())!;
  }

  // Jika tidak ada di cache, gunakan metode fallback untuk respon cepat
  const gender = determineGenderFromNameFallback(name);

  // Inisiasi API call untuk update cache di background tanpa menunggu hasilnya
  determineGenderFromNameAPI(name)
    .then(apiGender => {
      // Update cache jika hasilnya berbeda
      if (apiGender !== gender) {
        genderCache.set(name.toLowerCase(), apiGender);
      }
    })
    .catch(err => {
      console.error(`Error background update gender cache: ${err}`);
    });

  return gender;
}

// Fungsi untuk memproses batch nama sekaligus dengan Genderize.io API (max 10 nama per request)
async function processBatchGenderAPI(names: string[]): Promise<Map<string, Gender>> {
  const result = new Map<string, Gender>();

  // Filter nama yang belum ada di cache dan valid
  const namesToProcess = names.filter(name => {
    const firstName = name.split(' ')[0].toLowerCase();
    // Skip jika sudah di cache atau nama tidak valid
    return !genderCache.has(firstName) && 
           firstName.length >= 2 && 
           /^[a-zA-Z]+$/.test(firstName);
  });

  if (namesToProcess.length === 0) {
    // Semua nama sudah ada di cache atau tidak valid, gunakan cache/fallback saja
    names.forEach(name => {
      const firstName = name.split(' ')[0].toLowerCase();
      if (genderCache.has(firstName)) {
        result.set(name, genderCache.get(firstName)!);
      } else {
        const fallbackResult = determineGenderFromNameFallback(name);
        genderCache.set(firstName, fallbackResult);
        result.set(name, fallbackResult);
      }
    });
    return result;
  }

  // Proses batch maksimal 10 nama per request (batas API Genderize.io gratis)
  const batchSize = GENDERIZE_CONFIG.maxBatchSize;

  try {
    // Bagi nama menjadi batch-batch
    for (let i = 0; i < namesToProcess.length; i += batchSize) {
      const batch = namesToProcess.slice(i, i + batchSize);

      // Buat query string untuk API
      const queryParams = batch.map(name => {
        const firstName = name.split(' ')[0].toLowerCase();
        return `name[]=${encodeURIComponent(firstName)}`;
      }).join('&');

      // Buat AbortController untuk timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), GENDERIZE_CONFIG.batchTimeout);

      try {
      // Panggil API Genderize.io dengan batch request
        const response = await fetch(`https://api.genderize.io?${queryParams}`, {
          signal: controller.signal,
          headers: {
            'User-Agent': 'BreakTime-CRM/1.0',
            'Accept': 'application/json',
          },
        });

        clearTimeout(timeoutId);

      if (!response.ok) {
          throw new Error(`Genderize API batch HTTP error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Proses hasil dan simpan ke cache
      if (Array.isArray(data)) {
        data.forEach((item, index) => {
            if (index < batch.length) {
          const originalName = batch[index];
          const firstName = originalName.split(' ')[0].toLowerCase();

          let gender = Gender.OTHER;
              if (item && typeof item.gender === 'string') {
          if (item.gender === 'male') {
            gender = Gender.MALE;
          } else if (item.gender === 'female') {
            gender = Gender.FEMALE;
                }
          }

          // Simpan ke cache dan hasil
          genderCache.set(firstName, gender);
          result.set(originalName, gender);
            }
          });
        } else {
          throw new Error('Invalid batch response format from Genderize API');
        }
      } catch (batchError: unknown) {
        clearTimeout(timeoutId);
        const errorDetails = batchError instanceof Error ? {
          error: batchError.message,
          name: batchError.name,
          batchSize: batch.length
        } : { error: 'Unknown batch error', name: 'Unknown', batchSize: batch.length };
        
        console.error(`Error batch gender API untuk batch ${i}-${i + batch.length}:`, errorDetails);
        
        // Fallback ke metode lama untuk batch yang gagal
        batch.forEach(name => {
          const fallbackResult = determineGenderFromNameFallback(name);
          const firstName = name.split(' ')[0].toLowerCase();
          genderCache.set(firstName, fallbackResult);
          result.set(name, fallbackResult);
        });
      }

      // Tambahkan delay kecil antara batch untuk menghindari rate limiting
      if (i + batchSize < namesToProcess.length) {
        await new Promise(resolve => setTimeout(resolve, GENDERIZE_CONFIG.retryDelay));
      }
    }
  } catch (error: unknown) {
    const errorDetails = error instanceof Error ? {
      error: error.message,
      name: error.name,
      totalNames: namesToProcess.length
    } : { error: 'Unknown general error', name: 'Unknown', totalNames: namesToProcess.length };
    
    console.error(`Error umum batch gender API:`, errorDetails);
    
    // Fallback ke metode lama jika seluruh batch gagal
    namesToProcess.forEach(name => {
      const fallbackResult = determineGenderFromNameFallback(name);
      const firstName = name.split(' ')[0].toLowerCase();
      genderCache.set(firstName, fallbackResult);
      result.set(name, fallbackResult);
    });
  }

  // Tambahkan nama yang sudah ada di cache
  names.forEach(name => {
    if (!result.has(name)) {
      const firstName = name.split(' ')[0].toLowerCase();
      if (genderCache.has(firstName)) {
        result.set(name, genderCache.get(firstName)!);
      } else {
        const fallbackResult = determineGenderFromNameFallback(name);
        genderCache.set(firstName, fallbackResult);
        result.set(name, fallbackResult);
      }
    }
  });

  return result;
}

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  console.log('[API GET /api/reports/crm] Starting CRM data fetch...');
  
  // PRODUCTION TIMEOUT HANDLING - 45 detik timeout untuk menghindari 504 Gateway Timeout
  const PRODUCTION_TIMEOUT = 45000; // 45 seconds
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error('CRM query timeout - operation took too long'));
    }, PRODUCTION_TIMEOUT);
  });
  
  try {
    // Wrap main operation dengan timeout
    return await Promise.race([
      mainCRMOperation(request, startTime),
      timeoutPromise
    ]) as NextResponse;
  } catch (error: unknown) {
    const duration = Date.now() - startTime;
    console.error(`[API GET /api/reports/crm] Error after ${duration}ms:`, error);
    
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          error: 'Request timed out. Coba lagi atau gunakan rentang tanggal yang lebih pendek.',
          code: 'TIMEOUT_ERROR',
          duration: duration,
          suggestion: 'Gunakan filter tanggal dengan rentang yang lebih kecil untuk performa yang lebih baik.'
        },
        { status: 504 }
      );
    }
    
    return NextResponse.json(
      { 
        error: 'Terjadi kesalahan server saat memuat data CRM',
        code: 'SERVER_ERROR',
        duration: duration
      },
      { status: 500 }
    );
  }
}

async function mainCRMOperation(request: NextRequest, startTime: number) {
  try {
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');
    const outletIdParam = searchParams.get('outletId') || 'all';

    // Set default date range (30 hari terakhir)
    const endDate = endDateParam ? new Date(endDateParam) : endOfDay(new Date());
    const startDate = startDateParam ? new Date(startDateParam) : startOfDay(subDays(endDate, 30));

    console.log(`🔍 Query params: outlet=${outletIdParam}, start=${startDate.toISOString()}, end=${endDate.toISOString()}`);

    // CACHE CHECK - Cek apakah data sudah ada di cache
    const cacheKey = generateCacheKey(outletIdParam, startDate.toISOString(), endDate.toISOString());
    console.log(`🔍 Checking cache for key: ${cacheKey}`);
    
    // Cleanup expired cache entries terlebih dahulu
    cleanupExpiredCache();
    
    // Cek latest transaction date untuk validasi cache
    const latestTransactionDate = await getLatestTransactionDate(outletIdParam);
    console.log(`📅 Latest transaction date: ${latestTransactionDate?.toISOString() || 'none'}`);
    
    // Cek apakah ada cache yang valid
    const cachedEntry = crmCache.get(cacheKey);
    if (cachedEntry && isCacheValid(cachedEntry, latestTransactionDate)) {
      const cacheAge = Date.now() - cachedEntry.timestamp;
      console.log(`🎯 Cache HIT! Using cached data (age: ${Math.round(cacheAge/1000)}s)`);
      
      return NextResponse.json({
        message: 'Data CRM berhasil diambil (from cache)',
        crmData: cachedEntry.data,
        filters: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          outletId: outletIdParam,
        },
        performance: {
          duration: Date.now() - startTime,
          cached: true,
          cacheAge: Math.round(cacheAge/1000),
          originalDuration: cachedEntry.data.performance?.duration || 0
        }
      });
    } else {
      console.log(`❌ Cache MISS! Will fetch fresh data from database`);
      if (cachedEntry) {
        console.log(`🗑️ Removing invalid/expired cache entry`);
        crmCache.delete(cacheKey);
      }
    }

    // Buat where clause untuk filter outlet
    let outletWhere = {};
    if (outletIdParam && outletIdParam !== 'all') {
      outletWhere = { id: outletIdParam };
    }

    // 1. Ambil semua outlet
    const outlets = await prisma.outlet.findMany({
      where: outletWhere,
      select: {
        id: true,
        name: true,
      },
    });

    // 2. Ambil data transaksi untuk periode yang dipilih (optimized)
    const transactions = await prisma.transaction.findMany({
      where: {
        transactionDate: {
          gte: startDate,
          lte: endDate,
        },
        outletId: outletIdParam !== 'all' ? outletIdParam : undefined,
      },
      select: {
        id: true,
        transactionDate: true,
        totalAmount: true,
        customerId: true,
        outletId: true,
        customer: {
          select: {
            id: true,
            name: true,
            gender: true,
            tags: true, // Langsung ambil tags dari awal
            points: true, // Tambahkan points
            address: true, // Tambahkan address
          },
        },
        outlet: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        transactionDate: 'asc'
      }
    });

    // Kumpulkan semua nama customer untuk diproses sekaligus
    const customerNames = new Set<string>();
    transactions.forEach(transaction => {
      if (transaction.customer?.name) {
        customerNames.add(transaction.customer.name);
      }
    });

    // Pre-process semua nama customer untuk memperoleh gender mereka (optimized batch)
    const customerGenders = customerNames.size > 0 && 
                            customerNames.size <= GENDERIZE_CONFIG.maxDatasetSize &&
                            GENDERIZE_CONFIG.enableAPI
      ? await processBatchGenderAPI(Array.from(customerNames))
      : new Map<string, Gender>(); // Skip API call untuk dataset besar atau jika API dinonaktifkan

    // 3. Hitung data pelanggan berdasarkan transaksi dalam rentang tanggal
    const customerMap = new Map();
    const customerIdsInRange = new Set(); // Untuk melacak pelanggan yang memiliki transaksi dalam rentang

    transactions.forEach(transaction => {
      if (!transaction.customer) return;

      const customerId = transaction.customer.id;
      customerIdsInRange.add(customerId); // Tambahkan ke set pelanggan dalam rentang

      const transactionDate = new Date(transaction.transactionDate);

      // Inisialisasi data pelanggan jika belum ada
      if (!customerMap.has(customerId)) {
        // Tentukan gender berdasarkan nama jika tidak ada atau OTHER
        let gender = transaction.customer.gender;
        if (!gender || gender === Gender.OTHER) {
          // Gunakan hasil dari batch processing atau fallback
          if (customerGenders.has(transaction.customer.name)) {
            gender = customerGenders.get(transaction.customer.name);
          } else {
            gender = determineGenderFromName(transaction.customer.name);
          }
        }

        customerMap.set(customerId, {
          id: customerId,
          name: transaction.customer.name,
          gender: gender,
          totalVisits: 0,
          totalSpent: 0,
          visitsInRange: 0, // Tambahkan penghitung untuk kunjungan dalam rentang
          spentInRange: 0,  // Tambahkan penghitung untuk pengeluaran dalam rentang
          lastVisit: new Date(0),
          outletId: transaction.outlet.id,
          outletName: transaction.outlet.name,
          lastTransactionDate: new Date(0),
          tags: Array.isArray(transaction.customer.tags) ? transaction.customer.tags : [], // Gunakan tags dari database
          points: transaction.customer.points || 0, // Tambahkan field points
          address: transaction.customer.address || '', // Tambahkan field address
        });
      }

      const customerData = customerMap.get(customerId);

      // Update data pelanggan untuk transaksi dalam rentang
      customerData.visitsInRange += 1;
      customerData.spentInRange += transaction.totalAmount || 0;

      // Update data total (akan diperbarui dengan semua transaksi nanti)
      customerData.totalVisits += 1;
      customerData.totalSpent += transaction.totalAmount || 0;

      // Update tanggal kunjungan terakhir jika lebih baru
      if (transactionDate > customerData.lastVisit) {
        customerData.lastVisit = transactionDate;
      }

      // Update outlet terakhir jika transaksi ini lebih baru
      if (transactionDate > customerData.lastTransactionDate) {
        customerData.lastTransactionDate = transactionDate;
        customerData.outletId = transaction.outlet.id;
        customerData.outletName = transaction.outlet.name;
      }
    });

    // 3.5 Ambil semua transaksi untuk pelanggan yang memiliki transaksi dalam rentang (optimized)
    // OPTIMASI: Hanya ambil data yang dibutuhkan untuk menghitung total visits/spent
    const allTransactionsByCustomer = new Map<string, Array<{
      customerId: string;
      totalAmount: number | null;
      transactionDate: Date;
    }>>();

    if (customerIdsInRange.size > 0) {
      // Query yang lebih efisien: hanya ambil field yang dibutuhkan
      const allRawTransactions = await prisma.transaction.findMany({
        where: {
          customerId: {
            in: Array.from(customerIdsInRange) as string[]
          },
        },
        select: {
          customerId: true,
          totalAmount: true,
          transactionDate: true,
        },
        orderBy: {
          transactionDate: 'asc',
        }
      });

      // Kelompokkan transaksi per pelanggan
      allRawTransactions.forEach(txn => {
        if (!allTransactionsByCustomer.has(txn.customerId)) {
          allTransactionsByCustomer.set(txn.customerId, []);
        }
        allTransactionsByCustomer.get(txn.customerId)!.push(txn);
      });
    }

      // Reset dan hitung ulang total kunjungan dan pengeluaran
      customerIdsInRange.forEach(customerId => {
        if (customerMap.has(customerId)) {
          const customerData = customerMap.get(customerId);
          // Reset total (kita akan menghitung ulang dari semua transaksi)
          customerData.totalVisits = 0;
          customerData.totalSpent = 0;
          // customerData.allTransactions = allTransactionsByCustomer.get(customerId) || []; // Simpan semua transaksi
        }
      });

      // Hitung total dari semua transaksi
      // allCustomerTransactions.forEach(transaction => {
      //   if (customerMap.has(transaction.customerId)) {
      //     const customerData = customerMap.get(transaction.customerId);
      //     customerData.totalVisits += 1;
      //     customerData.totalSpent += transaction.totalAmount || 0;
      //   }
      // });
      allTransactionsByCustomer.forEach((transactions, customerId) => {
        if (customerMap.has(customerId)) {
          const customerData = customerMap.get(customerId);
          customerData.totalVisits = transactions.length;
          customerData.totalSpent = transactions.reduce((sum, txn) => sum + (txn.totalAmount || 0), 0);
          if (transactions.length > 0) {
            // Kunjungan terakhir (overall) dan outlet terakhir (overall) dari semua transaksi historis
            const lastHistoricalTx = transactions[transactions.length - 1];
            customerData.lastVisit = lastHistoricalTx.transactionDate; // Ini akan jadi lastVisit overall
            // customerData.outletId = lastHistoricalTx.outletId; // Ini akan jadi outletId dari lastVisit overall
            // customerData.outletName = outlets.find(o => o.id === lastHistoricalTx.outletId)?.name || 'Outlet Tidak Diketahui';
          }
        }
      });

    // OPTIMASI: Tags sudah diambil langsung dari query transactions, skip query tambahan
    console.log(`Processed ${customerMap.size} customers with transactions in range`);

    // 4. Konversi Map ke array dan tentukan kategori berdasarkan tag customer
    const customers = Array.from(customerMap.values()).map(customer => {
      // Gunakan tag customer sebagai kategori utama
      let finalCategory: 'A' | 'B' | 'C' | 'D' | 'Baru' = 'Baru'; // Default ke Baru
      
      // Cari tag kategori yang sesuai (A, B, C, D, atau Baru)
      const categoryTags = ['A', 'B', 'C', 'D', 'Baru', 'BARU'];
      const customerTags = Array.isArray(customer.tags) ? customer.tags : [];
      
      // Cek apakah ada tag kategori di customer tags (optimized)
      const foundTag = categoryTags.find(tag => customerTags.includes(tag));
      if (foundTag) {
        finalCategory = foundTag === 'BARU' ? 'Baru' : foundTag as 'A' | 'B' | 'C' | 'D' | 'Baru';
      }

      // Log hanya untuk debugging (remove in production)
      // console.log(`Customer ${customer.name}: tags=${JSON.stringify(customerTags)}, finalCategory=${finalCategory}`);

      return {
        ...customer,
        finalCategory: finalCategory,
        category: finalCategory === 'Baru' ? 'D' : finalCategory, // Fallback untuk kompatibilitas
        lastVisit: customer.lastVisit instanceof Date ? customer.lastVisit.toISOString() : new Date(0).toISOString(),
        visitsInRange: customer.visitsInRange,
        spentInRange: customer.spentInRange,
        tags: customerTags
      };
    });

    // 5. Hitung kategori pelanggan per outlet - PERBAIKAN: ambil semua pelanggan yang pernah bertransaksi
    const customerCategoriesMap = new Map();

    outlets.forEach(outlet => {
      customerCategoriesMap.set(outlet.id, {
        outletId: outlet.id,
        outletName: outlet.name,
        categoryA: 0,
        categoryB: 0,
        categoryC: 0,
        categoryD: 0,
        categoryBaru: 0, // Tambahkan kategori Baru
        total: 0,
        customerIds: new Set(), // Tambahkan Set untuk melacak pelanggan unik
      });
    });

    // OPTIMASI HEMAT BIAYA: Gunakan data transaksi per outlet sebagai basis kategorisasi
    console.log('💰 Starting transaction-based customer categorization per outlet...');
    
    // Query efisien: ambil langsung dari transaksi dengan customer tags per outlet
    let categoryCountsRaw;
    
    if (outletIdParam !== 'all') {
      // Query untuk outlet tertentu - basis dari transaksi
      categoryCountsRaw = await prisma.$queryRaw`
        SELECT 
          tx."outletId",
          o.name as outlet_name,
          CASE 
            WHEN 'A' = ANY(c.tags) THEN 'A'
            WHEN 'B' = ANY(c.tags) THEN 'B' 
            WHEN 'C' = ANY(c.tags) THEN 'C'
            WHEN 'D' = ANY(c.tags) THEN 'D'
            WHEN 'Baru' = ANY(c.tags) OR 'BARU' = ANY(c.tags) THEN 'Baru'
            ELSE 'Baru'
          END as category,
          COUNT(DISTINCT tx."customerId") as customer_count
        FROM "Transaction" tx
        INNER JOIN "Customer" c ON c.id = tx."customerId" AND c."isActive" = true
        INNER JOIN "Outlet" o ON o.id = tx."outletId"
        WHERE tx."customerId" IS NOT NULL 
          AND tx."outletId" = ${outletIdParam}
        GROUP BY tx."outletId", o.name, 
          CASE 
            WHEN 'A' = ANY(c.tags) THEN 'A'
            WHEN 'B' = ANY(c.tags) THEN 'B' 
            WHEN 'C' = ANY(c.tags) THEN 'C'
            WHEN 'D' = ANY(c.tags) THEN 'D'
            WHEN 'Baru' = ANY(c.tags) OR 'BARU' = ANY(c.tags) THEN 'Baru'
            ELSE 'Baru'
          END
        ORDER BY tx."outletId", category
      ` as Array<{
        outletId: string;
        outlet_name: string;
        category: string;
        customer_count: bigint;
      }>;
    } else {
      // Query untuk semua outlet - basis dari transaksi
      categoryCountsRaw = await prisma.$queryRaw`
        SELECT 
          tx."outletId",
          o.name as outlet_name,
          CASE 
            WHEN 'A' = ANY(c.tags) THEN 'A'
            WHEN 'B' = ANY(c.tags) THEN 'B' 
            WHEN 'C' = ANY(c.tags) THEN 'C'
            WHEN 'D' = ANY(c.tags) THEN 'D'
            WHEN 'Baru' = ANY(c.tags) OR 'BARU' = ANY(c.tags) THEN 'Baru'
            ELSE 'Baru'
          END as category,
          COUNT(DISTINCT tx."customerId") as customer_count
        FROM "Transaction" tx
        INNER JOIN "Customer" c ON c.id = tx."customerId" AND c."isActive" = true
        INNER JOIN "Outlet" o ON o.id = tx."outletId"
        WHERE tx."customerId" IS NOT NULL
        GROUP BY tx."outletId", o.name, 
          CASE 
            WHEN 'A' = ANY(c.tags) THEN 'A'
            WHEN 'B' = ANY(c.tags) THEN 'B' 
            WHEN 'C' = ANY(c.tags) THEN 'C'
            WHEN 'D' = ANY(c.tags) THEN 'D'
            WHEN 'Baru' = ANY(c.tags) OR 'BARU' = ANY(c.tags) THEN 'Baru'
            ELSE 'Baru'
          END
        ORDER BY tx."outletId", category
      ` as Array<{
        outletId: string;
        outlet_name: string;
        category: string;
        customer_count: bigint;
      }>;
    }

    console.log(`📊 Raw aggregation returned ${categoryCountsRaw.length} category counts`);

    // Proses hasil raw SQL ke format yang diinginkan
    categoryCountsRaw.forEach(row => {
      const outletData = customerCategoriesMap.get(row.outletId);
          if (!outletData) return;

      const count = Number(row.customer_count);
      
      switch (row.category) {
        case 'A':
          outletData.categoryA = count;
          break;
        case 'B':
          outletData.categoryB = count;
          break;
        case 'C':
          outletData.categoryC = count;
          break;
        case 'D':
          outletData.categoryD = count;
          break;
        case 'Baru':
          outletData.categoryBaru = count;
          break;
      }
    });

    // Hitung total per outlet
    outlets.forEach(outlet => {
      const outletData = customerCategoriesMap.get(outlet.id);
        if (!outletData) return;

      outletData.total = outletData.categoryA + outletData.categoryB + 
                       outletData.categoryC + outletData.categoryD + outletData.categoryBaru;
      
      console.log(`✅ ${outlet.name}: A:${outletData.categoryA}, B:${outletData.categoryB}, C:${outletData.categoryC}, D:${outletData.categoryD}, Baru:${outletData.categoryBaru} (Total: ${outletData.total})`);
    });

    // Hapus Set customerIds dari hasil akhir
    const customerCategories = Array.from(customerCategoriesMap.values()).map(outlet => ({
      outletId: outlet.outletId,
      outletName: outlet.outletName,
      categoryA: outlet.categoryA,
      categoryB: outlet.categoryB,
      categoryC: outlet.categoryC,
      categoryD: outlet.categoryD,
      categoryBaru: outlet.categoryBaru, // Tambahkan kategori Baru
      total: outlet.total,
    }));

    // 6. Hitung jam sibuk dari SEMUA transaksi per outlet (tidak dibatasi rentang tanggal)
    // SIMPLE & ROBUST TIMEZONE HANDLING: Gunakan pendekatan sederhana untuk dev dan production
    console.log('📊 Starting hourly data calculation from all transactions...');

    let hourlyDataRaw;
    
    if (outletIdParam !== 'all') {
      // Query jam sibuk untuk outlet tertentu - SIMPLE TIMEZONE FIX
      hourlyDataRaw = await prisma.$queryRaw`
        SELECT 
          EXTRACT(HOUR FROM (tx."transactionDate" + INTERVAL '8 hours')) as hour,
          COUNT(*) as transaction_count,
          COUNT(CASE WHEN c.gender = 'MALE' OR 
                         (c.name ~* '^(adi|agus|ahmad|andi|andri|anton|arief|bambang|bayu|budi|dedi|denny|dian|dicky|dodi|eko|fajar|fauzi|ferry|firman|hadi|hendra|herman|ikhsan|imam|indra|irfan|joko|lukman|mahmud|muhammad|mulyono|purnomo|rahmat|reza|ridwan|rizal|rizky|rudy|santoso|sigit|slamet|sugianto|suharto|sukarno|supriadi|surya|sutrisno|syaiful|taufik|teguh|tri|udin|wahyu|wawan|wibowo|widodo|yanto|yudi|yusuf|zaenal|zainudin|adam|aditya|agung|akbar|alex|ali|anwar|arya|bagus|bima|candra|dani|darma|david|dimas|edi|fahmi|faisal|febri|galih|gani|gunawan|haris|harry|hendri|ilham|iwan|jaya|johan|koko|leo|lutfi|malik|maman|mario|maulana|nanda|putra|raden|rama|randy|ricky|rio|ryan|satria|tono|toto|vicky|willy|yoga)') 
                     THEN 1 END) as male_count,
          COUNT(CASE WHEN c.gender = 'FEMALE' OR 
                         (c.name ~* '^(aini|ajeng|amalia|anisa|annisa|ayu|bunga|citra|dewi|dian|dina|dini|eka|endang|erika|erni|eva|farah|farida|fatimah|fitri|fitriani|hana|hani|hasanah|ida|indah|intan|ira|kartika|kiki|laila|lestari|lia|lina|linda|lisa|lusi|mawar|mega|melati|mira|nadia|nia|nina|nita|novi|novita|nur|nurul|putri|rahma|ratna|rini|rita|rosa|rosita|sari|sinta|siti|sri|suci|tari|tika|tina|tuti|umi|vera|wati|winda|wulan|yani|yanti|yuli|yulia|yuni|yunita|zahra|zubaidah|adinda|agnes|alya|amanda|angela|anggi|ani|anita|anny|astri|bella|betty|carla|clara|diana|dini|ella|elsa|erna|fanny|feby|fina|gita|hesti|icha|ika|irma|jessica|julia|karin|lala|lili|maya|melly|mila|nana|neni|olive|puti|ratu|rena|rina|sarah|shinta|silvi|tania|tasya|vina|wina|yolanda)') 
                     THEN 1 END) as female_count
        FROM "Transaction" tx
        INNER JOIN "Customer" c ON c.id = tx."customerId" AND c."isActive" = true
        WHERE tx."customerId" IS NOT NULL 
          AND tx."outletId" = ${outletIdParam}
          AND EXTRACT(HOUR FROM (tx."transactionDate" + INTERVAL '8 hours')) BETWEEN 9 AND 23
        GROUP BY EXTRACT(HOUR FROM (tx."transactionDate" + INTERVAL '8 hours'))
        ORDER BY hour
      ` as Array<{
        hour: number;
        transaction_count: bigint;
        male_count: bigint;
        female_count: bigint;
      }>;
    } else {
      // Query jam sibuk untuk semua outlet - SIMPLE TIMEZONE FIX
      hourlyDataRaw = await prisma.$queryRaw`
        SELECT 
          EXTRACT(HOUR FROM (tx."transactionDate" + INTERVAL '8 hours')) as hour,
          COUNT(*) as transaction_count,
          COUNT(CASE WHEN c.gender = 'MALE' OR 
                         (c.name ~* '^(adi|agus|ahmad|andi|andri|anton|arief|bambang|bayu|budi|dedi|denny|dian|dicky|dodi|eko|fajar|fauzi|ferry|firman|hadi|hendra|herman|ikhsan|imam|indra|irfan|joko|lukman|mahmud|muhammad|mulyono|purnomo|rahmat|reza|ridwan|rizal|rizky|rudy|santoso|sigit|slamet|sugianto|suharto|sukarno|supriadi|surya|sutrisno|syaiful|taufik|teguh|tri|udin|wahyu|wawan|wibowo|widodo|yanto|yudi|yusuf|zaenal|zainudin|adam|aditya|agung|akbar|alex|ali|anwar|arya|bagus|bima|candra|dani|darma|david|dimas|edi|fahmi|faisal|febri|galih|gani|gunawan|haris|harry|hendri|ilham|iwan|jaya|johan|koko|leo|lutfi|malik|maman|mario|maulana|nanda|putra|raden|rama|randy|ricky|rio|ryan|satria|tono|toto|vicky|willy|yoga)') 
                     THEN 1 END) as male_count,
          COUNT(CASE WHEN c.gender = 'FEMALE' OR 
                         (c.name ~* '^(aini|ajeng|amalia|anisa|annisa|ayu|bunga|citra|dewi|dian|dina|dini|eka|endang|erika|erni|eva|farah|farida|fatimah|fitri|fitriani|hana|hani|hasanah|ida|indah|intan|ira|kartika|kiki|laila|lestari|lia|lina|linda|lisa|lusi|mawar|mega|melati|mira|nadia|nia|nina|nita|novi|novita|nur|nurul|putri|rahma|ratna|rini|rita|rosa|rosita|sari|sinta|siti|sri|suci|tari|tika|tina|tuti|umi|vera|wati|winda|wulan|yani|yanti|yuli|yulia|yuni|yunita|zahra|zubaidah|adinda|agnes|alya|amanda|angela|anggi|ani|anita|anny|astri|bella|betty|carla|clara|diana|dini|ella|elsa|erna|fanny|feby|fina|gita|hesti|icha|ika|irma|jessica|julia|karin|lala|lili|maya|melly|mila|nana|neni|olive|puti|ratu|rena|rina|sarah|shinta|silvi|tania|tasya|vina|wina|yolanda)') 
                     THEN 1 END) as female_count
        FROM "Transaction" tx
        INNER JOIN "Customer" c ON c.id = tx."customerId" AND c."isActive" = true
        WHERE tx."customerId" IS NOT NULL
          AND EXTRACT(HOUR FROM (tx."transactionDate" + INTERVAL '8 hours')) BETWEEN 9 AND 23
        GROUP BY EXTRACT(HOUR FROM (tx."transactionDate" + INTERVAL '8 hours'))
        ORDER BY hour
      ` as Array<{
        hour: number;
        transaction_count: bigint;
        male_count: bigint;
        female_count: bigint;
      }>;
    }

    console.log(`📊 Raw hourly query returned ${hourlyDataRaw.length} hour records`);

    // Inisialisasi data untuk semua jam operasional (9:00-23:00)
    const hourlyData = new Map();
    for (let hour = 9; hour <= 23; hour++) {
      hourlyData.set(hour, {
        hour,
        count: 0,
        maleCount: 0,
        femaleCount: 0,
        otherCount: 0,
      });
    }

    // Proses hasil raw SQL
    hourlyDataRaw.forEach(row => {
      const hour = Number(row.hour);
      const totalCount = Number(row.transaction_count);
      const maleCount = Number(row.male_count);
      const femaleCount = Number(row.female_count);
      const otherCount = totalCount - maleCount - femaleCount;

      hourlyData.set(hour, {
        hour,
        count: totalCount,
        maleCount,
        femaleCount,
        otherCount: Math.max(0, otherCount), // Pastikan tidak negatif
      });
    });

    // Ambil semua data jam operasional (termasuk yang kosong) dan urutkan berdasarkan jam
    const peakHours = Array.from(hourlyData.values())
      .sort((a, b) => a.hour - b.hour); // Urutkan berdasarkan jam (dari pagi ke malam)

    console.log(`📊 Peak hours calculated:`, peakHours.map(h => `${h.hour}:00 = ${h.count} transactions`).join(', '));

    // 7. Siapkan data untuk response dan cache
    const endTime = Date.now();
    const performanceData = {
      duration: endTime - startTime,
      customerCount: customers.length,
      transactionCount: transactions.length,
      cached: false
    };

    const crmData: CRMCacheData = {
      customers,
      peakHours,
      customerCategories,
      performance: performanceData
    };

    console.log(`CRM API completed in ${endTime - startTime}ms - ${customers.length} customers, ${transactions.length} transactions`);

    // STORE IN CACHE - Simpan data fresh ke cache untuk request berikutnya
    try {
      await setCacheEntry(cacheKey, crmData, latestTransactionDate);
      console.log(`💾 Fresh data cached successfully with key: ${cacheKey}`);
    } catch (cacheError) {
      console.error('Failed to cache data:', cacheError);
      // Continue without caching if there's an error
    }
    
    // 8. Return response
    return NextResponse.json({
      message: 'Data CRM berhasil diambil',
      crmData,
      filters: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        outletId: outletIdParam,
      },
      performance: performanceData,
      cache: {
        size: crmCache.size,
        keys: Array.from(crmCache.keys()).slice(0, 5) // Show first 5 keys for debugging
      }
    });
  } catch (error: unknown) {
    console.error('[API GET /api/reports/crm] Error:', error);

    let errorMessage = 'Terjadi kesalahan saat mengambil data CRM';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat mengambil data CRM',
      crmData: {
        customers: [],
        peakHours: [],
        customerCategories: [],
      },
      filters: {
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        outletId: null,
      },
    }, { status: statusCode });
  }
}

// Tambahkan endpoint khusus untuk debugging penentuan gender
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { names } = body;

    if (!names || !Array.isArray(names) || names.length === 0) {
      return NextResponse.json(
        { error: 'Parameter names diperlukan dan harus berupa array tidak kosong' },
        { status: 400 }
      );
    }

    // Batasi jumlah nama yang dapat diproses
    const processNames = names.slice(0, 50);

    // Proses batch nama
    const results = await processBatchGenderAPI(processNames);

    // Format hasil untuk output
    const formattedResults = processNames.map(name => {
      const firstName = name.split(' ')[0];
      const gender = results.get(name) || Gender.OTHER;
      const fallbackGender = determineGenderFromNameFallback(name);

      return {
        name,
        firstName,
        apiGender: gender,
        fallbackGender,
        match: gender === fallbackGender
      };
    });

    return NextResponse.json({
      message: 'Berhasil memproses nama',
      totalProcessed: processNames.length,
      results: formattedResults,
      cacheSize: genderCache.size
    });
  } catch (error: unknown) {
    console.error('[API POST /api/reports/crm] Error:', error);

    let errorMessage = 'Terjadi kesalahan saat memproses nama';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat memproses nama'
    }, { status: statusCode });
  }
}

// Tambahkan endpoint untuk cache management
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'clear':
        const oldSize = crmCache.size;
        crmCache.clear();
        console.log(`🗑️ Cache cleared: ${oldSize} entries removed`);
        
        return NextResponse.json({
          message: 'Cache berhasil dibersihkan',
          entriesRemoved: oldSize,
          currentSize: crmCache.size
        });

      case 'status':
        cleanupExpiredCache(); // Cleanup expired entries first
        
        const entries = Array.from(crmCache.entries()).map(([key, entry]) => ({
          key,
          age: Math.round((Date.now() - entry.timestamp) / 1000),
          expiresIn: Math.round((entry.expiresAt - Date.now()) / 1000),
          lastTransactionDate: entry.lastTransactionDate?.toISOString() || null
        }));

        return NextResponse.json({
          message: 'Status cache CRM',
          totalEntries: crmCache.size,
          maxSize: MAX_CACHE_SIZE,
          ttlMinutes: CACHE_TTL / 60000,
          entries: entries.slice(0, 10) // Show only first 10 for readability
        });

      case 'invalidate':
        const { outletId, startDate, endDate } = body;
        if (!outletId || !startDate || !endDate) {
          return NextResponse.json(
            { error: 'Parameter outletId, startDate, dan endDate diperlukan untuk invalidate' },
            { status: 400 }
          );
        }

        const keyToInvalidate = generateCacheKey(outletId, startDate, endDate);
        const wasDeleted = crmCache.delete(keyToInvalidate);
        
        return NextResponse.json({
          message: wasDeleted ? 'Cache entry berhasil di-invalidate' : 'Cache entry tidak ditemukan',
          key: keyToInvalidate,
          deleted: wasDeleted,
          remainingEntries: crmCache.size
        });

      default:
        return NextResponse.json(
          { error: 'Action tidak valid. Gunakan: clear, status, atau invalidate' },
          { status: 400 }
        );
    }
  } catch (error: unknown) {
    console.error('[API PUT /api/reports/crm] Error:', error);

    let errorMessage = 'Terjadi kesalahan saat mengelola cache';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat mengelola cache'
    }, { status: statusCode });
  }
}
