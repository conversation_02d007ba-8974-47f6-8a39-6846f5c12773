// Script untuk migrasi data pelanggan dari Excel ke database
const { PrismaClient } = require('@prisma/client');
const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');

const prisma = new PrismaClient();

async function migrasiPelanggan() {
  try {
    console.log('🔄 Memulai migrasi data pelanggan dari Excel...');

    // Path file Excel
    const excelPath = path.join(process.cwd(), 'Data_Pelanggan_TERAKHIR DASRBOR LAMA SETIABUDI.xls');

    // Cek apakah file ada
    if (!fs.existsSync(excelPath)) {
      console.error(`❌ File tidak ditemukan: ${excelPath}`);
      return;
    }

    // Baca file Excel
    console.log(`📂 Membaca file Excel: ${excelPath}`);
    const workbook = XLSX.readFile(excelPath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    console.log(`📊 Jumlah data yang ditemukan: ${data.length}`);

    // Inisialisasi counter untuk ID
    let customerCounter = 1;

    // Ambil ID terakhir dari database untuk melanjutkan penomoran
    const lastCustomer = await prisma.customer.findFirst({
      orderBy: {
        id: 'desc'
      }
    });

    if (lastCustomer && lastCustomer.id.startsWith('P')) {
      const lastNumber = parseInt(lastCustomer.id.substring(1), 10);
      if (!isNaN(lastNumber)) {
        customerCounter = lastNumber + 1;
      }
    }

    console.log(`🔢 Memulai penomoran ID dari: P${String(customerCounter).padStart(7, '0')}`);

    // Proses setiap baris data
    let berhasil = 0;
    let gagal = 0;
    let updateByPhone = 0;
    let updateByName = 0;
    let newCustomers = 0;
    const errors = [];
    const allExcelTags = new Set(); // Untuk melacak semua tag unik dari Excel

    for (const row of data) {
      try {
        // Ekstrak data dari baris Excel
        const nama = row['Nama'] || row['NAMA'] || row['nama'];
        const telepon = String(row['Telepon'] || row['TELEPON'] || row['telepon'] || row['HP'] || row['No HP'] || row['NO HP'] || row['No Telepon'] || '');
        const alamat = row['Alamat'] || row['ALAMAT'] || row['alamat'] || '';
        const jumlahDatang = parseInt(row['Jumlah Datang'] || row['JUMLAH DATANG'] || row['jumlah datang'] || 0);

        // Ekstrak tag dari Excel jika ada
        let excelTags = [];
        const tagField = row['Tag'] || row['TAG'] || row['tag'] || row['Tags'] || row['TAGS'] || row['tags'];

        if (tagField) {
          // Jika tag dalam format string dengan pemisah koma, konversi ke array
          if (typeof tagField === 'string') {
            excelTags = tagField.split(',').map(tag => tag.trim()).filter(tag => tag);
          }
          // Jika tag sudah dalam format array
          else if (Array.isArray(tagField)) {
            excelTags = tagField.filter(tag => tag);
          }
          // Jika tag adalah nilai tunggal (bukan string dengan koma atau array)
          else {
            excelTags = [String(tagField).trim()];
          }
        }

        // Log tag yang ditemukan di Excel jika ada
        if (excelTags.length > 0) {
          console.log(`📌 Tag ditemukan untuk ${nama}: ${excelTags.join(', ')}`);

          // Tambahkan tag ke set tag unik
          excelTags.forEach(tag => allExcelTags.add(tag));
        }

        // Validasi data minimal
        if (!nama || !telepon) {
          throw new Error(`Data tidak lengkap: Nama atau telepon kosong`);
        }

        // Bersihkan nomor telepon (hapus spasi, tanda baca, dll)
        const cleanPhone = telepon.replace(/[^0-9]/g, '');

        // Tambahkan awalan 0 jika dimulai dengan 8
        const formattedPhone = cleanPhone.startsWith('8') ? `0${cleanPhone}` : cleanPhone;

        // Cek apakah pelanggan sudah ada berdasarkan nomor telepon atau nama yang sama dengan nomor telepon yang sama
        const existingCustomer = await prisma.customer.findFirst({
          where: {
            OR: [
              { phone: formattedPhone },
              {
                AND: [
                  { name: nama },
                  { phone: { startsWith: formattedPhone.substring(0, 5) } }
                ]
              }
            ]
          }
        });

        if (existingCustomer) {
          // Cek apakah ditemukan berdasarkan nomor telepon yang sama persis atau nama yang sama
          const matchType = existingCustomer.phone === formattedPhone
            ? 'nomor telepon yang sama'
            : 'nama yang sama dengan nomor telepon yang mirip';

          // Update data pelanggan yang sudah ada
          await prisma.customer.update({
            where: {
              id: existingCustomer.id
            },
            data: {
              // Update poin berdasarkan jumlah datang
              points: existingCustomer.points + jumlahDatang,
              // Update alamat jika sebelumnya kosong
              address: existingCustomer.address || alamat || null,
              // Gabungkan tag dari Excel dengan tag yang sudah ada dan tag default
              tags: {
                set: [...new Set([...existingCustomer.tags, ...excelTags, 'Migrasi', 'Baru'])]
              },
              // Update nomor telepon jika ditemukan berdasarkan nama dan nomor telepon yang mirip
              ...(matchType.includes('nama') && formattedPhone.length > existingCustomer.phone.length
                ? { phone: formattedPhone }
                : {})
            }
          });

          // Tambahkan counter berdasarkan jenis pencocokan
          if (matchType.includes('nomor telepon')) {
            updateByPhone++;
          } else {
            updateByName++;
          }

          console.log(`✅ Pelanggan diperbarui (${matchType}): ${nama} (${existingCustomer.id}), Poin: +${jumlahDatang}`);
        } else {
          // Buat ID pelanggan baru
          const newCustomerId = `P${String(customerCounter++).padStart(7, '0')}`;

          // Buat pelanggan baru
          await prisma.customer.create({
            data: {
              id: newCustomerId,
              name: nama,
              phone: formattedPhone,
              address: alamat || null,
              points: jumlahDatang, // Konversi jumlah datang menjadi poin
              tags: [...new Set([...excelTags, 'Migrasi', 'Baru'])], // Gabungkan tag dari Excel dengan tag default
              isActive: true
            }
          });

          // Tambahkan counter pelanggan baru
          newCustomers++;

          console.log(`✅ Pelanggan baru dibuat: ${nama} (${newCustomerId}), Poin: ${jumlahDatang}`);
        }

        berhasil++;
      } catch (error) {
        console.error(`❌ Error pada baris ${data.indexOf(row) + 2}: ${error.message}`);
        errors.push({
          row: data.indexOf(row) + 2,
          data: row,
          error: error.message
        });
        gagal++;
      }
    }

    // Tampilkan ringkasan
    console.log('\n📋 RINGKASAN MIGRASI:');
    console.log(`✅ Berhasil: ${berhasil} pelanggan`);
    console.log(`  - Pelanggan baru: ${newCustomers}`);
    console.log(`  - Diperbarui berdasarkan nomor telepon: ${updateByPhone}`);
    console.log(`  - Diperbarui berdasarkan nama yang sama: ${updateByName}`);
    console.log(`  - Semua pelanggan ditambahkan tag 'Migrasi' dan 'Baru', serta tag dari Excel jika ada`);

    // Tampilkan tag yang ditemukan di Excel
    if (allExcelTags.size > 0) {
      console.log(`  - Tag yang ditemukan di Excel: ${Array.from(allExcelTags).join(', ')}`);
    } else {
      console.log(`  - Tidak ada tag yang ditemukan di Excel`);
    }

    console.log(`❌ Gagal: ${gagal} pelanggan`);

    // Simpan log error jika ada
    if (errors.length > 0) {
      const errorLogPath = path.join(process.cwd(), 'migrasi-error-log.json');
      fs.writeFileSync(errorLogPath, JSON.stringify(errors, null, 2));
      console.log(`📝 Log error disimpan di: ${errorLogPath}`);
    }

    console.log('✨ Migrasi selesai!');
  } catch (error) {
    console.error('❌ Terjadi kesalahan:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Jalankan migrasi
migrasiPelanggan();
