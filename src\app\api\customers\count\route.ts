import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  try {
    console.log('Menghitung total pelanggan aktif...');

    // Ambil query parameter jika ada
    const { searchParams } = new URL(req.url);
    const isActive = searchParams.get('isActive') !== 'false'; // Default true
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    console.log(`API Count - Parameter filter: startDate=${startDate}, endDate=${endDate}`);

    // Buat filter untuk query
    const whereFilter: Record<string, any> = {
      isActive // Default: hanya pelanggan aktif
    };

    // Ambil parameter jenis filter
    const filterType = searchParams.get('filterType') || 'registration';
    console.log(`API Count - Filter type: ${filterType}`);

    // Filter berdasarkan tanggal
    if (startDate || endDate) {
      if (filterType === 'registration') {
        // Filter berdasarkan tanggal registrasi
        whereFilter.registeredAt = {};

        if (startDate) {
          // Konversi ke tanggal awal hari (00:00:00)
          const startDateTime = new Date(startDate);
          startDateTime.setHours(0, 0, 0, 0);
          whereFilter.registeredAt.gte = startDateTime;
          console.log(`API Count - Filtering by registration startDate: ${startDateTime.toISOString()}`);
        }

        if (endDate) {
          // Konversi ke tanggal akhir hari (23:59:59)
          const endDateTime = new Date(endDate);
          endDateTime.setHours(23, 59, 59, 999);
          whereFilter.registeredAt.lte = endDateTime;
          console.log(`API Count - Filtering by registration endDate: ${endDateTime.toISOString()}`);
        }
      } else if (filterType === 'transaction') {
        // Filter berdasarkan tanggal transaksi
        // Catatan: Ini memerlukan pendekatan berbeda karena kita perlu filter berdasarkan relasi
        console.log(`API Count - Using transaction date filter, will be applied post-query`);
      }
    }

    // Hitung total pelanggan
    let count = 0;

    if (filterType === 'transaction' && (startDate || endDate)) {
      console.log('Counting customers with transactions in date range...');

      // Konversi tanggal untuk perbandingan dengan penanganan zona waktu yang lebih baik
      let startDateTime = null;
      let endDateTime = null;

      if (startDate) {
        // Pastikan tanggal dimulai dari awal hari (00:00:00.000)
        startDateTime = new Date(startDate);
        startDateTime.setHours(0, 0, 0, 0);
        console.log(`Original startDate: ${startDate}, Converted to: ${startDateTime.toISOString()}`);
      }

      if (endDate) {
        // Pastikan tanggal berakhir di akhir hari (23:59:59.999)
        endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        console.log(`Original endDate: ${endDate}, Converted to: ${endDateTime.toISOString()}`);
      }

      console.log(`Counting transactions from: ${startDateTime?.toISOString() || 'any date'}`);
      console.log(`Counting transactions until: ${endDateTime?.toISOString() || 'any date'}`);

      // Untuk mendapatkan total yang akurat, kita perlu mengambil semua pelanggan dan memfilternya
      // Buat where clause untuk transaksi jika ada filter tanggal
      let transactionWhere = {};

      if (startDateTime || endDateTime) {
        transactionWhere = {
          createdAt: {}
        };

        if (startDateTime) {
          transactionWhere.createdAt.gte = startDateTime;
        }

        if (endDateTime) {
          transactionWhere.createdAt.lte = endDateTime;
        }
      }

      console.log(`Transaction where clause: ${JSON.stringify(transactionWhere)}`);

      const allCustomersWithTransactions = await prisma.customer.findMany({
        where: {
          isActive: true
        },
        select: {
          id: true,
          name: true, // Tambahkan nama untuk debugging
          transactions: {
            where: Object.keys(transactionWhere).length > 0 ? transactionWhere : undefined,
            select: {
              id: true,
              createdAt: true
            }
          }
        }
      });

      console.log(`Total customers with transactions data: ${allCustomersWithTransactions.length}`);

      // Filter pelanggan yang memiliki transaksi dalam rentang tanggal
      const filteredCustomerIds = allCustomersWithTransactions
        .filter(customer => {
          // Jika tidak ada transaksi, tidak termasuk dalam filter
          if (!customer.transactions || customer.transactions.length === 0) return false;

          // Dengan where clause di query, semua transaksi yang dikembalikan sudah dalam rentang tanggal
          // Jadi kita hanya perlu memastikan ada transaksi
          return customer.transactions.length > 0;
        })
        .map(customer => customer.id);

      // Log untuk debugging
      if (startDate === endDate && startDate === '2023-05-01') {
        console.log('Customers with transactions on May 1, 2023:');
        allCustomersWithTransactions.forEach(customer => {
          if (customer.transactions.length > 0) {
            console.log(`- ${customer.name} (${customer.id}): ${customer.transactions.length} transactions`);
            customer.transactions.forEach(trx => {
              console.log(`  - Transaction ${trx.id}: ${new Date(trx.createdAt).toISOString()}`);
            });
          }
        });
      }

      // Update total
      count = filteredCustomerIds.length;
      console.log(`Found ${count} customers with transactions in the selected date range`);

      // Log beberapa contoh pelanggan yang terfilter untuk debugging
      if (count > 0 && filteredCustomerIds.length > 0) {
        console.log('Sample filtered customer IDs:');
        filteredCustomerIds.slice(0, 5).forEach((id, index) => {
          console.log(`Customer ID ${index + 1}: ${id}`);
        });
      }
    } else {
      // Hitung dengan filter biasa
      count = await prisma.customer.count({
        where: whereFilter
      });
      console.log(`Total pelanggan aktif sesuai filter registrasi: ${count}`);
    }

    // Kembalikan jumlah pelanggan
    return NextResponse.json({
      count,
      isActive,
      filter: {
        type: filterType,
        startDate,
        endDate
      }
    });
  } catch (error) {
    console.error('Error menghitung pelanggan:', error);

    // Tampilkan detail error untuk debugging
    const errorMessage = error instanceof Error
      ? `${error.name}: ${error.message}`
      : 'Terjadi kesalahan yang tidak diketahui';

    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghitung data pelanggan', details: errorMessage },
      { status: 500 }
    );
  }
}
