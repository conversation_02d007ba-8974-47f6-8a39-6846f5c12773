'use client';

import React, { createContext, useState, useContext, useEffect, ReactNode, useCallback } from 'react';

type Theme = 'breaktime' | 'breaktime-dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const LOCAL_STORAGE_THEME_KEY = 'theme';

export function ThemeProvider({ children }: { children: ReactNode }) {
  const [theme, setTheme] = useState<Theme>('breaktime'); // Always start with light theme to avoid hydration mismatch

  // Load theme from localStorage after component mounts (client-side only)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedTheme = localStorage.getItem(LOCAL_STORAGE_THEME_KEY);
      if (storedTheme && (storedTheme === 'breaktime' || storedTheme === 'breaktime-dark')) {
        setTheme(storedTheme as Theme);
      }
      // Removed system preference check - always default to light theme
    }
  }, []);

  // Terapkan tema ke elemen HTML dan simpan ke Local Storage
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem(LOCAL_STORAGE_THEME_KEY, theme);
  }, [theme]);

  const toggleTheme = useCallback(() => {
    setTheme((prevTheme) => (prevTheme === 'breaktime' ? 'breaktime-dark' : 'breaktime'));
  }, []);

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useThemeContext() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  return context;
}