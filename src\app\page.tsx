'use client';

import Image from "next/image";
import { motion } from "framer-motion";
import Link from "next/link";
import { FiPhone, FiMail, FiCheckCircle, FiStar, FiSend, FiChevronDown, FiUser } from 'react-icons/fi';
import AddToHomeScreen from "@/components/AddToHomeScreen";
import SplitText from "@/components/SplitText";

export default function Home() {
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number = 1) => ({
      opacity: 1,
      y: 0,
      transition: { staggerChildren: 0.1, delayChildren: i * 0.1, duration: 0.5 }
    })
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const floatingAnimation = {
    y: [0, -10, 0],
    transition: {
      duration: 3,
      repeat: Infinity,
      repeatType: "reverse" as const,
      ease: "easeInOut"
    }
  };

  const bounceAnimation = {
    y: [0, -8, 0],
    opacity: [0.5, 1, 0.5],
    transition: {
      duration: 2,
      repeat: Infinity,
      repeatType: "loop" as const
    }
  };

  // Function to handle carousel hover
  const handleCarouselHover = (event: React.MouseEvent<HTMLDivElement>) => {
    const carousel = event.currentTarget;
    const rect = carousel.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;
    const carouselWidth = rect.width;
    const percentage = mouseX / carouselWidth;

    // Calculate position based on mouse position
    const maxShift = -50; // Maximum percentage to shift
    const position = maxShift * percentage;

    // Set the CSS variable for the hover position
    carousel.style.setProperty('--hover-x', `${position}%`);
  };

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen font-sans overflow-x-hidden bg-white text-gray-700">
      {/* PWA Install Prompt */}
      <AddToHomeScreen />

      {/* Decorative Elements - menggunakan warna tetapi dengan opacity lebih rendah */}
      <div className="absolute top-0 right-0 -z-10 w-96 h-96 rounded-full opacity-10 bg-gradient-to-r from-secondary/20 to-secondary-light/10 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 -z-10 w-96 h-96 rounded-full opacity-10 bg-gradient-to-r from-primary/20 to-primary-light/10 blur-3xl"></div>

      {/* Navbar with transparent background */}
      <div className="navbar bg-white/95 backdrop-blur-sm px-4 md:px-20 py-4 shadow-sm fixed top-0 left-0 right-0 z-50">
        <div className="flex-1">
          <div className="flex items-center">
            <Image
              src="/logo.png"
              alt="Breaktime Logo"
              width={44}
              height={44}
              className="mr-2"
            />
            <a className="text-2xl font-bold">
              <span className="text-amber-400">Break</span>
              <span className="text-teal-500">time</span>
            </a>
          </div>
        </div>
        <div className="flex-none gap-3">
          <ul className="menu menu-horizontal px-1 hidden lg:flex items-center">
            <li><a onClick={() => scrollToSection('layanan')} className="text-gray-600 font-medium hover:text-teal-500 cursor-pointer">Layanan</a></li>
            <li><a onClick={() => scrollToSection('kontak')} className="text-gray-600 font-medium hover:text-teal-500 cursor-pointer">Kontak</a></li>
            <li><a onClick={() => scrollToSection('tentang')} className="text-gray-600 font-medium hover:text-teal-500 cursor-pointer">Tentang</a></li>
            <li><a onClick={() => scrollToSection('testimoni')} className="text-gray-600 font-medium hover:text-teal-500 cursor-pointer">Testimoni</a></li>
            <li>
              <Link 
                href="/reservasi-online" 
                className="bg-gradient-to-r from-teal-500 to-teal-600 px-4 py-2 rounded-lg text-white font-medium hover:shadow-md hover:from-teal-600 hover:to-teal-700 transition-all duration-300"
              >
                Reservasi Online
              </Link>
            </li>
          </ul>
          <div className="flex gap-2">
            <Link
              href="/cek-poin"
              className="btn btn-sm normal-case font-medium bg-[#F5C24C] hover:bg-[#e5b346] text-gray-800 border-none shadow-md flex items-center justify-center gap-1 px-2 sm:px-3 whitespace-nowrap"
              aria-label="Cek Poin Loyalitas"
            >
              <FiStar size={14} />
              <span className="hidden sm:inline-block md:hidden">Cek Poin</span>
              <span className="hidden md:inline-block">Cek Poin Loyalitas</span>
            </Link>
            <Link href="/auth/login" className="btn btn-sm normal-case font-medium bg-white hover:bg-gray-100 text-gray-700 border border-gray-200">
              Login
            </Link>
          </div>
          <div className="dropdown dropdown-end lg:hidden ml-2">
            <label tabIndex={0} className="btn btn-ghost">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16m-7 6h7" /></svg>
            </label>
            <ul tabIndex={0} className="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
              <li><a onClick={() => scrollToSection('layanan')}>Layanan</a></li>
              <li><a onClick={() => scrollToSection('kontak')}>Kontak</a></li>
              <li><a onClick={() => scrollToSection('tentang')}>Tentang</a></li>
              <li><a onClick={() => scrollToSection('testimoni')}>Testimoni</a></li>
              <li>
                <Link 
                  href="/reservasi-online"
                  className="bg-gradient-to-r from-teal-500 to-teal-600 text-white px-2 py-1 rounded-md font-medium mt-2 block"
                >
                  Reservasi Online
                </Link>
              </li>
              <li>
                <Link href="/cek-poin" className="flex items-center gap-2 text-[#F5C24C] font-medium">
                  <FiStar size={16} />
                  <span className="hidden sm:inline-block">Cek Poin Loyalitas</span>
                </Link>
              </li>
              <li>
                <Link href="/auth/login" className="flex items-center gap-2">
                  <FiUser size={14} />
                  Login
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Hero Section with Full Background Image */}
      <div className="relative h-screen pt-16 flex items-center overflow-hidden">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0 z-0 transform scale-110 motion-safe:animate-subtle-zoom">
          <Image
            src="/Makassar.jpeg"
            alt="Makassar background"
            fill
            className="object-cover filter brightness-75"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-amber-900/40 to-amber-800/50"></div>

          {/* Decorative Elements untuk hero */}
          <div className="absolute inset-0 bg-pattern-dot opacity-10 mix-blend-soft-light"></div>
          <div className="absolute left-0 bottom-0 w-full h-24 bg-gradient-to-t from-black/40 to-transparent"></div>
          <div className="absolute -bottom-10 -left-10 w-80 h-80 bg-amber-600/40 blur-[100px] rounded-full opacity-50 mix-blend-multiply"></div>
          <div className="absolute -top-10 -right-10 w-80 h-80 bg-amber-800/40 blur-[100px] rounded-full opacity-50 mix-blend-multiply"></div>
        </div>

        <div className="container mx-auto px-4 md:px-20 relative z-10 text-white">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="relative z-10"
            >
              {/* Kembali Semangat! dengan animasi energik */}
              <div className="mb-6 relative overflow-hidden w-full">
                <motion.div
                  className="flex justify-end pr-2 md:pr-4 lg:pr-6"
                  initial={{ y: -100, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 15,
                    bounce: 0.5,
                    delay: 0.2
                  }}
                >
                  <motion.div
                    initial={{ scale: 0.8 }}
                    animate={{
                      scale: [0.9, 1.05, 0.95, 1.02, 1],
                      y: [0, -10, 5, -5, 0],
                    }}
                    transition={{
                      duration: 1.2,
                      ease: "easeOut",
                      times: [0, 0.3, 0.5, 0.8, 1],
                    }}
                    whileHover={{
                      scale: 1.05,
                      rotate: [0, -2, 2, -1, 0],
                      transition: {
                        duration: 0.5,
                        ease: "easeInOut",
                        times: [0, 0.25, 0.5, 0.75, 1],
                        repeat: Infinity,
                        repeatType: "mirror"
                      }
                    }}
                  >
                    <motion.div
                      animate={{
                        y: [0, -5, 0, -3, 0],
                      }}
                      transition={{
                        duration: 3,
                        ease: "easeInOut",
                        times: [0, 0.25, 0.5, 0.75, 1],
                        repeat: Infinity,
                        repeatType: "mirror"
                      }}
                    >
                      <Image
                        src="/kembali-semangat.png"
                        alt="Kembali Semangat!"
                        width={350}
                        height={150}
                        className="object-contain drop-shadow-lg animate-glow w-auto h-auto max-w-full"
                        priority
                      />
                    </motion.div>
                  </motion.div>
                </motion.div>
              </div>

              <motion.p
                variants={fadeIn}
                custom={1}
                className="text-teal-400 uppercase font-bold tracking-widest mb-2 drop-shadow-md"
              >
                BADAN SEGAR URUSAN LANCAR
              </motion.p>
              <motion.h1
                variants={fadeIn}
                custom={2}
                className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6 drop-shadow-lg"
              >
                Dapatkan dan rasakan <span className="text-amber-400 relative inline-block">kesegaran</span> <br/>
                yang produktif bersama <span className="relative inline-block"><span className="text-amber-400">Break</span><span className="text-teal-500">time</span></span>
              </motion.h1>
              <motion.p
                variants={fadeIn}
                custom={3}
                className="text-base text-gray-200 mb-8 max-w-md drop-shadow-md"
              >
                Segarkan pikiran dan tenaga anda, para Pejuang keluarga yang ingin menikmati me time.. yukk hempas badmood, hempas pegal dengan pijat relaksasi dari Breaktime.
              </motion.p>
              <motion.div variants={fadeIn} custom={4} className="flex flex-wrap gap-4">
                <a
                  href="https://wa.me/+6281248961009?text=Halo%20Breaktime,%20saya%20ingin%20reservasi%20layanan%20pijat"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn bg-amber-400 hover:bg-amber-500 text-gray-900 border-0 px-8 py-2 rounded-md"
                >
                  Pesan via WhatsApp
                </a>
                <Link
                  href="/reservasi-online"
                  className="btn bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white border-0 px-8 py-2 rounded-md shadow-md hover:shadow-lg transition-all duration-300"
                >
                  Reservasi Online
                </Link>
                <button className="btn bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 text-white flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-teal-500/70 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>Tonton Video</span>
                </button>
              </motion.div>

              {/* Floating Ratings Card */}
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8, duration: 0.6 }}
                className="mt-12 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-4 max-w-xs"
              >
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-amber-400/60 flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                      <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm9.707 5.707a1 1 0 00-1.414-1.414L9 12.586l-1.293-1.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-white">Kepuasan Terjamin</p>
                    <div className="flex items-center gap-1 mt-1">
                      {[...Array(5)].map((_, i) =>
                        <FiStar key={i} className="text-amber-400 fill-current" size={14} />
                      )}
                      <span className="text-xs text-gray-200 ml-1">5.0 Rating</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>

        {/* Scroll Down Indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 -translate-x-1/2 text-white cursor-pointer z-10"
          animate={bounceAnimation}
          onClick={() => scrollToSection('layanan')}
        >
          <div className="flex flex-col items-center">
            <span className="text-sm mb-2">Scroll Down</span>
            <FiChevronDown size={24} />
          </div>
        </motion.div>
      </div>

      {/* Category Section - Jadoo Style with Breaktime colors */}
      <motion.section
        id="layanan"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={staggerContainer}
        className="py-16 px-4 md:px-20"
      >
        <motion.div variants={fadeIn} className="text-center mb-16">
          <p className="text-secondary uppercase font-bold tracking-widest mb-2">LAYANAN KAMI</p>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800">
            Kami Menawarkan Layanan <br className="hidden md:block" />Pijat Terbaik
          </h2>
          <p className="text-gray-500 max-w-lg mx-auto">
            Pilih dari berbagai layanan pijat premium kami yang dirancang untuk memenuhi kebutuhan relaksasi Anda
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
          {[
            {
              title: "Pijat Tradisional",
              desc: "Pijat 5 titik dengan durasi 60, 90, atau 120 menit untuk relaksasi.",
              color: "from-primary/10 to-primary-light/5",
              iconBg: "bg-primary/20"
            },
            {
              title: "Pijat Kretek",
              desc: "Pijat tradisional dengan teknik khusus untuk meredakan ketegangan otot.",
              color: "from-secondary/10 to-secondary-light/5",
              iconBg: "bg-secondary/20",
              isNew: true
            },
            {
              title: "Lulur",
              desc: "Perawatan kulit dengan lulur tradisional untuk kulit lebih halus.",
              color: "from-primary/10 to-primary-light/5",
              iconBg: "bg-primary/20"
            },
            {
              title: "Masker Wajah",
              desc: "Perawatan wajah dengan masker untuk kulit wajah lebih segar dan bersih.",
              color: "from-secondary/10 to-secondary-light/5",
              iconBg: "bg-secondary/20"
            }
          ].map((service, i) => (
            <motion.div
              key={i}
              variants={fadeIn}
              custom={i + 1}
              className={`card hover:shadow-xl transition-all duration-300 hover:-translate-y-2 bg-gradient-to-br ${service.color} overflow-hidden group border border-gray-100 rounded-3xl p-6`}
            >
              <div className={`${service.iconBg} w-16 h-16 rounded-2xl flex items-center justify-center mb-5 group-hover:scale-110 transition-transform duration-300`}>
                <FiCheckCircle className={i % 2 === 0 ? "text-primary" : "text-secondary"} size={28} />
              </div>
              <h3 className="text-xl font-bold mb-3">{service.title}
                {service.isNew && <span className="ml-2 inline-block bg-teal-400 text-white text-xs px-2 py-0.5 rounded-full">Baru</span>}
              </h3>
              <p className="text-gray-500 text-sm">{service.desc}</p>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Featured Section - Jadoo Style with Breaktime colors */}
      <motion.section
        id="tentang"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={staggerContainer}
        className="py-20 px-4 md:px-20 bg-white border-y border-gray-100"
      >
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
          <motion.div variants={fadeIn}>
            <p className="text-secondary uppercase font-bold tracking-widest mb-2">KENAPA MEMILIH KAMI</p>
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800">
              Pengalaman Pijat Yang Tak Terlupakan
            </h2>
            <p className="text-gray-500 mb-8">
              Kami berkomitmen untuk memberikan pengalaman relaksasi terbaik dengan terapis profesional dan fasilitas premium.
            </p>

            <div className="space-y-6">
              {[
                {
                  title: "Terapis Berpengalaman",
                  desc: "Tim terapis kami terlatih dengan sertifikasi profesional."
                },
                {
                  title: "Ruangan yang Nyaman",
                  desc: "Ruangan pijat yang bersih, tenang, dan dilengkapi musik relaksasi."
                },
                {
                  title: "Produk Premium",
                  desc: "Kami hanya menggunakan minyak pijat dan produk berkualitas tinggi."
                }
              ].map((item, i) => (
                <motion.div
                  key={i}
                  variants={fadeIn}
                  custom={i + 1}
                  className="flex gap-4"
                >
                  <div className="w-10 h-10 rounded-full bg-primary/10 flex-shrink-0 flex items-center justify-center">
                    <FiCheckCircle className="text-primary" size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{item.title}</h3>
                    <p className="text-gray-500 text-sm">{item.desc}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          <motion.div
            variants={fadeIn}
            className="relative"
          >
            <div className="relative z-10">
              <Image
                src="https://images.unsplash.com/photo-1544161515-4ab6ce6db874?q=80&w=1080"
                alt="Massage therapy"
                width={600}
                height={500}
                className="rounded-3xl shadow-xl"
              />

              {/* Stats Card */}
              <motion.div
                className="absolute top-1/2 -left-12 bg-white rounded-lg shadow-lg p-4 z-20"
                animate={floatingAnimation}
              >
                <Image
                  src="https://images.unsplash.com/photo-1519823551278-64ac92734fb1?q=80&w=200"
                  alt="Massage"
                  width={100}
                  height={100}
                  className="rounded-lg mb-2"
                />
                <h4 className="font-bold">Pijat Refleksi</h4>
                <p className="text-xs text-gray-500">Layanan Populer</p>
              </motion.div>
            </div>

            {/* Decorative Elements */}
            <div className="absolute -z-10 top-1/4 right-1/4 bg-gradient-to-r from-primary to-secondary rounded-full w-64 h-64 blur-3xl opacity-20"></div>
          </motion.div>
        </div>
      </motion.section>

      {/* Testimonials - Updated with Framer Motion Carousel */}
      <motion.section
        id="testimoni"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={staggerContainer}
        className="py-20 px-4 md:px-20 overflow-hidden"
      >
        <motion.div variants={fadeIn} className="text-center mb-16">
          <p className="text-secondary uppercase font-bold tracking-widest mb-2">TESTIMONI</p>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800">
            Apa Kata Pelanggan <br className="hidden md:block" />Tentang Kami
          </h2>
        </motion.div>

        <div className="relative max-w-5xl mx-auto" onMouseMove={handleCarouselHover} onMouseLeave={(e) => e.currentTarget.style.setProperty('--hover-x', '0%')}>
          <motion.div
            className="flex flex-nowrap gap-6"
            animate={{ x: ["0%", "-50%"] }}
            transition={{
              repeat: Infinity,
              duration: 25,
              ease: "linear",
              repeatType: "loop"
            }}
            whileHover={{ x: "var(--hover-x)" }}
          >
            {/* Testimonials with duplicates for infinite loop effect */}
            {[
              // First set
              {
                name: "Siti Rahma",
                role: "Pengusaha",
                text: "Pengalaman pijat yang luar biasa! Terapis sangat profesional dan ruangannya nyaman. Saya merasa segar kembali setelah sesi pijat.",
                initials: "SR"
              },
              {
                name: "Budi Santoso",
                role: "Atlet",
                text: "Pijat refleksi di Breaktime sangat membantu mengatasi kelelahan otot setelah latihan intensif. Terapis sangat memahami kebutuhan saya sebagai atlet.",
                initials: "BS"
              },
              {
                name: "Dewi Kartika",
                role: "Dokter",
                text: "Sebagai dokter dengan jam kerja panjang, Breaktime adalah tempat favorit saya untuk relaksasi. Layanan lulur dan pijat kombinasi sangat menyegarkan.",
                initials: "DK"
              },
              {
                name: "Andi Firmansyah",
                role: "Programmer",
                text: "Pijat kretek di Breaktime sangat membantu meredakan ketegangan otot leher dan punggung saya akibat terlalu lama di depan komputer. Terapis sangat terampil!",
                initials: "AF"
              },
              {
                name: "Rina Wijaya",
                role: "Ibu Rumah Tangga",
                text: "Saya rutin melakukan perawatan di Breaktime setiap bulan. Layanan lulur dan masker wajah membuat kulit saya lebih sehat dan bercahaya.",
                initials: "RW"
              },
              // Second set (duplicate)
              {
                name: "Siti Rahma",
                role: "Pengusaha",
                text: "Pengalaman pijat yang luar biasa! Terapis sangat profesional dan ruangannya nyaman. Saya merasa segar kembali setelah sesi pijat.",
                initials: "SR"
              },
              {
                name: "Budi Santoso",
                role: "Atlet",
                text: "Pijat refleksi di Breaktime sangat membantu mengatasi kelelahan otot setelah latihan intensif. Terapis sangat memahami kebutuhan saya sebagai atlet.",
                initials: "BS"
              },
              {
                name: "Dewi Kartika",
                role: "Dokter",
                text: "Sebagai dokter dengan jam kerja panjang, Breaktime adalah tempat favorit saya untuk relaksasi. Layanan lulur dan pijat kombinasi sangat menyegarkan.",
                initials: "DK"
              },
              {
                name: "Andi Firmansyah",
                role: "Programmer",
                text: "Pijat kretek di Breaktime sangat membantu meredakan ketegangan otot leher dan punggung saya akibat terlalu lama di depan komputer. Terapis sangat terampil!",
                initials: "AF"
              },
              {
                name: "Rina Wijaya",
                role: "Ibu Rumah Tangga",
                text: "Saya rutin melakukan perawatan di Breaktime setiap bulan. Layanan lulur dan masker wajah membuat kulit saya lebih sehat dan bercahaya.",
                initials: "RW"
              },
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                variants={fadeIn}
                custom={index + 1}
                className="card min-w-[300px] sm:min-w-[350px] md:min-w-[400px] bg-white shadow-lg rounded-3xl p-6 border border-gray-100 flex-shrink-0 relative overflow-hidden"
                whileHover={{
                  y: -10,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                  scale: 1.02,
                  borderColor: "#4E9E97"
                }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="flex flex-col h-full relative z-10">
                  {/* Decorative elements */}
                  <div className="absolute -top-10 -right-10 w-20 h-20 rounded-full bg-primary/10 z-0"></div>
                  <div className="absolute -bottom-10 -left-10 w-20 h-20 rounded-full bg-secondary/10 z-0"></div>
                  <div className="flex mb-4">
                    {[...Array(5)].map((_, i) =>
                      <FiStar key={i} className="text-amber-400 fill-current" size={18} />
                    )}
                  </div>

                  <div className="relative mb-6 flex-grow">
                    <svg className="absolute -top-2 -left-2 w-8 h-8 text-secondary/20" fill="currentColor" viewBox="0 0 32 32">
                      <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
                    </svg>
                    <p className="italic text-gray-600 pl-6">&quot;{testimonial.text}&quot;</p>
                  </div>

                  <div className="flex items-center mt-auto">
                    <div className="relative mr-4">
                      <div className="w-[60px] h-[60px] rounded-full bg-gradient-to-br from-primary to-secondary flex items-center justify-center text-white font-bold text-xl shadow-md relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-to-br from-primary to-secondary opacity-80 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <span className="relative z-10">{testimonial.initials}</span>
                      </div>
                    </div>
                    <div>
                      <h3 className="font-bold text-lg">{testimonial.name}</h3>
                      <p className="text-gray-500 text-sm">{testimonial.role}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Gradient overlays for better UX */}
          <div className="absolute top-0 bottom-0 left-0 w-16 bg-gradient-to-r from-white to-transparent z-10"></div>
          <div className="absolute top-0 bottom-0 right-0 w-16 bg-gradient-to-l from-white to-transparent z-10"></div>
        </div>

        {/* Animated indicator showing continuous movement */}
        <div className="flex justify-center mt-10 items-center gap-2">
          <div className="text-sm text-gray-500">Scroll untuk melihat lebih banyak testimoni</div>
          <motion.div
            animate={{ x: [0, 10, 0] }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
            className="text-secondary"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M5 12h14"></path>
              <path d="m12 5 7 7-7 7"></path>
            </svg>
          </motion.div>
        </div>
      </motion.section>

      {/* Daftar Harga Section */}
      <motion.section
        id="daftar-harga"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={staggerContainer}
        className="py-20 px-4 md:px-20 bg-gradient-to-br from-amber-50 to-amber-100"
      >
        <motion.div variants={fadeIn} className="text-center mb-16">
          <p className="text-secondary uppercase font-bold tracking-widest mb-2">LAYANAN KAMI</p>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800">
            Daftar Harga
          </h2>
        </motion.div>

        {/* Outlet Service */}
        <div className="max-w-5xl mx-auto mb-16">
          <motion.div
            variants={fadeIn}
            custom={1}
            className="bg-gradient-to-r from-amber-500 to-amber-400 text-white p-4 rounded-t-xl"
          >
            <h3 className="text-2xl font-bold text-center">OUTLET SERVICE / PIJAT DI OUTLET</h3>
          </motion.div>

          {/* Tabel Durasi Pijat */}
          <motion.div
            variants={fadeIn}
            custom={2}
            className="overflow-hidden rounded-b-xl shadow-lg"
          >
            <div className="grid grid-cols-3 bg-amber-300 text-white font-semibold">
              <div className="p-4 text-center border-r border-amber-200">DURASI PIJAT</div>
              <div className="p-4 text-center border-r border-amber-200">TITIK PIJAT</div>
              <div className="p-4 text-center">HARGA</div>
            </div>

            <div className="bg-white">
              <div className="grid grid-cols-3 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50 relative">
                  <div className="font-semibold">60 MENIT</div>
                </div>
                <div className="p-4 border-r border-gray-100 text-center">
                  <div className="h-full flex items-center justify-center">
                    <div className="font-semibold text-lg">PIJAT 5 TITIK</div>
                  </div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 95.000</div>
              </div>

              <div className="grid grid-cols-3 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50 relative">
                  <div className="font-semibold">KRETEK 60 MENIT</div>
                  <div className="absolute -right-3 top-1/2 -translate-y-1/2 bg-teal-400 text-white px-2 py-1 rounded-lg text-xs transform rotate-0 shadow-md">
                    Terbaru
                  </div>
                </div>
                <div className="p-4 border-r border-gray-100 text-center">
                  <div className="font-semibold text-lg">PIJAT 5 TITIK</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 155.000</div>
              </div>

              <div className="grid grid-cols-3 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50">
                  <div className="font-semibold">90 MENIT</div>
                </div>
                <div className="p-4 border-r border-gray-100 text-center">
                  <div className="font-semibold text-lg">PIJAT 5 TITIK</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 135.000</div>
              </div>

              <div className="grid grid-cols-3 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50 relative">
                  <div className="font-semibold">120 MENIT</div>
                  <div className="absolute -right-3 top-1/2 -translate-y-1/2 bg-teal-400 text-white px-2 py-1 rounded-lg text-xs transform rotate-0 shadow-md">
                    Terlaris
                  </div>
                </div>
                <div className="p-4 border-r border-gray-100 text-center">
                  <div className="font-semibold text-lg">PIJAT 5 TITIK</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 180.000</div>
              </div>
            </div>

            {/* Tabel Layanan Lainnya */}
            <div className="grid grid-cols-2 bg-amber-300 text-white font-semibold">
              <div className="p-4 text-center border-r border-amber-200">LAYANAN</div>
              <div className="p-4 text-center">HARGA</div>
            </div>

            <div className="bg-white">
              <div className="grid grid-cols-2 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50">
                  <div className="font-semibold">LULUR + PIJAT 90 MENIT</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 205.000</div>
              </div>

              <div className="grid grid-cols-2 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50">
                  <div className="font-semibold">LULUR + PIJAT 120 MENIT</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 270.000</div>
              </div>

              <div className="grid grid-cols-2 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50">
                  <div className="font-semibold">EARCANDLE TREATMENT</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 45.000</div>
              </div>

              <div className="grid grid-cols-2 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50">
                  <div className="font-semibold">LULUR 60 MENIT</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 100.000</div>
              </div>

              <div className="grid grid-cols-2 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50">
                  <div className="font-semibold">MASKER WAJAH 30 MENIT</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 35.000</div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Home Service */}
        <div className="max-w-5xl mx-auto mb-16">
          <motion.div
            variants={fadeIn}
            custom={3}
            className="bg-gradient-to-r from-amber-600 to-amber-500 text-white p-4 rounded-t-xl"
          >
            <h3 className="text-2xl font-bold text-center">HOME SERVICE / PIJAT DIRUMAH</h3>
          </motion.div>

          {/* Tabel Layanan Home Service */}
          <motion.div
            variants={fadeIn}
            custom={4}
            className="overflow-hidden rounded-b-xl shadow-lg"
          >
            <div className="grid grid-cols-2 bg-amber-300 text-white font-semibold">
              <div className="p-4 text-center border-r border-amber-200">LAYANAN</div>
              <div className="p-4 text-center">HARGA</div>
            </div>

            <div className="bg-white">
              <div className="grid grid-cols-2 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50">
                  <div className="font-semibold">90 MENIT</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 160.000</div>
              </div>

              <div className="grid grid-cols-2 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50">
                  <div className="font-semibold">120 MENIT</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 205.000</div>
              </div>

              <div className="grid grid-cols-2 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50">
                  <div className="font-semibold">LULUR + 90 MENIT<br/>(PIJAT 5 TITIK + LULUR 30 MENIT)</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 245.000</div>
              </div>

              <div className="grid grid-cols-2 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50">
                  <div className="font-semibold">LULUR + 120 MENIT<br/>(PIJAT FULL BODY + LULUR 30 MENIT)</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 305.000</div>
              </div>

              <div className="grid grid-cols-2 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50 relative">
                  <div className="font-semibold">KRETEK 90 MENIT</div>
                  <div className="absolute -right-3 top-1/2 -translate-y-1/2 bg-teal-400 text-white px-2 py-1 rounded-lg text-xs transform rotate-0 shadow-md">
                    Terbaru
                  </div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 190.000</div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Layanan Tambahan */}
        <div className="max-w-5xl mx-auto">
          <motion.div
            variants={fadeIn}
            custom={5}
            className="bg-gradient-to-r from-amber-500 to-amber-400 text-white p-4 rounded-t-xl"
          >
            <h3 className="text-2xl font-bold text-center">LAYANAN TAMBAHAN</h3>
          </motion.div>

          {/* Tabel Layanan Tambahan */}
          <motion.div
            variants={fadeIn}
            custom={6}
            className="overflow-hidden rounded-b-xl shadow-lg"
          >
            <div className="grid grid-cols-3 bg-amber-300 text-white font-semibold">
              <div className="p-4 text-center border-r border-amber-200">LAYANAN</div>
              <div className="p-4 text-center border-r border-amber-200">TITIK PIJAT</div>
              <div className="p-4 text-center">HARGA</div>
            </div>

            <div className="bg-white">
              <div className="grid grid-cols-3 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50">
                  <div className="font-semibold">15 MENIT</div>
                </div>
                <div className="p-4 border-r border-gray-100 text-center">
                  <div className="font-semibold">PIJAT 1 TITIK</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 25.000</div>
              </div>

              <div className="grid grid-cols-3 border-b border-gray-100">
                <div className="p-4 border-r border-gray-100 bg-amber-50">
                  <div className="font-semibold">30 MENIT</div>
                </div>
                <div className="p-4 border-r border-gray-100 text-center">
                  <div className="font-semibold">PIJAT 3 TITIK</div>
                </div>
                <div className="p-4 text-right font-semibold">Rp. 50.000</div>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Subscription - Jadoo Style with Breaktime colors */}
      <motion.section
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={staggerContainer}
        className="py-16 px-4 md:px-20 mb-20"
      >
        <motion.div
          variants={fadeIn}
          className="bg-white rounded-3xl p-8 md:p-12 relative overflow-hidden max-w-5xl mx-auto shadow-xl border border-gray-100"
        >
          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 w-40 h-40 bg-secondary/20 rounded-full -translate-y-1/2 translate-x-1/4"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-primary/20 rounded-full translate-y-1/2 -translate-x-1/4"></div>

          <div className="text-center mb-8 relative z-10">
            <h2 className="text-2xl md:text-3xl font-bold mb-4 text-gray-800">
              Dapatkan Diskon Khusus <br className="hidden md:block" />untuk Pelanggan Baru
            </h2>
            <p className="text-gray-600 max-w-md mx-auto">
              Berlangganan untuk mendapatkan info tentang promo dan layanan terbaru kami
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto relative z-10">
            <div className="relative flex-grow w-full">
              <FiMail className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
              <input
                type="email"
                placeholder="Email Anda"
                className="input w-full pl-10 pr-4 bg-white border-gray-200 focus:border-primary focus:ring-0"
              />
            </div>
            <button className="btn bg-secondary hover:bg-secondary-dark text-white gap-2 w-full sm:w-auto border-0">
              <FiSend size={16} />
              <span>Berlangganan</span>
            </button>
          </div>
        </motion.div>
      </motion.section>

      {/* Footer - Jadoo Style with Breaktime colors */}
      <footer id="kontak" className="bg-white pt-16 pb-8 px-4 md:px-20">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-16">
          <div className="lg:col-span-2">
            <div className="flex items-center mb-4">
              <Image
                src="/logo.png"
                alt="Breaktime Logo"
                width={44}
                height={44}
                className="mr-2"
              />
              <h3 className="text-2xl font-bold">
                <span className="text-primary">Break</span>
                <span className="text-secondary">time</span>
              </h3>
            </div>
            <p className="text-gray-500 max-w-xs mb-2">
              Memberikan pengalaman relaksasi terbaik untuk kesejahteraan dan kenyamanan Anda.
            </p>
            <p className="text-primary-light mb-6">Badan Segar Urusan Lancar</p>

            {/* Reservasi CTA */}
            <a
              href="https://wa.me/+6281248961009?text=Halo%20Breaktime,%20saya%20ingin%20reservasi%20layanan%20pijat"
              target="_blank"
              rel="noopener noreferrer"
              className="btn btn-primary gap-2 mb-6 w-full sm:w-auto"
            >
              <FiPhone size={16} />
              <span>Reservasi via WhatsApp</span>
            </a>

            <div className="flex gap-4">
              <a
                href="https://twitter.com/breaktime"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path></svg>
              </a>
              <a
                href="https://www.youtube.com/breaktime"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center text-secondary hover:bg-secondary hover:text-white transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"></path></svg>
              </a>
              <a
                href="https://www.instagram.com/breaktime.official_/"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>
              </a>
            </div>
          </div>

          <div>
            <h6 className="font-bold text-lg mb-4">Lokasi Kami di Palu</h6>
            <ul className="space-y-3">
              <li>
                <div className="bg-secondary/10 rounded-lg p-3 hover:bg-secondary/20 transition-colors">
                  <h4 className="font-bold text-secondary mb-1">Jl. Emisaelan</h4>
                  <p className="text-gray-500 text-sm">Palu, Sulawesi Tengah</p>
                </div>
              </li>
              <li>
                <div className="bg-secondary/10 rounded-lg p-3 hover:bg-secondary/20 transition-colors">
                  <h4 className="font-bold text-secondary mb-1">Jl. Setiabudi</h4>
                  <p className="text-gray-500 text-sm">Palu, Sulawesi Tengah</p>
                </div>
              </li>
            </ul>

            <h6 className="font-bold text-lg mt-6 mb-4">Lokasi Kami di Makassar</h6>
            <div className="bg-primary/10 rounded-lg p-3 hover:bg-primary/20 transition-colors">
              <h4 className="font-bold text-primary mb-1">Jl. Pengayoman</h4>
              <p className="text-gray-500 text-sm">Ruko Cornelian No. 32-34</p>
              <p className="text-gray-500 text-sm">Makassar, Sulawesi Selatan</p>
            </div>
          </div>

          <div>
            <h6 className="font-bold text-lg mb-4">Layanan Kami</h6>
            <ul className="space-y-3">
              <li><a className="text-gray-500 hover:text-primary">Pijat Tradisional</a></li>
              <li><a className="text-gray-500 hover:text-primary">Pijat Kretek</a></li>
              <li><a className="text-gray-500 hover:text-primary">Lulur</a></li>
              <li><a className="text-gray-500 hover:text-primary">Masker Wajah</a></li>
              <li><a className="text-gray-500 hover:text-primary">Earcandle Treatment</a></li>
              <li>
                <Link href="/cek-poin" className="flex items-center gap-2 text-amber-500 hover:text-amber-600 font-medium">
                  <FiStar size={14} />
                  Cek Poin Loyalitas
                </Link>
              </li>
              <li>
                <Link href="/reservasi-online" className="flex items-center gap-2 text-teal-500 hover:text-teal-600 font-medium">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                  Reservasi Online
                </Link>
              </li>
            </ul>

            <div className="mt-6 p-3 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-lg border border-gray-100">
              <h4 className="font-bold text-gray-700 mb-2">Fasilitas Unggulan</h4>
              <p className="text-gray-500 text-sm flex items-center mb-2">
                <FiCheckCircle className="text-primary mr-2 flex-shrink-0" size={14} />
                <span>Terapis profesional sesuai gender</span>
              </p>
              <p className="text-gray-500 text-sm flex items-center mb-2">
                <FiCheckCircle className="text-primary mr-2 flex-shrink-0" size={14} />
                <span>Ruangan yang nyaman dan bersih</span>
              </p>
              <p className="text-gray-500 text-sm flex items-center mb-2">
                <FiCheckCircle className="text-primary mr-2 flex-shrink-0" size={14} />
                <span>Produk berkualitas tinggi</span>
              </p>
              <Link
                href="/reservasi-online"
                className="w-full block mt-3 py-2 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white text-center text-sm rounded-md font-medium"
              >
                Reservasi Online
              </Link>
            </div>
          </div>

          <div>
            <h6 className="font-bold text-lg mb-4">Kontak & Reservasi</h6>
            <ul className="space-y-3">
              <li className="flex items-center gap-2 text-gray-500">
                <FiPhone className="text-primary" />
                <span>0812-4896-1009</span>
              </li>
              <li className="flex items-center gap-2 text-gray-500">
                <FiMail className="text-primary" />
                <span><EMAIL></span>
              </li>
              <li className="flex items-center gap-2 text-gray-500">
                <svg xmlns="http://www.w3.org/2000/svg" className="text-primary" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                  <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                </svg>
                <a
                  href="https://www.instagram.com/breaktime.official_/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-primary"
                >
                  @breaktime.official_
                </a>
              </li>
              <li>
                <p className="text-gray-600 mt-6 mb-1 font-medium">Jam Operasional:</p>
                <p className="text-sm text-gray-500">Setiap hari, pukul 09.00–23.00 WITA</p>
              </li>
              <li>
                <a
                  href="https://wa.me/+6281248961009?text=Halo%20Breaktime,%20saya%20ingin%20reservasi%20layanan%20pijat"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-primary btn-sm gap-2 mt-6 w-full"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
                  </svg>
                  <span>Reservasi Sekarang</span>
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t pt-8 text-center">
          <p className="text-gray-500 text-sm">
            © 2024 BREAKTIME. Semua Hak Dilindungi.
          </p>
        </div>
      </footer>
    </div>
  );
}
