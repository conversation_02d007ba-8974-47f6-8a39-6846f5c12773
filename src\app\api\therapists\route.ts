import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import { logTherapist } from '@/lib/logger';
import { getUserIdFromToken } from '@/lib/auth-utils';

// GET semua terapis
export async function GET(request: NextRequest) {
  try {
    // Ambil URL parameters
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');
    const specialization = searchParams.get('specialization');
    const includeActiveSessions = searchParams.get('includeActiveSessions') === 'true';

    // Parse parameter include untuk relasi yang akan disertakan
    const includeParam = searchParams.get('include');
    const includeRelations = includeParam ? includeParam.split(',') : [];

    // Build query - Gunakan tipe dari argumen findMany
    const where: Prisma.TherapistFindManyArgs['where'] = {
      isActive: true
    };

    // Filter berdasarkan outlet jika ada
    if (outletId) {
      where.outletId = outletId;
    }

    // Filter berdasarkan specialization jika ada
    if (specialization) {
      where.specialization = {
        contains: specialization,
        mode: 'insensitive'
      };
    }

    // Ambil semua terapis dari database yang aktif (isActive: true)
    const therapists = await prisma.therapist.findMany({
      where,
      include: {
        outlet: {
          select: {
            id: true,
            name: true
          }
        },
        captain: {
          select: {
            id: true,
            name: true
          }
        },
        ...(includeActiveSessions ? {
          activeSessions: {
            where: {
              endTime: {
                gt: new Date() // Hanya sesi yang belum berakhir
              }
            },
            include: {
              service: {
                select: {
                  id: true,
                  name: true,
                  duration: true
                }
              }
            }
          }
        } : {})
      },
      orderBy: {
        experience: 'desc' // Terapis dengan pengalaman lebih banyak ditampilkan terlebih dahulu
      }
    });

    // Jika tidak ada terapis, kembalikan pesan kosong
    if (therapists.length === 0) {
      return NextResponse.json(
        { message: 'Tidak ada terapis yang tersedia' },
        { status: 404 }
      );
    }

    // Tambahkan flag isServing berdasarkan aktiveSessions
    const therapistsWithStatus = therapists.map(therapist => {
      const isServing = includeActiveSessions &&
        therapist.activeSessions &&
        therapist.activeSessions.length > 0;

      // Jika includeActiveSessions true, tambahkan informasi sesi aktif
      // Jika tidak, hapus properti activeSessions
      if (includeActiveSessions) {
        return {
          ...therapist,
          isServing,
          // Jika sedang melayani, tambahkan informasi kapan selesai
          ...(isServing ? {
            currentSession: therapist.activeSessions[0],
            availableAt: therapist.activeSessions[0].endTime,
            activeSessionId: therapist.activeSessions[0].id
          } : {})
        };
      } else {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { activeSessions, ...therapistWithoutSessions } = therapist;
        return therapistWithoutSessions;
      }
    });

    // Kembalikan data terapis
    return NextResponse.json({
      message: 'Data terapis berhasil diambil',
      therapists: therapistsWithStatus
    });
  } catch (error) {
    console.error('Error fetching therapists:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data terapis' },
      { status: 500 }
    );
  }
}

// POST untuk membuat terapis baru
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, outletId, specialization, experience, isActive, captainUserId } = body;

    // Validasi input
    if (!name || !outletId) {
      return NextResponse.json(
        { error: 'Nama dan outlet diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah outlet ada
    const outlet = await prisma.outlet.findUnique({
      where: {
        id: outletId
      }
    });

    if (!outlet) {
      return NextResponse.json(
        { error: 'Outlet tidak ditemukan' },
        { status: 404 }
      );
    }

    // Buat terapis baru
    const newTherapist = await prisma.therapist.create({
      data: {
        name,
        outletId,
        specialization: specialization,
        experience: experience || 0,
        isActive: isActive !== undefined ? isActive : true,
        captainUserId: captainUserId || null
      },
      include: {
        outlet: {
          select: {
            id: true,
            name: true
          }
        },
        captain: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Log pembuatan terapis baru
    await logTherapist(
      'create',
      newTherapist.id,
      {
        name: newTherapist.name,
        outletId: newTherapist.outletId,
        specialization: newTherapist.specialization,
        experience: newTherapist.experience,
        isActive: newTherapist.isActive,
        captainUserId: newTherapist.captainUserId
      },
      outletId,
      userId // Gunakan userId dari token
    );

    return NextResponse.json({
      message: 'Terapis berhasil dibuat',
      therapist: newTherapist
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating therapist:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat terapis' },
      { status: 500 }
    );
  }
}