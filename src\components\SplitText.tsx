'use client';

import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

interface SplitTextProps {
  text: string;
  className?: string;
  charClassName?: string;
  delay?: number;
  duration?: number;
  staggerChildren?: number;
}

const SplitText: React.FC<SplitTextProps> = ({
  text,
  className = '',
  charClassName = '',
  delay = 0,
  duration = 0.5,
  staggerChildren = 0.05
}) => {
  // Split the text into an array of characters
  const characters = text.split('');

  // Animation variants
  const container = {
    hidden: {},
    visible: (i = 1) => ({
      transition: {
        staggerChildren,
        delayChildren: delay
      }
    })
  };

  const child = {
    hidden: {
      opacity: 0,
      y: 20,
      rotate: -10,
      scale: 0.5
    },
    visible: {
      opacity: 1,
      y: 0,
      rotate: 0,
      scale: 1,
      transition: {
        type: 'spring',
        damping: 12,
        stiffness: 200,
        duration
      }
    }
  };

  return (
    <motion.div
      className={`inline-block ${className}`}
      variants={container}
      initial="hidden"
      animate="visible"
    >
      {characters.map((char, index) => (
        <motion.span
          key={index}
          variants={child}
          className={`inline-block ${charClassName}`}
          style={{ display: char === ' ' ? 'inline-block' : 'inline-block' }}
        >
          {char === ' ' ? '\u00A0' : char}
        </motion.span>
      ))}
    </motion.div>
  );
};

export default SplitText;
