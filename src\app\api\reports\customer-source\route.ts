import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');

    // Ambil parameter tanggal dari query
    const rawStartDate = searchParams.get('startDate');
    const rawEndDate = searchParams.get('endDate');

    console.log(`[API GET /api/reports/customer-source] Raw date params: startDate=${rawStartDate}, endDate=${rawEndDate}`);

    if (!rawStartDate || !rawEndDate) {
      throw new Error('Tanggal mulai dan tanggal akhir harus disediakan');
    }

    // Buat tanggal dengan pendekatan yang sama seperti di reports/route.ts
    const [startYear, startMonth, startDay] = rawStartDate.split('-').map(Number);
    const [endYear, endMonth, endDay] = rawEndDate.split('-').map(Number);

    // Buat tanggal dengan komponen tanggal lokal
    const startDate = new Date(startYear, startMonth - 1, startDay, 0, 0, 0, 0);
    const endDate = new Date(endYear, endMonth - 1, endDay, 23, 59, 59, 999);

    // Validasi tanggal
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.log(`[API GET /api/reports/customer-source] Invalid date: startDate=${startDate}, endDate=${endDate}`);
      throw new Error('Format tanggal tidak valid');
    }

    console.log(`[API GET /api/reports/customer-source] Using dates: startDate=${startDate.toISOString()}, endDate=${endDate.toISOString()}`);

    // Filter dasar untuk transaksi berdasarkan tanggal dan outlet terpilih
    const transactionWhere: Prisma.TransactionWhereInput = {
      transactionDate: { gte: startDate, lte: endDate },
      ...(outletId && { outletId: outletId }),
    };

    console.log(`[API GET /api/reports/customer-source] Transaction filter: ${JSON.stringify({
      transactionDate: { gte: startDate.toISOString(), lte: endDate.toISOString() },
      ...(outletId && { outletId: outletId }),
    })}`);

    // Ambil semua transaksi dengan notes untuk analisis sumber pelanggan
    const transactions = await prisma.transaction.findMany({
      where: transactionWhere,
      select: {
        id: true,
        notes: true,
        totalAmount: true,
        transactionDate: true,
        customer: {
          select: {
            id: true,
            name: true
          }
        },
        outlet: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    console.log(`[API GET /api/reports/customer-source] Found ${transactions.length} transactions`);

    // Kategorisasi sumber pelanggan berdasarkan notes
    const customerSourceAnalysis = {
      fromAdvertisement: {
        count: 0,
        totalRevenue: 0,
        customers: new Set<string>(),
        transactions: [] as any[]
      },
      alreadyKnow: {
        count: 0,
        totalRevenue: 0,
        customers: new Set<string>(),
        transactions: [] as any[]
      },
      referral: {
        count: 0,
        totalRevenue: 0,
        customers: new Set<string>(),
        transactions: [] as any[]
      },
      socialMedia: {
        count: 0,
        totalRevenue: 0,
        customers: new Set<string>(),
        transactions: [] as any[]
      },
      walkIn: {
        count: 0,
        totalRevenue: 0,
        customers: new Set<string>(),
        transactions: [] as any[]
      },
      other: {
        count: 0,
        totalRevenue: 0,
        customers: new Set<string>(),
        transactions: [] as any[]
      },
      noInfo: {
        count: 0,
        totalRevenue: 0,
        customers: new Set<string>(),
        transactions: [] as any[]
      }
    };

    // Keywords untuk kategorisasi
    const advertisementKeywords = ['iklan', 'advertisement', 'ads', 'promosi', 'promo', 'banner', 'brosur', 'flyer'];
    const alreadyKnowKeywords = ['sudah tahu', 'sudah kenal', 'already know', 'tahu sebelumnya', 'pelanggan lama', 'repeat customer', 'ingat', 'info bt'];
    const referralKeywords = ['referral', 'rujukan', 'rekomendasi', 'teman', 'keluarga', 'saudara', 'recommend', 'refer'];
    const socialMediaKeywords = ['instagram', 'facebook', 'tiktok', 'whatsapp', 'sosial media', 'social media', 'ig', 'fb', 'wa'];
    const walkInKeywords = ['walk in', 'lewat', 'kebetulan', 'spontan', 'jalan-jalan', 'passing by'];

    // Analisis setiap transaksi
    transactions.forEach(transaction => {
      const notes = (transaction.notes || '').toLowerCase();
      const transactionData = {
        id: transaction.id,
        date: transaction.transactionDate,
        amount: transaction.totalAmount,
        customerName: transaction.customer?.name || 'Unknown',
        outletName: transaction.outlet?.name || 'Unknown',
        notes: transaction.notes
      };

      let categorized = false;

      // Cek iklan/advertisement
      if (!categorized && advertisementKeywords.some(keyword => notes.includes(keyword))) {
        customerSourceAnalysis.fromAdvertisement.count++;
        customerSourceAnalysis.fromAdvertisement.totalRevenue += transaction.totalAmount;
        customerSourceAnalysis.fromAdvertisement.customers.add(transaction.customer?.id || '');
        customerSourceAnalysis.fromAdvertisement.transactions.push(transactionData);
        categorized = true;
      }

      // Cek sudah tahu sebelumnya
      if (!categorized && alreadyKnowKeywords.some(keyword => notes.includes(keyword))) {
        customerSourceAnalysis.alreadyKnow.count++;
        customerSourceAnalysis.alreadyKnow.totalRevenue += transaction.totalAmount;
        customerSourceAnalysis.alreadyKnow.customers.add(transaction.customer?.id || '');
        customerSourceAnalysis.alreadyKnow.transactions.push(transactionData);
        categorized = true;
      }

      // Cek referral
      if (!categorized && referralKeywords.some(keyword => notes.includes(keyword))) {
        customerSourceAnalysis.referral.count++;
        customerSourceAnalysis.referral.totalRevenue += transaction.totalAmount;
        customerSourceAnalysis.referral.customers.add(transaction.customer?.id || '');
        customerSourceAnalysis.referral.transactions.push(transactionData);
        categorized = true;
      }

      // Cek social media
      if (!categorized && socialMediaKeywords.some(keyword => notes.includes(keyword))) {
        customerSourceAnalysis.socialMedia.count++;
        customerSourceAnalysis.socialMedia.totalRevenue += transaction.totalAmount;
        customerSourceAnalysis.socialMedia.customers.add(transaction.customer?.id || '');
        customerSourceAnalysis.socialMedia.transactions.push(transactionData);
        categorized = true;
      }

      // Cek walk in
      if (!categorized && walkInKeywords.some(keyword => notes.includes(keyword))) {
        customerSourceAnalysis.walkIn.count++;
        customerSourceAnalysis.walkIn.totalRevenue += transaction.totalAmount;
        customerSourceAnalysis.walkIn.customers.add(transaction.customer?.id || '');
        customerSourceAnalysis.walkIn.transactions.push(transactionData);
        categorized = true;
      }

      // Jika ada notes tapi tidak masuk kategori manapun
      if (!categorized && notes.trim() !== '') {
        customerSourceAnalysis.other.count++;
        customerSourceAnalysis.other.totalRevenue += transaction.totalAmount;
        customerSourceAnalysis.other.customers.add(transaction.customer?.id || '');
        customerSourceAnalysis.other.transactions.push(transactionData);
        categorized = true;
      }

      // Jika tidak ada notes
      if (!categorized) {
        customerSourceAnalysis.noInfo.count++;
        customerSourceAnalysis.noInfo.totalRevenue += transaction.totalAmount;
        customerSourceAnalysis.noInfo.customers.add(transaction.customer?.id || '');
        customerSourceAnalysis.noInfo.transactions.push(transactionData);
      }
    });

    // Convert Set ke array untuk response
    const result = {
      fromAdvertisement: {
        count: customerSourceAnalysis.fromAdvertisement.count,
        totalRevenue: customerSourceAnalysis.fromAdvertisement.totalRevenue,
        uniqueCustomers: customerSourceAnalysis.fromAdvertisement.customers.size,
        transactions: customerSourceAnalysis.fromAdvertisement.transactions
      },
      alreadyKnow: {
        count: customerSourceAnalysis.alreadyKnow.count,
        totalRevenue: customerSourceAnalysis.alreadyKnow.totalRevenue,
        uniqueCustomers: customerSourceAnalysis.alreadyKnow.customers.size,
        transactions: customerSourceAnalysis.alreadyKnow.transactions
      },
      referral: {
        count: customerSourceAnalysis.referral.count,
        totalRevenue: customerSourceAnalysis.referral.totalRevenue,
        uniqueCustomers: customerSourceAnalysis.referral.customers.size,
        transactions: customerSourceAnalysis.referral.transactions
      },
      socialMedia: {
        count: customerSourceAnalysis.socialMedia.count,
        totalRevenue: customerSourceAnalysis.socialMedia.totalRevenue,
        uniqueCustomers: customerSourceAnalysis.socialMedia.customers.size,
        transactions: customerSourceAnalysis.socialMedia.transactions
      },
      walkIn: {
        count: customerSourceAnalysis.walkIn.count,
        totalRevenue: customerSourceAnalysis.walkIn.totalRevenue,
        uniqueCustomers: customerSourceAnalysis.walkIn.customers.size,
        transactions: customerSourceAnalysis.walkIn.transactions
      },
      other: {
        count: customerSourceAnalysis.other.count,
        totalRevenue: customerSourceAnalysis.other.totalRevenue,
        uniqueCustomers: customerSourceAnalysis.other.customers.size,
        transactions: customerSourceAnalysis.other.transactions
      },
      noInfo: {
        count: customerSourceAnalysis.noInfo.count,
        totalRevenue: customerSourceAnalysis.noInfo.totalRevenue,
        uniqueCustomers: customerSourceAnalysis.noInfo.customers.size,
        transactions: customerSourceAnalysis.noInfo.transactions
      }
    };

    // Hitung total untuk persentase
    const totalTransactions = transactions.length;
    const totalRevenue = transactions.reduce((sum, t) => sum + t.totalAmount, 0);

    console.log(`[API GET /api/reports/customer-source] Analysis complete. Total transactions: ${totalTransactions}`);

    return NextResponse.json({
      customerSourceAnalysis: result,
      summary: {
        totalTransactions,
        totalRevenue,
        dateRange: {
          startDate: rawStartDate,
          endDate: rawEndDate
        },
        outletFilter: outletId || 'all'
      }
    });

  } catch (error: any) {
    console.error('[API GET /api/reports/customer-source] Error:', error);
    return NextResponse.json(
      { error: error.message || 'Gagal memuat analisis sumber pelanggan' },
      { status: 500 }
    );
  }
}