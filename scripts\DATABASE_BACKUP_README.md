# 📦 Panduan Backup dan Restore Database

Dokumen ini berisi panduan untuk melakukan backup dan restore database menggunakan script yang telah disediakan.

## 🔄 Backup Database

Script backup database akan mengekspor semua data dari database ke file JSON. File backup akan disimpan di folder `database-backups` dengan format nama `backup-[timestamp].json`.

### Cara Menggunakan

```bash
node scripts/backup-database.js
```

### Output

Setelah proses backup selesai, Anda akan melihat pesan konfirmasi dan lokasi file backup yang telah dibuat.

Contoh output:
```
🔄 Memulai proses backup database...
📋 Ditemukan 20 model untuk di-backup
🔍 Mengambil data dari model: user
✅ Berhasil mengambil 10 data dari model user
...
✅ Backup database berhasil disimpan ke: F:\breaktimedash5\database-backups\backup-2025-06-20T10-00-16.024Z.json
🎉 Proses backup selesai!
```

## 🔄 Restore Database

Script restore database akan mengimpor data dari file backup JSON ke database. 

⚠️ **PERINGATAN**: Proses restore dapat menimpa data yang ada di database. Pastikan Anda telah membuat backup terlebih dahulu!

### Cara Menggunakan

```bash
node scripts/restore-database.js <path-to-backup-file>
```

Contoh:
```bash
node scripts/restore-database.js ./database-backups/backup-2025-06-20T10-00-16.024Z.json
```

### Opsi Konfigurasi

Dalam file `restore-database.js`, ada beberapa opsi yang dapat dimodifikasi sesuai kebutuhan:

1. **Menghapus Data yang Ada Sebelum Restore**
   
   Secara default, script tidak akan menghapus data yang ada sebelum melakukan restore. Jika Anda ingin menghapus data yang ada terlebih dahulu, uncomment baris berikut di file `restore-database.js`:
   
   ```javascript
   // await tx[name].deleteMany({});
   ```

2. **Ukuran Batch**
   
   Untuk menghindari timeout, data di-restore dalam batch. Ukuran batch default adalah 100 records. Anda dapat mengubah nilai ini sesuai kebutuhan:
   
   ```javascript
   const batchSize = 100;
   ```

## 📋 Praktik Terbaik

1. **Backup Rutin**
   
   Lakukan backup database secara rutin, terutama sebelum melakukan perubahan besar pada aplikasi atau database.

2. **Simpan Backup di Lokasi Aman**
   
   Salin file backup ke lokasi yang aman, seperti penyimpanan cloud atau hard drive eksternal.

3. **Uji Restore**
   
   Secara berkala, uji proses restore untuk memastikan backup berfungsi dengan baik.

4. **Dokumentasi**
   
   Catat tanggal dan waktu backup, serta perubahan penting yang terjadi pada database.

## ⚠️ Troubleshooting

### Error: Prisma Client tidak dapat terhubung ke database

Pastikan konfigurasi database di file `.env` sudah benar dan database server berjalan.

### Error: Out of Memory

Jika database sangat besar, proses backup atau restore mungkin memerlukan lebih banyak memori. Coba jalankan dengan alokasi memori yang lebih besar:

```bash
node --max-old-space-size=4096 scripts/backup-database.js
```

### Error: Timeout

Jika proses timeout, coba kurangi ukuran batch pada script restore:

```javascript
const batchSize = 50; // Kurangi dari 100 menjadi 50
```

## 📞 Dukungan

Jika Anda mengalami masalah atau memiliki pertanyaan, silakan hubungi tim pengembang aplikasi.