'use client';

import React, { useRef } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer,
  Legend, ZAxis, Cell, PieChart, Pie
} from 'recharts';

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('id-ID', { 
    style: 'currency', 
    currency: 'IDR', 
    minimumFractionDigits: 0 
  }).format(value);
};

// Helper Warna berdasarkan Logo (Gold & Teal)
const LOGO_COLORS = ['#FDBA74', '#2DD4BF', '#FCD34D', '#5EEAD4', '#FB923C', '#0D9488']; 

// Helper untuk label Pie Chart
const RADIAN = Math.PI / 180;
const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }: any) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central" fontSize={10}>
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

interface CustomerSegment {
  id: string;
  name: string;
  color: string;
  description: string;
}

interface CustomerData {
  id: string;
  name: string;
  visitCount: number;
  totalSpent: number;
  avgSpent: number;
  segment: string;
}

interface SegmentSummary {
  segment: string;
  count: number;
  totalSpent: number;
  avgSpent: number;
}

interface CustomerSegmentationChartProps {
  customers: CustomerData[];
  segments: CustomerSegment[];
  type?: 'scatter' | 'pie';
  title?: string;
  height?: number;
  className?: string;
}

const CustomerSegmentationChart: React.FC<CustomerSegmentationChartProps> = ({
  customers,
  segments,
  type = 'scatter',
  title = 'Segmentasi Pelanggan',
  height = 400,
  className = ''
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  // Prepare segment summary for pie chart
  const segmentSummary: SegmentSummary[] = segments.map(segment => {
    const segmentCustomers = customers.filter(c => c.segment === segment.id);
    return {
      segment: segment.name,
      count: segmentCustomers.length,
      totalSpent: segmentCustomers.reduce((sum, c) => sum + c.totalSpent, 0),
      avgSpent: segmentCustomers.length > 0 
        ? segmentCustomers.reduce((sum, c) => sum + c.totalSpent, 0) / segmentCustomers.length
        : 0
    };
  });

  // Get color for segment
  const getSegmentColor = (segmentId: string) => {
    const segment = segments.find(s => s.id === segmentId);
    return segment ? segment.color : '#cccccc';
  };

  // Get name for segment
  const getSegmentName = (segmentId: string) => {
    const segment = segments.find(s => s.id === segmentId);
    return segment ? segment.name : 'Unknown';
  };

  return (
    <div className={`w-full ${className}`}>
      {title && <h3 className="text-lg font-semibold mb-2">{title}</h3>}
      <div ref={chartRef} style={{ width: '100%', height: `${height}px` }}>
        <ResponsiveContainer>
          {type === 'scatter' ? (
            <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
              <XAxis 
                type="number" 
                dataKey="visitCount" 
                name="Kunjungan" 
                tick={{ fill: '#6b7280' }}
                label={{ value: 'Jumlah Kunjungan', position: 'insideBottomRight', offset: -5, fill: '#6b7280' }}
              />
              <YAxis 
                type="number" 
                dataKey="totalSpent" 
                name="Total Pengeluaran" 
                tick={{ fill: '#6b7280' }}
                tickFormatter={(value) => new Intl.NumberFormat('id-ID', { 
                  notation: 'compact', 
                  compactDisplay: 'short' 
                }).format(value)}
                label={{ value: 'Total Pengeluaran (Rp)', angle: -90, position: 'insideLeft', fill: '#6b7280' }}
              />
              <ZAxis type="category" dataKey="segment" name="Segmen" />
              <Tooltip 
                cursor={{ strokeDasharray: '3 3' }}
                formatter={(value: any, name: string) => {
                  if (name === 'Total Pengeluaran') return [formatCurrency(value), name];
                  if (name === 'Segmen') return [getSegmentName(value), name];
                  return [value, name];
                }}
                labelFormatter={(label) => ''}
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    const customer = payload[0].payload;
                    return (
                      <div className="bg-white p-2 border border-gray-200 shadow-sm rounded-md">
                        <p className="font-semibold">{customer.name}</p>
                        <p>Kunjungan: {customer.visitCount}</p>
                        <p>Total: {formatCurrency(customer.totalSpent)}</p>
                        <p>Rata-rata: {formatCurrency(customer.avgSpent)}</p>
                        <p>Segmen: {getSegmentName(customer.segment)}</p>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Legend />
              {segments.map(segment => (
                <Scatter 
                  key={segment.id}
                  name={segment.name}
                  data={customers.filter(c => c.segment === segment.id)}
                  fill={segment.color}
                />
              ))}
            </ScatterChart>
          ) : (
            <PieChart>
              <Pie
                data={segmentSummary}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius="80%"
                innerRadius="40%"
                fill="#8884d8"
                dataKey="count"
                nameKey="segment"
                paddingAngle={2}
              >
                {segmentSummary.map((entry, index) => {
                  const segment = segments.find(s => s.name === entry.segment);
                  return (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={segment ? segment.color : LOGO_COLORS[index % LOGO_COLORS.length]} 
                    />
                  );
                })}
              </Pie>
              <Tooltip 
                formatter={(value: number, name: string, props: any) => {
                  const entry = props.payload;
                  if (name === 'count') return [value, 'Jumlah Pelanggan'];
                  return [value, name];
                }}
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    const data = payload[0].payload;
                    return (
                      <div className="bg-white p-2 border border-gray-200 shadow-sm rounded-md">
                        <p className="font-semibold">{data.segment}</p>
                        <p>Jumlah: {data.count} pelanggan</p>
                        <p>Total: {formatCurrency(data.totalSpent)}</p>
                        <p>Rata-rata: {formatCurrency(data.avgSpent)}</p>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Legend />
            </PieChart>
          )}
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default CustomerSegmentationChart;
