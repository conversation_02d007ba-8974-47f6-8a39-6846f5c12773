'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Sidebar from '@/components/dashboard/Sidebar';
import { motion } from 'framer-motion';
import { useOutletContext } from '@/contexts/OutletContext';
import { FiMenu, FiAlertTriangle } from 'react-icons/fi'; // Ikon untuk hamburger dan alert

// Komponen untuk menangani useSearchParams
function DashboardContent({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { selectedOutletId } = useOutletContext();
  const [isClient, setIsClient] = useState(false); // State untuk menandai sudah di client
  const [showAccessDeniedModal, setShowAccessDeniedModal] = useState(false);
  const [accessDeniedMessage, setAccessDeniedMessage] = useState('');

  // Tandai bahwa komponen sudah di-mount di client
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Lakukan redirect hanya jika sudah di client dan outlet belum dipilih
  useEffect(() => {
    if (isClient && !selectedOutletId) {
      router.push('/outlet-selection');
    }
  }, [isClient, selectedOutletId, router]);

  // Cek parameter error dan message untuk menampilkan modal
  useEffect(() => {
    if (isClient) {
      const error = searchParams.get('error');
      const message = searchParams.get('message');

      if (error === 'unauthorized' && message) {
        setAccessDeniedMessage(message);
        setShowAccessDeniedModal(true);

        // Hapus parameter dari URL tanpa refresh halaman
        const url = new URL(window.location.href);
        url.searchParams.delete('error');
        url.searchParams.delete('message');
        window.history.replaceState({}, '', url);
      }
    }
  }, [isClient, searchParams]);

  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } },
  };

  // Jangan render apapun atau tampilkan loading sampai client siap dan outlet terpilih
  if (!isClient || !selectedOutletId) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <span className="loading loading-spinner loading-lg text-primary"></span>
      </div>
    );
  }

  // Outlet sudah terpilih dan kita berada di client, render layout
  return (
    <div className="drawer lg:drawer-open" data-theme="breaktime">
      {/* Checkbox Toggle (Hidden) */}
      <input id="dashboard-drawer" type="checkbox" className="drawer-toggle" />

      {/* Page Content */}
      <div className="drawer-content flex flex-col">
        {/* Navbar/Header Simpel untuk Tombol Toggle (Opsional, bisa ditaruh di main) */}
        <div className="w-full navbar bg-white lg:hidden sticky top-0 z-30 shadow-sm">
          <div className="flex-none">
            <label htmlFor="dashboard-drawer" aria-label="open sidebar" className="btn btn-square btn-ghost">
              <FiMenu className="w-5 h-5 text-gray-700" />
            </label>
          </div>
          <div className="flex-1 px-2 mx-2 font-bold">
             <span className="text-teal-500">Break</span>
             <span className="text-amber-500">time</span>
          </div>
        </div>
        {/* Main Content Area */}
        <main className="flex-1 overflow-y-auto p-6 md:p-8 bg-gray-100">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            className="p-4 md:p-6 mx-auto max-w-7xl"
          >
            {children}
          </motion.div>
        </main>
      </div>

      {/* Sidebar Content */}
      <div className="drawer-side z-40">
        {/* Overlay untuk menutup drawer di mobile */}
        <label htmlFor="dashboard-drawer" aria-label="close sidebar" className="drawer-overlay"></label>
        {/* Panggil komponen Sidebar di sini */}
        <Sidebar />
      </div>

      {/* Modal Akses Ditolak */}
      {showAccessDeniedModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
          >
            <div className="p-6">
              <div className="flex items-center justify-center mb-4">
                <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                  <FiAlertTriangle className="text-red-500 w-6 h-6" />
                </div>
              </div>
              <h3 className="text-lg font-bold text-center mb-2">Akses Ditolak</h3>
              <p className="text-center text-gray-600 mb-6">{accessDeniedMessage}</p>
              <div className="flex justify-center">
                <button
                  className="btn btn-primary w-full sm:w-auto"
                  onClick={() => setShowAccessDeniedModal(false)}
                >
                  Mengerti
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
}

// Layout utama yang menggunakan Suspense untuk membungkus komponen yang menggunakan useSearchParams
export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen bg-gray-100">
      <span className="loading loading-spinner loading-lg text-primary"></span>
    </div>}>
      <DashboardContent>{children}</DashboardContent>
    </Suspense>
  );
}