'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  FiPlus, FiSearch, FiEdit, FiTrash2,
  FiInbox, FiCheck, FiX, FiEye, FiEyeOff, FiAlertTriangle,
  FiShield, FiHome, FiUsers
} from 'react-icons/fi';
import Link from 'next/link';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import { toast } from 'sonner';
import PermissionGuard from '@/components/permission/PermissionGuard';

// --- Interfaces & Enums (Disesuaikan dengan Prisma Schema & API) ---
// Role disesuaikan dengan Prisma Enum
enum UserRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  STAFF = 'STAFF',
  INVESTOR = 'INVESTOR'
}

// Interface untuk Outlet (minimal untuk pilihan)
interface OutletOption {
  id: string;
  name: string;
}

// Interface untuk Permission (BARU)
interface ModulePermission {
  module: string;
  canCreate: boolean;
  canRead: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

// User interface disesuaikan dengan API response (tambahkan permissions dan allowedOutlets)
interface User {
  id: string;
  username: string;
  name: string;
  email: string;
  role: UserRole;
  isActive: boolean;
  isCaptain?: boolean; // Tambahkan properti kapten
  lastLoginAt?: string | null;
  createdAt: string;
  updatedAt: string;
  permissions: ModulePermission[];
  allowedOutlets?: OutletOption[];
}

// Deskripsi role (opsional, bisa disesuaikan)
// const ROLE_DESCRIPTIONS = {
//   [UserRole.ADMIN]: 'Akses Penuh',
//   [UserRole.MANAGER]: 'Manajer Outlet',
//   [UserRole.STAFF]: 'Staff/Kasir/Resepsionis'
// };

// Warna badge untuk role (opsional, bisa disesuaikan)
const ROLE_BADGE_COLORS = {
  [UserRole.ADMIN]: 'badge-primary',
  [UserRole.MANAGER]: 'badge-secondary',
  [UserRole.STAFF]: 'badge-accent',
  [UserRole.INVESTOR]: 'badge-info'
};
// --- Akhir Interfaces & Enums ---

// Daftar modul sistem (sinkronkan dengan API dan seed) (BARU)
const SYSTEM_MODULES = [
  'dashboard',
  'transactions',
  'customers',
  'therapists',
  'services',
  'bookings',
  'reports',
  'settings',
  'users',
];

// Fungsi helper untuk generate default permissions (BARU, untuk reset di modal)
const generateDefaultPermissions = (role: UserRole): ModulePermission[] => {
  return SYSTEM_MODULES.map(moduleName => {
    let canCreate = false, canRead = false, canUpdate = false, canDelete = false;
    switch (role) {
      case 'ADMIN':
        canCreate = canRead = canUpdate = canDelete = true;
        break;
      case 'MANAGER':
        canRead = true;
        if (!['users', 'settings'].includes(moduleName)) {
          canCreate = canUpdate = canDelete = true;
        }
        break;
      case 'STAFF':
        canRead = ['dashboard', 'transactions', 'customers', 'therapists', 'services', 'bookings'].includes(moduleName);
        canCreate = ['transactions', 'customers', 'bookings'].includes(moduleName);
        canUpdate = ['transactions', 'customers', 'bookings'].includes(moduleName);
        canDelete = false;
        break;
      case UserRole.INVESTOR:
        canRead = ['dashboard', 'reports'].includes(moduleName);
        break;
    }
    // Pastikan struktur sesuai ModulePermission
    return { module: moduleName, canCreate, canRead, canUpdate, canDelete };
  });
};

// Varian Animasi
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
};
const staggerContainer = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

export default function UserManagementPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null); // State untuk error fetching
  const [searchTerm, setSearchTerm] = useState('');
  const [allOutlets, setAllOutlets] = useState<OutletOption[]>([]); // State untuk semua outlet
  const [isLoadingOutlets, setIsLoadingOutlets] = useState(false); // State loading outlet

  // State Modal Add/Edit User
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
  const [selectedUserForEdit, setSelectedUserForEdit] = useState<User | null>(null);
  const [isSubmittingUser, setIsSubmittingUser] = useState(false); // Loading state submit user
  const [formDataUser, setFormDataUser] = useState({
    username: '',
    name: '', // Mengganti fullName
    email: '',
    password: '', // Untuk input form
    role: UserRole.STAFF, // Default role disesuaikan
    isActive: true,
    isCaptain: false, // Tambahkan state untuk kapten
    allowedOutletIds: [] as string[], // Tambahkan state untuk outlet yang dipilih
  });

  // State Modal Permission Detail (BARU)
  const [isPermissionModalOpen, setIsPermissionModalOpen] = useState(false);
  const [selectedUserForPermission, setSelectedUserForPermission] = useState<User | null>(null);
  const [isSubmittingPermission, setIsSubmittingPermission] = useState(false); // Loading state submit permission
  const [editedPermissions, setEditedPermissions] = useState<ModulePermission[]>([]);

  // State untuk password
  const [isChangePassword, setIsChangePassword] = useState(false);
  const [passwordVisibility, setPasswordVisibility] = useState({
    password: false,
    confirmPassword: false,
  });
  const [confirmPassword, setConfirmPassword] = useState('');

  // --- State Baru untuk Modal Hapus ---
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [isDeleting, setIsDeleting] = useState(false); // Loading state untuk proses hapus
  // --- End State Modal Hapus ---

  // --- Fetch Outlets (BARU) ---
  const fetchOutlets = useCallback(async () => {
    if (allOutlets.length > 0) return; // Jangan fetch ulang jika sudah ada
    setIsLoadingOutlets(true);
    try {
      const response = await fetch('/api/outlets'); // Asumsi endpoint ini ada
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Gagal memuat data outlet');
      }
      const data = await response.json();
      setAllOutlets(data.outlets || []); // Asumsi API mengembalikan { outlets: [...] }
    } catch (err) {
      console.error("Gagal load data outlet:", err);
      toast.error(`Gagal memuat daftar outlet: ${err instanceof Error ? err.message : 'Error tidak diketahui'}`);
    } finally {
      setIsLoadingOutlets(false);
    }
  }, [allOutlets.length]); // Dependency agar tidak fetch terus menerus

  // --- Load User Data from API ---
  const fetchUsers = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/users'); // Panggil API GET
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Gagal memuat data pengguna: ${response.statusText}`);
      }
      const data = await response.json();
      // Pastikan data user yang diterima memiliki array permissions
      const usersWithDetails = (data.users || []).map((user: Omit<User, 'permissions' | 'allowedOutlets'> & { permissions?: Partial<ModulePermission>[], allowedOutlets?: OutletOption[] }) => ({
        ...user,
        // Generate default permissions jika tidak ada atau kosong
        permissions: (Array.isArray(user.permissions) && user.permissions.length > 0)
                       ? user.permissions.map(p => ({ // Pastikan struktur sesuai ModulePermission
                            module: p.module || '',
                            canCreate: p.canCreate ?? false,
                            canRead: p.canRead ?? false,
                            canUpdate: p.canUpdate ?? false,
                            canDelete: p.canDelete ?? false,
                         }))
                       : generateDefaultPermissions(user.role as UserRole),
        // Pastikan allowedOutlets adalah array (kosong jika tidak ada)
        allowedOutlets: Array.isArray(user.allowedOutlets) ? user.allowedOutlets : [],
      }));
      setUsers(usersWithDetails);
    } catch (err) {
      console.error("Gagal load data user:", err);
      toast.error(`Gagal memuat data pengguna: ${err instanceof Error ? err.message : "Error tidak diketahui"}`);
      setUsers([]);
    } finally {
    setIsLoading(false);
    }
  }, []); // useCallback agar tidak re-create terus

  useEffect(() => {
    fetchUsers(); // Panggil fetchUsers saat komponen mount
  }, [fetchUsers]); // Tambahkan fetchUsers sebagai dependency

  // Definisikan urutan prioritas role
  const roleOrder = {
    [UserRole.ADMIN]: 1,
    [UserRole.MANAGER]: 2,
    [UserRole.STAFF]: 3,
    [UserRole.INVESTOR]: 4
  };

  // Filter dan urutkan Users berdasarkan role
  const filteredUsers = useMemo(() => {
    // Filter users berdasarkan search term jika ada
    let filtered = users;
    if (searchTerm) {
      const lowerSearch = searchTerm.toLowerCase();
      filtered = users.filter(u =>
        u.username.toLowerCase().includes(lowerSearch) ||
        u.name.toLowerCase().includes(lowerSearch) || // Mengganti fullName
        u.email.toLowerCase().includes(lowerSearch) ||
        (u.role as string).toLowerCase().includes(lowerSearch) || // Cast role ke string
        (u.allowedOutlets?.map(o => o.name.toLowerCase()).join(' ') || '').includes(lowerSearch) // Cari di nama outlet jika ada
      );
    }

    // Urutkan users berdasarkan role
    return [...filtered].sort((a, b) => {
      // Urutkan berdasarkan role terlebih dahulu
      const roleComparison = roleOrder[a.role] - roleOrder[b.role];
      if (roleComparison !== 0) return roleComparison;

      // Jika role sama, urutkan berdasarkan nama
      return a.name.localeCompare(b.name);
    });
  }, [users, searchTerm]);

  // --- Handlers Add/Edit User (Disesuaikan untuk API) ---
  const handleOpenAddEditModal = (user: User | null = null) => {
    setSelectedUserForEdit(user);
    setIsChangePassword(!user); // True jika add mode, false jika edit mode
    setConfirmPassword('');
    setPasswordVisibility({ password: false, confirmPassword: false }); // Reset visibility

    // Fetch outlets jika belum ada (penting untuk pilihan investor)
    if (allOutlets.length === 0) {
      fetchOutlets();
    }

    if (user) { // Edit mode
      setFormDataUser({
        username: user.username,
        name: user.name,
        email: user.email,
        password: '', // Kosongkan password saat edit
        role: user.role,
        isActive: user.isActive,
        isCaptain: user.isCaptain || false, // Tambahkan status kapten
        allowedOutletIds: user.allowedOutlets?.map(outlet => outlet.id) || [],
      });
    } else { // Add mode
      setFormDataUser({
        username: '',
        name: '',
        email: '',
        password: '',
        role: UserRole.STAFF, // Default role
        isActive: true,
        isCaptain: false, // Default bukan kapten
        allowedOutletIds: [], // Default kosong
      });
    }
    setIsAddEditModalOpen(true);
  };

  const handleCloseAddEditModal = () => {
    setIsAddEditModalOpen(false);
    setSelectedUserForEdit(null);
    setIsSubmittingUser(false); // Reset loading
  };

  const handleInputChangeUser = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLInputElement[]>) => {
    const { name, value, type } = e.target as HTMLInputElement | HTMLSelectElement;

    if (name === 'allowedOutletIds') {
      // Handle multi-select untuk outlet
      const selectedOptions = (e.target as unknown as HTMLSelectElement).options;
      const selectedIds: string[] = [];
      for (let i = 0; i < selectedOptions.length; i++) {
        if (selectedOptions[i].selected) {
          selectedIds.push(selectedOptions[i].value);
        }
      }
      setFormDataUser(prev => ({ ...prev, allowedOutletIds: selectedIds }));
    } else if (type === 'checkbox') {
      setFormDataUser(prev => ({ ...prev, [name]: (e.target as HTMLInputElement).checked }));
    } else {
       setFormDataUser(prev => ({ ...prev, [name]: value }));
    }

    // Reset password confirmation jika password utama diubah
    if (name === 'password') {
      setConfirmPassword('');
    }
  };

  const handleSaveUser = async () => {
    // Validasi dengan toast
    if (!formDataUser.username.trim()) return toast.error('Username wajib diisi.');
    if (!formDataUser.name.trim()) return toast.error('Nama Lengkap wajib diisi.');
    if (!formDataUser.email.trim() || !/\S+@\S+\.\S+/.test(formDataUser.email)) return toast.error('Email tidak valid.');
    if (!selectedUserForEdit && !formDataUser.password) return toast.error('Password wajib diisi untuk pengguna baru.');
    if (isChangePassword && formDataUser.password !== confirmPassword) return toast.error('Konfirmasi password tidak cocok.');
    if (formDataUser.role === UserRole.INVESTOR && formDataUser.allowedOutletIds.length === 0) {
      return toast.error('Investor harus memilih minimal satu outlet yang diizinkan.');
    }

    // Tambahkan log untuk debugging
    console.log('Form data yang akan dikirim:', {
      ...formDataUser,
      password: formDataUser.password ? '******' : undefined
    });

    setIsSubmittingUser(true);
    const isEditMode = !!selectedUserForEdit;
    const url = isEditMode ? `/api/users/${selectedUserForEdit.id}` : '/api/users';
    const method = isEditMode ? 'PUT' : 'POST';

    // Buat payload dasar
    const basePayload: Record<string, any> = {
      username: formDataUser.username,
      name: formDataUser.name,
      email: formDataUser.email,
      role: formDataUser.role,
      isActive: formDataUser.isActive
    };

    // Tambahkan properti opsional secara eksplisit
    if (formDataUser.role === UserRole.STAFF) {
      basePayload.isCaptain = formDataUser.isCaptain;
    }

    if (formDataUser.role === UserRole.INVESTOR) {
      basePayload.allowedOutletIds = formDataUser.allowedOutletIds;
    }

    // Definisikan tipe payload
    type UserPayload = {
      username: string;
      name: string;
      email: string;
      role: string;
      isActive: boolean;
      isCaptain?: boolean;
      allowedOutletIds?: string[];
      password?: string;
    };

    const payload: UserPayload = { ...basePayload };

    if ((!isEditMode || isChangePassword) && formDataUser.password) {
      payload.password = formDataUser.password;
    }

    try {
      const response = await fetch(url, {
        method: method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `Gagal ${isEditMode ? 'memperbarui' : 'menambah'} pengguna`);
      }

      // Ganti alert dengan toast
      toast.success(`Pengguna ${result.user.username} berhasil ${isEditMode ? 'diperbarui' : 'ditambahkan'}!`);

      // Update state lokal
      if (isEditMode) {
        setUsers(prev => prev.map(u => (u.id === result.user.id ? { ...u, ...result.user } : u)));
      } else {
        // Tambahkan pengguna baru dengan permissions default
        const newUserWithDefaults = {
          ...result.user,
          permissions: generateDefaultPermissions(result.user.role),
          allowedOutlets: result.user.allowedOutlets || [],
        };
        setUsers(prev => [...prev, newUserWithDefaults]);
      }

      handleCloseAddEditModal(); // Tutup modal

    } catch (error) {
      console.error('Error saving user:', error);
      // Ganti alert dengan toast
      toast.error(`Gagal menyimpan pengguna: ${error instanceof Error ? error.message : 'Error tidak diketahui'}`);
    } finally {
      setIsSubmittingUser(false);
    }
  };

  // --- Handler Delete User (Disesuaikan untuk API) ---
  const handleDeleteUser = (user: User) => {
    // Tidak jadi hapus di sini, panggil fungsi buka modal
    setUserToDelete(user);
    setIsDeleteModalOpen(true);
  };

  // --- Handler Toggle Active Status (Baru, via API) ---
  const handleToggleActiveStatus = async (user: User) => {
    const newStatus = !user.isActive;
    const action = newStatus ? 'mengaktifkan' : 'menonaktifkan';

    toast.promise(
      fetch(`/api/users/${user.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive: newStatus }), // Kirim hanya isActive
      })
      .then(async res => {
        if (!res.ok) {
          const errorData = await res.json().catch(() => ({}));
          throw new Error(errorData.error || `Gagal ${action} (Status: ${res.status})`);
        }
      })
      .then(() => {
        // Update state lokal
        setUsers(prev => prev.map(u => (u.id === user.id ? { ...u, isActive: newStatus } : u)));
      }),
      {
        loading: `Sedang ${action} ${user.username}...`,
        success: `Pengguna ${user.username} berhasil ${action}.`,
        error: (err) => `Gagal ${action} pengguna: ${err.message}`,
      }
    );
  };

  // --- Handlers Permission (BARU) ---
  const handleOpenPermissionModal = (user: User) => {
    setSelectedUserForPermission(user);
    // Salin permissions dari user atau generate default jika tidak ada/kosong
    const currentPermissions = (user.permissions && user.permissions.length > 0)
                               ? JSON.parse(JSON.stringify(user.permissions)) // Deep copy
                               : generateDefaultPermissions(user.role);
    setEditedPermissions(currentPermissions);
    setIsPermissionModalOpen(true);
  };

  const handleClosePermissionModal = () => {
    setIsPermissionModalOpen(false);
    setSelectedUserForPermission(null);
    setEditedPermissions([]);
    setIsSubmittingPermission(false); // Reset loading
  };

  const handlePermissionChange = (moduleIndex: number, permissionType: keyof Omit<ModulePermission, 'module'>, value: boolean) => {
    setEditedPermissions(prev =>
      prev.map((p, index) =>
        index === moduleIndex ? { ...p, [permissionType]: value } : p
      )
    );
  };

  const handleResetToDefault = () => {
    if (!selectedUserForPermission) return;
    setEditedPermissions(generateDefaultPermissions(selectedUserForPermission.role));
    toast.info('Hak akses dikembalikan ke default sesuai role.');
  };

  const handleSavePermissions = async () => {
    if (!selectedUserForPermission) return;

    setIsSubmittingPermission(true);
    try {
      const response = await fetch(`/api/users/${selectedUserForPermission.id}/permissions`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ permissions: editedPermissions }),
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || 'Gagal menyimpan hak akses');
      }

      // Ganti alert dengan toast
      toast.success(`Hak akses untuk ${selectedUserForPermission.username} berhasil diperbarui.`);

      // Update state user utama
      setUsers(prev => prev.map(u =>
        u.id === selectedUserForPermission.id ? { ...u, permissions: editedPermissions } : u
      ));
      handleClosePermissionModal();

    } catch (error) {
      console.error('Error saving permissions:', error);
      // Ganti alert dengan toast
      toast.error(`Gagal menyimpan hak akses: ${error instanceof Error ? error.message : 'Error tidak diketahui'}`);
    } finally {
      setIsSubmittingPermission(false);
    }
  };
  // --- Akhir Handlers Permission ---

  // Fungsi helper format tanggal
  const formatNullableDate = (dateString: string | null | undefined) => {
    if (!dateString) return '-';
    try {
      return format(new Date(dateString), 'dd MMM yyyy, HH:mm', { locale: id });
    } catch {
      return 'Invalid Date';
    }
  };

  // --- Fungsi Baru untuk Eksekusi Hapus (Dipanggil dari Modal) ---
  const executeDeleteUser = async () => {
    if (!userToDelete) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/users/${userToDelete.id}`, { method: 'DELETE' });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Gagal menghapus (Status: ${response.status})`);
      }
      toast.success(`Pengguna ${userToDelete.username} berhasil dihapus.`);
      setUsers(prev => prev.filter(u => u.id !== userToDelete.id));
      closeDeleteModal(); // Tutup modal
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error(`Gagal menghapus pengguna: ${error instanceof Error ? error.message : 'Error tidak diketahui'}`);
      // Jangan tutup modal jika error
    } finally {
      setIsDeleting(false);
    }
  };

  // --- Fungsi Baru untuk Menutup Modal Konfirmasi Hapus ---
  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setUserToDelete(null);
    setIsDeleting(false); // Reset loading state juga
  };

  if (isLoading && users.length === 0 && !error) { // Tampilkan loading hanya jika belum ada error
    return <div className="flex justify-center items-center h-screen"><span className="loading loading-spinner loading-lg text-primary"></span></div>;
  }

  return (
    <motion.div
      className="container mx-auto p-4 md:p-6"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {/* Header & Tools */}
      <motion.div variants={fadeInUp} className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Manajemen Pengguna</h1>
          <p className="text-gray-600">Kelola akun pengguna sistem</p>
        </div>
        <PermissionGuard module="users" action="create">
          <button
            type="button"
            onClick={() => handleOpenAddEditModal()}
            className="btn btn-primary mt-4 md:mt-0 gap-2 w-full sm:w-auto"
            aria-label="Tambah Pengguna Baru"
          >
            <FiPlus /> Tambah Pengguna Baru
          </button>
        </PermissionGuard>
      </motion.div>

      {/* Menampilkan error fetching jika ada */}
      {error && (
        <motion.div variants={fadeInUp} className="alert alert-error mb-6">
          <FiAlertTriangle className="w-6 h-6" />
          <span>{error}</span>
        </motion.div>
      )}

      {/* Search & Filter */}
      <motion.div variants={fadeInUp} className="relative mb-6">
         <div className="flex items-center w-full max-w-lg border rounded-lg overflow-hidden focus-within:ring-2 focus-within:ring-primary">
           <FiSearch className="mx-3 text-gray-500" />
               <input
                 type="text"
             placeholder="Cari pengguna (nama, username, email, role)..."
             className="input input-ghost w-full focus:outline-none"
                 value={searchTerm}
                 onChange={(e) => setSearchTerm(e.target.value)}
                />
         </div>
      </motion.div>

      {/* User List Table */}
      {(!isLoading || users.length > 0) && filteredUsers.length > 0 ? ( // Tampilkan tabel jika tidak loading atau sudah ada user
        <motion.div variants={fadeInUp}>
          <div className="overflow-x-auto bg-white rounded-lg shadow">
            <div className="overflow-x-auto">
              <table className="table w-full table-zebra">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="p-3 text-left">Nama</th>
                    <th className="p-3 text-left hidden sm:table-cell">Username</th>
                    <th className="p-3 text-left hidden md:table-cell">Email</th>
                    <th className="p-3 text-left">Role</th>
                    <th className="p-3 text-center hidden sm:table-cell">Status</th>
                    <th className="p-3 text-center hidden md:table-cell">Kapten</th>
                    <th className="p-3 text-left hidden lg:table-cell">Login Terakhir</th>
                    <th className="p-3 text-left hidden md:table-cell">Outlet</th>
                    <th className="p-3 text-center">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className={`${!user.isActive ? 'opacity-60 bg-gray-50' : ''} hover:bg-gray-50`}>
                      <td className="p-3">
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-xs text-gray-500 sm:hidden">{user.username}</div>
                          <div className="text-xs text-gray-500 md:hidden">{user.email}</div>
                          <div className="text-xs text-gray-500 sm:hidden">
                            {user.isActive ?
                              <span className="text-green-600 flex items-center gap-1"><FiCheck size={12} /> Aktif</span> :
                              <span className="text-red-600 flex items-center gap-1"><FiX size={12} /> Nonaktif</span>
                            }
                          </div>
                          {user.role === UserRole.STAFF && user.isCaptain && (
                            <div className="text-xs text-secondary md:hidden flex items-center gap-1 mt-1">
                              <FiUsers size={12} /> Kapten Terapis
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-3 hidden sm:table-cell">{user.username}</td>
                      <td className="p-3 hidden md:table-cell">{user.email}</td>
                      <td className="p-3">
                        <span className={`badge ${ROLE_BADGE_COLORS[user.role] || 'badge-ghost'} text-xs`}>
                          {user.role}
                        </span>
                      </td>
                      <td className="p-3 text-center hidden sm:table-cell">
                        <span className={`badge ${user.isActive ? 'badge-success' : 'badge-error'} badge-sm`}>
                          {user.isActive ? 'Aktif' : 'Nonaktif'}
                        </span>
                      </td>
                      <td className="p-3 text-center hidden md:table-cell">
                        {user.role === UserRole.STAFF && (
                          <span className={`badge ${user.isCaptain ? 'badge-secondary' : 'badge-ghost'} badge-sm`}>
                            {user.isCaptain ? 'Kapten' : '-'}
                          </span>
                        )}
                      </td>
                      <td className="p-3 text-sm text-gray-600 hidden lg:table-cell">{formatNullableDate(user.lastLoginAt)}</td>
                      <td className="p-3 hidden md:table-cell">
                        {user.role === UserRole.INVESTOR && (
                          <div className="text-xs opacity-60 mt-1 flex items-center gap-1">
                            <FiHome size={12}/>
                            <span>{user.allowedOutlets?.length || 0} Outlet</span>
                          </div>
                        )}
                      </td>
                      <td className="p-3">
                        <div className="flex justify-center gap-1">
                          <PermissionGuard module="users" action="update">
                            <button
                              type="button"
                              onClick={() => handleOpenPermissionModal(user)}
                              className="btn btn-xs btn-ghost text-purple-600"
                              title="Atur Permission"
                              disabled={user.role === UserRole.ADMIN}
                              aria-label="Atur Permission"
                            >
                              <FiShield size={14} />
                            </button>
                          </PermissionGuard>
                          {user.role === UserRole.STAFF && user.isCaptain && (
                            <PermissionGuard module="users" action="update">
                              <Link
                                href="/dashboard/reports/captain-performance"
                                className="btn btn-xs btn-ghost text-secondary"
                                title="Lihat Kinerja Kapten"
                                aria-label="Lihat Kinerja Kapten"
                              >
                                <FiUsers size={14} />
                              </Link>
                            </PermissionGuard>
                          )}
                          <PermissionGuard module="users" action="update">
                            <button
                              type="button"
                              onClick={() => handleOpenAddEditModal(user)}
                              className="btn btn-xs btn-ghost text-blue-600"
                              title="Edit"
                              aria-label="Edit Pengguna"
                            >
                              <FiEdit size={14} />
                            </button>
                          </PermissionGuard>
                          <PermissionGuard module="users" action="update">
                            <button
                              type="button"
                              onClick={() => handleToggleActiveStatus(user)}
                              className={`btn btn-xs btn-ghost ${user.isActive ? 'text-orange-600' : 'text-green-600'}`}
                              title={user.isActive ? 'Nonaktifkan' : 'Aktifkan'}
                              aria-label={user.isActive ? 'Nonaktifkan Pengguna' : 'Aktifkan Pengguna'}
                              disabled={user.role === UserRole.ADMIN && users.filter(u => u.role === UserRole.ADMIN && u.isActive).length <= 1}
                            >
                              {user.isActive ? <FiX size={14} /> : <FiCheck size={14} />}
                            </button>
                          </PermissionGuard>
                          <PermissionGuard module="users" action="delete">
                            <button
                              type="button"
                              onClick={() => handleDeleteUser(user)}
                              className="btn btn-xs btn-ghost text-red-600"
                              title="Hapus"
                              aria-label="Hapus Pengguna"
                              disabled={user.role === UserRole.ADMIN && users.filter(u => u.role === UserRole.ADMIN).length <= 1}
                            >
                              <FiTrash2 size={14} />
                            </button>
                          </PermissionGuard>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </motion.div>
      ) : (
        !isLoading && (
          <motion.div variants={fadeInUp} className="text-center py-8 bg-white rounded-lg shadow">
            <FiInbox className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900">
              {searchTerm ? 'Pengguna tidak ditemukan' : 'Belum ada pengguna'}
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'Coba cari dengan kata kunci lain.' : 'Mulai dengan menambahkan pengguna baru.'}
            </p>
             {!searchTerm && (
                <div className="mt-6">
                   <PermissionGuard module="users" action="create">
                     <button
                        type="button"
                        onClick={() => handleOpenAddEditModal()}
                        className="btn btn-primary gap-2 w-full sm:w-auto"
                        aria-label="Tambah Pengguna"
                     >
                        <FiPlus /> Tambah Pengguna
                     </button>
                   </PermissionGuard>
                </div>
             )}
          </motion.div>
        )
      )}

      {/* Add/Edit User Modal (Disesuaikan) */}
      {isAddEditModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div className="fixed inset-0 transition-opacity" aria-hidden="true" onClick={handleCloseAddEditModal}>
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            {/* Modal content */}
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg w-full max-w-[95%] mx-auto">
              <form onSubmit={(e) => { e.preventDefault(); handleSaveUser(); }}>
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium leading-6 text-gray-900">
                      {selectedUserForEdit ? 'Edit Pengguna' : 'Tambah Pengguna Baru'}
                    </h3>
                    <button
                      type="button"
                      onClick={handleCloseAddEditModal}
                      className="btn btn-sm btn-circle btn-ghost"
                      aria-label="Tutup"
                    >
                      <FiX size={18} />
                    </button>
                  </div>
                  <div className="space-y-4">
                    <div className="form-control">
                      <label className="label" htmlFor="username"><span className="label-text">Username <span className="text-red-500">*</span></span></label>
                      <input
                        id="username"
                        type="text"
                        name="username"
                        value={formDataUser.username}
                        onChange={handleInputChangeUser}
                        className="input input-bordered w-full"
                        required
                        readOnly={!!selectedUserForEdit}
                        disabled={!!selectedUserForEdit}
                        placeholder="Masukkan username"
                        aria-label="Username"
                      />
                      {selectedUserForEdit && <span className="text-xs text-gray-500 mt-1">Username tidak dapat diubah.</span>}
                    </div>
                    <div className="form-control">
                      <label className="label" htmlFor="name"><span className="label-text">Nama Lengkap <span className="text-red-500">*</span></span></label>
                      <input
                        id="name"
                        type="text"
                        name="name"
                        value={formDataUser.name}
                        onChange={handleInputChangeUser}
                        className="input input-bordered w-full"
                        required
                        placeholder="Masukkan nama lengkap"
                        aria-label="Nama Lengkap"
                      />
                    </div>
                    <div className="form-control">
                      <label className="label" htmlFor="email"><span className="label-text">Email <span className="text-red-500">*</span></span></label>
                      <input
                        id="email"
                        type="email"
                        name="email"
                        value={formDataUser.email}
                        onChange={handleInputChangeUser}
                        className="input input-bordered w-full"
                        required
                        placeholder="Masukkan email"
                        aria-label="Email"
                      />
                    </div>
                    <div className="form-control">
                      <label className="label" htmlFor="role"><span className="label-text">Role <span className="text-red-500">*</span></span></label>
                      <select
                        id="role"
                        name="role"
                        value={formDataUser.role}
                        onChange={handleInputChangeUser}
                        className="select select-bordered w-full"
                        required
                        aria-label="Role Pengguna"
                      >
                        {Object.values(UserRole).map(roleValue => (
                          <option key={roleValue} value={roleValue}>{roleValue}</option>
                        ))}
                      </select>
                    </div>

                    {/* Password Section */}
                    {selectedUserForEdit && (
                      <div className="form-control">
                        <label className="label cursor-pointer justify-start gap-2" htmlFor="change-password">
                          <input
                            id="change-password"
                            type="checkbox"
                            className="checkbox checkbox-sm"
                            checked={isChangePassword}
                            onChange={(e) => setIsChangePassword(e.target.checked)}
                            aria-label="Ubah Password"
                          />
                          <span className="label-text">Ubah Password</span>
                        </label>
                      </div>
                    )}

                    {(selectedUserForEdit === null || isChangePassword) && (
                      <>
                        <div className="form-control relative">
                          <label className="label" htmlFor="password"><span className="label-text">Password {selectedUserForEdit === null && <span className="text-red-500">*</span>}</span></label>
                          <input
                            id="password"
                            type={passwordVisibility.password ? 'text' : 'password'}
                            name="password"
                            value={formDataUser.password}
                            onChange={handleInputChangeUser}
                            className="input input-bordered pr-10"
                            required={selectedUserForEdit === null}
                            minLength={6}
                            placeholder="Masukkan password"
                            aria-label="Password"
                          />
                          <button
                            type="button"
                            className="absolute right-3 top-12 btn btn-ghost btn-sm"
                            onClick={() => setPasswordVisibility(p => ({...p, password: !p.password}))}
                            aria-label={passwordVisibility.password ? "Sembunyikan Password" : "Tampilkan Password"}
                          >
                            {passwordVisibility.password ? <FiEyeOff/> : <FiEye/>}
                          </button>
                        </div>
                        <div className="form-control relative">
                          <label className="label" htmlFor="confirmPassword"><span className="label-text">Konfirmasi Password {selectedUserForEdit === null && <span className="text-red-500">*</span>}</span></label>
                          <input
                            id="confirmPassword"
                            type={passwordVisibility.confirmPassword ? 'text' : 'password'}
                            name="confirmPassword"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            className={`input input-bordered pr-10 ${formDataUser.password && confirmPassword && formDataUser.password !== confirmPassword ? 'input-error' : ''}`}
                            required={selectedUserForEdit === null || (isChangePassword && formDataUser.password.length > 0)}
                            minLength={6}
                            placeholder="Konfirmasi password"
                            aria-label="Konfirmasi Password"
                          />
                          <button
                            type="button"
                            className="absolute right-3 top-12 btn btn-ghost btn-sm"
                            onClick={() => setPasswordVisibility(p => ({...p, confirmPassword: !p.confirmPassword}))}
                            aria-label={passwordVisibility.confirmPassword ? "Sembunyikan Konfirmasi Password" : "Tampilkan Konfirmasi Password"}
                          >
                            {passwordVisibility.confirmPassword ? <FiEyeOff/> : <FiEye/>}
                          </button>
                          {formDataUser.password && confirmPassword && formDataUser.password !== confirmPassword && (
                            <label className="label">
                              <span className="label-text-alt text-error">Password tidak cocok</span>
                            </label>
                          )}
                        </div>
                      </>
                    )}

                    {/* Outlet Section */}
                    {formDataUser.role === UserRole.INVESTOR && (
                      <div className="form-control mt-4 border border-base-300 p-4 rounded-md">
                        <label className="label pb-0">
                          <span className="label-text font-semibold">Outlet yang Diizinkan</span>
                        </label>
                        <span className="text-xs text-base-content/70 mb-2">Pilih outlet yang dapat diakses oleh investor ini.</span>
                        {isLoadingOutlets && <span className="loading loading-sm"></span>}
                        {!isLoadingOutlets && allOutlets.length === 0 && <span className="text-error text-sm">Gagal memuat outlet atau tidak ada outlet tersedia.</span>}
                        {!isLoadingOutlets && allOutlets.length > 0 && (
                          <div className="max-h-48 overflow-y-auto space-y-1 mt-1">
                            {allOutlets.map(outlet => (
                              <label key={outlet.id} className="label cursor-pointer justify-start gap-2 p-1 hover:bg-base-200 rounded">
                                <input
                                  type="checkbox"
                                  className="checkbox checkbox-primary checkbox-xs"
                                  checked={formDataUser.allowedOutletIds.includes(outlet.id)}
                                  onChange={(e) => {
                                    const { checked } = e.target;
                                    setFormDataUser(prev => ({
                                      ...prev,
                                      allowedOutletIds: checked
                                        ? [...prev.allowedOutletIds, outlet.id]
                                        : prev.allowedOutletIds.filter(id => id !== outlet.id)
                                    }));
                                  }}
                                />
                                <span className="label-text text-sm">{outlet.name}</span>
                              </label>
                            ))}
                          </div>
                        )}
                        {formDataUser.allowedOutletIds.length === 0 && (
                          <label className="label pt-1">
                            <span className="label-text-alt text-warning">Pilih minimal satu outlet untuk Investor.</span>
                          </label>
                        )}
                      </div>
                    )}

                    {/* Status Aktif */}
                    <div className="form-control">
                      <label className="label cursor-pointer">
                        <span className="label-text">User Aktif</span>
                        <input
                          type="checkbox"
                          name="isActive"
                          checked={formDataUser.isActive}
                          onChange={handleInputChangeUser}
                          className="toggle toggle-primary"
                        />
                      </label>
                    </div>

                    {/* Status Kapten (hanya untuk role STAFF) */}
                    {formDataUser.role === UserRole.STAFF && (
                      <div className="form-control">
                        <label className="label cursor-pointer">
                          <span className="label-text">Kapten Terapis</span>
                          <input
                            type="checkbox"
                            name="isCaptain"
                            checked={formDataUser.isCaptain}
                            onChange={handleInputChangeUser}
                            className="toggle toggle-secondary"
                          />
                        </label>
                        {formDataUser.isCaptain && (
                          <p className="text-xs text-gray-500 mt-1">
                            User ini akan menjadi kapten yang dapat memimpin tim terapis.
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                {/* Modal Actions */}
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="submit"
                    className="btn btn-primary sm:ml-3"
                    disabled={isSubmittingUser}
                  >
                    {isSubmittingUser && <span className="loading loading-spinner loading-xs"></span>}
                    {selectedUserForEdit ? 'Perbarui' : 'Simpan'}
                  </button>
                  <button
                    type="button"
                    className="btn btn-ghost mt-3 sm:mt-0"
                    onClick={handleCloseAddEditModal}
                    disabled={isSubmittingUser}
                  >
                    Batal
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Modal Permission Detail (BARU) */}
      {isPermissionModalOpen && selectedUserForPermission && (
        <div className="fixed inset-0 z-[60] overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div className="fixed inset-0 transition-opacity" aria-hidden="true" onClick={handleClosePermissionModal}>
              <div className="absolute inset-0 bg-gray-600 opacity-75"></div>
            </div>
            {/* Modal content */}
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl w-full max-w-[95%] mx-auto">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-medium leading-6 text-gray-900">
                      Atur Permission: {selectedUserForPermission.name}
                    </h3>
                    <p className="text-sm text-gray-500">@{selectedUserForPermission.username} - <span className={`badge ${ROLE_BADGE_COLORS[selectedUserForPermission.role] || 'badge-ghost'} badge-sm`}>{selectedUserForPermission.role}</span></p>
                  </div>
                  <button
                    type="button"
                    onClick={handleClosePermissionModal}
                    className="btn btn-sm btn-circle btn-ghost text-gray-500 hover:bg-gray-200"
                    aria-label="Tutup"
                  >
                    <FiX size={20} />
                  </button>
                </div>

                <div className="mb-4 flex justify-end">
                  <button
                    type="button"
                    className="btn btn-xs btn-outline"
                    onClick={handleResetToDefault}
                    disabled={isSubmittingPermission}
                    aria-label={`Reset ke Default Role (${selectedUserForPermission.role})`}
                  >
                    Reset ke Default Role ({selectedUserForPermission.role})
                  </button>
                </div>

                <div className="max-h-[60vh] overflow-x-auto overflow-y-auto border rounded-md">
                  <table className="table table-zebra w-full">
                    <thead className="sticky top-0 bg-gray-100 z-10">
                      <tr>
                        <th className="p-3 text-left">Modul</th>
                        <th className="p-3 text-center">Buat</th>
                        <th className="p-3 text-center">Lihat</th>
                        <th className="p-3 text-center">Ubah</th>
                        <th className="p-3 text-center">Hapus</th>
                      </tr>
                    </thead>
                    <tbody>
                      {editedPermissions.map((perm, index) => (
                        <tr key={perm.module}>
                          <td className="p-3 font-medium capitalize">{perm.module.replace(/_/g, ' ')}</td>
                          <td className="p-3 text-center">
                            <label className="cursor-pointer" htmlFor={`create-${perm.module}`}>
                              <input
                                id={`create-${perm.module}`}
                                type="checkbox"
                                className="checkbox checkbox-sm checkbox-primary"
                                checked={perm.canCreate}
                                onChange={(e) => handlePermissionChange(index, 'canCreate', e.target.checked)}
                                disabled={isSubmittingPermission}
                                aria-label={`Izin Buat untuk ${perm.module}`}
                              />
                            </label>
                          </td>
                          <td className="p-3 text-center">
                            <label className="cursor-pointer" htmlFor={`read-${perm.module}`}>
                              <input
                                id={`read-${perm.module}`}
                                type="checkbox"
                                className="checkbox checkbox-sm checkbox-primary"
                                checked={perm.canRead}
                                onChange={(e) => handlePermissionChange(index, 'canRead', e.target.checked)}
                                disabled={isSubmittingPermission}
                                aria-label={`Izin Lihat untuk ${perm.module}`}
                              />
                            </label>
                          </td>
                          <td className="p-3 text-center">
                            <label className="cursor-pointer" htmlFor={`update-${perm.module}`}>
                              <input
                                id={`update-${perm.module}`}
                                type="checkbox"
                                className="checkbox checkbox-sm checkbox-primary"
                                checked={perm.canUpdate}
                                onChange={(e) => handlePermissionChange(index, 'canUpdate', e.target.checked)}
                                disabled={isSubmittingPermission}
                                aria-label={`Izin Ubah untuk ${perm.module}`}
                              />
                            </label>
                          </td>
                          <td className="p-3 text-center">
                            <label className="cursor-pointer" htmlFor={`delete-${perm.module}`}>
                              <input
                                id={`delete-${perm.module}`}
                                type="checkbox"
                                className="checkbox checkbox-sm checkbox-primary"
                                checked={perm.canDelete}
                                onChange={(e) => handlePermissionChange(index, 'canDelete', e.target.checked)}
                                disabled={isSubmittingPermission}
                                aria-label={`Izin Hapus untuk ${perm.module}`}
                              />
                            </label>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
                <button
                  type="button"
                  className="btn btn-ghost w-full sm:w-auto"
                  onClick={handleClosePermissionModal}
                  disabled={isSubmittingPermission}
                  aria-label="Batal"
                >
                  Batal
                </button>
                <button
                  type="button"
                  className="btn btn-primary w-full sm:w-auto"
                  onClick={handleSavePermissions}
                  disabled={isSubmittingPermission}
                  aria-label="Simpan Permissions"
                >
                  {isSubmittingPermission && <span className="loading loading-spinner loading-xs"></span>}
                  Simpan Permissions
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* === MODAL KONFIRMASI HAPUS BARU === */}
      <dialog id="delete_user_confirmation_modal" className={`modal ${isDeleteModalOpen ? 'modal-open' : ''}`}>
        <div className="modal-box max-w-md w-full">
          <h3 className="font-bold text-lg text-error flex items-center">
            <FiAlertTriangle className="mr-2"/> Konfirmasi Hapus Pengguna
          </h3>
          <p className="py-4">
            Anda yakin ingin menghapus pengguna
            <strong className="ml-1">{userToDelete?.username} ({userToDelete?.name})</strong>?
            <br />
            Tindakan ini tidak dapat dibatalkan.
          </p>
          <div className="modal-action flex flex-col-reverse sm:flex-row gap-2">
            <button
              type="button"
              className="btn btn-sm w-full sm:w-auto"
              onClick={closeDeleteModal}
              disabled={isDeleting}
              aria-label="Batal"
            >
              Batal
            </button>
            <button
              type="button"
              className={`btn btn-error btn-sm w-full sm:w-auto ${isDeleting ? 'btn-disabled' : ''}`}
              onClick={executeDeleteUser}
              disabled={isDeleting}
              aria-label="Hapus Pengguna"
            >
              {isDeleting ?
                <><span className="loading loading-spinner loading-xs"></span> Menghapus...</> :
                "Ya, Hapus Pengguna"
              }
            </button>
          </div>
        </div>
        {/* Klik backdrop untuk menutup */}
        <form method="dialog" className="modal-backdrop" onClick={closeDeleteModal}>
           <button type="button" disabled={isDeleting} aria-label="Tutup modal">close</button>
        </form>
      </dialog>
      {/* === SELESAI MODAL KONFIRMASI HAPUS === */}

    </motion.div>
  );
}