import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');

    // Ambil parameter tanggal dari query
    const rawStartDate = searchParams.get('startDate');
    const rawEndDate = searchParams.get('endDate');

    if (!rawStartDate || !rawEndDate) {
      throw new Error('Tanggal mulai dan tanggal akhir harus disediakan');
    }

    // Buat tanggal dengan pendekatan yang sama seperti di reports/route.ts
    // Format: YYYY-MM-DD -> parse sebagai tanggal lokal
    const [startYear, startMonth, startDay] = rawStartDate.split('-').map(Number);
    const [endYear, endMonth, endDay] = rawEndDate.split('-').map(Number);

    // Buat tanggal dengan komponen tanggal lokal
    // Bulan dalam JavaScript dimulai dari 0 (Januari = 0)
    const startDate = new Date(startYear, startMonth - 1, startDay, 0, 0, 0, 0);
    const endDate = new Date(endYear, endMonth - 1, endDay, 23, 59, 59, 999);

    // Validasi tanggal
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.log(`Invalid date: startDate=${startDate}, endDate=${endDate}`);
      throw new Error('Format tanggal tidak valid');
    }
    
    // Log tanggal yang akan digunakan
    console.log(`[API GET /api/reports/cashier-performance] Using dates: startDate=${startDate.toISOString()}, endDate=${endDate.toISOString()}`);

    // Filter dasar untuk transaksi berdasarkan tanggal dan outlet terpilih
    const transactionWhere: Prisma.TransactionWhereInput = {
      transactionDate: { gte: startDate, lte: endDate },
      ...(outletId && { outletId: outletId }),
    };

    // Ambil data transaksi dengan informasi kasir (createdBy)
    const transactions = await prisma.transaction.findMany({
      where: transactionWhere,
      select: {
        id: true,
        totalAmount: true,
        outletId: true,
        createdById: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
        outlet: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Kelompokkan transaksi berdasarkan kasir
    const cashierPerformanceMap = new Map();

    transactions.forEach(transaction => {
      const cashierId = transaction.createdById;
      const cashierName = transaction.createdBy?.name || 'Unknown';
      const cashierUsername = transaction.createdBy?.username || 'Unknown';
      const outletId = transaction.outletId;
      const outletName = transaction.outlet?.name || 'Unknown';
      const amount = transaction.totalAmount || 0;

      // Buat kunci unik untuk kombinasi kasir dan outlet
      const key = `${cashierId}-${outletId}`;

      if (!cashierPerformanceMap.has(key)) {
        cashierPerformanceMap.set(key, {
          cashierId,
          cashierName,
          cashierUsername,
          outletId,
          outletName,
          totalTransactions: 0,
          totalAmount: 0,
        });
      }

      const data = cashierPerformanceMap.get(key);
      data.totalTransactions += 1;
      data.totalAmount += amount;
    });

    // Konversi Map ke array untuk respons
    const cashierPerformance = Array.from(cashierPerformanceMap.values());

    // Urutkan berdasarkan total amount (dari tertinggi ke terendah)
    cashierPerformance.sort((a, b) => b.totalAmount - a.totalAmount);

    // Hitung total pendapatan keseluruhan
    const totalRevenue = cashierPerformance.reduce((sum, item) => sum + item.totalAmount, 0);

    return NextResponse.json({
      message: 'Data kinerja kasir berhasil diambil',
      cashierPerformance,
      totalRevenue,
      filters: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        outletId: outletId,
      },
    });
  } catch (error: unknown) {
    console.error('[API GET /api/reports/cashier-performance] Error:', error);

    let errorMessage = 'Terjadi kesalahan saat mengambil data kinerja kasir';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat mengambil data kinerja kasir',
      cashierPerformance: [],
      totalRevenue: 0,
      filters: {
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        outletId: null,
      },
    }, { status: statusCode });
  }
}
