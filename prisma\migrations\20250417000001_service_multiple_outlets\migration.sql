-- <PERSON><PERSON> relasi one-to-many antara Outlet dan Service
ALTER TABLE "Service" DROP CONSTRAINT "Service_outletId_fkey";

-- <PERSON><PERSON> kolom outletId dari Service
ALTER TABLE "Service" DROP COLUMN "outletId";

-- Buat tabel junction untuk relasi many-to-many antara Service dan Outlet
CREATE TABLE "ServiceOutlet" (
    "id" TEXT NOT NULL,
    "serviceId" TEXT NOT NULL,
    "outletId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ServiceOutlet_pkey" PRIMARY KEY ("id")
);

-- Buat unique constraint untuk memastikan tidak ada duplikasi
CREATE UNIQUE INDEX "ServiceOutlet_serviceId_outletId_key" ON "ServiceOutlet"("serviceId", "outletId");

-- Buat foreign key constraints
ALTER TABLE "ServiceOutlet" ADD CONSTRAINT "ServiceOutlet_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "Service"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ServiceOutlet" ADD CONSTRAINT "ServiceOutlet_outletId_fkey" FOREIGN KEY ("outletId") REFERENCES "Outlet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Buat index untuk performa query
CREATE INDEX "ServiceOutlet_serviceId_idx" ON "ServiceOutlet"("serviceId");
CREATE INDEX "ServiceOutlet_outletId_idx" ON "ServiceOutlet"("outletId");
