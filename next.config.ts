import type { NextConfig } from "next";

// Interface tambahan untuk tipe yang belum tersedia di @types/next
interface CustomNextConfig extends NextConfig {
  outputFileTracingExcludes?: {
    [path: string]: string[];
  };
}

const nextConfig: CustomNextConfig = {
  /* config options here */
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  output: 'standalone',
  // Hapus pengecualian untuk file Prisma agar bisa berjalan di Vercel
  outputFileTracingExcludes: {
    '**': [
      // './node_modules/@prisma/client/**/*', // Jangan exclude file Prisma
      // './**/generated/prisma/**/*', // Jangan exclude file Prisma
      './node_modules/.pnpm/**/*',
      '**/.yarn/**/*',
      './node_modules/@swc/core-win32-x64-msvc/**/*',
      '**/node_modules/next/dist/compiled/@napi-rs/triples/**/*',
      '**/node_modules/sharp/**/*',
      "./Application Data/**",
      "./AppData/**",
      "C:/Users/<USER>/Application Data/**",
      "C:/Users/<USER>/AppData/**",
    ],
  },
};

export default nextConfig;
