import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET pengaturan CTA
export async function GET() {
  try {
    // Ambil pengaturan dengan kategori 'cta'
    const settings = await prisma.setting.findMany({
      where: {
        category: 'cta'
      },
      orderBy: {
        label: 'asc'
      }
    });

    return NextResponse.json({
      message: 'Data pengaturan CTA berhasil diambil',
      settings
    });
  } catch (error) {
    console.error('Error fetching CTA settings:', error);
    return NextResponse.json(
      { error: 'Ter<PERSON>di kesalahan saat mengambil data pengaturan CTA' },
      { status: 500 }
    );
  }
}

// POST untuk membuat atau memperbarui pengaturan CTA
export async function POST(request: Request) {
  try {
    const body = await request.json();
    // body bisa array atau objek tunggal
    const settingsData = Array.isArray(body) ? body : [body];
    
    if (settingsData.length === 0) {
      return NextResponse.json(
        { error: 'Tidak ada data pengaturan yang diterima' },
        { status: 400 }
      );
    }
    
    // Validasi semua data
    for (const setting of settingsData) {
      const { key, value, label } = setting;
      if (!key || value === undefined || !label) {
        return NextResponse.json(
          { error: 'Key, value, dan label diperlukan untuk semua pengaturan' },
          { status: 400 }
        );
      }
    }
    
    // Ubah semua pengaturan dalam transaksi
    const results = await prisma.$transaction(
      settingsData.map(setting => {
        const { key, value, label, type = 'TEXT', options } = setting;
        
        // Pastikan kategori adalah 'cta'
        return prisma.setting.upsert({
          where: { key },
          create: {
            key,
            value,
            category: 'cta',
            label,
            type,
            options: options || null
          },
          update: {
            value,
            label,
            type,
            options: options || null
          }
        });
      })
    );
    
    return NextResponse.json({
      message: 'Pengaturan CTA berhasil diperbarui',
      settings: results
    });
  } catch (error) {
    console.error('Error updating CTA settings:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui pengaturan CTA' },
      { status: 500 }
    );
  }
} 