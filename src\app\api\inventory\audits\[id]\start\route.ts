import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserIdFromToken } from '@/lib/auth-utils';

// PATCH - Mulai audit inventori
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = getUserIdFromToken(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Cek apakah audit ada
    const existingAudit = await prisma.inventoryAudit.findUnique({
      where: { id: params.id }
    });

    if (!existingAudit) {
      return NextResponse.json(
        { error: 'Audit inventori tidak ditemukan' },
        { status: 404 }
      );
    }

    // Validasi status audit
    if (existingAudit.status !== 'PENDING') {
      return NextResponse.json(
        { error: 'Audit hanya dapat dimulai jika statusnya terjadwal' },
        { status: 400 }
      );
    }

    // Update status audit menjadi IN_PROGRESS dan set startedAt
    const audit = await prisma.inventoryAudit.update({
      where: { id: params.id },
      data: {
        status: 'IN_PROGRESS',
        startedAt: new Date()
      },
      include: {
        outlet: true,
        createdBy: {
          select: {
            id: true,
            name: true
          }
        },

        _count: {
          select: {
            auditItems: true
          }
        }
      }
    });

    return NextResponse.json({
      message: 'Audit inventori berhasil dimulai',
      audit
    });
  } catch (error) {
    console.error('Error starting inventory audit:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memulai audit inventori' },
      { status: 500 }
    );
  }
}

// POST - Mulai audit inventori (generate audit items)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const userId = getUserIdFromToken(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    // Cek apakah audit ada
    const existingAudit = await prisma.inventoryAudit.findUnique({
      where: { id },
      include: {
        outlet: true,
        _count: {
          select: {
            auditItems: true
          }
        }
      }
    });

    if (!existingAudit) {
      return NextResponse.json(
        { error: 'Audit inventori tidak ditemukan' },
        { status: 404 }
      );
    }

    // Validasi status audit
    if (existingAudit.status !== 'PENDING') {
      return NextResponse.json(
        { error: 'Audit hanya dapat dimulai jika statusnya terjadwal' },
        { status: 400 }
      );
    }

    // Jika sudah ada audit items, return early
    if (existingAudit._count.auditItems > 0) {
      return NextResponse.json({
        message: 'Audit sudah dimulai sebelumnya',
        audit: existingAudit
      });
    }

    // Ambil semua item inventori untuk outlet ini
    const inventoryItems = await prisma.inventoryItem.findMany({
      where: {
        outletId: existingAudit.outletId
      },
      include: {
        category: true
      }
    });

    if (inventoryItems.length === 0) {
      return NextResponse.json(
        { error: 'Tidak ada item inventori yang perlu diaudit di outlet ini' },
        { status: 400 }
      );
    }

    // Buat audit items
    const auditItems = inventoryItems.map(item => ({
      auditId: id,
      itemId: item.id,
      expectedGood: item.goodCondition,
      expectedDamaged: item.damagedCondition,
      expectedLost: item.lostCondition,
      actualGood: 0,
      actualDamaged: 0,
      actualLost: 0,
      variance: 0,
      isChecked: false
    }));

    // Insert audit items dalam batch
    await prisma.inventoryAuditItem.createMany({
      data: auditItems
    });

    // Update status audit menjadi IN_PROGRESS
    const updatedAudit = await prisma.inventoryAudit.update({
      where: { id },
      data: {
        status: 'IN_PROGRESS',
        startedAt: new Date()
      },
      include: {
        outlet: true,
        createdBy: {
          select: {
            id: true,
            name: true
          }
        },
        auditItems: {
          include: {
            item: {
              include: {
                category: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      message: `Audit berhasil dimulai dengan ${inventoryItems.length} item`,
      audit: updatedAudit
    });
  } catch (error) {
    console.error('Error starting inventory audit:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memulai audit inventori' },
      { status: 500 }
    );
  }
}