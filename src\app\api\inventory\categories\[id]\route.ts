import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET - Ambil kategori inventori berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const category = await prisma.inventoryCategory.findUnique({
      where: { id: params.id },
      include: {
        items: {
          include: {
            outlet: true
          }
        },
        _count: {
          select: {
            items: true
          }
        }
      }
    });

    if (!category) {
      return NextResponse.json(
        { error: 'Kategori inventori tidak ditemukan' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Kategori inventori berhasil diambil',
      category
    });
  } catch (error) {
    console.error('Error fetching inventory category:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil kategori inventori' },
      { status: 500 }
    );
  }
}

// PUT - Update kategori inventori
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { name, description, isActive } = body;

    // Validasi input
    if (!name || name.trim() === '') {
      return NextResponse.json(
        { error: 'Nama kategori harus diisi' },
        { status: 400 }
      );
    }

    // Cek apakah kategori ada
    const existingCategory = await prisma.inventoryCategory.findUnique({
      where: { id: params.id }
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: 'Kategori inventori tidak ditemukan' },
        { status: 404 }
      );
    }

    // Cek apakah nama kategori sudah digunakan oleh kategori lain
    if (name.trim() !== existingCategory.name) {
      const duplicateCategory = await prisma.inventoryCategory.findUnique({
        where: { name: name.trim() }
      });

      if (duplicateCategory) {
        return NextResponse.json(
          { error: 'Kategori dengan nama tersebut sudah ada' },
          { status: 400 }
        );
      }
    }

    const category = await prisma.inventoryCategory.update({
      where: { id: params.id },
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        isActive: isActive !== undefined ? isActive : existingCategory.isActive
      }
    });

    return NextResponse.json({
      message: 'Kategori inventori berhasil diperbarui',
      category
    });
  } catch (error) {
    console.error('Error updating inventory category:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui kategori inventori' },
      { status: 500 }
    );
  }
}

// DELETE - Hapus kategori inventori
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Cek apakah kategori ada
    const existingCategory = await prisma.inventoryCategory.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            items: true
          }
        }
      }
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: 'Kategori inventori tidak ditemukan' },
        { status: 404 }
      );
    }

    // Cek apakah kategori masih memiliki item
    if (existingCategory._count.items > 0) {
      return NextResponse.json(
        { error: 'Kategori tidak dapat dihapus karena masih memiliki item inventori' },
        { status: 400 }
      );
    }

    await prisma.inventoryCategory.delete({
      where: { id: params.id }
    });

    return NextResponse.json({
      message: 'Kategori inventori berhasil dihapus'
    });
  } catch (error) {
    console.error('Error deleting inventory category:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus kategori inventori' },
      { status: 500 }
    );
  }
}