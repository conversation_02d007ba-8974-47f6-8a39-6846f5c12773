import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Customer } from '@prisma/client';

// PUT untuk mengupdate tags customer (menambah atau menghapus)
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    
    // Tangani kemungkinan body kosong atau error parsing JSON
    let body;
    try {
      body = await request.json();
    } catch (e) {
      console.error("Error parsing JSON body:", e);
      return NextResponse.json(
        { error: 'Format JSON tidak valid' },
        { status: 400 }
      );
    }
    
    const { action, tag } = body || {};

    // Log untuk debugging
    console.log('Request body:', body);
    console.log('ID:', id);
    console.log('Action:', action);
    console.log('Tag:', tag);

    if (!id) {
      return NextResponse.json(
        { error: 'ID customer diperlukan' },
        { status: 400 }
      );
    }

    if (!action || !['add', 'remove'].includes(action)) {
      return NextResponse.json(
        { error: 'Action harus berupa "add" atau "remove"' },
        { status: 400 }
      );
    }

    if (!tag || typeof tag !== 'string') {
      return NextResponse.json(
        { error: 'Tag diperlukan dan harus berupa string' },
        { status: 400 }
      );
    }

    // Cek apakah customer ada
    const existingCustomer = await prisma.customer.findUnique({
      where: {
        id
      }
    });

    if (!existingCustomer) {
      return NextResponse.json(
        { error: 'Customer tidak ditemukan' },
        { status: 404 }
      );
    }

    // Ambil tags yang ada (dengan type assertion) dan pastikan selalu array
    let currentTags: string[] = [];
    try {
      currentTags = Array.isArray((existingCustomer as any).tags) 
        ? (existingCustomer as any).tags 
        : [];
    } catch (error) {
      console.error('Error getting customer tags:', error);
      // Default ke array kosong jika error
    }

    let updatedTags: string[] = [...currentTags];

    // Lakukan aksi sesuai action
    if (action === 'add' && !currentTags.includes(tag)) {
      updatedTags.push(tag);
    } else if (action === 'remove') {
      updatedTags = currentTags.filter((t) => t !== tag);
    }

    console.log('Current tags:', currentTags);
    console.log('Updated tags:', updatedTags);

    // Update tags customer dengan try/catch terpisah
    try {
      const updatedCustomer = await prisma.customer.update({
        where: {
          id
        },
        data: {
          tags: updatedTags
        }
      });

      return NextResponse.json({
        message: `Tag berhasil ${action === 'add' ? 'ditambahkan' : 'dihapus'}`,
        customer: updatedCustomer
      });
    } catch (updateError) {
      console.error('Error in prisma update:', updateError);
      return NextResponse.json(
        { error: 'Gagal memperbarui tag customer di database' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error updating customer tags:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengupdate tags customer' },
      { status: 500 }
    );
  }
} 