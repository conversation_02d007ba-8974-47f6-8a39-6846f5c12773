import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// API route untuk memigrasikan layanan dari struktur lama ke struktur baru
// Akses dengan: GET /api/migrate-services
export async function GET() {
  try {
    console.log('Memulai migrasi layanan...');

    // 1. Ambil semua layanan dari database
    const services = await prisma.service.findMany({
      include: {
        outlets: true
      }
    });

    console.log(`Ditemukan ${services.length} layanan di database.`);

    // 2. Ambil semua outlet dari database
    const outlets = await prisma.outlet.findMany({
      where: {
        isOpen: true
      }
    });

    if (outlets.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'Tidak ada outlet yang ditemukan. Migrasi dibatalkan.'
      });
    }

    console.log(`Ditemukan ${outlets.length} outlet aktif.`);

    // 3. Untuk setiap layanan yang belum memiliki relasi outlet, tambahkan ke outlet pertama
    let migratedCount = 0;
    let migratedServices = [];

    for (const service of services) {
      // Jika layanan belum memiliki relasi outlet
      if (service.outlets.length === 0) {
        // Gunakan outlet pertama (biasanya outlet utama)
        const mainOutlet = outlets.find(o => o.isMain) || outlets[0];

        // Buat relasi baru
        await prisma.serviceOutlet.create({
          data: {
            serviceId: service.id,
            outletId: mainOutlet.id
          }
        });

        console.log(`Menambahkan layanan "${service.name}" ke outlet "${mainOutlet.name}".`);
        migratedCount++;
        migratedServices.push({
          id: service.id,
          name: service.name,
          outletName: mainOutlet.name
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: `Berhasil memigrasikan ${migratedCount} layanan ke struktur baru.`,
      totalServices: services.length,
      migratedServices: migratedServices
    });
  } catch (error) {
    console.error('Terjadi kesalahan saat migrasi:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Terjadi kesalahan saat migrasi',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
