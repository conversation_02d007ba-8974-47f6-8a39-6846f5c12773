'use client';

import React, { useState, useEffect, Fragment } from 'react';
import { motion } from 'framer-motion';
import { FiSearch, FiCalendar, FiFileText, FiDollarSign, FiUser, FiClock, FiAlertTriangle } from 'react-icons/fi';
import { format, subDays } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { useThemeContext } from '@/contexts/ThemeContext';
import { toast } from 'sonner';

// Animasi
const fadeInUp = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.5 } } };
const staggerContainer = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1 } } };

// Interface untuk service item
interface ServiceItem {
  name: string;
  price: number;
  commission: number;
  quantity: number;
}

// Interface untuk data komisi terapis
interface TherapistCommission {
  transactionId: string | number; // Bisa string atau number dari database
  transactionDate: string;
  serviceName: string;
  customerName: string;
  amount: number;
  commission: number;
  outletName: string;
  services?: ServiceItem[]; // Array services opsional
  discountType?: 'percentage' | 'fixed' | 'none'; // Tipe diskon
  discountValue?: number; // Nilai diskon (persentase atau nominal)
  discountAmount?: number; // Jumlah diskon yang diterapkan
  additionalCharge?: number; // Biaya tambahan
  overtimeMinutes?: number; // Menit lembur
  overtimeAmount?: number; // Jumlah bayaran lembur
}

// Format currency
const formatCurrency = (amount: number) => {
  // Pastikan amount adalah angka dan tidak infinity
  if (isNaN(amount) || !isFinite(amount)) amount = 0;
  
  // Batasi nilai maksimum untuk mencegah tampilan yang tidak wajar
  // Maksimum 999 miliar (999,999,999,999)
  const maxValue = 999999999999;
  if (amount > maxValue) {
    return 'Rp999.999.999.999+';
  }
  
  // Pastikan nilai tidak negatif
  if (amount < 0) amount = 0;

  // Format manual tanpa menggunakan Intl.NumberFormat untuk menghindari masalah pemisahan
  // Format: Rp X.XXX.XXX
  const amountStr = Math.round(amount).toString();
  let result = '';

  // Tambahkan titik sebagai pemisah ribuan
  for (let i = 0; i < amountStr.length; i++) {
    if (i > 0 && (amountStr.length - i) % 3 === 0) {
      result += '.';
    }
    result += amountStr[i];
  }

  // Tambahkan prefix Rp
  return `Rp${result}`;
};

// Komponen Receipt
const ReceiptModal = ({
  isOpen,
  onClose,
  transaction
}: {
  isOpen: boolean;
  onClose: () => void;
  transaction: TherapistCommission | null;
}) => {
  const { theme } = useThemeContext();
  const [mounted, setMounted] = useState(false);

  // Menunda penerapan tema sampai komponen di-mount di client
  useEffect(() => {
    setMounted(true);
  }, []);

  // Gunakan tema terang sebagai default
  // Kita tidak menggunakan tema dari context untuk memastikan halaman ini selalu menggunakan tema terang
  const isDark = false;

  if (!isOpen || !transaction) return null;

  const transactionDate = new Date(transaction.transactionDate);
  const formattedDate = format(transactionDate, 'dd MMMM yyyy', { locale: idLocale });
  const formattedTime = format(transactionDate, 'HH:mm', { locale: idLocale });

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-2 sm:p-4">
      <div className="fixed inset-0 bg-black opacity-50" onClick={onClose}></div>
      <div className="relative w-full max-w-xs sm:max-w-md p-4 sm:p-6 rounded-lg shadow-lg bg-white text-gray-800 break-normal">
        <button
          type="button"
          onClick={onClose}
          className="absolute top-2 sm:top-3 right-2 sm:right-3 p-1 rounded-full hover:bg-gray-100"
          aria-label="Tutup"
        >
          <span className="sr-only">Tutup</span>
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>

        <div className="text-center mb-3 sm:mb-4">
          <h3 className="text-base sm:text-lg font-bold">Detail Komisi</h3>
          <p className="text-xs sm:text-sm text-gray-600">{transaction.outletName}</p>
        </div>

        <div className="p-3 sm:p-4 rounded-lg mb-3 sm:mb-4 bg-gray-100 break-normal">
          <div className="flex justify-between mb-2">
            <span className="text-xs sm:text-sm text-gray-600">Tanggal:</span>
            <span className="text-xs sm:text-sm font-medium">{formattedDate}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span className="text-xs sm:text-sm text-gray-600">Waktu:</span>
            <span className="text-xs sm:text-sm font-medium">{formattedTime}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span className="text-xs sm:text-sm text-gray-600">ID Transaksi:</span>
            <span className="text-xs sm:text-sm font-medium">{String(transaction.transactionId).substring(0, 8)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-xs sm:text-sm text-gray-600">Pelanggan:</span>
            <span className="text-xs sm:text-sm font-medium">{transaction.customerName}</span>
          </div>
        </div>

        <div className="p-3 sm:p-4 rounded-lg mb-3 sm:mb-4 bg-gray-100">
          {transaction.services && transaction.services.length > 0 ? (
            // Tampilkan detail layanan jika services tersedia
            <>
              <div className="mb-3">
                <span className="text-xs sm:text-sm text-gray-600 font-medium">Detail Layanan:</span>
              </div>
              {transaction.services.map((service, index) => (
                <div key={index} className="mb-2 pb-2 border-b border-gray-200 last:border-0 last:mb-0 last:pb-0">
                  <div className="flex justify-between mb-1">
                    <span className="text-xs sm:text-sm text-gray-600">
                      {service.name} {service.quantity > 1 ? `(${service.quantity}x)` : ''}
                    </span>
                    <span className="text-xs sm:text-sm font-medium whitespace-nowrap">{formatCurrency(service.price * service.quantity)}</span>
                  </div>
                  {service.quantity > 1 && (
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>{service.quantity} x {formatCurrency(service.price)}</span>
                      <span className="whitespace-nowrap">=</span>
                    </div>
                  )}
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>Komisi per item:</span>
                    <span className="whitespace-nowrap">{formatCurrency(service.commission)}</span>
                  </div>
                </div>
              ))}
            </>
          ) : (
            // Fallback jika services tidak tersedia
            <div className="flex justify-between mb-2">
              <span className="text-xs sm:text-sm text-gray-600">Layanan:</span>
              <span className="text-xs sm:text-sm font-medium">{transaction.serviceName}</span>
            </div>
          )}
          {/* Subtotal */}
          <div className="flex justify-between pt-2 border-t border-gray-200">
            <span className="text-xs sm:text-sm text-gray-600">Subtotal:</span>
            <span className="text-xs sm:text-sm font-medium whitespace-nowrap">
              {transaction.services && transaction.services.length > 0
                ? formatCurrency(transaction.services.reduce((sum, service) => sum + (service.price * service.quantity), 0))
                : formatCurrency(transaction.amount)
              }
            </span>
          </div>

          {/* Diskon (jika ada dan nilainya lebih dari 0) */}
          {transaction.discountAmount && transaction.discountAmount > 0 && (
            <div className="flex justify-between mt-1">
              <span className="text-xs sm:text-sm text-gray-600">
                Diskon {transaction.discountType === 'percentage'
                  ? `(${transaction.discountValue}%)`
                  : transaction.discountType === 'fixed'
                    ? `(${formatCurrency(transaction.discountValue || 0)})`
                    : ''}:
              </span>
              <span className="text-xs sm:text-sm font-medium text-red-500 whitespace-nowrap">
                -{formatCurrency(transaction.discountAmount)}
              </span>
            </div>
          )}

          {/* Biaya Tambahan (jika ada dan nilainya lebih dari 0) */}
          {transaction.additionalCharge && transaction.additionalCharge > 0 && (
            <div className="flex justify-between mt-1">
              <span className="text-xs sm:text-sm text-gray-600">Biaya Tambahan:</span>
              <span className="text-xs sm:text-sm font-medium whitespace-nowrap">
                +{formatCurrency(transaction.additionalCharge)}
              </span>
            </div>
          )}

          {/* Lembur (jika ada dan nilainya lebih dari 0) */}
          {transaction.overtimeMinutes && transaction.overtimeMinutes > 0 && (
            <div className="flex justify-between mt-1">
              <span className="text-xs sm:text-sm text-gray-600">Lembur ({transaction.overtimeMinutes} menit):</span>
              <span className="text-xs sm:text-sm font-medium text-orange-600 whitespace-nowrap">
                +{formatCurrency(transaction.overtimeAmount || 0)}
              </span>
            </div>
          )}

          {/* Total Harga */}
          <div className="flex justify-between mt-1 font-bold">
            <span className="text-xs sm:text-sm text-gray-700">Total Pendapatan Terapis:</span>
            <span className="text-xs sm:text-sm whitespace-nowrap">
              {formatCurrency((() => {
                // Hitung total pendapatan terapis (harga layanan asli + biaya tambahan)
                let baseAmount = Number(transaction.amount) || 0;
                
                // Validasi nilai individual maksimum 100 juta per komponen
                const maxIndividualValue = 100000000; // 100 juta

                // Jika ada diskon, tambahkan kembali ke total untuk mendapatkan harga asli
                if (transaction.discountAmount && transaction.discountAmount > 0) {
                  let discountAmount = Number(transaction.discountAmount) || 0;
                  if (discountAmount > maxIndividualValue) {
                    console.warn(`Discount amount terlalu besar: ${discountAmount}, dibatasi ke ${maxIndividualValue}`);
                    discountAmount = maxIndividualValue;
                  }
                  baseAmount += discountAmount;
                }

                // Tambahkan biaya tambahan ke pendapatan terapis
                if (transaction.additionalCharge && transaction.additionalCharge > 0) {
                  let additionalCharge = Number(transaction.additionalCharge) || 0;
                  if (additionalCharge > maxIndividualValue) {
                    console.warn(`Additional charge terlalu besar: ${additionalCharge}, dibatasi ke ${maxIndividualValue}`);
                    additionalCharge = maxIndividualValue;
                  }
                  baseAmount += additionalCharge;
                }

                // Tambahkan lembur ke pendapatan terapis
                if (transaction.overtimeAmount && transaction.overtimeAmount > 0) {
                  let overtimeAmount = Number(transaction.overtimeAmount) || 0;
                  if (overtimeAmount > maxIndividualValue) {
                    console.warn(`Overtime amount terlalu besar: ${overtimeAmount}, dibatasi ke ${maxIndividualValue}`);
                    overtimeAmount = maxIndividualValue;
                  }
                  baseAmount += overtimeAmount;
                }

                // Batasi total maksimum 999 miliar
                const maxTotalValue = 999999999999; // 999 miliar
                if (baseAmount > maxTotalValue) {
                  console.warn(`Base amount terlalu besar: ${baseAmount}, dibatasi ke ${maxTotalValue}`);
                  baseAmount = maxTotalValue;
                }

                return baseAmount;
              })())}
            </span>
          </div>

          {/* Garis pemisah */}
          <div className="border-t border-gray-300 my-2"></div>

          {/* Total Komisi */}
          <div className="flex justify-between font-bold">
            <span className="text-xs sm:text-sm">Total Komisi:</span>
            <span className="text-xs sm:text-sm text-primary whitespace-nowrap">
              {formatCurrency(
                // SIMPLIFIED: Langsung gunakan nilai commission dari database
                transaction.commission
              )}
            </span>
          </div>
          {transaction.services && (
            <div className="mt-2 pt-2 border-t border-gray-200">
              <div className="text-xs text-gray-500 mb-1">Detail Komisi per Layanan:</div>
              {transaction.services.map((service, idx) => (
                <div key={idx} className="flex justify-between text-xs">
                  <span>{service.name} {service.quantity > 1 ? `(${service.quantity}x)` : ''}</span>
                  <span className="whitespace-nowrap">{formatCurrency(service.commission)}</span>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="text-center text-xs mt-4 sm:mt-6 mb-1 sm:mb-2">
          <p className="text-gray-500 text-[10px] sm:text-xs">
            Terima kasih atas pelayanan Anda
          </p>
          <p className="text-gray-500 text-[10px] sm:text-xs">
            Breaktime Badan Segar Urusan Lancar
          </p>
        </div>
      </div>
    </div>
  );
};

export default function TherapistCommissionPage() {
  const [therapistName, setTherapistName] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [commissions, setCommissions] = useState<TherapistCommission[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTransaction, setSelectedTransaction] = useState<TherapistCommission | null>(null);
  const [isReceiptOpen, setIsReceiptOpen] = useState(false);
  const [totalCommission, setTotalCommission] = useState(0);
  const [totalSales, setTotalSales] = useState(0);
  const [outlets, setOutlets] = useState<string[]>([]);
  const [totalOvertimeMinutes, setTotalOvertimeMinutes] = useState(0);
  const [totalOvertimeAmount, setTotalOvertimeAmount] = useState(0);
  const { theme } = useThemeContext();
  const [mounted, setMounted] = useState(false);

  // State untuk menyimpan nama terapis - pindahkan ke atas sebelum digunakan
  const [rememberName, setRememberName] = useState<boolean>(() => {
    // Cek apakah ada nama terapis yang tersimpan di localStorage
    if (typeof window !== 'undefined') {
      const savedPreference = localStorage.getItem('rememberTherapistName');
      return savedPreference === 'true';
    }
    return false;
  });

  // State untuk filter tanggal
  const [startDate, setStartDate] = useState<Date>(() => {
    return subDays(new Date(), 30); // 30 hari ke belakang
  });
  const [endDate, setEndDate] = useState<Date>(() => {
    return new Date(); // Hari ini
  });

  // Gunakan tema terang sebagai default
  // Kita tidak menggunakan tema dari context untuk memastikan halaman ini selalu menggunakan tema terang
  const isDark = false;

  // Menunda penerapan tema sampai komponen di-mount di client
  useEffect(() => {
    setMounted(true);

    // Ambil nama terapis yang tersimpan jika rememberName aktif
    if (rememberName && typeof window !== 'undefined') {
      const savedName = localStorage.getItem('therapistName');
      if (savedName) {
        setTherapistName(savedName);
      }
    }
  }, [rememberName]);

  // Format tanggal untuk tampilan
  const formatDateDisplay = (date: Date) => {
    return format(date, 'dd MMM yyyy', { locale: idLocale });
  };

  // Fungsi untuk menangani perubahan pada checkbox "Simpan Nama"
  const handleRememberNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    setRememberName(isChecked);

    if (typeof window !== 'undefined') {
      localStorage.setItem('rememberTherapistName', isChecked ? 'true' : 'false');

      if (isChecked && therapistName.trim()) {
        // Simpan nama terapis jika checkbox dicentang dan nama terisi
        localStorage.setItem('therapistName', therapistName.trim());
      } else if (!isChecked) {
        // Hapus nama terapis dari localStorage jika checkbox tidak dicentang
        localStorage.removeItem('therapistName');
      }
    }
  };

  const handleSearch = async () => {
    if (!therapistName.trim()) {
      toast.error('Silakan masukkan nama terapis');
      return;
    }

    // Simpan nama terapis jika checkbox dicentang
    if (rememberName && typeof window !== 'undefined') {
      localStorage.setItem('therapistName', therapistName.trim());
    }

    setIsLoading(true);
    setError(null);
    setIsSearching(true);

    try {
      // Format tanggal untuk API (YYYY-MM-DD)
      const formatDateForApi = (date: Date) => {
        return format(date, 'yyyy-MM-dd');
      };

      const params = new URLSearchParams({
        name: therapistName,
        startDate: formatDateForApi(startDate),
        endDate: formatDateForApi(endDate)
      });

      // Menggunakan endpoint yang sama tetapi dengan data yang lebih akurat dari /api/transactions/
      const response = await fetch(`/api/therapists/commissions?${params.toString()}`);
      const data = await response.json();

      // Cek apakah ada saran terapis
      if (data.suggestedTherapist) {
        // Tampilkan toast dengan saran terapis
        toast.info(
          <div>
            <p>{data.message}</p>
            <button
              type="button"
              className="mt-2 px-3 py-1 bg-primary text-white rounded-md hover:bg-primary-dark"
              onClick={() => {
                setTherapistName(data.suggestedTherapist);
                handleSearch();
              }}
            >
              Gunakan "{data.suggestedTherapist}"
            </button>
          </div>,
          { duration: 10000 } // 10 detik
        );
        setIsLoading(false);
        return;
      }

      // Cek apakah response tidak ok
      if (!response.ok) {
        // Jika ada daftar semua terapis, tampilkan di console untuk debugging
        if (data.allTherapists) {
          console.log('All therapists in database:', data.allTherapists);

          // Cari terapis yang namanya mirip dengan yang dicari
          const similarTherapists = data.allTherapists.filter((t: any) =>
            t.name.toLowerCase().includes(therapistName.toLowerCase()) ||
            therapistName.toLowerCase().includes(t.name.toLowerCase())
          );

          if (similarTherapists.length > 0) {
            // Tampilkan toast dengan saran terapis
            toast.info(
              <div>
                <p>Terapis "{therapistName}" tidak ditemukan. Apakah Anda mencari:</p>
                <div className="mt-2 space-y-1">
                  {similarTherapists.map((t: any) => (
                    <button
                      type="button"
                      key={t.id}
                      className="block w-full px-3 py-1 bg-primary text-white rounded-md hover:bg-primary-dark text-left"
                      onClick={() => {
                        setTherapistName(t.name);
                        handleSearch();
                      }}
                    >
                      {t.name} {!t.isActive ? "(Tidak Aktif)" : ""}
                    </button>
                  ))}
                </div>
              </div>,
              { duration: 10000 } // 10 detik
            );
          }
        }

        throw new Error(data.message || 'Gagal mengambil data komisi');
      }

      // Pastikan data komisi memiliki format yang benar
      const formattedCommissions = (data.commissions || []).map((commission: TherapistCommission) => {
        // Pastikan serviceName menampilkan quantity dengan benar
        if (commission.services && commission.services.length > 0) {
          // Jika services tersedia, format ulang serviceName
          commission.serviceName = commission.services.map((service: ServiceItem) =>
            service.quantity > 1 ? `${service.name} (${service.quantity}x)` : service.name
          ).join(', ');
        }

        return commission;
      });
      setCommissions(formattedCommissions);

      // Hitung total komisi (pastikan tidak termasuk biaya tambahan)
      const total = formattedCommissions.reduce(
        (sum: number, item: TherapistCommission) => {
          let commission = Number(item.commission) || 0;
          // Batasi nilai komisi individual maksimum 50 juta per transaksi
          const maxCommissionValue = 50000000;
          if (commission > maxCommissionValue) {
            console.warn(`Commission per transaksi terlalu besar: ${commission}, dibatasi ke ${maxCommissionValue}`);
            commission = maxCommissionValue;
          }
          let result = sum + commission;
          // Batasi total komisi maksimum 999 miliar
          const maxTotalCommission = 999999999999;
          if (result > maxTotalCommission) {
            console.warn(`Total commission terlalu besar: ${result}, dibatasi ke ${maxTotalCommission}`);
            return maxTotalCommission;
          }
          return result;
        },
        0
      );
      setTotalCommission(total);

      // Set total penjualan dari respons API
      setTotalSales(data.totalSales || 0);

      // Set daftar outlet dari respons API
      setOutlets(data.outlets || []);

      // Hitung total lembur
      const totalMinutes = formattedCommissions.reduce(
        (sum: number, item: TherapistCommission) => {
          const minutes = Number(item.overtimeMinutes) || 0;
          return sum + minutes;
        },
        0
      );
      setTotalOvertimeMinutes(totalMinutes);

      const totalAmount = formattedCommissions.reduce(
        (sum: number, item: TherapistCommission) => {
          let amount = Number(item.overtimeAmount) || 0;
          // Batasi nilai lembur individual maksimum 50 juta per transaksi
          const maxOvertimeValue = 50000000;
          if (amount > maxOvertimeValue) {
            console.warn(`Overtime amount per transaksi terlalu besar: ${amount}, dibatasi ke ${maxOvertimeValue}`);
            amount = maxOvertimeValue;
          }
          let result = sum + amount;
          // Batasi total lembur maksimum 999 miliar
          const maxTotalOvertime = 999999999999;
          if (result > maxTotalOvertime) {
            console.warn(`Total overtime amount terlalu besar: ${result}, dibatasi ke ${maxTotalOvertime}`);
            return maxTotalOvertime;
          }
          return result;
        },
        0
      );
      setTotalOvertimeAmount(totalAmount);

      // Tampilkan pesan jika tidak ada data komisi
      if (formattedCommissions.length === 0) {
        toast.info(
          <div>
            <p>Terapis <strong>{data.therapistName}</strong> ditemukan, tetapi tidak ada data komisi dalam rentang tanggal {formatDateDisplay(startDate)} - {formatDateDisplay(endDate)}.</p>
            <p className="mt-2 text-sm">Coba ubah rentang tanggal atau periksa apakah terapis ini memiliki transaksi.</p>
          </div>,
          { duration: 5000 }
        );
      }
    } catch (err) {
      console.error('Error fetching therapist commissions:', err);
      setError(err instanceof Error ? err.message : 'Terjadi kesalahan saat mengambil data');
      setCommissions([]);
      setTotalCommission(0);
      setTotalSales(0);
      setOutlets([]);
      setTotalOvertimeMinutes(0);
      setTotalOvertimeAmount(0);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewReceipt = async (transaction: TherapistCommission) => {
    try {
      // Ambil data transaksi lengkap dari API untuk mendapatkan diskon dan biaya tambahan
      const response = await fetch(`/api/transactions/${transaction.transactionId}`);

      if (response.ok) {
        const data = await response.json();

        // Tambahkan data diskon dan biaya tambahan ke objek transaksi
        // Hanya jika data tersebut ada dan nilainya lebih dari 0
        if (data) {
          // Tambahkan informasi diskon jika ada dan nilainya lebih dari 0
          if (data.discountType && data.discountAmount && data.discountAmount > 0) {
            transaction.discountType = data.discountType;
            transaction.discountValue = data.discountValue || 0;
            transaction.discountAmount = data.discountAmount;
          } else {
            // Hapus properti diskon jika tidak ada atau nilainya 0
            delete transaction.discountType;
            delete transaction.discountValue;
            delete transaction.discountAmount;
          }

          // Tambahkan informasi biaya tambahan jika ada dan nilainya lebih dari 0
          if (data.additionalCharge && data.additionalCharge > 0) {
            transaction.additionalCharge = data.additionalCharge;
          } else {
            // Hapus properti biaya tambahan jika tidak ada atau nilainya 0
            delete transaction.additionalCharge;
          }

          // Tambahkan informasi lembur jika ada dan nilainya lebih dari 0
          if (data.overtimeMinutes && data.overtimeMinutes > 0) {
            transaction.overtimeMinutes = data.overtimeMinutes;
            transaction.overtimeAmount = data.overtimeAmount || 0;
          } else {
            // Hapus properti lembur jika tidak ada atau nilainya 0
            delete transaction.overtimeMinutes;
            delete transaction.overtimeAmount;
          }
        }
      }
    } catch (error) {
      // Lanjutkan meskipun ada error, kita masih bisa menampilkan receipt dengan data yang ada
    }

    setSelectedTransaction(transaction);
    setIsReceiptOpen(true);
  };

  const handleCloseReceipt = () => {
    setIsReceiptOpen(false);
    setSelectedTransaction(null);
  };

  // Jika belum di-mount, tampilkan skeleton loader atau div kosong
  // untuk mencegah hydration error
  if (!mounted) {
    return <div className="min-h-screen bg-gray-50"></div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <motion.div
        className="container mx-auto p-4 md:p-6"
        initial="hidden"
        animate="visible"
        variants={staggerContainer}
      >
      {/* Header */}
      <motion.div variants={fadeInUp} className="text-center mb-8">
        <div className="flex justify-center mb-4">
          <img src="/logo.png" alt="BreakTime Logo" className="h-16" />
        </div>
        <h1 className="text-2xl md:text-3xl font-bold mb-2 text-gray-800">
          Cek Komisi Terapis
        </h1>
        <p className="text-gray-600">
          Masukkan nama Anda untuk melihat riwayat dan total komisi
        </p>
      </motion.div>

      {/* Search Form */}
      <motion.div
        variants={fadeInUp}
        className={`p-6 rounded-lg shadow-md mb-8 ${isDark ? 'bg-gray-800' : 'bg-white'}`}
      >
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              <FiUser className="inline mr-2" />
              Nama Terapis
            </label>
            <div className={`flex items-center border rounded-lg overflow-hidden ${isDark ? 'border-gray-700 bg-gray-700' : 'border-gray-300'}`}>
              <input
                type="text"
                value={therapistName}
                onChange={(e) => setTherapistName(e.target.value)}
                className={`flex-1 p-3 focus:outline-none ${isDark ? 'bg-gray-700 text-white' : 'bg-white text-gray-800'}`}
                placeholder="Masukkan nama terapis..."
              />
            </div>
            <div className="flex items-center mt-2">
              <input
                type="checkbox"
                id="rememberName"
                checked={rememberName}
                onChange={handleRememberNameChange}
                className="checkbox checkbox-sm checkbox-primary mr-2"
              />
              <label htmlFor="rememberName" className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                Simpan nama untuk pencarian berikutnya
              </label>
            </div>
          </div>

          <div className="flex-1">
            <label className="block text-sm font-medium mb-2 text-gray-700">
              <FiCalendar className="inline mr-2" />
              Dari Tanggal
            </label>
            <div className="dropdown dropdown-end w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline border-gray-300 text-gray-700 m-1 w-full md:min-w-36 hover:bg-gray-100 hover:border-gray-400">
                {formatDateDisplay(startDate)}
              </div>
              <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-white border border-gray-200 rounded-box w-64 md:w-auto left-0 md:left-auto right-0 md:right-auto">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2 border-b border-gray-100">
                      <button className="btn btn-sm btn-ghost text-gray-700 hover:bg-gray-100" onClick={() => setStartDate(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1))}>
                        «
                      </button>
                      <div className="text-sm font-medium text-gray-800">
                        {format(startDate, 'MMMM yyyy', { locale: idLocale })}
                      </div>
                      <button className="btn btn-sm btn-ghost text-gray-700 hover:bg-gray-100" onClick={() => setStartDate(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1))}>
                        »
                      </button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {/* Hari dalam seminggu */}
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}

                      {/* Tanggal */}
                      {Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(startDate.getFullYear(), startDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === startDate.getMonth();
                        const isSelected = date.getDate() === startDate.getDate() &&
                                          date.getMonth() === startDate.getMonth() &&
                                          date.getFullYear() === startDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? 'text-gray-700' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-white font-bold' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary font-medium' : ''}
                                      ${isCurrentMonth && !isSelected ? 'hover:bg-gray-100' : ''}`}
                            onClick={() => setStartDate(new Date(date))}
                            disabled={!isCurrentMonth}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex-1">
            <label className="block text-sm font-medium mb-2 text-gray-700">
              <FiCalendar className="inline mr-2" />
              Sampai Tanggal
            </label>
            <div className="dropdown dropdown-end w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline border-gray-300 text-gray-700 m-1 w-full md:min-w-36 hover:bg-gray-100 hover:border-gray-400">
                {formatDateDisplay(endDate)}
              </div>
              <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-white border border-gray-200 rounded-box w-64 md:w-auto left-0 md:left-auto right-0 md:right-auto">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2 border-b border-gray-100">
                      <button className="btn btn-sm btn-ghost text-gray-700 hover:bg-gray-100" onClick={() => setEndDate(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1))}>
                        «
                      </button>
                      <div className="text-sm font-medium text-gray-800">
                        {format(endDate, 'MMMM yyyy', { locale: idLocale })}
                      </div>
                      <button className="btn btn-sm btn-ghost text-gray-700 hover:bg-gray-100" onClick={() => setEndDate(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1))}>
                        »
                      </button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {/* Hari dalam seminggu */}
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}

                      {/* Tanggal */}
                      {Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(endDate.getFullYear(), endDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === endDate.getMonth();
                        const isSelected = date.getDate() === endDate.getDate() &&
                                          date.getMonth() === endDate.getMonth() &&
                                          date.getFullYear() === endDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();
                        const isDisabled = !isCurrentMonth || date < startDate;

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? 'text-gray-700' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-white font-bold' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary font-medium' : ''}
                                      ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-gray-100' : ''}
                                      ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={() => !isDisabled && setEndDate(new Date(date))}
                            disabled={isDisabled}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-4 flex justify-center">
          <button
            type="button"
            onClick={handleSearch}
            disabled={isLoading}
            className="btn btn-primary gap-2 px-8"
          >
            {isLoading ? (
              <span className="loading loading-spinner loading-sm"></span>
            ) : (
              <FiSearch />
            )}
            Cari Komisi
          </button>
        </div>
      </motion.div>

      {/* Error Message */}
      {error && (
        <motion.div variants={fadeInUp} className="alert alert-error mb-6">
          <FiAlertTriangle />
          <span>{error}</span>
        </motion.div>
      )}

      {/* Results */}
      {isSearching && (
        <>
          {/* Summary Card */}
          <motion.div
            variants={fadeInUp}
            className={`p-6 rounded-lg shadow-md mb-6 ${isDark ? 'bg-gray-800' : 'bg-white'}`}
          >
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="mb-4 md:mb-0">
                <h2 className={`text-xl font-bold mb-1 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  Ringkasan Komisi
                </h2>
                <p className="text-gray-600">
                  {therapistName} • {formatDateDisplay(startDate)} - {formatDateDisplay(endDate)}
                </p>
                {outlets.length > 0 && (
                  <div className="mt-1">
                    <p className="text-xs text-gray-500">
                      Dari {outlets.length} outlet:
                    </p>
                    <p className="text-xs text-gray-500 mt-0.5">
                      {outlets.join(', ')}
                    </p>
                  </div>
                )}
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className={`text-center p-4 rounded-lg ${isDark ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <p className={`text-sm mb-1 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>Total Penjualan</p>
                  <p className="text-2xl font-bold text-gray-800">{formatCurrency(totalSales)}</p>
                  <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    Dari {commissions.length} transaksi
                  </p>
                </div>
                <div className={`text-center p-4 rounded-lg ${isDark ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <p className={`text-sm mb-1 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>Total Komisi</p>
                  <p className="text-2xl font-bold text-primary">{formatCurrency(totalCommission)}</p>
                  <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    {totalSales > 0 && isFinite(totalSales) && isFinite(totalCommission) ? 
                      `${Math.round((totalCommission / totalSales) * 100)}% dari penjualan` : '0% dari penjualan'}
                  </p>
                </div>
                {/* Card Total Lembur */}
                {totalOvertimeMinutes > 0 && (
                  <div className={`text-center p-4 rounded-lg ${isDark ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <p className={`text-sm mb-1 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>Total Lembur</p>
                    <p className="text-2xl font-bold text-orange-600">{formatCurrency(totalOvertimeAmount)}</p>
                    <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                      {totalOvertimeMinutes} menit lembur
                    </p>
                  </div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Transactions Table */}
          {commissions.length > 0 ? (
            <motion.div
              variants={fadeInUp}
              className={`rounded-lg shadow-md overflow-hidden ${isDark ? 'bg-gray-800' : 'bg-white'}`}
            >
              {/* Tabel untuk layar medium dan di atasnya */}
              <div className="hidden md:block overflow-x-auto">
                <table className="w-full table-auto">
                  <thead className={isDark ? 'bg-gray-700 text-gray-200' : 'bg-gray-50 text-gray-700'}>
                    <tr>
                      <th className="px-4 py-3 text-left">Tanggal</th>
                      <th className="px-4 py-3 text-left">Layanan</th>
                      <th className="px-4 py-3 text-left">Pelanggan</th>
                      <th className="px-4 py-3 text-left">Outlet</th>
                      <th className="px-4 py-3 text-right">Pendapatan Terapis</th>
                      <th className="px-4 py-3 text-right">Komisi</th>
                      <th className="px-4 py-3 text-center">Struk</th>
                    </tr>
                  </thead>
                  <tbody className={isDark ? 'text-gray-300' : 'text-gray-700'}>
                    {/* Kelompokkan data berdasarkan outlet */}
                    {outlets.map((outlet) => (
                      <Fragment key={`outlet-${outlet}`}>
                        {/* Header outlet */}
                        <tr className={`${isDark ? 'bg-gray-700' : 'bg-gray-100'}`}>
                          <td colSpan={7} className="px-4 py-2 font-medium">
                            Outlet: {outlet}
                            <span className="ml-2 text-xs">
                              (Total: {formatCurrency(commissions.filter(c => c.outletName === outlet).reduce((sum, c) => {
                                let amount = Number(c.amount) || 0;
                                // Batasi nilai individual maksimum 100 juta per transaksi
                                const maxIndividualValue = 100000000;
                                if (amount > maxIndividualValue) {
                                  console.warn(`Amount per transaksi terlalu besar: ${amount}, dibatasi ke ${maxIndividualValue}`);
                                  amount = maxIndividualValue;
                                }
                                let result = sum + amount;
                                // Batasi total maksimum 999 miliar
                                const maxTotalValue = 999999999999;
                                if (result > maxTotalValue) {
                                  console.warn(`Total amount terlalu besar: ${result}, dibatasi ke ${maxTotalValue}`);
                                  return maxTotalValue;
                                }
                                return result;
                              }, 0))},
                              Komisi: {formatCurrency(commissions.filter(c => c.outletName === outlet).reduce((sum, c) => {
                                let commission = Number(c.commission) || 0;
                                // Batasi nilai komisi individual maksimum 50 juta per transaksi
                                const maxCommissionValue = 50000000;
                                if (commission > maxCommissionValue) {
                                  console.warn(`Commission per transaksi terlalu besar: ${commission}, dibatasi ke ${maxCommissionValue}`);
                                  commission = maxCommissionValue;
                                }
                                let result = sum + commission;
                                // Batasi total komisi maksimum 999 miliar
                                const maxTotalCommission = 999999999999;
                                if (result > maxTotalCommission) {
                                  console.warn(`Total commission terlalu besar: ${result}, dibatasi ke ${maxTotalCommission}`);
                                  return maxTotalCommission;
                                }
                                return result;
                              }, 0))})
                            </span>
                          </td>
                        </tr>

                        {/* Transaksi untuk outlet ini */}
                        {commissions
                          .filter(commission => commission.outletName === outlet)
                          .map((commission, index) => (
                            <tr
                              key={`desktop-${commission.transactionId}-${index}`}
                              className={`border-t ${isDark ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-200 hover:bg-gray-50'}`}
                            >
                        <td className="px-4 py-3">
                          <div>{format(new Date(commission.transactionDate), 'dd MMM yyyy', { locale: idLocale })}</div>
                          <div className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                            {format(new Date(commission.transactionDate), 'HH:mm', { locale: idLocale })}
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          {commission.services && commission.services.length > 0 ? (
                            commission.services.map((service, idx) => (
                              <div key={idx}>
                                {service.name} {service.quantity > 1 ? `(${service.quantity}x)` : ''}
                              </div>
                            ))
                          ) : (
                            commission.serviceName
                          )}
                        </td>
                        <td className="px-4 py-3">{commission.customerName}</td>
                        <td className="px-4 py-3">{commission.outletName}</td>
                        <td className="px-4 py-3 text-right">
                          <div>
                            {formatCurrency((() => {
                              // Hitung total pendapatan terapis (harga layanan asli + biaya tambahan)
                              let baseAmount = Number(commission.amount) || 0;
                              
                              // Validasi nilai individual maksimum 100 juta per komponen
                              const maxIndividualValue = 100000000; // 100 juta

                              // Jika ada diskon, tambahkan kembali ke total untuk mendapatkan harga asli
                              if (commission.discountAmount && commission.discountAmount > 0) {
                                let discountAmount = Number(commission.discountAmount) || 0;
                                if (discountAmount > maxIndividualValue) {
                                  console.warn(`Discount amount terlalu besar: ${discountAmount}, dibatasi ke ${maxIndividualValue}`);
                                  discountAmount = maxIndividualValue;
                                }
                                baseAmount += discountAmount;
                              }

                              // Tambahkan biaya tambahan ke pendapatan terapis
                              if (commission.additionalCharge && commission.additionalCharge > 0) {
                                let additionalCharge = Number(commission.additionalCharge) || 0;
                                if (additionalCharge > maxIndividualValue) {
                                  console.warn(`Additional charge terlalu besar: ${additionalCharge}, dibatasi ke ${maxIndividualValue}`);
                                  additionalCharge = maxIndividualValue;
                                }
                                baseAmount += additionalCharge;
                              }

                              // Tambahkan lembur ke pendapatan terapis
                              if (commission.overtimeAmount && commission.overtimeAmount > 0) {
                                let overtimeAmount = Number(commission.overtimeAmount) || 0;
                                if (overtimeAmount > maxIndividualValue) {
                                  console.warn(`Overtime amount terlalu besar: ${overtimeAmount}, dibatasi ke ${maxIndividualValue}`);
                                  overtimeAmount = maxIndividualValue;
                                }
                                baseAmount += overtimeAmount;
                              }

                              // Batasi total maksimum 999 miliar
                              const maxTotalValue = 999999999999; // 999 miliar
                              if (baseAmount > maxTotalValue) {
                                console.warn(`Base amount terlalu besar: ${baseAmount}, dibatasi ke ${maxTotalValue}`);
                                baseAmount = maxTotalValue;
                              }

                              return baseAmount;
                            })())}
                          </div>

                          {/* Tampilkan informasi biaya tambahan jika ada */}
                          {commission.additionalCharge && commission.additionalCharge > 0 && (
                            <div className="text-xs text-info">
                              +Biaya: {formatCurrency(commission.additionalCharge)}
                            </div>
                          )}

                          {/* Tampilkan informasi lembur jika ada */}
                          {commission.overtimeMinutes && commission.overtimeMinutes > 0 && (
                            <div className="text-xs text-orange-600">
                              +Lembur ({commission.overtimeMinutes}m): {formatCurrency(commission.overtimeAmount || 0)}
                            </div>
                          )}
                        </td>
                        <td className="px-4 py-3 text-right font-medium text-primary">
                          {formatCurrency(
                            // SIMPLIFIED: Langsung gunakan nilai commission dari database
                            commission.commission
                          )}
                        </td>
                        <td className="px-4 py-3 text-center">
                          <button
                            type="button"
                            onClick={() => handleViewReceipt(commission)}
                            className={`btn btn-sm btn-ghost ${isDark ? 'text-blue-400 hover:bg-gray-700' : 'text-blue-600 hover:bg-gray-100'}`}
                            title="Lihat Struk"
                            aria-label="Lihat Struk"
                          >
                            <FiFileText />
                          </button>
                        </td>
                      </tr>
                          ))}
                      </Fragment>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Tampilan kartu untuk layar kecil */}
              <div className="md:hidden">
                {/* Kelompokkan data berdasarkan outlet */}
                {outlets.map((outlet) => (
                  <div key={`mobile-outlet-${outlet}`}>
                    {/* Header outlet */}
                    <div className={`p-2 font-medium ${isDark ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-800'}`}>
                      <div>Outlet: {outlet}</div>
                      <div className="text-xs mt-1">
                        Total: {formatCurrency(commissions.filter(c => c.outletName === outlet).reduce((sum, c) => {
                          let amount = Number(c.amount) || 0;
                          // Batasi nilai individual maksimum 100 juta per transaksi
                          const maxIndividualValue = 100000000;
                          if (amount > maxIndividualValue) {
                            console.warn(`Amount per transaksi terlalu besar: ${amount}, dibatasi ke ${maxIndividualValue}`);
                            amount = maxIndividualValue;
                          }
                          let result = sum + amount;
                          // Batasi total maksimum 999 miliar
                          const maxTotalValue = 999999999999;
                          if (result > maxTotalValue) {
                            console.warn(`Total amount terlalu besar: ${result}, dibatasi ke ${maxTotalValue}`);
                            return maxTotalValue;
                          }
                          return result;
                        }, 0))}
                      </div>
                      <div className="text-xs">
                        Komisi: {formatCurrency(commissions.filter(c => c.outletName === outlet).reduce((sum, c) => {
                          let commission = Number(c.commission) || 0;
                          // Batasi nilai komisi individual maksimum 50 juta per transaksi
                          const maxCommissionValue = 50000000;
                          if (commission > maxCommissionValue) {
                            console.warn(`Commission per transaksi terlalu besar: ${commission}, dibatasi ke ${maxCommissionValue}`);
                            commission = maxCommissionValue;
                          }
                          let result = sum + commission;
                          // Batasi total komisi maksimum 999 miliar
                          const maxTotalCommission = 999999999999;
                          if (result > maxTotalCommission) {
                            console.warn(`Total commission terlalu besar: ${result}, dibatasi ke ${maxTotalCommission}`);
                            return maxTotalCommission;
                          }
                          return result;
                        }, 0))}
                      </div>
                    </div>

                    {/* Transaksi untuk outlet ini */}
                    {commissions
                      .filter(commission => commission.outletName === outlet)
                      .map((commission, index) => (
                        <div
                          key={`mobile-${commission.transactionId}-${index}`}
                          className={`p-4 border-b ${isDark ? 'border-gray-700' : 'border-gray-200'} ${index === 0 ? '' : 'border-t-0'} bg-white`}
                        >
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="font-medium text-gray-800">{format(new Date(commission.transactionDate), 'dd MMM yyyy', { locale: idLocale })}</div>
                        <div className="text-xs text-gray-500">
                          {format(new Date(commission.transactionDate), 'HH:mm', { locale: idLocale })}
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleViewReceipt(commission)}
                        className="btn btn-sm btn-ghost text-blue-600 hover:bg-gray-100"
                        title="Lihat Struk"
                        aria-label="Lihat Struk"
                      >
                        <FiFileText />
                      </button>
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <div className="text-xs font-medium mb-1 text-gray-500">Layanan</div>
                        <div className="text-gray-800">
                          {commission.services && commission.services.length > 0 ? (
                            commission.services.map((service, idx) => (
                              <div key={idx} className={idx > 0 ? "mt-1" : ""}>
                                {service.name} {service.quantity > 1 ? `(${service.quantity}x)` : ''}
                              </div>
                            ))
                          ) : (
                            commission.serviceName
                          )}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs font-medium mb-1 text-gray-500">Pelanggan</div>
                        <div className="text-gray-800">{commission.customerName}</div>
                      </div>
                      <div>
                        <div className="text-xs font-medium mb-1 text-gray-500">Outlet</div>
                        <div className="text-gray-800">{commission.outletName}</div>
                      </div>
                      <div>
                        <div className="text-xs font-medium mb-1 text-gray-500">Pendapatan Terapis</div>
                        <div className="text-gray-800">
                          {formatCurrency((() => {
                            // Hitung total pendapatan terapis (harga layanan asli + biaya tambahan)
                            let baseAmount = Number(commission.amount) || 0;
                            
                            // Validasi nilai individual maksimum 100 juta per komponen
                            const maxIndividualValue = 100000000; // 100 juta

                            // Jika ada diskon, tambahkan kembali ke total untuk mendapatkan harga asli
                            if (commission.discountAmount && commission.discountAmount > 0) {
                              let discountAmount = Number(commission.discountAmount) || 0;
                              if (discountAmount > maxIndividualValue) {
                                console.warn(`Discount amount terlalu besar: ${discountAmount}, dibatasi ke ${maxIndividualValue}`);
                                discountAmount = maxIndividualValue;
                              }
                              baseAmount += discountAmount;
                            }

                            // Tambahkan biaya tambahan ke pendapatan terapis
                            if (commission.additionalCharge && commission.additionalCharge > 0) {
                              let additionalCharge = Number(commission.additionalCharge) || 0;
                              if (additionalCharge > maxIndividualValue) {
                                console.warn(`Additional charge terlalu besar: ${additionalCharge}, dibatasi ke ${maxIndividualValue}`);
                                additionalCharge = maxIndividualValue;
                              }
                              baseAmount += additionalCharge;
                            }

                            // Tambahkan lembur ke pendapatan terapis
                            if (commission.overtimeAmount && commission.overtimeAmount > 0) {
                              let overtimeAmount = Number(commission.overtimeAmount) || 0;
                              if (overtimeAmount > maxIndividualValue) {
                                console.warn(`Overtime amount terlalu besar: ${overtimeAmount}, dibatasi ke ${maxIndividualValue}`);
                                overtimeAmount = maxIndividualValue;
                              }
                              baseAmount += overtimeAmount;
                            }

                            // Batasi total maksimum 999 miliar
                            const maxTotalValue = 999999999999; // 999 miliar
                            if (baseAmount > maxTotalValue) {
                              console.warn(`Base amount terlalu besar: ${baseAmount}, dibatasi ke ${maxTotalValue}`);
                              baseAmount = maxTotalValue;
                            }

                            return baseAmount;
                          })())}

                          {/* Tampilkan informasi biaya tambahan jika ada */}
                          {commission.additionalCharge && commission.additionalCharge > 0 && (
                            <div className="text-xs text-info mt-1">
                              +Biaya: {formatCurrency(commission.additionalCharge)}
                            </div>
                          )}

                          {/* Tampilkan informasi lembur jika ada */}
                          {commission.overtimeMinutes && commission.overtimeMinutes > 0 && (
                            <div className="text-xs text-orange-600 mt-1">
                              +Lembur ({commission.overtimeMinutes}m): {formatCurrency(commission.overtimeAmount || 0)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="mt-3 pt-2 border-t border-dashed flex justify-between items-center">
                      <div className="text-xs font-medium text-gray-500">Komisi (Asli)</div>
                      <div className="font-medium text-primary">
                        {formatCurrency(
                          // SIMPLIFIED: Langsung gunakan nilai commission dari database
                          commission.commission
                        )}
                      </div>
                    </div>
                        </div>
                      ))}
                  </div>
                ))}
              </div>
            </motion.div>
          ) : (
            <motion.div
              variants={fadeInUp}
              className="text-center p-8 rounded-lg shadow-md bg-white text-gray-600"
            >
              <FiClock className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2 text-gray-800">
                Tidak ada riwayat komisi
              </h3>
              <p>
                Tidak ditemukan riwayat komisi untuk terapis "<strong>{therapistName}</strong>" pada periode yang dipilih.
              </p>
              <div className="mt-4">
                <p className="text-sm text-gray-500 mb-2">Kemungkinan penyebab:</p>
                <ul className="text-sm text-gray-600 list-disc list-inside">
                  <li>Belum ada transaksi yang selesai pada periode tersebut</li>
                  <li>Terapis belum memiliki komisi yang tercatat</li>
                </ul>
              </div>
              <div className="flex flex-col sm:flex-row justify-center gap-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    // Set tanggal mulai ke 3 bulan yang lalu
                    setStartDate(subDays(new Date(), 90));
                    // Set tanggal akhir ke hari ini
                    setEndDate(new Date());
                    // Jalankan pencarian lagi
                    setTimeout(handleSearch, 100);
                  }}
                  className="btn btn-primary"
                >
                  <FiCalendar className="mr-2" /> Cari 3 Bulan Terakhir
                </button>
                <button
                  type="button"
                  onClick={() => {
                    // Set tanggal mulai ke 6 bulan yang lalu
                    setStartDate(subDays(new Date(), 180));
                    // Set tanggal akhir ke hari ini
                    setEndDate(new Date());
                    // Jalankan pencarian lagi
                    setTimeout(handleSearch, 100);
                  }}
                  className="btn btn-outline"
                >
                  <FiCalendar className="mr-2" /> Cari 6 Bulan Terakhir
                </button>
              </div>
            </motion.div>
          )}
        </>
      )}

      {/* Receipt Modal */}
      <ReceiptModal
        isOpen={isReceiptOpen}
        onClose={handleCloseReceipt}
        transaction={selectedTransaction}
      />
    </motion.div>
  </div>
  );
}
