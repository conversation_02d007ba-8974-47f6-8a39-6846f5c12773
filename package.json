{"name": "breaktimedash5", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate --no-engine && next build", "start": "next start", "lint": "next lint", "seed": "prisma db seed", "prisma:generate": "prisma generate", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:commission": "jest --testPathPattern=commission"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.3", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.6.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/react-datepicker": "^6.2.0", "@types/uuid": "^10.0.0", "@yudiel/react-qr-scanner": "^1.2.10", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cally": "^0.8.0", "daisyui": "^4.12.24", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dom-to-image": "^2.6.0", "exceljs": "^4.4.0", "framer-motion": "^12.10.1", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "html5-qrcode": "^2.3.8", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "next": "15.1.7", "prisma": "^6.6.0", "qrcode": "^1.5.4", "react": "^18.2.0", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-to-print": "^3.0.5", "recharts": "^2.15.2", "sonner": "^2.0.3", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/dom-to-image": "^2.6.7", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^18", "@types/react-dom": "^18", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.1.7", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5"}}