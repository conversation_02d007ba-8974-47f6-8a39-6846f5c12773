import React, { useState, useEffect, useRef } from 'react';
import { Html5Qrcode } from 'html5-qrcode';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { 
  ArrowPathIcon, 
  CheckCircleIcon, 
  XCircleIcon, 
  ExclamationTriangleIcon,
  CameraIcon,
  LockClosedIcon,
  LockOpenIcon
} from '@heroicons/react/24/outline';

interface QRScannerProps {
  onScanSuccess: (data: string) => void;
  scanType: 'IN' | 'OUT';
}

const QRScanner: React.FC<QRScannerProps> = ({ onScanSuccess, scanType }) => {
  const [isScanning, setIsScanning] = useState(false);
  const [lastScan, setLastScan] = useState<{ name: string; success: boolean; isDuplicate?: boolean } | null>(null);
  const [cameraError, setCameraError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isRetrying, setIsRetrying] = useState(false);
  const [cameraPermission, setCameraPermission] = useState<'prompt'|'granted'|'denied'|'checking'>('checking');
  const scannerRef = useRef<Html5Qrcode | null>(null);
  const scannerContainerId = 'html5-qrcode-reader';
  const hasScanner = useRef(false);

  // Periksa izin kamera saat komponen dimuat
  useEffect(() => {
    checkCameraPermission();
    
    // Cleanup function untuk menghentikan scanner
    return () => {
      if (scannerRef.current && scannerRef.current.isScanning) {
        scannerRef.current.stop().catch(err => console.error('Error stopping scanner:', err));
      }
    };
  }, []);

  // Effect untuk menginisialisasi scanner setelah DOM dirender dan izin diberikan
  useEffect(() => {
    // Pastikan elemen ada dan izin diberikan
    if (cameraPermission === 'granted' && isScanning && !hasScanner.current) {
      // Gunakan setTimeout untuk memastikan DOM sudah dirender sepenuhnya
      const timer = setTimeout(() => {
        initScanner();
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [cameraPermission, isScanning]);
  
  // Fungsi untuk memeriksa status izin kamera
  const checkCameraPermission = async () => {
    try {
      // Periksa apakah navigator.permissions API tersedia
      if (navigator.permissions && navigator.permissions.query) {
        const result = await navigator.permissions.query({ name: 'camera' as PermissionName });
        
        setCameraPermission(result.state as 'prompt'|'granted'|'denied');
        
        // Mulai scanning jika izin sudah diberikan
        if (result.state === 'granted') {
          setIsScanning(true);
          setCameraError(false);
          // Jangan langsung memanggil initScanner() di sini
        } else if (result.state === 'denied') {
          setCameraError(true);
          setErrorMessage('Akses kamera ditolak. Silakan aktifkan kamera di pengaturan browser Anda.');
        }
        
        // Tambahkan event listener untuk perubahan status izin
        result.addEventListener('change', function() {
          setCameraPermission(result.state as 'prompt'|'granted'|'denied');
          if (result.state === 'granted') {
            setIsScanning(true);
            setCameraError(false);
            // Jangan langsung memanggil initScanner() di sini
          } else if (result.state === 'denied') {
            setIsScanning(false);
            setCameraError(true);
            setErrorMessage('Akses kamera ditolak. Silakan aktifkan kamera di pengaturan browser Anda.');
            stopScanner();
          }
        });
        
        return;
      }
      
      // Alternatif: coba akses langsung jika navigator.permissions tidak tersedia
      setCameraPermission('prompt');
      requestCameraAccess();
    } catch (error) {
      console.error('Error checking camera permission:', error);
      setCameraPermission('prompt');
      requestCameraAccess();
    }
  };

  // Fungsi untuk meminta akses kamera secara eksplisit
  const requestCameraAccess = async () => {
    try {
      setIsRetrying(true);
      setCameraError(false);
      
      // Meminta izin kamera secara eksplisit dengan mencoba mendapatkan media stream
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' },
        audio: false 
      });
      
      // Jika berhasil, atur state sesuai
      setCameraPermission('granted');
      setIsScanning(true);
      setCameraError(false);
      setErrorMessage('');
      // Jangan langsung memanggil initScanner() di sini
      
      // Pastikan untuk melepaskan stream setelah digunakan
      stream.getTracks().forEach(track => track.stop());
    } catch (err: any) {
      console.error('Error requesting camera:', err);
      
      if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
        setCameraPermission('denied');
        setCameraError(true);
        setErrorMessage('Akses kamera ditolak. Silakan aktifkan kamera di pengaturan browser Anda.');
        toast.error('Izin kamera ditolak');
      } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') {
        setCameraError(true);
        setErrorMessage('Tidak dapat menemukan kamera. Pastikan perangkat Anda memiliki kamera yang berfungsi.');
        toast.error('Kamera tidak ditemukan');
      } else {
        setCameraError(true);
        setErrorMessage(`Terjadi kesalahan saat mengakses kamera: ${err.message || 'Unknown error'}`);
        toast.error('Gagal mengakses kamera');
      }
    } finally {
      setIsRetrying(false);
    }
  };

  // Inisialisasi scanner
  const initScanner = async () => {
    try {
      // Periksa apakah elemen DOM ada
      const element = document.getElementById(scannerContainerId);
      if (!element) {
        console.error(`HTML Element with id=${scannerContainerId} not found, waiting for DOM...`);
        return;
      }

      if (scannerRef.current) {
        if (scannerRef.current.isScanning) {
          await scannerRef.current.stop();
        }
        scannerRef.current = null;
      }

      const html5QrCode = new Html5Qrcode(scannerContainerId);
      scannerRef.current = html5QrCode;
      hasScanner.current = true;
      
      const qrCodeSuccessCallback = async (decodedText: string) => {
        if (isScanning) {
          await stopScanner();
          setIsScanning(false);
          
          try {
            const response = await fetch('/api/attendance/scan', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                qrData: decodedText,
                scanType: scanType
              }),
            });
            
            const result = await response.json();
            
            if (response.ok) {
              // Scan berhasil normal
              setLastScan({ name: result.data.name, success: true, isDuplicate: false });
              toast.success(`${scanType === 'IN' ? 'Check-in' : 'Check-out'} berhasil untuk ${result.data.name}`);
              onScanSuccess(decodedText);
            } else if (response.status === 409 && result.data?.isDuplicate) {
              // Scan berhasil tetapi adalah duplikat (sudah pernah absen)
              setLastScan({ name: result.data.name, success: true, isDuplicate: true });
              toast.success(`${result.data.name} sudah ${scanType === 'IN' ? 'check-in' : 'check-out'} sebelumnya`);
              onScanSuccess(decodedText);
            } else {
              // Scan gagal karena alasan lain
              setLastScan({ name: 'Unknown', success: false });
              toast.error(result.error || 'Gagal memproses QR code');
            }
            
            // Auto reset scanner setelah 3 detik
            setTimeout(() => {
              setIsScanning(true);
              setLastScan(null);
              hasScanner.current = false;
              // initScanner dipanggil melalui useEffect saat isScanning berubah
            }, 3000);
          } catch (error) {
            console.error('Error scanning:', error);
            toast.error('Gagal memproses QR code');
            
            setTimeout(() => {
              setIsScanning(true);
              setLastScan(null);
              hasScanner.current = false;
              // initScanner dipanggil melalui useEffect saat isScanning berubah
            }, 3000);
          }
        }
      };

      const qrCodeErrorCallback = (errorMessage: string) => {
        // Ignoring error messages during normal scanning
        // Only log if it's not a normal scanning error
        if (errorMessage.includes("NotAllowedError") || 
            errorMessage.includes("PermissionDeniedError")) {
          setCameraPermission('denied');
          setCameraError(true);
          setErrorMessage('Akses kamera ditolak. Silakan aktifkan kamera di pengaturan browser Anda.');
          toast.error('Izin kamera ditolak');
          stopScanner();
        }
      };

      const config = { 
        fps: 10,
        qrbox: { width: 250, height: 250 },
        aspectRatio: 1,
        disableFlip: false,
        videoConstraints: {
          facingMode: "environment"
        }
      };

      await html5QrCode.start(
        { facingMode: "environment" }, 
        config,
        qrCodeSuccessCallback,
        qrCodeErrorCallback
      );
      
    } catch (err) {
      console.error("Error starting scanner:", err);
      hasScanner.current = false;
      handleScannerError(err);
    }
  };

  // Stop scanner
  const stopScanner = async () => {
    if (scannerRef.current && scannerRef.current.isScanning) {
      try {
        await scannerRef.current.stop();
        hasScanner.current = false;
      } catch (err) {
        console.error("Error stopping scanner:", err);
      }
    }
  };

  // Handler untuk error scanner
  const handleScannerError = (err: any) => {
    console.error('QR scan error:', err);
    
    // Deteksi jenis error kamera
    if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') {
      setCameraError(true);
      setErrorMessage('Kamera tidak ditemukan. Pastikan perangkat Anda memiliki kamera dan izin kamera diberikan.');
      toast.error('Kamera tidak tersedia');
    } else if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
      setCameraPermission('denied');
      setCameraError(true);
      setErrorMessage('Akses kamera ditolak. Silakan aktifkan kamera di pengaturan browser Anda.');
      toast.error('Izin kamera ditolak');
    } else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') {
      setCameraError(true);
      setErrorMessage('Kamera tidak dapat diakses. Perangkat kamera mungkin sedang digunakan aplikasi lain.');
      toast.error('Kamera tidak dapat diakses');
    } else {
      setCameraError(true);
      setErrorMessage('Terjadi kesalahan saat mengakses kamera.');
      toast.error('Gagal memulai kamera');
    }
  };

  // Fungsi untuk mencoba ulang kamera
  const retryCamera = () => {
    // Untuk pengguna yang telah menolak izin, buka pengaturan browser
    if (cameraPermission === 'denied') {
      alert('Anda telah menolak akses kamera. Silakan buka pengaturan browser dan aktifkan izin kamera untuk situs ini, kemudian muat ulang halaman.');
      return;
    }
    
    // Untuk kasus lain, coba minta izin lagi
    requestCameraAccess();
  };

  return (
    <div className="flex flex-col items-center">
      <div className={`relative rounded-lg overflow-hidden border-4 ${
        scanType === 'IN' 
          ? 'border-emerald-400' 
          : 'border-blue-400'
      } shadow-md mb-6`}>
        {cameraPermission === 'checking' ? (
          // Tampilan loading saat memeriksa izin
          <div 
            style={{ 
              width: '400px', 
              height: '400px',
              backgroundColor: '#f9f9f9'
            }}
            className="flex items-center justify-center"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="text-center p-6"
            >
              <ArrowPathIcon className="w-16 h-16 mx-auto mb-4 text-gray-500 animate-spin" />
              <p className="text-lg font-medium text-gray-700 mb-2">Memeriksa Izin Kamera...</p>
              <p className="text-gray-600 mb-6 text-sm">Harap tunggu sementara kami memeriksa izin kamera.</p>
            </motion.div>
          </div>
        ) : cameraPermission === 'prompt' ? (
          // Tampilan permintaan izin
          <div 
            style={{ 
              width: '400px', 
              height: '400px',
              backgroundColor: '#f9f9f9'
            }}
            className="flex items-center justify-center"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="text-center p-6"
            >
              <LockClosedIcon className="w-16 h-16 mx-auto mb-4 text-blue-500" />
              <p className="text-lg font-medium text-gray-700 mb-2">Izin Kamera Diperlukan</p>
              <p className="text-gray-600 mb-6 text-sm">
                Aplikasi ini memerlukan akses ke kamera untuk memindai QR code. 
                Silakan klik tombol di bawah untuk memberikan izin.
              </p>
              <button
                onClick={requestCameraAccess}
                disabled={isRetrying}
                className="px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 flex items-center justify-center mx-auto"
              >
                {isRetrying ? (
                  <ArrowPathIcon className="w-5 h-5 animate-spin mr-2" />
                ) : (
                  <LockOpenIcon className="w-5 h-5 mr-2" />
                )}
                {isRetrying ? 'Meminta Izin...' : 'Izinkan Akses Kamera'}
              </button>
            </motion.div>
          </div>
        ) : cameraError ? (
          <div 
            style={{ 
              width: '400px', 
              height: '400px',
              backgroundColor: '#f9f9f9'
            }}
            className="flex items-center justify-center"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="text-center p-6"
            >
              <ExclamationTriangleIcon className="w-16 h-16 mx-auto mb-4 text-amber-500" />
              <p className="text-lg font-medium text-gray-700 mb-2">Kamera Tidak Tersedia</p>
              <p className="text-gray-600 mb-6 text-sm">{errorMessage}</p>
              <button
                onClick={retryCamera}
                disabled={isRetrying}
                className="px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 flex items-center justify-center mx-auto"
              >
                {isRetrying ? (
                  <ArrowPathIcon className="w-5 h-5 animate-spin mr-2" />
                ) : (
                  <CameraIcon className="w-5 h-5 mr-2" />
                )}
                {isRetrying ? 'Mencoba ulang...' : 'Coba Lagi'}
              </button>
            </motion.div>
          </div>
        ) : isScanning ? (
          <div className="relative" style={{ width: '400px', height: '400px' }}>
            <div 
              id={scannerContainerId} 
              style={{ width: '400px', height: '400px' }}
              className="qr-scanner-container"
            />
            <div 
              className="absolute inset-0 pointer-events-none"
              style={{ 
                border: '2px dashed white', 
                borderRadius: '8px',
                margin: '40px',
                boxShadow: 'inset 0 0 0 3px rgba(0, 0, 0, 0.1)'
              }}
            ></div>
            <div className="absolute top-0 left-0 right-0 py-3 px-4 bg-black bg-opacity-50 text-white text-center">
              {scanType === 'IN' ? 'Scan QR untuk Absen Masuk' : 'Scan QR untuk Absen Keluar'}
            </div>
            
            {/* Pulse animation */}
            <motion.div 
              className="absolute inset-0"
              animate={{
                boxShadow: [
                  'inset 0 0 0 3px rgba(255, 255, 255, 0)',
                  'inset 0 0 0 3px rgba(255, 255, 255, 0.5)',
                  'inset 0 0 0 3px rgba(255, 255, 255, 0)',
                ]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>
        ) : (
          <div 
            style={{ 
              width: '400px', 
              height: '400px',
              backgroundColor: lastScan?.success 
                ? '#dcfce7' // green-100 hex equivalent 
                : '#fee2e2' // red-100 hex equivalent
            }}
            className="flex items-center justify-center"
          >
            <AnimatePresence>
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.8, opacity: 0 }}
                className="text-center p-6"
              >
                {lastScan?.success ? (
                  <>
                    <CheckCircleIcon className="w-16 h-16 mx-auto mb-4 text-emerald-500" />
                    <p className="text-lg font-medium text-emerald-700">
                      {lastScan.isDuplicate ? 'Sudah Terabsen!' : 'Berhasil!'}
                    </p>
                    <p className="text-emerald-600">{lastScan.name}</p>
                    {lastScan.isDuplicate && (
                      <p className="text-sm text-emerald-500 mt-1">
                        {`Sudah melakukan absensi ${scanType === 'IN' ? 'masuk' : 'keluar'} hari ini`}
                      </p>
                    )}
                  </>
                ) : (
                  <>
                    <XCircleIcon className="w-16 h-16 mx-auto mb-4 text-red-500" />
                    <p className="text-lg font-medium text-red-700">Gagal!</p>
                    <p className="text-red-600">QR Code tidak valid</p>
                  </>
                )}
                <motion.div
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="mt-6 text-gray-500"
                >
                  <ArrowPathIcon className="w-6 h-6 mx-auto animate-spin" />
                  <p className="mt-1">Scanning akan dilanjutkan dalam 3 detik...</p>
                </motion.div>
              </motion.div>
            </AnimatePresence>
          </div>
        )}
      </div>
      <div 
        className="text-sm bg-gray-50 border border-gray-200 p-4 rounded-lg text-gray-600 max-w-md"
      >
        <p className="mb-2 font-medium">{scanType === 'IN' ? 'Absen Masuk' : 'Absen Keluar'}</p>
        <ul className="list-disc pl-5 space-y-1">
          <li>Arahkan kamera ke QR code pada ID card terapis/staff</li>
          <li>Pastikan QR code terlihat jelas dan tidak terhalang</li>
          <li>Konfirmasi akan muncul setelah QR code berhasil dipindai</li>
          {cameraPermission === 'denied' && (
            <li className="text-amber-600 font-semibold">
              Akses kamera ditolak! Aktifkan izin kamera di pengaturan browser Anda dan muat ulang halaman.
            </li>
          )}
        </ul>
      </div>
    </div>
  );
};

export default QRScanner; 