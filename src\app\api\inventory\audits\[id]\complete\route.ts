import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserIdFromToken } from '@/lib/auth-utils';

// PATCH - Selesaikan audit dan update stock berdasarkan hasil audit
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = getUserIdFromToken(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { updateStock = false, notes } = body;

    // Cek apakah audit ada
    const existingAudit = await prisma.inventoryAudit.findUnique({
      where: { id: params.id },
      include: {
        auditItems: {
          include: {
            item: true
          }
        }
      }
    });

    if (!existingAudit) {
      return NextResponse.json(
        { error: 'Audit inventori tidak ditemukan' },
        { status: 404 }
      );
    }

    // Cek apakah audit sudah selesai
    if (existingAudit.status === 'COMPLETED') {
      return NextResponse.json(
        { error: 'Audit sudah selesai' },
        { status: 400 }
      );
    }

    // Cek apakah semua item sudah dicek
    const uncheckedItems = existingAudit.auditItems.filter(item => !item.checkedAt);
    if (uncheckedItems.length > 0) {
      return NextResponse.json(
        { error: `Masih ada ${uncheckedItems.length} item yang belum dicek` },
        { status: 400 }
      );
    }

    // Gunakan transaction untuk memastikan konsistensi data
    const result = await prisma.$transaction(async (tx) => {
      // Update status audit menjadi COMPLETED
      const completedAudit = await tx.inventoryAudit.update({
        where: { id: params.id },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
          notes: notes?.trim() || existingAudit.notes
        }
      });

      // Jika diminta untuk update stock, update berdasarkan hasil audit
      if (updateStock) {
        const stockUpdates = [];
        
        for (const auditItem of existingAudit.auditItems) {
          if (auditItem.actualGood !== null || 
              auditItem.actualDamaged !== null || 
              auditItem.actualLost !== null) {
            
            const updateData: any = {};
            let hasChanges = false;
            
            if (auditItem.actualGood !== null) {
              updateData.goodCondition = auditItem.actualGood;
              hasChanges = true;
            }
            
            if (auditItem.actualDamaged !== null) {
              updateData.damagedCondition = auditItem.actualDamaged;
              hasChanges = true;
            }
            
            if (auditItem.actualLost !== null) {
              updateData.lostCondition = auditItem.actualLost;
              hasChanges = true;
            }
            
            if (hasChanges) {
              // Hitung remaining stock
              const goodCondition = auditItem.actualGood ?? auditItem.item.goodCondition;
              const damaged = auditItem.actualDamaged ?? auditItem.item.damagedCondition;
              const lost = auditItem.actualLost ?? auditItem.item.lostCondition;
              const remaining = Math.max(0, goodCondition - damaged - lost);
              
              updateData.remainingQuantity = remaining;
              
              // Update item
              await tx.inventoryItem.update({
                where: { id: auditItem.itemId },
                data: updateData
              });
              
              // Buat movement record untuk adjustment
              const oldTotal = auditItem.item.goodCondition + auditItem.item.damagedCondition + auditItem.item.lostCondition;
              const newTotal = (auditItem.actualGood ?? auditItem.item.goodCondition) + 
                              (auditItem.actualDamaged ?? auditItem.item.damagedCondition) + 
                              (auditItem.actualLost ?? auditItem.item.lostCondition);
              
              const quantityDiff = newTotal - oldTotal;
              
              if (quantityDiff !== 0) {
                await tx.inventoryMovement.create({
                  data: {
                    itemId: auditItem.itemId,
                    movementType: quantityDiff > 0 ? 'IN' : 'ADJUST',
                    quantity: Math.abs(quantityDiff),
                    reason: `Penyesuaian stock dari audit: ${existingAudit.title}`,
                    createdById: userId
                  }
                });
              }
              
              stockUpdates.push({
                itemId: auditItem.itemId,
                itemName: auditItem.item.name,
                quantityDiff
              });
            }
          }
        }
        
        return { audit: completedAudit, stockUpdates };
      }
      
      return { audit: completedAudit, stockUpdates: [] };
    });

    // Ambil audit lengkap dengan relasi
    const fullAudit = await prisma.inventoryAudit.findUnique({
      where: { id: params.id },
      include: {
        outlet: true,
        createdBy: {
          select: {
            id: true,
            name: true
          }
        },

        auditItems: {
          include: {
            item: {
              include: {
                category: true
              }
            },
            checkedBy: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      message: updateStock 
        ? `Audit berhasil diselesaikan dan ${result.stockUpdates.length} item stock telah diperbarui`
        : 'Audit berhasil diselesaikan',
      audit: fullAudit,
      stockUpdates: result.stockUpdates
    });
  } catch (error) {
    console.error('Error completing inventory audit:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menyelesaikan audit inventori' },
      { status: 500 }
    );
  }
}

// POST - Complete audit inventori dan update stock
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const userId = getUserIdFromToken(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    // Cek apakah audit ada dan statusnya IN_PROGRESS
    const existingAudit = await prisma.inventoryAudit.findUnique({
      where: { id },
      include: {
        outlet: true,
        auditItems: {
          include: {
            item: true
          }
        }
      }
    });

    if (!existingAudit) {
      return NextResponse.json(
        { error: 'Audit inventori tidak ditemukan' },
        { status: 404 }
      );
    }

    if (existingAudit.status !== 'IN_PROGRESS') {
      return NextResponse.json(
        { error: 'Audit harus dalam status "Sedang Berlangsung" untuk dapat diselesaikan' },
        { status: 400 }
      );
    }

    // Cek apakah semua item sudah dicek
    const uncheckedItems = existingAudit.auditItems.filter(item => !item.isChecked);
    if (uncheckedItems.length > 0) {
      return NextResponse.json(
        { error: `Masih ada ${uncheckedItems.length} item yang belum dicek` },
        { status: 400 }
      );
    }

    // Update stock untuk setiap item berdasarkan hasil audit
    for (const auditItem of existingAudit.auditItems) {
      const newGoodCondition = auditItem.actualGood;
      const newDamagedCondition = auditItem.actualDamaged;
      const newLostCondition = auditItem.actualLost;
      const newRemainingQuantity = Math.max(0, newGoodCondition - newDamagedCondition - newLostCondition);

      await prisma.inventoryItem.update({
        where: { id: auditItem.itemId },
        data: {
          goodCondition: newGoodCondition,
          damagedCondition: newDamagedCondition,
          lostCondition: newLostCondition,
          remainingQuantity: newRemainingQuantity
        }
      });

      // Buat record movement untuk mencatat perubahan stok
      const variance = auditItem.variance;
      if (variance !== 0) {
        await prisma.inventoryMovement.create({
          data: {
            itemId: auditItem.itemId,
            movementType: variance > 0 ? 'STOCK_ADJUSTMENT_IN' : 'STOCK_ADJUSTMENT_OUT',
            quantity: Math.abs(variance),
            reason: `Penyesuaian stok dari audit: ${existingAudit.title}`,
            notes: auditItem.notes,
            userId: userId
          }
        });
      }
    }

    // Update status audit menjadi COMPLETED
    const completedAudit = await prisma.inventoryAudit.update({
      where: { id },
      data: {
        status: 'COMPLETED',
        completedAt: new Date()
      },
      include: {
        outlet: true,
        createdBy: {
          select: {
            id: true,
            name: true
          }
        },
        auditItems: {
          include: {
            item: {
              include: {
                category: true
              }
            },
            checkedBy: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      message: 'Audit inventori berhasil diselesaikan dan stok telah diperbarui',
      audit: completedAudit
    });
  } catch (error) {
    console.error('Error completing inventory audit:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menyelesaikan audit inventori' },
      { status: 500 }
    );
  }
}