import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { <PERSON><PERSON><PERSON><PERSON>, FiRefreshCw, FiCalendar, FiClock, FiUser, FiMapPin, FiTag, FiInfo } from 'react-icons/fi';
import LoadingSpinner from './LoadingSpinner';

interface SystemLog {
  id: string;
  type: string;
  message: string;
  details: any;
  outletId: string | null;
  userId: string | null;
  createdAt: string;
  outlet: {
    id: string;
    name: string;
  } | null;
  user: {
    id: string;
    name: string;
    email: string;
  } | null;
}

interface Outlet {
  id: string;
  name: string;
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export default function SystemLogs() {
  // Style untuk tooltip
  useEffect(() => {
    // Tambahkan style untuk tooltip
    const style = document.createElement('style');
    style.id = 'system-logs-tooltip-style';
    style.innerHTML = `
      .tooltip:before {
        max-width: 300px;
        font-size: 0.75rem;
        line-height: 1.25;
        padding: 0.5rem;
        border-radius: 0.375rem;
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        z-index: 50;
      }
    `;
    document.head.appendChild(style);

    // Cleanup style saat komponen unmount
    return () => {
      const existingStyle = document.getElementById('system-logs-tooltip-style');
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, []);
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [logTypes, setLogTypes] = useState<string[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 50,
    totalPages: 0
  });

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filter state
  const [selectedOutletId, setSelectedOutletId] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);

  // Fungsi untuk memuat data log
  const fetchLogs = async (page = 1) => {
    setIsLoading(true);
    setError(null);
    setCurrentPage(page);

    try {
      // Buat URL dengan parameter filter
      let url = `/api/logs?page=${page}&limit=${pagination.limit}`;

      if (selectedOutletId !== 'all') {
        url += `&outletId=${selectedOutletId}`;
      }

      if (selectedType) {
        url += `&type=${selectedType}`;
      }

      if (startDate) {
        url += `&startDate=${startDate}`;
      }

      if (endDate) {
        url += `&endDate=${endDate}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('Gagal memuat data log');
      }

      const data = await response.json();
      setLogs(data.logs);
      setPagination(data.pagination);
      setOutlets(data.outlets || []);
      setLogTypes(data.logTypes || []);
    } catch (err) {
      console.error('Error fetching logs:', err);
      setError('Terjadi kesalahan saat memuat data log');
      toast.error('Gagal memuat data log');
    } finally {
      setIsLoading(false);
    }
  };

  // Muat data log saat mount dan saat filter berubah
  useEffect(() => {
    fetchLogs(1);
  }, [selectedOutletId, selectedType, startDate, endDate]);

  // Fungsi untuk format tanggal
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  // Fungsi untuk format waktu
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // Fungsi untuk mendapatkan warna badge berdasarkan tipe log
  const getLogTypeBadgeColor = (type: string) => {
    switch (type.toUpperCase()) {
      case 'ERROR':
        return 'badge-error';
      case 'WARNING':
        return 'badge-warning';
      case 'INFO':
        return 'badge-info';
      case 'SUCCESS':
        return 'badge-success';
      case 'SYSTEM':
        return 'badge-secondary';
      case 'AUTH':
        return 'badge-primary';
      case 'CREATE':
        return 'badge-success';
      case 'UPDATE':
        return 'badge-info';
      case 'DELETE':
        return 'badge-error';
      case 'BOOKING':
        return 'badge-accent';
      case 'TRANSACTION':
        return 'badge-secondary';
      case 'THERAPIST':
        return 'badge-primary';
      case 'SERVICE':
        return 'badge-accent';
      case 'OUTLET':
        return 'badge-secondary';
      case 'USER':
        return 'badge-primary';
      default:
        return 'badge-ghost';
    }
  };

  // Fungsi untuk memformat detail log menjadi tooltip yang lebih mudah dibaca
  const formatLogDetails = (details: any): string => {
    if (!details) return '';

    try {
      // Jika details sudah dalam bentuk string, parse dulu
      const detailsObj = typeof details === 'string' ? JSON.parse(details) : details;

      // Ekstrak informasi penting saja
      const simplifiedDetails: Record<string, any> = {};

      // Booking info
      if (detailsObj.bookingDate) {
        // Format tanggal dengan lebih singkat
        const date = new Date(detailsObj.bookingDate);
        simplifiedDetails.Tgl = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
      }
      if (detailsObj.bookingTime) {
        simplifiedDetails.Jam = detailsObj.bookingTime;
      }
      if (detailsObj.status) {
        // Terjemahkan status ke bahasa Indonesia
        const statusMap: Record<string, string> = {
          'PENDING': 'Menunggu',
          'CONFIRMED': 'Dikonfirmasi',
          'COMPLETED': 'Selesai',
          'CANCELLED': 'Dibatalkan',
          'NO_SHOW': 'Tidak Hadir'
        };
        simplifiedDetails.Status = statusMap[detailsObj.status] || detailsObj.status;
      }

      // Customer info
      if (detailsObj.customer?.name) {
        simplifiedDetails.Pelanggan = detailsObj.customer.name;
      } else if (detailsObj.customerName) {
        simplifiedDetails.Pelanggan = detailsObj.customerName;
      }

      // Therapist info
      if (detailsObj.therapist?.name) {
        simplifiedDetails.Terapis = detailsObj.therapist.name;
      } else if (detailsObj.therapistName) {
        simplifiedDetails.Terapis = detailsObj.therapistName;
      }

      // Service info
      if (detailsObj.service?.name) {
        simplifiedDetails.Layanan = detailsObj.service.name;
      } else if (detailsObj.serviceName) {
        simplifiedDetails.Layanan = detailsObj.serviceName;
      } else if (detailsObj.services && Array.isArray(detailsObj.services)) {
        // Jika ada multiple services, ambil nama service pertama saja
        if (detailsObj.services.length > 0) {
          const firstService = detailsObj.services[0];
          simplifiedDetails.Layanan = firstService.name || 'Layanan tidak diketahui';
        }
      }

      // Transaction info
      if (detailsObj.totalAmount) {
        simplifiedDetails.Total = `Rp ${Number(detailsObj.totalAmount).toLocaleString('id-ID')}`;
      }
      if (detailsObj.paymentMethod) {
        // Terjemahkan metode pembayaran ke bahasa Indonesia
        const paymentMap: Record<string, string> = {
          'CASH': 'Tunai',
          'DEBIT': 'Debit',
          'CREDIT': 'Kredit',
          'TRANSFER': 'Transfer',
          'QRIS': 'QRIS',
          'SPLIT': 'Split'
        };
        simplifiedDetails.Pembayaran = paymentMap[detailsObj.paymentMethod] || detailsObj.paymentMethod;
      }

      // Format menjadi string yang mudah dibaca
      return Object.entries(simplifiedDetails)
        .map(([key, value]) => `${key}: ${value}`)
        .join('\n');
    } catch (error) {
      // Jika gagal parsing, tampilkan sebagai string biasa dengan format yang lebih baik
      console.error('Error formatting log details:', error);
      try {
        // Coba format JSON dengan lebih baik
        const formatted = typeof details === 'string' ? details : JSON.stringify(details, null, 2);
        // Batasi panjang string
        return formatted.length > 200 ? formatted.substring(0, 200) + '...' : formatted;
      } catch {
        return 'Detail tidak dapat ditampilkan';
      }
    }
  };

  // Fungsi untuk reset filter
  const resetFilters = () => {
    setSelectedOutletId('all');
    setSelectedType('');
    setStartDate('');
    setEndDate('');
    fetchLogs(1);
  };

  // Fungsi untuk set tanggal hari ini
  const setTodayFilter = () => {
    const today = new Date().toISOString().split('T')[0];
    setStartDate(today);
    setEndDate(today);
  };

  // Fungsi untuk set tanggal minggu ini
  const setThisWeekFilter = () => {
    const today = new Date();
    const firstDay = new Date(today.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1)));
    const lastDay = new Date();

    setStartDate(firstDay.toISOString().split('T')[0]);
    setEndDate(lastDay.toISOString().split('T')[0]);
  };

  // Fungsi untuk set tanggal bulan ini
  const setThisMonthFilter = () => {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date();

    setStartDate(firstDay.toISOString().split('T')[0]);
    setEndDate(lastDay.toISOString().split('T')[0]);
  };

  // Render pagination
  const renderPagination = () => {
    if (pagination.totalPages <= 1) return null;

    const pageNumbers = [];
    // Pada layar kecil tampilkan lebih sedikit halaman
    // Gunakan nilai default 5 untuk menghindari error saat SSR
    const maxVisiblePages = typeof window !== 'undefined' && window.innerWidth < 640 ? 3 : 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(pagination.totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    return (
      <div className="flex justify-center mt-6 px-2">
        <div className="join flex-wrap justify-center">
          <button
            className="join-item btn btn-sm"
            onClick={() => fetchLogs(1)}
            disabled={currentPage === 1}
            aria-label="First page"
          >
            «
          </button>
          <button
            className="join-item btn btn-sm"
            onClick={() => fetchLogs(currentPage - 1)}
            disabled={currentPage === 1}
            aria-label="Previous page"
          >
            ‹
          </button>

          {/* Pada layar kecil, tampilkan hanya halaman saat ini */}
          <span className="join-item btn btn-sm sm:hidden">
            {currentPage} / {pagination.totalPages}
          </span>

          {/* Pada layar lebih besar, tampilkan nomor halaman */}
          {pageNumbers.map(number => (
            <button
              key={number}
              className={`join-item btn btn-sm hidden sm:inline-flex ${currentPage === number ? 'btn-active' : ''}`}
              onClick={() => fetchLogs(number)}
            >
              {number}
            </button>
          ))}

          <button
            className="join-item btn btn-sm"
            onClick={() => fetchLogs(currentPage + 1)}
            disabled={currentPage === pagination.totalPages}
            aria-label="Next page"
          >
            ›
          </button>
          <button
            className="join-item btn btn-sm"
            onClick={() => fetchLogs(pagination.totalPages)}
            disabled={currentPage === pagination.totalPages}
            aria-label="Last page"
          >
            »
          </button>
        </div>
      </div>
    );
  };

  // Tampilkan loading spinner jika masih memuat
  if (isLoading && logs.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[500px]">
        <LoadingSpinner />
        <p className="mt-4 text-gray-500">Memuat data log...</p>
      </div>
    );
  }

  // Tampilkan pesan error jika ada
  if (error && logs.length === 0) {
    return (
      <div className="p-8 text-center">
        <div className="text-red-500 mb-4">{error}</div>
        <button
          className="btn btn-primary"
          onClick={() => fetchLogs(1)}
        >
          Coba Lagi
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-4 sm:p-6 border-b">
        <h3 className="text-lg font-medium flex items-center">
          <FiInfo className="w-5 h-5 mr-2" />
          Log Sistem
        </h3>
        <p className="text-sm text-gray-500 mt-1">
          Lihat aktivitas dan log sistem dari semua outlet
        </p>
      </div>

      {/* Filter Section */}
      <div className="p-4 sm:p-6 border-b">
        <div className="flex flex-col md:flex-row gap-4 flex-wrap">
          <div className="form-control w-full md:w-auto md:flex-1">
            <label className="label">
              <span className="label-text flex items-center">
                <FiMapPin className="mr-1" /> Outlet
              </span>
            </label>
            <select
              className="select select-bordered w-full"
              value={selectedOutletId}
              onChange={(e) => setSelectedOutletId(e.target.value)}
            >
              <option value="all">Semua Outlet</option>
              {outlets.map((outlet) => (
                <option key={outlet.id} value={outlet.id}>
                  {outlet.name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-control w-full md:w-auto md:flex-1">
            <label className="label">
              <span className="label-text flex items-center">
                <FiTag className="mr-1" /> Tipe Log
              </span>
            </label>
            <select
              className="select select-bordered w-full"
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
            >
              <option value="">Semua Tipe</option>
              {logTypes.map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
          </div>

          <div className="form-control w-full md:w-auto md:flex-1">
            <label className="label">
              <span className="label-text flex items-center">
                <FiCalendar className="mr-1" /> Tanggal Mulai
              </span>
            </label>
            <input
              type="date"
              className="input input-bordered w-full"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
            />
          </div>

          <div className="form-control w-full md:w-auto md:flex-1">
            <label className="label">
              <span className="label-text flex items-center">
                <FiCalendar className="mr-1" /> Tanggal Akhir
              </span>
            </label>
            <input
              type="date"
              className="input input-bordered w-full"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
            />
          </div>
        </div>

        <div className="flex flex-wrap gap-2 mt-4">
          <button
            className="btn btn-sm btn-outline"
            onClick={setTodayFilter}
          >
            Hari Ini
          </button>
          <button
            className="btn btn-sm btn-outline"
            onClick={setThisWeekFilter}
          >
            Minggu Ini
          </button>
          <button
            className="btn btn-sm btn-outline"
            onClick={setThisMonthFilter}
          >
            Bulan Ini
          </button>
          <button
            className="btn btn-sm btn-outline"
            onClick={resetFilters}
          >
            <FiFilter className="mr-1" /> Reset Filter
          </button>
          <button
            className="btn btn-sm btn-primary"
            onClick={() => fetchLogs(currentPage)}
            disabled={isLoading}
          >
            <FiRefreshCw className={`mr-1 ${isLoading ? 'animate-spin' : ''}`} /> Refresh
          </button>
        </div>
      </div>

      {/* Log Table */}
      <div className="overflow-x-auto">
        <table className="table table-zebra w-full">
          <thead>
            <tr>
              <th className="w-auto sm:w-1/6">Waktu</th>
              <th className="w-auto sm:w-1/12">Tipe</th>
              <th className="w-auto sm:w-1/6 hidden sm:table-cell">Outlet</th>
              <th className="w-auto sm:w-1/6">User</th>
              <th className="w-auto">Pesan</th>
            </tr>
          </thead>
          <tbody>
            {logs.length === 0 ? (
              <tr>
                <td colSpan={5} className="text-center py-8">
                  Tidak ada data log yang ditemukan
                </td>
              </tr>
            ) : (
              logs.map((log) => (
                <tr key={log.id} className="hover">
                  <td className="whitespace-normal">
                    <div className="flex flex-col">
                      <span className="font-medium flex items-center text-xs sm:text-sm">
                        <FiCalendar className="mr-1 text-gray-500 flex-shrink-0" /> {formatDate(log.createdAt)}
                      </span>
                      <span className="text-xs text-gray-500 flex items-center">
                        <FiClock className="mr-1 flex-shrink-0" /> {formatTime(log.createdAt)}
                      </span>
                    </div>
                  </td>
                  <td>
                    <span className={`badge badge-sm sm:badge-md ${getLogTypeBadgeColor(log.type)} whitespace-nowrap`}>
                      {log.type}
                    </span>
                  </td>
                  <td className="hidden sm:table-cell">
                    {log.outlet ? (
                      <span className="flex items-center">
                        <FiMapPin className="mr-1 text-gray-500 flex-shrink-0" />
                        <span className="break-words">{log.outlet.name}</span>
                      </span>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </td>
                  <td>
                    {log.user ? (
                      <div className="flex flex-col">
                        <span className="flex items-center">
                          <FiUser className="mr-1 text-gray-500 flex-shrink-0" />
                          <span className="break-words">{log.user.name}</span>
                        </span>
                        <span className="text-xs text-gray-500 break-words w-full">
                          {log.user.email}
                        </span>
                      </div>
                    ) : (
                      <span className="text-gray-400 flex items-center">
                        <FiUser className="mr-1 text-gray-400 flex-shrink-0" /> System
                      </span>
                    )}
                  </td>
                  <td className="whitespace-normal">
                    <div
                      className="tooltip tooltip-left max-w-xs whitespace-pre-line"
                      data-tip={formatLogDetails(log.details)}
                    >
                      <div className="break-words text-sm cursor-help">{log.message}</div>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {renderPagination()}

      {/* Summary */}
      <div className="p-4 border-t text-xs sm:text-sm text-gray-500 break-words">
        <div className="flex flex-wrap gap-1">
          <span>Menampilkan {logs.length} dari {pagination.total} log</span>
          {selectedOutletId !== 'all' && outlets.find(o => o.id === selectedOutletId) && (
            <span className="whitespace-normal">
              untuk outlet {outlets.find(o => o.id === selectedOutletId)?.name}
            </span>
          )}
          {selectedType && (
            <span className="whitespace-normal">
              dengan tipe {selectedType}
            </span>
          )}
        </div>
        {startDate && endDate && (
          <div className="mt-1 whitespace-normal">
            dari tanggal {formatDate(startDate)} sampai {formatDate(endDate)}
          </div>
        )}
      </div>
    </div>
  );
}
