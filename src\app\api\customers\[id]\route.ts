import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET customer berdasarkan ID
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params untuk menghindari error di Next.js 15
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: 'ID customer diperlukan' },
        { status: 400 }
      );
    }

    // Ambil detail customer berdasarkan ID
    const customer = await prisma.customer.findUnique({
      where: {
        id
      },
      include: {
        bookings: {
          orderBy: {
            createdAt: 'desc'
          },
          include: {
            service: {
              select: {
                id: true,
                name: true,
                price: true,
                outlet: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            },
            therapist: {
              select: {
                id: true,
                name: true
              }
            },
            transaction: {
              select: {
                status: true,
                paymentMethod: true,
                updatedAt: true
              }
            }
          }
        },
        _count: {
          select: {
            bookings: true
          }
        }
      }
    });

    // Jika customer tidak ditemukan
    if (!customer) {
      return NextResponse.json(
        { error: 'Customer tidak ditemukan' },
        { status: 404 }
      );
    }

    // Hitung total yang dihabiskan customer
    const totalSpent = customer.bookings.reduce((total, booking) => {
      if (booking.transaction?.status === 'COMPLETED') {
        return total + (booking.price || 0);
      }
      return total;
    }, 0);

    // Kembalikan data customer
    return NextResponse.json({
      message: 'Data customer berhasil diambil',
      customer: {
        ...customer,
        totalSpent
      }
    });
  } catch (error) {
    console.error('Error fetching customer details:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil detail customer' },
      { status: 500 }
    );
  }
}

// PUT untuk mengupdate customer
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params untuk menghindari error di Next.js 15
    const { id } = await params;
    const body = await request.json();
    const { name, email, phone, birthdate, address, gender } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID customer diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah customer ada
    const existingCustomer = await prisma.customer.findUnique({
      where: {
        id
      }
    });

    if (!existingCustomer) {
      return NextResponse.json(
        { error: 'Customer tidak ditemukan' },
        { status: 404 }
      );
    }

    // Cek duplikat email jika diubah
    if (email && email !== existingCustomer.email) {
      const existingEmail = await prisma.customer.findFirst({
        where: {
          email,
          id: { not: id }
        }
      });

      if (existingEmail) {
        return NextResponse.json(
          { error: 'Email sudah terdaftar oleh customer lain' },
          { status: 400 }
        );
      }
    }

    // Cek duplikat nomor telepon jika diubah
    if (phone && phone !== existingCustomer.phone) {
      const existingPhone = await prisma.customer.findFirst({
        where: {
          phone,
          id: { not: id }
        }
      });

      if (existingPhone) {
        return NextResponse.json(
          { error: 'Nomor telepon sudah terdaftar oleh customer lain' },
          { status: 400 }
        );
      }
    }

    // Update customer
    const updatedCustomer = await prisma.customer.update({
      where: {
        id
      },
      data: {
        ...(name && { name }),
        ...(email !== undefined && { email }),
        ...(phone && { phone }),
        ...(birthdate !== undefined && {
          birthdate: birthdate ? new Date(birthdate) : null
        }),
        ...(address !== undefined && { address }),
        ...(gender !== undefined && { gender })
      }
    });

    return NextResponse.json({
      message: 'Customer berhasil diupdate',
      customer: updatedCustomer
    });
  } catch (error) {
    console.error('Error updating customer:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengupdate customer' },
      { status: 500 }
    );
  }
}

// DELETE customer (soft delete atau archive)
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params untuk menghindari error di Next.js 15
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: 'ID customer diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah customer ada
    const existingCustomer = await prisma.customer.findUnique({
      where: {
        id
      },
      include: {
        bookings: {
          where: {
            status: {
              in: ['PENDING', 'CONFIRMED']
            }
          }
        }
      }
    });

    if (!existingCustomer) {
      return NextResponse.json(
        { error: 'Customer tidak ditemukan' },
        { status: 404 }
      );
    }

    // Cek apakah customer memiliki booking aktif
    if (existingCustomer.bookings.length > 0) {
      return NextResponse.json(
        { error: 'Customer memiliki booking aktif dan tidak dapat dihapus' },
        { status: 400 }
      );
    }

    // Hard delete customer
    // Catatan: Dalam aplikasi sebenarnya, sebaiknya gunakan soft delete
    const deletedCustomer = await prisma.customer.delete({
      where: {
        id
      }
    });

    return NextResponse.json({
      message: 'Customer berhasil dihapus',
      customer: deletedCustomer
    });
  } catch (error) {
    console.error('Error deleting customer:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus customer' },
      { status: 500 }
    );
  }
}