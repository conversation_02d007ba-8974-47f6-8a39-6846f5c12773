import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET: Mendapatkan semua booking
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');
    const date = searchParams.get('date');
    
    let whereClause: any = {};
    
    if (outletId) {
      whereClause.outletId = outletId;
    }
    
    if (date) {
      whereClause.scheduledDate = date;
    }
    
    const bookings = await prisma.booking.findMany({
      where: whereClause,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            phone: true
          }
        },
        therapist: {
          select: {
            id: true,
            name: true,
            specialty: true
          }
        },
        services: {
          select: {
            id: true,
            name: true,
            duration: true,
            price: true
          }
        },
        outlet: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: [
        { scheduledDate: 'asc' },
        { scheduledTime: 'asc' }
      ]
    });
    
    return NextResponse.json(bookings);
  } catch (error) {
    console.error("Gagal mengambil data booking:", error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data booking' },
      { status: 500 }
    );
  }
}

// POST: Membuat booking baru
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { 
      outletId, 
      customerId, 
      therapistId, 
      serviceIds, 
      scheduledDate, 
      scheduledTime, 
      note 
    } = body;
    
    // Validasi data yang diperlukan
    if (!outletId || !customerId || !therapistId || !serviceIds || !scheduledDate || !scheduledTime) {
      return NextResponse.json(
        { error: 'Data booking tidak lengkap' },
        { status: 400 }
      );
    }
    
    // Buat ID booking unik
    const bookingId = `BK${Date.now()}`;
    
    // Simpan data booking
    const booking = await prisma.booking.create({
      data: {
        bookingId,
        bookingDateTime: new Date().toISOString(),
        scheduledDate,
        scheduledTime,
        note: note || '',
        outletId,
        customerId,
        therapistId,
        services: {
          connect: serviceIds.map((id: string) => ({ id }))
        }
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            phone: true
          }
        },
        therapist: {
          select: {
            id: true,
            name: true,
            specialty: true
          }
        },
        services: {
          select: {
            id: true,
            name: true,
            duration: true,
            price: true
          }
        },
        outlet: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });
    
    return NextResponse.json({
      message: 'Booking berhasil dibuat',
      booking
    });
  } catch (error) {
    console.error("Gagal membuat booking:", error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat booking' },
      { status: 500 }
    );
  }
}

// DELETE: Menghapus booking berdasarkan ID
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const bookingId = searchParams.get('bookingId');
    
    if (!bookingId) {
      return NextResponse.json(
        { error: 'ID booking tidak ditemukan' },
        { status: 400 }
      );
    }
    
    // Cek apakah booking ada
    const existingBooking = await prisma.booking.findUnique({
      where: { bookingId }
    });
    
    if (!existingBooking) {
      return NextResponse.json(
        { error: 'Booking tidak ditemukan' },
        { status: 404 }
      );
    }
    
    // Hapus relasi dengan service dulu
    await prisma.booking.update({
      where: { bookingId },
      data: {
        services: {
          set: []
        }
      }
    });
    
    // Hapus booking
    await prisma.booking.delete({
      where: { bookingId }
    });
    
    return NextResponse.json({
      message: 'Booking berhasil dihapus'
    });
  } catch (error) {
    console.error("Gagal menghapus booking:", error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus booking' },
      { status: 500 }
    );
  }
}

// PATCH: Update data booking
export async function PATCH(request: Request) {
  try {
    const body = await request.json();
    const { 
      bookingId,
      therapistId, 
      serviceIds, 
      scheduledDate, 
      scheduledTime,
      note 
    } = body;
    
    if (!bookingId) {
      return NextResponse.json(
        { error: 'ID booking diperlukan' },
        { status: 400 }
      );
    }
    
    // Cek apakah booking ada
    const existingBooking = await prisma.booking.findUnique({
      where: { bookingId }
    });
    
    if (!existingBooking) {
      return NextResponse.json(
        { error: 'Booking tidak ditemukan' },
        { status: 404 }
      );
    }
    
    // Update data
    const updateData: any = {};
    
    if (therapistId) updateData.therapistId = therapistId;
    if (scheduledDate) updateData.scheduledDate = scheduledDate;
    if (scheduledTime) updateData.scheduledTime = scheduledTime;
    if (note !== undefined) updateData.note = note;
    
    // Update booking
    const booking = await prisma.booking.update({
      where: { bookingId },
      data: updateData,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            phone: true
          }
        },
        therapist: {
          select: {
            id: true,
            name: true,
            specialty: true
          }
        },
        services: {
          select: {
            id: true,
            name: true,
            duration: true,
            price: true
          }
        },
        outlet: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });
    
    // Update services jika disediakan
    if (serviceIds && serviceIds.length > 0) {
      await prisma.booking.update({
        where: { bookingId },
        data: {
          services: {
            set: serviceIds.map((id: string) => ({ id }))
          }
        }
      });
    }
    
    return NextResponse.json({
      message: 'Booking berhasil diperbarui',
      booking
    });
  } catch (error) {
    console.error("Gagal memperbarui booking:", error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui booking' },
      { status: 500 }
    );
  }
} 