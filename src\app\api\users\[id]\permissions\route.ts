import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { logUser } from '@/lib/logger';
import { getUserIdFromToken } from '@/lib/auth-utils';
// Import tipe Permission dari Prisma Client jika diperlukan untuk validasi lebih ketat
// import { Prisma } from '@prisma/client';

// Asumsi struktur data permission yang dikirim dari frontend
interface PermissionInput {
  module: string;
  canCreate: boolean;
  canRead: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Pastikan params.id di-await
  const { id: userId } = await params;
  // TODO: Tambahkan validasi autentikasi & otorisasi jika perlu

  if (!userId) {
    return NextResponse.json({ error: 'User ID diperlukan.' }, { status: 400 });
  }

  try {
    const body = await request.json();
    const newPermissions: PermissionInput[] = body.permissions;

    // Validasi input permissions
    if (!Array.isArray(newPermissions)) {
      return NextResponse.json({ error: 'Format data permissions tidak valid (harus array).' }, { status: 400 });
    }
    // Validasi lebih lanjut per item (opsional tapi bagus)
    const isValid = newPermissions.every(p =>
        typeof p.module === 'string' && p.module.trim() !== '' && // Pastikan module tidak string kosong
        typeof p.canCreate === 'boolean' &&
        typeof p.canRead === 'boolean' &&
        typeof p.canUpdate === 'boolean' &&
        typeof p.canDelete === 'boolean'
    );
    if (!isValid) {
         return NextResponse.json({ error: 'Struktur data setiap item permission tidak valid (pastikan semua field ada dan module tidak kosong).' }, { status: 400 });
    }

    // Pastikan user ada
    const userExists = await prisma.user.findUnique({ where: { id: userId } });
    if (!userExists) {
        return NextResponse.json({ error: 'User tidak ditemukan.' }, { status: 404 });
    }

    console.log(`Updating permissions for user ID: ${userId}`);
    console.log('New permissions data:', JSON.stringify(newPermissions, null, 2)); // Log data baru

    // Gunakan transaksi untuk memastikan atomicity
    await prisma.$transaction(async (tx) => {
      // 1. Hapus semua permission lama untuk user ini
      await tx.permission.deleteMany({
        where: { userId: userId },
      });

      console.log(`Deleted old permissions for user ID: ${userId}`);

      // 2. Buat permission baru jika array tidak kosong
      if (newPermissions.length > 0) {
        const permissionsToCreate = newPermissions.map(p => ({
          userId: userId,
          module: p.module,
          canCreate: p.canCreate,
          canRead: p.canRead,
          canUpdate: p.canUpdate,
          canDelete: p.canDelete,
        }));

        await tx.permission.createMany({
          data: permissionsToCreate,
        });
        console.log(`Created ${permissionsToCreate.length} new permissions for user ID: ${userId}`);
      } else {
         console.log(`No new permissions to create for user ID: ${userId}`);
      }
    });

    // Ambil data user untuk logging
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { name: true, email: true }
    });

    // Ambil user ID dari token
    const adminUserId = getUserIdFromToken(request);

    // Log perubahan permissions
    await logUser(
      'permission_change',
      userId,
      {
        userName: user?.name,
        userEmail: user?.email,
        permissionCount: newPermissions.length,
        modules: newPermissions.map(p => p.module)
      },
      null, // Tidak ada outletId spesifik
      adminUserId // Gunakan userId dari token
    );

    return NextResponse.json({ message: `Hak akses untuk user ${userId} berhasil diperbarui.` });

  } catch (error: unknown) {
    console.error(`[API PUT Permissions] Error updating permissions for user ${userId}:`, error);
    // Periksa jika error spesifik dari Prisma
     let errorMessage = 'Gagal memperbarui hak akses.';
     const statusCode = 500;
     // Anda bisa menambahkan pengecekan error Prisma di sini jika perlu
     // Contoh sederhana:
     if (error instanceof Error) {
        errorMessage = error.message;
        // Cek jika error terkait constraint unik atau foreign key
        if ('code' in error && typeof error.code === 'string') {
           if (error.code === 'P2002') errorMessage = 'Gagal menyimpan permission: Duplikasi data.';
           if (error.code === 'P2003') errorMessage = 'Gagal menyimpan permission: User tidak valid.';
           // Tambahkan kode error lain jika perlu
           // Jika kode error tertentu, Anda bisa ubah statusCode di sini:
           // if (error.code === 'Pxxxx') statusCode = 4xx;
        }
     }

    return NextResponse.json({ error: errorMessage }, { status: statusCode });
  }
}