'use client';

import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';

/**
 * Komponen untuk menangani session management secara global
 * Mendeteksi token expired dan memberikan notifikasi yang user-friendly
 */
export function SessionManager() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const lastNotificationTime = useRef<number>(0);
  const isRedirecting = useRef<boolean>(false);

  useEffect(() => {
    // Hanya jalankan jika user sedang login
    if (!isAuthenticated || !user) return;

    // Function untuk handle response 401 secara global
    const handleUnauthorizedResponse = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { url, status } = customEvent.detail;

      if (status === 401 && !isRedirecting.current) {
        const now = Date.now();
        
        // Prevent multiple notifications dalam 5 detik
        if (now - lastNotificationTime.current < 5000) {
          return;
        }
        
        lastNotificationTime.current = now;
        isRedirecting.current = true;

        // Tampilkan toast notification
        toast.error('🔒 Sesi Anda telah berakhir. Silakan login kembali.', {
          duration: 4000,
          position: 'top-center',
          style: {
            background: '#ef4444',
            color: 'white',
            fontWeight: 'bold',
          },
        });

        // Hapus token dari cookie
        document.cookie = 'user_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

        // Redirect ke login setelah delay singkat untuk memberi waktu user membaca notifikasi
        setTimeout(() => {
          const currentPath = window.location.pathname + window.location.search;
          router.push(`/auth/login?redirect=${encodeURIComponent(currentPath)}`);
        }, 1500);
      }
    };

    // Listen untuk custom event dari fetch interceptor
    window.addEventListener('unauthorized-response', handleUnauthorizedResponse);

    // Cleanup
    return () => {
      window.removeEventListener('unauthorized-response', handleUnauthorizedResponse);
    };
  }, [isAuthenticated, user, router]);

  // Komponen ini tidak render apapun, hanya menangani logic
  return null;
}

/**
 * Function untuk dispatch custom event ketika ada response 401
 * Digunakan oleh fetch interceptor atau error handler
 */
export function dispatchUnauthorizedEvent(url: string, status: number) {
  if (typeof window !== 'undefined') {
    const event = new CustomEvent('unauthorized-response', {
      detail: { url, status }
    });
    window.dispatchEvent(event);
  }
}

/**
 * Enhanced fetch function yang otomatis dispatch event untuk 401
 */
export async function fetchWithSessionHandling(url: string, options: RequestInit = {}) {
  const defaultOptions: RequestInit = {
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, defaultOptions);
    
    // Jika response 401, dispatch event untuk SessionManager
    if (response.status === 401) {
      dispatchUnauthorizedEvent(url, response.status);
    }
    
    return response;
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
}