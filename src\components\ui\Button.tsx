'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';

type ButtonProps = {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
};

export default function Button({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  onClick,
}: ButtonProps) {
  const baseClasses = 'btn font-medium transition-all duration-300 flex items-center gap-2';
  
  const variantClasses = {
    primary: 'bg-primary hover:bg-primary-dark text-white border-0',
    secondary: 'bg-secondary hover:bg-secondary-dark text-white border-0',
    outline: 'bg-transparent border border-primary text-primary hover:bg-primary/10',
    ghost: 'bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 text-white',
  };
  
  const sizeClasses = {
    sm: 'btn-sm px-4',
    md: 'px-6 py-2',
    lg: 'btn-lg px-8',
  };
  
  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;
  
  return (
    <motion.button
      whileHover={{ scale: 1.03 }}
      whileTap={{ scale: 0.98 }}
      className={buttonClasses}
      onClick={onClick}
    >
      {children}
    </motion.button>
  );
}
