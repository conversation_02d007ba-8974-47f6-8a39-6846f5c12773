import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { formatISO, subDays, parseISO } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const outletId = searchParams.get('outletId') || undefined;
    const type = searchParams.get('type'); // 'therapist' atau 'user'
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Default tanggal: 30 hari terakhir
    const defaultEndDate = new Date();
    const defaultStartDate = subDays(defaultEndDate, 30);

    // Filter berdasarkan tanggal
    const dateStart = startDate ? parseISO(startDate) : defaultStartDate;
    const dateEnd = endDate ? parseISO(endDate) : defaultEndDate;

    // Filter untuk therapistAttendance
    const therapistAttendanceFilter: any = {
      createdAt: {
        gte: dateStart,
        lte: dateEnd,
      }
    };

    // Filter untuk systemLog
    const systemLogFilter: any = {
      createdAt: {
        gte: dateStart,
        lte: dateEnd,
      },
      type: {
        in: ['USER_ATTENDANCE_IN', 'USER_ATTENDANCE_OUT']
      }
    };

    // Tambahkan filter outletId jika ada
    if (outletId) {
      therapistAttendanceFilter.outletId = outletId;
      systemLogFilter.outletId = outletId;
    }

    // Hitung offset untuk pagination
    const skip = (page - 1) * limit;

    let attendances = [];
    let totalItems = 0;

    // Ambil data berdasarkan tipe
    if (!type || type === 'therapist') {
      // Ambil data absensi terapis
      const therapistAttendances = await prisma.therapistAttendance.findMany({
        where: therapistAttendanceFilter,
        skip,
        take: type ? limit : Math.floor(limit / 2),
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          therapist: true,
          outlet: true
        }
      });

      // Format data absensi terapis
      const formattedTherapistAttendances = therapistAttendances.map(attendance => ({
        id: attendance.id,
        userId: attendance.therapistId,
        name: attendance.therapist.name,
        role: 'Terapis', 
        outletId: attendance.outletId,
        outletName: attendance.outlet.name,
        attendanceType: attendance.attendanceType,
        timestamp: attendance.createdAt,
        type: 'therapist',
        notes: attendance.notes || ''
      }));

      attendances = [...attendances, ...formattedTherapistAttendances];
      
      // Hitung total untuk pagination
      if (!type || type === 'therapist') {
        const totalTherapistAttendances = await prisma.therapistAttendance.count({
          where: therapistAttendanceFilter
        });
        totalItems += totalTherapistAttendances;
      }
    }

    if (!type || type === 'user') {
      // Ambil data absensi user (staff/admin)
      const userAttendances = await prisma.systemLog.findMany({
        where: systemLogFilter,
        skip: type ? skip : 0,
        take: type ? limit : Math.floor(limit / 2),
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          user: true,
          outlet: true
        }
      });

      // Format data absensi user
      const formattedUserAttendances = userAttendances.map(log => ({
        id: log.id,
        userId: log.userId,
        name: log.user?.name || 'Unknown',
        role: log.user?.role || 'Staff',
        outletId: log.outletId,
        outletName: log.outlet?.name || 'Unknown',
        attendanceType: log.type === 'USER_ATTENDANCE_IN' ? 'IN' : 'OUT',
        timestamp: log.createdAt,
        type: 'user',
        notes: log.details || ''
      }));

      attendances = [...attendances, ...formattedUserAttendances];

      // Hitung total untuk pagination
      if (!type || type === 'user') {
        const totalUserAttendances = await prisma.systemLog.count({
          where: systemLogFilter
        });
        totalItems += totalUserAttendances;
      }
    }

    // Sort gabungan data berdasarkan timestamp terbaru
    attendances.sort((a, b) => {
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    });

    // Jika gabungan data, batasi jumlah hasil
    if (!type) {
      attendances = attendances.slice(0, limit);
    }

    // Ambil data outlets untuk filter
    const outlets = await prisma.outlet.findMany({
      select: {
        id: true,
        name: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    return NextResponse.json({
      success: true,
      data: attendances,
      pagination: {
        page,
        limit,
        totalItems,
        totalPages: Math.ceil(totalItems / limit)
      },
      filters: {
        outlets
      }
    });

  } catch (error) {
    console.error('Error fetching attendance records:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data absensi' },
      { status: 500 }
    );
  }
} 