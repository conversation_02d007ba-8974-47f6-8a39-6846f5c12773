'use client';

import React, { useState } from 'react';
import { FiSearch, FiPhone, FiUser, FiCalendar, FiClock, FiMapPin, FiStar, FiList, FiInbox, FiHome } from 'react-icons/fi';
import { format, parseISO, isValid } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { toast } from 'sonner';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';

// Tipe data untuk pelanggan
interface Customer {
  id: string;
  name: string;
  phone: string;
  points: number;
  registeredAt?: string | Date;
  tags?: string[];
}

// Tipe data untuk transaksi
interface Transaction {
  transactionId: string;
  customerId?: string;
  customerName?: string;
  therapistId?: string;
  therapistName?: string;
  outletId?: string;
  outletName?: string;
  cashierId?: string;
  cashierName?: string;
  transactionDate: string | Date;
  totalAmount: number;
  items?: Array<{
    id: string;
    name: string;
    price: number;
    quantity: number;
  }>;
}

export default function CekPoinPage() {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isSearched, setIsSearched] = useState(false);

  // Animasi untuk Framer Motion
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number = 1) => ({
      opacity: 1,
      y: 0,
      transition: { staggerChildren: 0.1, delayChildren: i * 0.1, duration: 0.5 }
    })
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const pulseAnimation = {
    scale: [1, 1.05, 1],
    transition: {
      duration: 2,
      repeat: Infinity,
      repeatType: "reverse" as const
    }
  };

  // Fungsi untuk mencari pelanggan berdasarkan nomor telepon
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!phoneNumber.trim()) {
      toast.error('Silakan masukkan nomor telepon Anda');
      return;
    }

    setIsLoading(true);
    setIsSearched(true);

    try {
      // Cari pelanggan berdasarkan nomor telepon
      const customerResponse = await fetch(`/api/customers/search?phone=${phoneNumber.trim()}`);

      if (!customerResponse.ok) {
        throw new Error(`Error: ${customerResponse.status}`);
      }

      const customerData = await customerResponse.json();

      if (!customerData.customer) {
        setCustomer(null);
        setTransactions([]);
        toast.error('Pelanggan tidak ditemukan');
        return;
      }

      setCustomer(customerData.customer);

      // Ambil riwayat transaksi pelanggan
      const transactionsResponse = await fetch(`/api/transactions?customerId=${customerData.customer.id}`);

      if (!transactionsResponse.ok) {
        throw new Error(`Error: ${transactionsResponse.status}`);
      }

      const transactionsData = await transactionsResponse.json();
      setTransactions(transactionsData.transactions || []);

    } catch (error) {
      console.error('Error searching customer:', error);
      toast.error('Terjadi kesalahan saat mencari data. Silakan coba lagi.');
    } finally {
      setIsLoading(false);
    }
  };

  // Format nomor telepon saat input
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Hanya izinkan angka
    const value = e.target.value.replace(/[^0-9]/g, '');
    setPhoneNumber(value);
  };

  return (
    <div className="min-h-screen bg-white text-gray-800">
      {/* Header */}
      <header className="bg-[#5EB3A0] text-white shadow-md">
        <div className="container mx-auto py-6 px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <Link href="/" className="flex items-center mb-4 md:mb-0 group">
              <motion.div
                className="w-12 h-12 mr-3 relative bg-white rounded-full p-1 border-2 border-white shadow-md overflow-hidden"
                whileHover={{ rotate: 10, scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
                initial={{ boxShadow: '0 0 0 rgba(245, 194, 76, 0)' }}
                animate={{
                  boxShadow: ['0 0 10px rgba(245, 194, 76, 0.5)', '0 0 20px rgba(245, 194, 76, 0.3)', '0 0 10px rgba(245, 194, 76, 0.5)']
                }}
                transition={{
                  boxShadow: { repeat: Infinity, duration: 2, ease: 'easeInOut' },
                  rotate: { type: "spring", stiffness: 300 },
                  scale: { type: "spring", stiffness: 300 }
                }}
              >
                <div className="w-full h-full relative rounded-full bg-white flex items-center justify-center">
                  <Image
                    src="/logo.png"
                    alt="BreakTime Logo"
                    fill
                    style={{ objectFit: 'contain', padding: '2px' }}
                  />
                </div>
              </motion.div>
              <h1 className="text-2xl font-bold">
                <span className="text-white">Break</span>
                <span className="text-[#F5C24C]">time</span>
              </h1>
            </Link>
            <motion.h2
              className="text-xl font-semibold flex items-center"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <FiStar className="mr-2 text-[#F5C24C]" />
              Cek Poin Loyalitas
            </motion.h2>
          </div>
        </div>
      </header>

      <main className="container mx-auto py-8 px-4">
        <div className="max-w-3xl mx-auto">
          {/* Form Pencarian */}
          <motion.div
            className="bg-white rounded-lg shadow-lg p-6 mb-8 border border-gray-100"
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
          >
            <motion.div variants={fadeIn} custom={1}>
              <h3 className="text-lg font-semibold mb-4 text-center flex items-center justify-center text-[#1e3a5f]">
                <FiPhone className="mr-2 text-[#5EB3A0]" />
                Masukkan Nomor Telepon Anda
              </h3>
            </motion.div>

            <motion.form
              onSubmit={handleSearch}
              className="flex flex-col md:flex-row gap-3"
              variants={fadeIn}
              custom={2}
            >
              <div className="relative flex-grow">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <FiPhone />
                </div>
                <input
                  type="tel"
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#5EB3A0] focus:border-[#5EB3A0] transition-colors bg-white text-gray-800 shadow-sm"
                  placeholder="Contoh: 08123456789"
                  value={phoneNumber}
                  onChange={handlePhoneChange}
                  disabled={isLoading}
                />
              </div>
              <motion.button
                type="submit"
                className="py-3 px-6 bg-[#F5C24C] hover:bg-[#e5b346] text-gray-800 font-medium rounded-lg flex items-center justify-center transition-colors shadow-sm"
                disabled={isLoading}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {isLoading ? (
                  <svg className="animate-spin h-5 w-5 text-gray-800" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <>
                    <FiSearch className="mr-2" /> Cek Poin
                  </>
                )}
              </motion.button>
            </motion.form>

            <motion.div
              className="mt-4 text-center text-sm text-gray-500"
              variants={fadeIn}
              custom={3}
            >
              Cek poin loyalitas dan riwayat kunjungan Anda dengan mudah
            </motion.div>
          </motion.div>

          {/* Hasil Pencarian */}
          {isSearched && (
            <motion.div
              className="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {/* Loading State */}
              {isLoading && (
                <div className="flex justify-center items-center p-16">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    className="w-12 h-12 border-4 border-[#5EB3A0] border-t-transparent rounded-full"
                  />
                </div>
              )}

              {/* Pelanggan Tidak Ditemukan */}
              {!isLoading && !customer && (
                <motion.div
                  className="text-center py-16 px-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <motion.div
                    animate={pulseAnimation}
                    className="w-20 h-20 mx-auto mb-4 rounded-full bg-red-50 flex items-center justify-center"
                  >
                    <FiUser className="w-10 h-10 text-red-400" />
                  </motion.div>
                  <h3 className="text-xl font-semibold mb-2">Pelanggan Tidak Ditemukan</h3>
                  <p className="text-gray-500 max-w-md mx-auto">
                    Nomor telepon <span className="font-semibold">{phoneNumber}</span> tidak terdaftar dalam sistem kami.
                    Pastikan nomor yang Anda masukkan benar atau hubungi staf kami untuk bantuan.
                  </p>
                  <motion.button
                    className="mt-6 py-2 px-4 bg-[#5EB3A0] text-white rounded-lg flex items-center mx-auto"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setIsSearched(false)}
                  >
                    <FiSearch className="mr-2" /> Coba Lagi
                  </motion.button>
                </motion.div>
              )}

              {/* Informasi Pelanggan */}
              {!isLoading && customer && (
                <div className="divide-y divide-gray-100">
                  {/* Header Informasi Pelanggan */}
                  <motion.div
                    className="p-6"
                    initial="hidden"
                    animate="visible"
                    variants={staggerContainer}
                  >
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                      <motion.div className="flex items-center" variants={fadeIn} custom={1}>
                        <motion.div
                          className="mr-4"
                          whileHover={{ rotate: 10, scale: 1.05 }}
                          transition={{ type: "spring", stiffness: 300 }}
                        >
                          <div className="w-16 h-16 rounded-full bg-white border-2 border-[#5EB3A0]/20 shadow-md flex items-center justify-center text-[#5EB3A0] text-2xl font-bold overflow-hidden">
                            <div className="w-full h-full bg-[#5EB3A0]/10 flex items-center justify-center">
                              {customer.name.charAt(0).toUpperCase()}
                            </div>
                          </div>
                        </motion.div>
                        <div>
                          <h3 className="text-xl font-bold text-gray-800">{customer.name}</h3>
                          <p className="text-gray-500 flex items-center mt-1">
                            <FiPhone className="mr-2" size={14} />
                            {customer.phone}
                          </p>
                        </div>
                      </motion.div>
                      <motion.div
                        className="bg-[#F5C24C]/10 rounded-lg p-4 text-center"
                        variants={fadeIn}
                        custom={2}
                        whileHover={{ scale: 1.05 }}
                        transition={{ type: "spring", stiffness: 300 }}
                      >
                        <p className="text-sm text-gray-600 mb-1">Poin Loyalitas Anda</p>
                        <div className="flex items-center justify-center">
                          <motion.div
                            animate={pulseAnimation}
                          >
                            <FiStar className="text-[#F5C24C] mr-2" size={24} />
                          </motion.div>
                          <span className="text-3xl font-bold text-[#F5C24C]">{customer.points || 0}</span>
                        </div>
                      </motion.div>
                    </div>

                    {/* Informasi Tambahan */}
                    <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                      <motion.div
                        className="bg-gray-50 rounded-lg p-4 border border-gray-100"
                        variants={fadeIn}
                        custom={3}
                        whileHover={{ y: -5 }}
                        transition={{ type: "spring", stiffness: 300 }}
                      >
                        <h4 className="font-semibold mb-2 flex items-center text-gray-700">
                          <FiCalendar className="mr-2 text-[#5EB3A0]" /> Terdaftar Sejak
                        </h4>
                        <p className="text-gray-600">
                          {customer.registeredAt && isValid(parseISO(customer.registeredAt as string))
                            ? format(parseISO(customer.registeredAt as string), 'dd MMMM yyyy', { locale: idLocale })
                            : 'Tidak tersedia'}
                        </p>
                      </motion.div>
                      <motion.div
                        className="bg-gray-50 rounded-lg p-4 border border-gray-100"
                        variants={fadeIn}
                        custom={4}
                        whileHover={{ y: -5 }}
                        transition={{ type: "spring", stiffness: 300 }}
                      >
                        <h4 className="font-semibold mb-2 flex items-center text-gray-700">
                          <FiList className="mr-2 text-[#5EB3A0]" /> Total Kunjungan
                        </h4>
                        <p className="text-gray-600">
                          {transactions.length} kali
                        </p>
                      </motion.div>
                    </div>
                  </motion.div>

                  {/* Riwayat Kunjungan */}
                  <motion.div
                    className="p-6"
                    initial="hidden"
                    animate="visible"
                    variants={staggerContainer}
                  >
                    <motion.h3
                      className="text-lg font-semibold mb-4 flex items-center text-gray-800"
                      variants={fadeIn}
                      custom={1}
                    >
                      <FiClock className="mr-2 text-[#5EB3A0]" /> Riwayat Kunjungan
                    </motion.h3>

                    {transactions.length === 0 ? (
                      <motion.div
                        className="text-center py-8"
                        variants={fadeIn}
                        custom={2}
                      >
                        <FiInbox className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                        <p className="text-gray-500">Belum ada riwayat kunjungan</p>
                      </motion.div>
                    ) : (
                      <div className="space-y-4">
                        {transactions.map((transaction, index) => {
                          // Format tanggal
                          const formattedDate = transaction.transactionDate && isValid(parseISO(transaction.transactionDate as string))
                            ? format(parseISO(transaction.transactionDate as string), 'dd MMMM yyyy', { locale: idLocale })
                            : 'Tanggal tidak valid';

                          return (
                            <motion.div
                              key={transaction.transactionId}
                              className="border border-gray-100 rounded-lg p-4 hover:bg-gray-50 transition-colors shadow-sm"
                              variants={fadeIn}
                              custom={index + 3}
                              whileHover={{ y: -3, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)" }}
                            >
                              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                                <div>
                                  <div className="flex items-center text-gray-500 text-sm mb-1">
                                    <FiCalendar className="mr-2 text-[#5EB3A0]" size={14} />
                                    {formattedDate}
                                  </div>
                                  <h4 className="font-semibold text-gray-800">
                                    {transaction.items?.map(item => item.name).join(', ') || 'Layanan tidak tersedia'}
                                  </h4>
                                </div>
                                <div className="text-right">
                                  <span className="font-bold text-lg text-[#5EB3A0]">
                                    Rp {transaction.totalAmount.toLocaleString('id-ID')}
                                  </span>
                                </div>
                              </div>

                              <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-500">
                                {transaction.therapistName && (
                                  <div className="flex items-center">
                                    <FiUser className="mr-2 text-[#F5C24C]" size={14} />
                                    <span>Terapis: <span className="font-medium text-gray-700">{transaction.therapistName}</span></span>
                                  </div>
                                )}
                                {transaction.outletName && (
                                  <div className="flex items-center">
                                    <FiMapPin className="mr-2 text-[#F5C24C]" size={14} />
                                    <span>Outlet: <span className="font-medium text-gray-700">{transaction.outletName}</span></span>
                                  </div>
                                )}
                              </div>
                            </motion.div>
                          );
                        })}
                      </div>
                    )}
                  </motion.div>
                </div>
              )}
            </motion.div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-[#5EB3A0] text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <div className="flex justify-center items-center mb-4">
            <Link href="/" className="flex items-center group">
              <motion.div
                className="w-10 h-10 mr-2 relative bg-white rounded-full p-1 border-2 border-white shadow-md overflow-hidden"
                whileHover={{ rotate: 10, scale: 1.05 }}
                initial={{ boxShadow: '0 0 0 rgba(245, 194, 76, 0)' }}
                animate={{
                  boxShadow: ['0 0 10px rgba(245, 194, 76, 0.5)', '0 0 20px rgba(245, 194, 76, 0.3)', '0 0 10px rgba(245, 194, 76, 0.5)']
                }}
                transition={{
                  boxShadow: { repeat: Infinity, duration: 2, ease: 'easeInOut' },
                  rotate: { type: "spring", stiffness: 300 },
                  scale: { type: "spring", stiffness: 300 }
                }}
              >
                <div className="w-full h-full relative rounded-full bg-white flex items-center justify-center">
                  <Image
                    src="/logo.png"
                    alt="BreakTime Logo"
                    fill
                    style={{ objectFit: 'contain', padding: '2px' }}
                  />
                </div>
              </motion.div>
              <h3 className="text-lg font-bold">
                <span className="text-white">Break</span>
                <span className="text-[#F5C24C]">time</span>
              </h3>
            </Link>
          </div>
          <p>© {new Date().getFullYear()} BreakTime. Semua hak dilindungi.</p>
          <p className="text-sm mt-2 text-white/80">Untuk informasi lebih lanjut, silakan hubungi outlet terdekat kami.</p>
          <motion.div
            className="mt-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Link
              href="/"
              className="inline-flex items-center text-white hover:text-[#F5C24C] transition-colors"
            >
              <FiHome className="mr-2" /> Kembali ke Beranda
            </Link>
          </motion.div>
        </div>
      </footer>
    </div>
  );
}
