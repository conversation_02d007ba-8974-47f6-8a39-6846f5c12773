import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import { format, eachDayOfInterval, addDays, subDays } from 'date-fns';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');
    const period = searchParams.get('period') || 'daily'; // daily, weekly, monthly, yearly
    const compareWith = searchParams.get('compareWith') || 'none'; // none, previous, lastYear

    // Ambil parameter tanggal dari query
    const rawStartDate = searchParams.get('startDate');
    const rawEndDate = searchParams.get('endDate');

    if (!rawStartDate || !rawEndDate) {
      throw new Error('Tanggal mulai dan tanggal akhir harus disediakan');
    }

    // Buat tanggal dengan pendekatan yang sama seperti di reports/route.ts
    // Format: YYYY-MM-DD -> parse sebagai tanggal lokal
    const [startYear, startMonth, startDay] = rawStartDate.split('-').map(Number);
    const [endYear, endMonth, endDay] = rawEndDate.split('-').map(Number);

    // Buat tanggal dengan komponen tanggal lokal
    // Bulan dalam JavaScript dimulai dari 0 (Januari = 0)
    const startDate = new Date(startYear, startMonth - 1, startDay, 0, 0, 0, 0);
    const endDate = new Date(endYear, endMonth - 1, endDay, 23, 59, 59, 999);

    // Validasi tanggal
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.log(`Invalid date: startDate=${startDate}, endDate=${endDate}`);
      throw new Error('Format tanggal tidak valid');
    }

    // Log tanggal yang akan digunakan
    console.log(`[API GET /api/reports/analytics] Using dates: startDate=${startDate.toISOString()}, endDate=${endDate.toISOString()}`);
    console.log(`[API GET /api/reports/analytics] Local date parts: startDay=${startDate.getDate()}, endDay=${endDate.getDate()}`);

    // Hitung tanggal perbandingan jika diperlukan
    let comparisonStartDate: Date | null = null;
    let comparisonEndDate: Date | null = null;

    if (compareWith === 'previous') {
      // Periode sebelumnya dengan durasi yang sama
      const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      comparisonStartDate = subDays(startDate, daysDiff);
      comparisonEndDate = subDays(endDate, daysDiff);
    } else if (compareWith === 'lastYear') {
      // Periode yang sama tahun lalu
      comparisonStartDate = new Date(startDate);
      comparisonStartDate.setFullYear(comparisonStartDate.getFullYear() - 1);
      comparisonEndDate = new Date(endDate);
      comparisonEndDate.setFullYear(comparisonEndDate.getFullYear() - 1);
    }

    // Filter dasar untuk transaksi berdasarkan tanggal dan outlet terpilih
    const transactionWhere: Prisma.TransactionWhereInput = {
      transactionDate: { gte: startDate, lte: endDate },
      ...(outletId && { outletId: outletId }),
    };

    // Log filter transaksi untuk debugging
    console.log(`[API GET /api/reports/analytics] Transaction filter: ${JSON.stringify({
      transactionDate: { gte: startDate.toISOString(), lte: endDate.toISOString() },
      ...(outletId && { outletId: outletId }),
    })}`);

    // Filter untuk data perbandingan jika diperlukan
    let comparisonTransactionWhere: Prisma.TransactionWhereInput | null = null;
    if (comparisonStartDate && comparisonEndDate) {
      comparisonTransactionWhere = {
        transactionDate: { gte: comparisonStartDate, lte: comparisonEndDate },
        ...(outletId && { outletId: outletId }),
      };
    }

    // Tidak perlu lagi mengambil semua transaksi karena kita menggunakan agregasi

    // 3. Ambil data outlet untuk perbandingan
    const outlets = await prisma.outlet.findMany({
      select: {
        id: true,
        name: true,
      },
    });

    // 4. Hitung total pendapatan untuk periode saat ini menggunakan agregasi
    // Gunakan metode yang sama persis dengan src/app/api/reports/route.ts
    const totalRevenueData = await prisma.transaction.aggregate({
      _sum: {
        totalAmount: true,
      },
      where: transactionWhere,
    });
    const totalRevenue = totalRevenueData._sum.totalAmount || 0;

    // Log untuk debugging
    console.log(`[API GET /api/reports/analytics] Total revenue: ${totalRevenue}`);

    // 5. Hitung total pendapatan untuk periode perbandingan jika diperlukan
    let comparisonTotalRevenue = 0;
    if (comparisonTransactionWhere) {
      const comparisonTotalRevenueData = await prisma.transaction.aggregate({
        _sum: {
          totalAmount: true,
        },
        where: comparisonTransactionWhere,
      });
      comparisonTotalRevenue = comparisonTotalRevenueData._sum.totalAmount || 0;
    }

    // 6. Hitung persentase perubahan
    const revenueChangePercentage = comparisonTotalRevenue > 0
      ? ((totalRevenue - comparisonTotalRevenue) / comparisonTotalRevenue) * 100
      : null;

    // 7. Hitung pendapatan per outlet menggunakan agregasi
    const revenueByOutlet = await Promise.all(outlets.map(async outlet => {
      // Hitung pendapatan outlet untuk periode saat ini
      const outletRevenueData = await prisma.transaction.aggregate({
        _sum: {
          totalAmount: true,
        },
        where: {
          ...transactionWhere,
          outletId: outlet.id,
        },
      });
      const outletRevenue = outletRevenueData._sum.totalAmount || 0;

      // Hitung pendapatan outlet untuk periode perbandingan jika ada
      let comparisonOutletRevenue = 0;
      if (comparisonTransactionWhere) {
        const comparisonOutletRevenueData = await prisma.transaction.aggregate({
          _sum: {
            totalAmount: true,
          },
          where: {
            ...comparisonTransactionWhere,
            outletId: outlet.id,
          },
        });
        comparisonOutletRevenue = comparisonOutletRevenueData._sum.totalAmount || 0;
      }

      const changePercentage = comparisonOutletRevenue > 0
        ? ((outletRevenue - comparisonOutletRevenue) / comparisonOutletRevenue) * 100
        : null;

      return {
        outletId: outlet.id,
        outletName: outlet.name,
        revenue: outletRevenue,
        comparisonRevenue: comparisonOutletRevenue,
        changePercentage,
      };
    }));

    // Urutkan outlet berdasarkan pendapatan tertinggi
    revenueByOutlet.sort((a, b) => b.revenue - a.revenue);

    // 8. Hitung pendapatan berdasarkan periode (harian, mingguan, bulanan, tahunan)
    let revenueByPeriod: any[] = [];
    let comparisonRevenueByPeriod: any[] = [];

    if (period === 'daily') {
      // Buat array tanggal dalam rentang
      const dateRange = eachDayOfInterval({
        start: startDate,
        end: endDate,
      });

      // Hitung pendapatan untuk setiap hari menggunakan agregasi
      revenueByPeriod = await Promise.all(dateRange.map(async date => {
        const dayStart = new Date(date);
        dayStart.setHours(0, 0, 0, 0);

        const dayEnd = new Date(date);
        dayEnd.setHours(23, 59, 59, 999);

        const dateString = format(date, 'yyyy-MM-dd');

        // Gunakan agregasi untuk menghitung pendapatan harian
        const dailyRevenueData = await prisma.transaction.aggregate({
          _sum: {
            totalAmount: true,
          },
          where: {
            ...transactionWhere,
            transactionDate: {
              gte: dayStart,
              lte: dayEnd,
            },
          },
        });

        return {
          date: dateString,
          revenue: dailyRevenueData._sum.totalAmount || 0,
        };
      }));

      // Jika ada perbandingan, hitung pendapatan harian untuk periode perbandingan
      if (comparisonStartDate && comparisonEndDate && comparisonTransactionWhere) {
        const comparisonDateRange = eachDayOfInterval({
          start: comparisonStartDate,
          end: comparisonEndDate,
        });

        comparisonRevenueByPeriod = await Promise.all(comparisonDateRange.map(async date => {
          const dayStart = new Date(date);
          dayStart.setHours(0, 0, 0, 0);

          const dayEnd = new Date(date);
          dayEnd.setHours(23, 59, 59, 999);

          const dateString = format(date, 'yyyy-MM-dd');

          // Gunakan agregasi untuk menghitung pendapatan harian perbandingan
          const dailyRevenueData = await prisma.transaction.aggregate({
            _sum: {
              totalAmount: true,
            },
            where: {
              ...comparisonTransactionWhere,
              transactionDate: {
                gte: dayStart,
                lte: dayEnd,
              },
            },
          });

          return {
            date: dateString,
            revenue: dailyRevenueData._sum.totalAmount || 0,
          };
        }));
      }
    } else if (period === 'weekly') {
      // Implementasi untuk periode mingguan
      // ...
    } else if (period === 'monthly') {
      // Implementasi untuk periode bulanan
      // ...
    } else if (period === 'yearly') {
      // Implementasi untuk periode tahunan
      // ...
    }

    // 9. Prediksi pendapatan berdasarkan tren historis
    // Implementasi sederhana: rata-rata pertumbuhan harian
    let revenuePrediction: any[] = [];

    if (revenueByPeriod.length > 0) {
      // Hitung rata-rata pertumbuhan harian
      let totalGrowth = 0;
      let growthCount = 0;

      for (let i = 1; i < revenueByPeriod.length; i++) {
        const prevRevenue = revenueByPeriod[i - 1].revenue;
        const currRevenue = revenueByPeriod[i].revenue;

        if (prevRevenue > 0) {
          const growth = (currRevenue - prevRevenue) / prevRevenue;
          totalGrowth += growth;
          growthCount++;
        }
      }

      const avgGrowth = growthCount > 0 ? totalGrowth / growthCount : 0;

      // Prediksi untuk 7 hari ke depan
      const lastDate = new Date(revenueByPeriod[revenueByPeriod.length - 1].date);
      let lastRevenue = revenueByPeriod[revenueByPeriod.length - 1].revenue;

      for (let i = 1; i <= 7; i++) {
        const nextDate = addDays(lastDate, i);
        const dateString = format(nextDate, 'yyyy-MM-dd');
        const predictedRevenue = lastRevenue * (1 + avgGrowth);

        revenuePrediction.push({
          date: dateString,
          revenue: predictedRevenue > 0 ? Math.round(predictedRevenue) : 0,
        });

        lastRevenue = predictedRevenue;
      }
    }

    return NextResponse.json({
      message: 'Data analitik berhasil diambil',
      analytics: {
        summary: {
          totalRevenue,
          comparisonTotalRevenue,
          revenueChangePercentage,
        },
        revenueByOutlet,
        revenueByPeriod,
        comparisonRevenueByPeriod,
        revenuePrediction,
      },
      filters: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        outletId: outletId,
        period,
        compareWith,
      },
    });
  } catch (error: unknown) {
    console.error('[API GET /api/reports/analytics] Error:', error);

    let errorMessage = 'Terjadi kesalahan saat mengambil data analitik';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat mengambil data analitik',
      analytics: {
        summary: {
          totalRevenue: 0,
          comparisonTotalRevenue: 0,
          revenueChangePercentage: null,
        },
        revenueByOutlet: [],
        revenueByPeriod: [],
        comparisonRevenueByPeriod: [],
        revenuePrediction: [],
      },
      filters: {
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        outletId: null,
        period: 'daily',
        compareWith: 'none',
      },
    }, { status: statusCode });
  }
}
