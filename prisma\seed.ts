import { PrismaClient } from '../prisma/generated/client';
import * as bcrypt from 'bcrypt';

export const prisma = new PrismaClient();

async function main() {
  // Hapus data yang ada (optional, dengan hati-hati di production)
  console.log('🚀 Me<PERSON><PERSON> proses seeding database...');

  try {
    // Buat admin user
    console.log('Membuat admin user...');
    const passwordHash = await bcrypt.hash('admin', 10);

    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        username: 'admin',
        name: 'Administrator',
        email: '<EMAIL>',
        password: passwordHash,
        role: 'ADMIN',
        isActive: true,
      },
    });

    console.log('👤 Admin user berhasil dibuat:', admin.email);

    // Buat staff user
    console.log('Membuat staff user...');
    const staffPasswordHash = await bcrypt.hash('staff123', 10);

    const staff = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        username: 'staff',
        name: 'Staff Kasir',
        email: '<EMAIL>',
        password: staffPasswordHash,
        role: 'STAFF',
        isActive: true,
      },
    });

    console.log('👤 Staff user berhasil dibuat:', staff.email);

    // Buat outlet di Palu - Emisaelan (sebagai outlet utama)
    console.log('Membuat outlet di Palu Emisaelan...');
    const paluEmisaelanOutlet = await prisma.outlet.upsert({
      where: { id: 'palu-emisaelan' },
      update: { name: 'Breaktime Palu Emisaelan' },
      create: {
        id: 'palu-emisaelan',
        name: 'Breaktime Palu Emisaelan',
        address: 'Jl. Emisaelan',
        city: 'Palu',
        phone: '0451-421234',
        operationalHours: '09:00-21:00',
        isOpen: true,
        isMain: true,
      },
    });

    console.log('🏢 Outlet Palu Emisaelan berhasil dibuat:', paluEmisaelanOutlet.name);

    // Buat outlet di Palu - Setiabudi
    console.log('Membuat outlet di Palu Setiabudi...');
    const paluSetiaBudiOutlet = await prisma.outlet.upsert({
      where: { id: 'palu-setiabudi' },
      update: { name: 'Breaktime Palu Setiabudi' },
      create: {
        id: 'palu-setiabudi',
        name: 'Breaktime Palu Setiabudi',
        address: 'Jl. Setiabudi',
        city: 'Palu',
        phone: '0451-458765',
        operationalHours: '10:00-22:00',
        isOpen: true,
        isMain: false,
      },
    });

    console.log('🏢 Outlet Palu Setiabudi berhasil dibuat:', paluSetiaBudiOutlet.name);

    // Buat outlet di Makassar
    console.log('Membuat outlet di Makassar...');
    const makassarOutlet = await prisma.outlet.upsert({
      where: { id: 'makassar-pengayoman' },
      update: { name: 'Breaktime Makassar Pengayoman' },
      create: {
        id: 'makassar-pengayoman',
        name: 'Breaktime Makassar Pengayoman',
        address: 'Jl. Pengayoman, Ruko Cornelian No. 32-34',
        city: 'Makassar',
        phone: '0411-456789',
        operationalHours: '09:00-21:00',
        isOpen: true,
        isMain: false,
      },
    });

    console.log('🏢 Outlet Makassar berhasil dibuat:', makassarOutlet.name);

    // --- Tambahkan Pembuatan Permissions & Investor (SETELAH OUTLET DIBUAT) ---
    console.log('🛡️ Membuat data permission default...');
    const systemModules = [
      'dashboard',
      'transactions',
      'customers',
      'therapists',
      'services',
      'bookings',
      'reports',
      'settings',
      'users',
    ];

    // Fungsi untuk membuat permission berdasarkan role
    const createPermissionsForUser = async (userId: string, role: 'ADMIN' | 'MANAGER' | 'STAFF' | 'INVESTOR') => {
      const permissionPromises = [];
      for (const moduleName of systemModules) {
        let canCreate = false, canRead = false, canUpdate = false, canDelete = false;

        switch (role) {
          case 'ADMIN':
            canCreate = canRead = canUpdate = canDelete = true;
            break;
          case 'MANAGER':
            canRead = true;
            if (!['users', 'settings'].includes(moduleName)) {
              canCreate = canUpdate = canDelete = true;
            }
            break;
          case 'STAFF':
             canRead = ['dashboard', 'transactions', 'customers', 'therapists', 'services', 'bookings'].includes(moduleName);
             canCreate = ['transactions', 'customers', 'bookings'].includes(moduleName);
             canUpdate = ['transactions', 'customers', 'bookings'].includes(moduleName);
             canDelete = false;
            break;
          case 'INVESTOR':
             canRead = ['dashboard', 'reports'].includes(moduleName);
             canCreate = canUpdate = canDelete = false;
             break;
        }

        permissionPromises.push(
          prisma.permission.upsert({
            where: { userId_module: { userId: userId, module: moduleName } },
            update: { canCreate, canRead, canUpdate, canDelete },
            create: { userId: userId, module: moduleName, canCreate, canRead, canUpdate, canDelete },
          })
        );
      }
      await Promise.all(permissionPromises);
      console.log(`Permissions default untuk user ${userId} (role: ${role}) berhasil dibuat/diupdate.`);
    };

    // Buat permissions untuk admin dan staff (setelah mereka dibuat)
    await createPermissionsForUser(admin.id, 'ADMIN');
    await createPermissionsForUser(staff.id, 'STAFF');

    // Buat contoh Investor
    console.log('Membuat contoh user Investor...');
    const investorPasswordHash = await bcrypt.hash('investor123', 10);
    const investor = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        username: 'investor1',
        name: 'Investor Satu',
        email: '<EMAIL>',
        password: investorPasswordHash,
        role: 'INVESTOR',
        isActive: true,
      },
    });
    console.log('👤 Investor user berhasil dibuat:', investor.email);
    // Buat permission default untuk investor
    await createPermissionsForUser(investor.id, 'INVESTOR');

    // Berikan akses investor ke outlet Palu Emisaelan dan Makassar
    console.log(`🔑 Memberikan akses outlet untuk investor ${investor.id}...`);
    await prisma.userOutletAccess.createMany({
      data: [
        { userId: investor.id, outletId: paluEmisaelanOutlet.id }, // Sekarang variabel sudah dideklarasi
        { userId: investor.id, outletId: makassarOutlet.id },      // Sekarang variabel sudah dideklarasi
      ],
      skipDuplicates: true,
    });
    console.log(`Akses outlet untuk investor ${investor.id} berhasil ditambahkan.`);
    // --- Akhir Pembuatan Permissions & Investor ---

    // Buat terapis
    console.log('Membuat data terapis...');

    // Data terapis untuk setiap outlet
    const therapistsData = {
      // Outlet Palu Setiabudi
      'palu-setiabudi': [
        'Alam', 'Aan', 'Bayu', 'Sarfan', 'Mahrani', 'Gita', 'Retno', 'Raja',
        'Syarif', 'Vito', 'Ramadhan', 'Zul', 'Rifai Ao', 'Iin', 'Aprilia', 'Aisyah', 'Isra'
      ],
      // Outlet Palu Emy Saelan
      'palu-emisaelan': [
        'Eka', 'Rifal', 'Riono', 'Amin Anugrah', 'Fajar hidayat', 'Wahyuni',
        'Fitry', 'Yurni', 'Vinda', 'Annas', 'Haikal', 'Aldi', 'Dimas',
        'Andris', 'Asma', 'Mima', 'Diva', 'Emy'
      ],
      // Outlet Makassar Pengayoman
      'makassar-pengayoman': [
        'Nazir', 'Angga', 'Azmi', 'Andi', 'Richa', 'Delisa', 'Salmiah', 'Rani',
        'Putra', 'Marwan', 'Heru', 'Ikbal', 'Saiful', 'Mariani', 'Febrianti', 'Deby', 'Anita'
      ]
    };

    // Fungsi untuk membuat nomor telepon acak
    const generateRandomPhone = () => {
      const prefix = '08';
      const randomDigits = Math.floor(10000000000 + Math.random() * 90000000000);
      return prefix + randomDigits.toString().substring(0, 10);
    };

    // Fungsi untuk membuat pengalaman acak (1-10 tahun)
    const generateRandomExperience = () => {
      return Math.floor(1 + Math.random() * 10);
    };

    // Buat terapis untuk setiap outlet
    const therapistsCreated = [];
    let therapistCounter = 1;

    for (const [outletId, therapistNames] of Object.entries(therapistsData)) {
      console.log(`Membuat terapis untuk outlet ${outletId}...`);

      for (const name of therapistNames) {
        const therapistId = `therapist-${therapistCounter++}`;
        const phone = generateRandomPhone();
        const experience = generateRandomExperience();

        const therapist = await prisma.therapist.upsert({
          where: { id: therapistId },
          update: {
            name,
            phone,
            experience,
            outletId
          },
          create: {
            id: therapistId,
            name,
            phone,
            experience,
            isActive: true,
            outletId
          },
        });

        therapistsCreated.push(therapist);
      }
    }

    // Simpan referensi ke beberapa terapis untuk digunakan dalam transaksi
    const therapist1 = therapistsCreated.find(t => t.outletId === 'palu-emisaelan') || therapistsCreated[0];
    // Terapis kedua dari Palu Emisaelan (untuk transaksi di masa depan jika diperlukan)
    // const therapist2 = therapistsCreated.find(t => t.outletId === 'palu-emisaelan' && t.id !== therapist1.id) || therapistsCreated[1];
    const therapist3 = therapistsCreated.find(t => t.outletId === 'palu-setiabudi') || therapistsCreated[2];
    const therapist4 = therapistsCreated.find(t => t.outletId === 'makassar-pengayoman') || therapistsCreated[3];

    console.log(`👩‍⚕️ ${therapistsCreated.length} terapis berhasil dibuat untuk 3 outlet.`);

    // --- BUAT LAYANAN BARU BERDASARKAN DAFTAR HARGA GAMBAR ---
    console.log('Membuat data layanan baru berdasarkan daftar harga...');

    const outlets = [paluEmisaelanOutlet, paluSetiaBudiOutlet, makassarOutlet];

    const newServicesData = [
      // Outlet Service / Pijat di Outlet
      { baseId: 'pijat-titik-60', name: 'Pijat 5 Titik 60 Menit', duration: 60, price: 95000, commission: 15000 },
      { baseId: 'kretek-60', name: 'Kretek 60 Menit', duration: 60, price: 155000, commission: 25000 },
      { baseId: 'pijat-titik-90', name: 'Pijat 5 Titik 90 Menit', duration: 90, price: 135000, commission: 20000 },
      { baseId: 'pijat-titik-120', name: 'Pijat 5 Titik 120 Menit', duration: 120, price: 180000, commission: 25000 },
      { baseId: 'lulur-pijat-90', name: 'Lulur + Pijat 90 Menit', duration: 90, price: 205000, commission: 30000 },
      { baseId: 'lulur-pijat-120', name: 'Lulur + Pijat 120 Menit', duration: 120, price: 270000, commission: 35000 },
      { baseId: 'earcandle', name: 'Earcandle Treatment', duration: 30, price: 45000, commission: 5000 }, // Durasi estimasi
      { baseId: 'lulur-60', name: 'Lulur 60 Menit', duration: 60, price: 100000, commission: 15000 },
      { baseId: 'masker-wajah-30', name: 'Masker Wajah 30 Menit', duration: 30, price: 35000, commission: 5000 },

      // Home Service / Pijat Dirumah (Ditambahkan sebagai service biasa)
      { baseId: 'home-pijat-90', name: 'Pijat Home Service 90 Menit', duration: 90, price: 160000, commission: 25000 },
      { baseId: 'home-pijat-120', name: 'Pijat Home Service 120 Menit', duration: 120, price: 205000, commission: 30000 },
      { baseId: 'home-lulur-pijat-titik-90', name: 'Lulur + Pijat Titik Home Service 90 Menit', duration: 90, price: 245000, commission: 35000 },
      { baseId: 'home-lulur-pijat-full-120', name: 'Lulur + Pijat Full Body Home Service 120 Menit', duration: 120, price: 305000, commission: 40000 },
      { baseId: 'home-kretek-90', name: 'Kretek Home Service 90 Menit', duration: 90, price: 190000, commission: 30000 },

      // Layanan Tambahan
      { baseId: 'extra-pijat-1titik-15', name: 'Pijat Tambahan 1 Titik 15 Menit', duration: 15, price: 25000, commission: 3000 },
      { baseId: 'extra-pijat-3titik-30', name: 'Pijat Tambahan 3 Titik 30 Menit', duration: 30, price: 50000, commission: 5000 },
    ];

    const servicePromises = [];
    for (const outlet of outlets) {
      console.log(`Membuat layanan untuk outlet: ${outlet.name}...`);
      for (const serviceData of newServicesData) {
        const serviceId = `${outlet.id}-${serviceData.baseId}`; // Buat ID unik per outlet-service
        servicePromises.push(
          prisma.service.upsert({
            where: { id: serviceId },
            update: { // Update jika ID sudah ada (untuk idempotency)
              name: serviceData.name,
              duration: serviceData.duration,
              price: serviceData.price,
              commission: serviceData.commission,
              isActive: true,
            },
            create: {
              id: serviceId,
              name: serviceData.name,
              duration: serviceData.duration,
              price: serviceData.price,
              commission: serviceData.commission,
              isActive: true,
              outletId: outlet.id,
            },
          })
        );
      }
    }

    await Promise.all(servicePromises);
    console.log(`🧖‍♀️ Layanan baru berhasil dibuat untuk ${outlets.length} outlet.`);

    // Buat customer
    console.log('Membuat data pelanggan dengan ID kustom...');
    let customerCounter = 1; // Inisialisasi counter untuk ID

    const customer1 = await prisma.customer.upsert({
      where: { phone: '081111222333' },
      update: {}, // Tidak update jika sudah ada berdasarkan phone
      create: {
        id: `P${String(customerCounter++).padStart(7, '0')}`, // Generate ID P0000001
        name: 'Rina Wulandari',
        phone: '081111222333',
        email: '<EMAIL>',
        address: 'Jl. Merdeka No. 55, Palu',
        birthdate: new Date('1990-05-15'),
        gender: 'FEMALE',
        tags: ['Loyal'], // Contoh tag awal
        isActive: true,
        // registeredAt akan diisi otomatis oleh @default(now())
      },
    });

    const customer2 = await prisma.customer.upsert({
      where: { phone: '082222333444' },
      update: {},
      create: {
        id: `P${String(customerCounter++).padStart(7, '0')}`, // Generate ID P0000002
        name: 'Andi Prasetyo',
        phone: '082222333444',
        email: '<EMAIL>',
        address: 'Jl. Sudirman No. 12, Palu',
        birthdate: new Date('1985-09-23'),
        gender: 'MALE',
        tags: ['Baru'],
        isActive: true,
        // registeredAt otomatis
      },
    });

    const customer3 = await prisma.customer.upsert({
      where: { phone: '083333444555' },
      update: {},
      create: {
        id: `P${String(customerCounter++).padStart(7, '0')}`, // Generate ID P0000003
        name: 'Siti Rahma',
        phone: '083333444555',
        email: '<EMAIL>',
        birthdate: new Date('1992-11-08'),
        gender: 'FEMALE',
        tags: ['VIP', 'Loyal'],
        isActive: true,
        // registeredAt otomatis
      },
    });

    console.log('👨‍👩‍👧 Pelanggan berhasil dibuat:', customer1.id, customer2.id, customer3.id);

    // Buat booking
    console.log('Membuat data booking...');
    // --- PENTING: Sesuaikan serviceId di booking & transaksi lama ---
    // Karena ID service lama dihapus, kita perlu mengarahkan booking/transaksi seed ke ID service BARU.
    // Contoh: Jika bookingService sebelumnya pakai service1.id, ganti dengan ID service baru yang relevan DARI OUTLET YANG SESUAI.
    // Misalnya, serviceId untuk booking1 di Palu Emisaelan harus merujuk ke ID service baru di outlet tsb.
    // Pilih service baru yang sesuai, misal 'Pijat 5 Titik 60 Menit' di Palu Emisaelan
    const paluEmisaelanServiceId_Pijat60 = `${paluEmisaelanOutlet.id}-pijat-titik-60`;
    const paluEmisaelanServiceId_Lulur60 = `${paluEmisaelanOutlet.id}-lulur-60`;
    const paluSetiabudiServiceId_Kretek60 = `${paluSetiaBudiOutlet.id}-kretek-60`; // Contoh service di Setiabudi
    const makassarServiceId_HomeKretek90 = `${makassarOutlet.id}-home-kretek-90`; // Contoh service di Makassar

    // --- Penyesuaian Booking & Transaksi ---
    console.log('Menyesuaikan booking & transaksi dengan ID baru dan menambahkan komisi...');

    // Contoh Penyesuaian Booking 1 (sudah ada)
    const booking1 = await prisma.booking.upsert({
      where: { id: 'booking-1' },
      update: {
        therapistId: therapist1.id, // Pastikan terapis masih ada di outlet ini
        outletId: paluEmisaelanOutlet.id,
        customerId: customer1.id, // Pastikan menggunakan customer1.id
      },
      create: {
        id: 'booking-1',
        bookingDate: new Date(Date.now() + 86400000),
        bookingTime: '10:00',
        status: 'CONFIRMED',
        notes: 'Pelanggan meminta terapis yang berpengalaman',
        createdById: staff.id,
        customerId: customer1.id, // Pastikan menggunakan customer1.id
        outletId: paluEmisaelanOutlet.id,
        therapistId: therapist1.id,
      },
    });

    // Sesuaikan BookingService 1 (sebelumnya service1)
    await prisma.bookingService.upsert({
      where: { id: 'booking-service-1' },
      update: {
        serviceId: paluEmisaelanServiceId_Pijat60, // Ganti ke ID baru
        price: newServicesData.find(s => s.baseId === 'pijat-titik-60')?.price || 0, // Ambil harga baru
      },
      create: {
        id: 'booking-service-1',
        bookingId: booking1.id,
        serviceId: paluEmisaelanServiceId_Pijat60, // Ganti ke ID baru
        price: newServicesData.find(s => s.baseId === 'pijat-titik-60')?.price || 0,
      },
    });

    // Sesuaikan BookingService 2 (sebelumnya service2, kita ganti ke Lulur 60)
    await prisma.bookingService.upsert({
      where: { id: 'booking-service-2' },
      update: {
        serviceId: paluEmisaelanServiceId_Lulur60, // Ganti ke ID baru
        price: newServicesData.find(s => s.baseId === 'lulur-60')?.price || 0,
      },
      create: {
        id: 'booking-service-2',
        bookingId: booking1.id,
        serviceId: paluEmisaelanServiceId_Lulur60, // Ganti ke ID baru
        price: newServicesData.find(s => s.baseId === 'lulur-60')?.price || 0,
      },
    });

    console.log('📅 Booking 1 berhasil dibuat/diupdate dengan service baru:', booking1.id);

    // Contoh Penyesuaian Booking 2 (completed) (sebelumnya service4 -> Hot Stone, kita ganti ke Kretek 60 di Setiabudi)
    const booking2 = await prisma.booking.upsert({
      where: { id: 'booking-2' },
      update: {
        therapistId: therapist3.id, // Pastikan terapis ada di Setiabudi
        outletId: paluSetiaBudiOutlet.id,
        customerId: customer2.id, // Pastikan menggunakan customer2.id
      },
      create: {
        id: 'booking-2',
        bookingDate: new Date(Date.now() - 86400000),
        bookingTime: '14:00',
        status: 'COMPLETED',
        createdById: staff.id,
        customerId: customer2.id, // Pastikan menggunakan customer2.id
        outletId: paluSetiaBudiOutlet.id,
        therapistId: therapist3.id,
      },
    });

    // Sesuaikan BookingService 3 (sebelumnya service4)
    const kretek60Price = newServicesData.find(s => s.baseId === 'kretek-60')?.price || 0;
    await prisma.bookingService.upsert({
      where: { id: 'booking-service-3' },
      update: {
        serviceId: paluSetiabudiServiceId_Kretek60, // Ganti ID service
        price: kretek60Price,
      },
      create: {
        id: 'booking-service-3',
        bookingId: booking2.id,
        serviceId: paluSetiabudiServiceId_Kretek60,
        price: kretek60Price,
      },
    });

    // --- Penyesuaian Seeding Transaksi ---
    console.log('Menyesuaikan booking & transaksi dengan ID baru dan menambahkan komisi...');

    // -- Transaksi 1 (dari booking2 - Kretek 60 Setiabudi) --
    const kretek60Commission = newServicesData.find(s => s.baseId === 'kretek-60')?.commission || 0;

    // Cek apakah booking2 sudah memiliki transaksi
    const existingTransaction = await prisma.transaction.findUnique({
      where: { bookingId: booking2.id }
    });

    let transaction1;

    if (existingTransaction) {
      // Update transaksi yang sudah ada
      transaction1 = await prisma.transaction.update({
        where: { id: existingTransaction.id },
        data: {
          transactionDate: new Date(Date.now() - 86400000 + 7200000),
          totalAmount: kretek60Price,
          paymentMethod: 'CASH',
          paymentStatus: 'PAID',
          createdById: staff.id,
          customerId: customer2.id,
          outletId: paluSetiaBudiOutlet.id,
          therapistId: therapist3.id,
          therapistCommissionEarned: kretek60Commission
        },
      });
      console.log('💰 Transaksi 1 (dari booking) berhasil diupdate dengan ID DB:', transaction1.id);
    } else {
      // Buat transaksi baru
      transaction1 = await prisma.transaction.create({
        data: {
          transactionDate: new Date(Date.now() - 86400000 + 7200000),
          totalAmount: kretek60Price,
          paymentMethod: 'CASH',
          paymentStatus: 'PAID',
          bookingId: booking2.id,
          createdById: staff.id,
          customerId: customer2.id,
          outletId: paluSetiaBudiOutlet.id,
          therapistId: therapist3.id,
          therapistCommissionEarned: kretek60Commission
        },
      });
      console.log('💰 Transaksi 1 (dari booking) berhasil dibuat dengan ID DB:', transaction1.id);
    }
    const generatedId1 = transaction1.id; // Simpan ID integer

    // Buat atau update TransactionItem 1 (menggunakan ID integer)
    // Cek apakah item sudah ada
    const existingItem = await prisma.transactionItem.findFirst({
      where: {
        transactionId: generatedId1,
        serviceId: paluSetiabudiServiceId_Kretek60
      }
    });

    if (existingItem) {
      // Update item yang sudah ada
      await prisma.transactionItem.update({
        where: { id: existingItem.id },
        data: {
          price: kretek60Price,
        },
      });
      console.log('🧾 TransactionItem 1 berhasil diupdate');
    } else {
      // Buat item baru
      await prisma.transactionItem.create({
        data: {
          transactionId: generatedId1,
          serviceId: paluSetiabudiServiceId_Kretek60,
          price: kretek60Price,
        },
      });
      console.log('🧾 TransactionItem 1 berhasil dibuat');
    }

    // -- Transaksi 2 (langsung - Pijat 60 + Lulur 60 Emisaelan) --
    const pijat60Commission = newServicesData.find(s => s.baseId === 'pijat-titik-60')?.commission || 0;
    const lulur60Commission = newServicesData.find(s => s.baseId === 'lulur-60')?.commission || 0;
    const transaction2Commission = pijat60Commission + lulur60Commission;
    const pijat60Price = newServicesData.find(s => s.baseId === 'pijat-titik-60')?.price || 0;
    const lulur60Price = newServicesData.find(s => s.baseId === 'lulur-60')?.price || 0;
    const transaction2Total = pijat60Price + lulur60Price;

    // Cari transaksi dengan kombinasi unik untuk menghindari duplikasi
    const existingTransaction2 = await prisma.transaction.findFirst({
      where: {
        customerId: customer3.id,
        outletId: paluEmisaelanOutlet.id,
        therapistId: therapist1.id,
        totalAmount: transaction2Total,
        paymentMethod: 'CREDIT_CARD'
      }
    });

    let transaction2;

    if (existingTransaction2) {
      // Update transaksi yang sudah ada
      transaction2 = await prisma.transaction.update({
        where: { id: existingTransaction2.id },
        data: {
          transactionDate: new Date(Date.now() - 172800000),
          totalAmount: transaction2Total,
          paymentMethod: 'CREDIT_CARD',
          paymentStatus: 'PAID',
          createdById: staff.id,
          customerId: customer3.id,
          outletId: paluEmisaelanOutlet.id,
          therapistId: therapist1.id,
          therapistCommissionEarned: transaction2Commission
        },
      });
      console.log('💰 Transaksi 2 (langsung) berhasil diupdate dengan ID DB:', transaction2.id);
    } else {
      // Buat transaksi baru
      transaction2 = await prisma.transaction.create({
        data: {
          transactionDate: new Date(Date.now() - 172800000),
          totalAmount: transaction2Total,
          paymentMethod: 'CREDIT_CARD',
          paymentStatus: 'PAID',
          createdById: staff.id,
          customerId: customer3.id,
          outletId: paluEmisaelanOutlet.id,
          therapistId: therapist1.id,
          therapistCommissionEarned: transaction2Commission
        },
      });
      console.log('💰 Transaksi 2 (langsung) berhasil dibuat dengan ID DB:', transaction2.id);
    }
    const generatedId2 = transaction2.id; // Simpan ID integer

    // Buat atau update TransactionItem 2 & 3 (menggunakan ID integer)
    // Cek apakah item sudah ada
    const existingItems = await prisma.transactionItem.findMany({
      where: {
        transactionId: generatedId2
      }
    });

    // Jika tidak ada item, buat baru
    if (existingItems.length === 0) {
      await prisma.transactionItem.createMany({
        data: [
          {
            transactionId: generatedId2,
            serviceId: paluEmisaelanServiceId_Pijat60,
            price: pijat60Price,
          },
          {
            transactionId: generatedId2,
            serviceId: paluEmisaelanServiceId_Lulur60,
            price: lulur60Price,
          }
        ]
      });
      console.log('🧾 TransactionItem 2 & 3 berhasil dibuat');
    } else {
      // Update item yang sudah ada
      for (const item of existingItems) {
        if (item.serviceId === paluEmisaelanServiceId_Pijat60) {
          await prisma.transactionItem.update({
            where: { id: item.id },
            data: { price: pijat60Price }
          });
        } else if (item.serviceId === paluEmisaelanServiceId_Lulur60) {
          await prisma.transactionItem.update({
            where: { id: item.id },
            data: { price: lulur60Price }
          });
        }
      }
      console.log('🧾 TransactionItem 2 & 3 berhasil diupdate');
    }

    // Buat Transaksi 3 (Makassar - Home Kretek 90)
    const homeKretek90Price = newServicesData.find(s => s.baseId === 'home-kretek-90')?.price || 0;
    const homeKretek90Commission = newServicesData.find(s => s.baseId === 'home-kretek-90')?.commission || 0;

    // Cari transaksi dengan kombinasi unik untuk menghindari duplikasi
    const existingTransaction3 = await prisma.transaction.findFirst({
      where: {
        customerId: customer1.id,
        outletId: makassarOutlet.id,
        therapistId: therapist4.id,
        totalAmount: homeKretek90Price,
        paymentMethod: 'DEBIT_CARD'
      }
    });

    let transaction3;

    if (existingTransaction3) {
      // Update transaksi yang sudah ada
      transaction3 = await prisma.transaction.update({
        where: { id: existingTransaction3.id },
        data: {
          transactionDate: new Date(Date.now() - 259200000),
          totalAmount: homeKretek90Price,
          paymentMethod: 'DEBIT_CARD',
          paymentStatus: 'PAID',
          createdById: staff.id,
          customerId: customer1.id,
          outletId: makassarOutlet.id,
          therapistId: therapist4.id,
          therapistCommissionEarned: homeKretek90Commission
        },
      });
      console.log('💰 Transaksi 3 (Makassar) berhasil diupdate dengan ID DB:', transaction3.id);
    } else {
      // Buat transaksi baru
      transaction3 = await prisma.transaction.create({
        data: {
          transactionDate: new Date(Date.now() - 259200000),
          totalAmount: homeKretek90Price,
          paymentMethod: 'DEBIT_CARD',
          paymentStatus: 'PAID',
          createdById: staff.id,
          customerId: customer1.id,
          outletId: makassarOutlet.id,
          therapistId: therapist4.id,
          therapistCommissionEarned: homeKretek90Commission
        },
      });
      console.log('💰 Transaksi 3 (Makassar) berhasil dibuat dengan ID DB:', transaction3.id);
    }
    const generatedId3 = transaction3.id; // Simpan ID integer

    // Buat atau update TransactionItem 4 (menggunakan ID integer)
    // Cek apakah item sudah ada
    const existingItem4 = await prisma.transactionItem.findFirst({
      where: {
        transactionId: generatedId3,
        serviceId: makassarServiceId_HomeKretek90
      }
    });

    if (existingItem4) {
      // Update item yang sudah ada
      await prisma.transactionItem.update({
        where: { id: existingItem4.id },
        data: {
          price: homeKretek90Price,
        },
      });
      console.log('🧾 TransactionItem 4 berhasil diupdate');
    } else {
      // Buat item baru
      await prisma.transactionItem.create({
        data: {
          transactionId: generatedId3,
          serviceId: makassarServiceId_HomeKretek90,
          price: homeKretek90Price,
        },
      });
      console.log('🧾 TransactionItem 4 berhasil dibuat');
    }

    // --- (Opsional) Update displayId untuk semua transaksi ---
    const allTransactions = [transaction1, transaction2, transaction3];
    for (const trans of allTransactions) {
        const displayId = `TR${String(trans.id).padStart(7, '0')}`;
        await prisma.transaction.update({
            where: { id: trans.id },
            data: { displayId: displayId },
        });
        console.log(`Updated displayId for transaction ${trans.id} to ${displayId}`);
    }

    console.log('✅ Seeding berhasil dilakukan');
  } catch (error) {
    console.error('Error dalam proses seeding:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('❌ Error dalam proses seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    console.log('Disconnecting Prisma client...');
    await prisma.$disconnect();
  });