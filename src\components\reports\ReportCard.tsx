'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  FiTrendingUp, FiTrendingDown, FiArrowUp, FiArrowDown,
  FiDollarSign, FiUsers, FiCalendar, FiClock
} from 'react-icons/fi';

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(value);
};

// Format percentage
const formatPercentage = (value: number) => {
  return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
};

interface ReportCardProps {
  title: string;
  value: number | string;
  previousValue?: number;
  type?: 'currency' | 'number' | 'percentage' | 'text';
  icon?: 'money' | 'users' | 'calendar' | 'clock';
  className?: string;
}

const ReportCard: React.FC<ReportCardProps> = ({
  title,
  value,
  previousValue,
  type = 'number',
  icon = 'money',
  className = ''
}) => {
  // Format value based on type
  const formattedValue = () => {
    if (typeof value === 'string') return value;

    switch(type) {
      case 'currency': return formatCurrency(value);
      case 'percentage': return `${Number(value).toFixed(1)}%`;
      case 'number': return new Intl.NumberFormat('id-ID').format(value);
      default: return value.toString();
    }
  };

  // Calculate percentage change
  const calculateChange = () => {
    if (previousValue === undefined || previousValue === 0) return null;
    if (typeof value === 'string') return null;

    return ((value - previousValue) / previousValue) * 100;
  };

  const percentageChange = calculateChange();

  // Determine icon
  const IconComponent = () => {
    switch(icon) {
      case 'money': return <FiDollarSign className="text-white" size={20} />;
      case 'users': return <FiUsers className="text-white" size={20} />;
      case 'calendar': return <FiCalendar className="text-white" size={20} />;
      case 'clock': return <FiClock className="text-white" size={20} />;
      default: return <FiDollarSign className="text-white" size={20} />;
    }
  };

  return (
    <motion.div
      className={`bg-white rounded-lg shadow-md p-4 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-start justify-between">
        <div>
          <h3 className="text-sm text-gray-500 font-medium">{title}</h3>
          <p className="text-2xl font-bold mt-1">{formattedValue()}</p>
        </div>
        <div className={`p-2 rounded-full ${
          icon === 'money' ? 'bg-emerald-500' :
          icon === 'users' ? 'bg-blue-500' :
          icon === 'calendar' ? 'bg-amber-500' :
          'bg-purple-500'
        }`}>
          <IconComponent />
        </div>
      </div>

      {percentageChange !== null && (
        <div className="mt-3 flex items-center">
          {percentageChange > 0 ? (
            <>
              <FiTrendingUp className="text-green-500 mr-1" />
              <span className="text-green-500 text-sm font-medium">
                {formatPercentage(percentageChange)}
              </span>
            </>
          ) : percentageChange < 0 ? (
            <>
              <FiTrendingDown className="text-red-500 mr-1" />
              <span className="text-red-500 text-sm font-medium">
                {formatPercentage(percentageChange)}
              </span>
            </>
          ) : (
            <span className="text-gray-500 text-sm font-medium">
              0% perubahan
            </span>
          )}
          <span className="text-gray-400 text-xs ml-1">dari periode sebelumnya</span>
        </div>
      )}
    </motion.div>
  );
};

export default ReportCard;
