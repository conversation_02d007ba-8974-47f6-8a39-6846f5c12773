import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { logService } from '@/lib/logger';
import { getUserIdFromToken } from '@/lib/auth-utils';

// GET layanan berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Pastikan params.id ada
    const id = params?.id;
    if (!id) {
      return NextResponse.json(
        { error: 'ID layanan diperlukan' },
        { status: 400 }
      );
    }

    // Ambil parameter includeInactive dari query string
    const { searchParams } = new URL(request.url);
    const includeInactive = searchParams.get('includeInactive') === 'true';

    // Ambil detail layanan berdasarkan ID
    const service = await prisma.service.findUnique({
      where: {
        id: id
      },
      include: {
        outlets: {
          include: {
            outlet: {
              select: {
                id: true,
                name: true,
                address: true,
                city: true,
                operationalHours: true
              }
            }
          }
        }
      }
    });

    // Jika layanan tidak ditemukan
    if (!service) {
      return NextResponse.json(
        { error: 'Layanan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Jika layanan tidak aktif dan includeInactive tidak true, kembalikan error
    if (!service.isActive && !includeInactive) {
      return NextResponse.json(
        { error: 'Layanan ini tidak tersedia saat ini' },
        { status: 403 }
      );
    }

    // Ambil outlet IDs dari layanan
    const outletIds = service.outlets.map(o => o.outlet.id);

    // Ambil terapis yang dapat melakukan layanan ini (dari semua outlet terkait)
    const therapists = await prisma.therapist.findMany({
      where: {
        outletId: { in: outletIds },
        isActive: true
      },
      select: {
        id: true,
        name: true,
        specialization: true,
        experience: true,
        outletId: true,
        outlet: {
          select: {
            name: true
          }
        }
      }
    });

    // Kembalikan data layanan dengan terapis yang tersedia
    return NextResponse.json({
      message: 'Data layanan berhasil diambil',
      service: {
        ...service,
        availableTherapists: therapists
      }
    });
  } catch (error) {
    console.error('Error fetching service details:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil detail layanan' },
      { status: 500 }
    );
  }
}

// PUT untuk mengupdate layanan
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Pastikan params.id ada
    const id = params?.id;
    if (!id) {
      return NextResponse.json(
        { error: 'ID layanan diperlukan' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { name, description, duration, price, commission, outletIds, isActive, ...rest } = body;

    // Cek apakah layanan ada
    const existingService = await prisma.service.findUnique({
      where: {
        id
      }
    });

    if (!existingService) {
      return NextResponse.json(
        { error: 'Layanan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Jika outletIds ada, cek apakah semua outlet ada
    if (outletIds && outletIds.length > 0) {
      const outlets = await prisma.outlet.findMany({
        where: {
          id: { in: outletIds }
        }
      });

      if (outlets.length !== outletIds.length) {
        return NextResponse.json(
          { error: 'Satu atau beberapa outlet tidak ditemukan' },
          { status: 404 }
        );
      }
    } else if (outletIds && outletIds.length === 0) {
      return NextResponse.json(
        { error: 'Minimal satu outlet harus dipilih' },
        { status: 400 }
      );
    }

    // Logika deaktivasi: cek booking aktif sebelum menonaktifkan
    if (isActive === false) {
      console.log(`Attempting to deactivate service ${id}. Checking active bookings...`);
      // Perbaiki pencarian booking: Cari di BookingService, lalu cek status Booking terkait
      const activeBookingServices = await prisma.bookingService.findMany({
        where: {
          serviceId: id,
          booking: {
            status: {
              in: ['PENDING', 'CONFIRMED']
            }
          }
        },
        select: { bookingId: true }
      });

      if (activeBookingServices.length > 0) {
        console.warn(`Service ${id} cannot be deactivated due to active bookings: ${activeBookingServices.map(bs => bs.bookingId).join(', ')}`);
        return NextResponse.json(
          { error: 'Layanan tidak dapat dinonaktifkan karena masih ada booking aktif yang menggunakannya.' },
          { status: 409 }
        );
      }
      console.log(`No active bookings found for service ${id}. Proceeding with deactivation.`);
    }

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Update layanan dengan transaksi untuk menangani relasi outlets
    const updatedService = await prisma.$transaction(async (tx) => {
      // 1. Update data layanan dasar
      const updated = await tx.service.update({
        where: { id },
        data: {
          ...(name && { name }),
          ...(description !== undefined && { description }),
          ...(duration && { duration }),
          ...(price !== undefined && { price }), // Izinkan harga 0
          ...(commission !== undefined && { commission }),
          ...(isActive !== undefined && { isActive })
        },
        include: {
          outlets: {
            include: {
              outlet: true
            }
          }
        }
      });

      // 2. Jika outletIds disediakan, update relasi outlets
      if (outletIds) {
        // Hapus semua relasi outlet yang ada
        await tx.serviceOutlet.deleteMany({
          where: { serviceId: id }
        });

        // Buat relasi outlet baru
        if (outletIds.length > 0) {
          await tx.serviceOutlet.createMany({
            data: outletIds.map((outletId: string) => ({
              serviceId: id,
              outletId
            }))
          });
        }

        // Ambil data terbaru dengan relasi outlets
        return await tx.service.findUnique({
          where: { id },
          include: {
            outlets: {
              include: {
                outlet: true
              }
            }
          }
        });
      }

      return updated;
    });

    // Log update layanan
    if (updatedService) {
      await logService(
        'update',
        id,
        {
          name: updatedService.name,
          description: updatedService.description,
          duration: updatedService.duration,
          price: updatedService.price,
          commission: updatedService.commission,
          isActive: updatedService.isActive,
          outlets: updatedService.outlets.map(o => o.outlet.name).join(', '),
          updatedFields: Object.keys({
            ...(name && { name }),
            ...(description !== undefined && { description }),
            ...(duration && { duration }),
            ...(price !== undefined && { price }), // Izinkan harga 0
            ...(commission !== undefined && { commission }),
            ...(outletIds && { outlets: 'updated' }),
            ...(isActive !== undefined && { isActive })
          })
        },
        undefined, // Ganti null dengan undefined
        userId // Gunakan userId dari token
      );
    }

    return NextResponse.json({
      message: 'Layanan berhasil diupdate',
      service: updatedService
    });
  } catch (error) {
    console.error('Error updating service:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengupdate layanan' },
      { status: 500 }
    );
  }
}

// DELETE layanan (hard delete)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Pastikan params.id ada
    const id = params?.id;
    if (!id) {
      return NextResponse.json(
        { error: 'ID layanan diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah layanan ada
    const existingService = await prisma.service.findUnique({
      where: {
        id
      },
      include: {
        outlets: {
          include: {
            outlet: true
          }
        }
      }
    });

    if (!existingService) {
      return NextResponse.json(
        { error: 'Layanan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Layanan nonaktif memiliki pemeriksaan ketat yang lebih sedikit
    // Mereka masih diperiksa untuk booking aktif, tetapi bisa langsung dihapus jika tidak ada booking aktif
    if (!existingService.isActive) {
      // Cek booking aktif saja untuk layanan nonaktif
      const activeBookingServices = await prisma.bookingService.findMany({
        where: {
          serviceId: id,
          booking: { status: { in: ['PENDING', 'CONFIRMED'] } }
        },
        select: { bookingId: true }
      });
      
      if (activeBookingServices.length > 0) {
        console.warn(`Inactive service ${id} cannot be deleted due to active bookings: ${activeBookingServices.map(bs => bs.bookingId).join(', ')}`);
        return NextResponse.json(
          { error: 'Layanan tidak dapat dihapus karena masih digunakan dalam booking yang aktif (Pending/Confirmed).' },
          { status: 409 }
        );
      }
      
      console.log(`No active bookings found for inactive service ${id}. Proceeding with deletion.`);
    } else {
      // 1. Cek Booking Aktif (PENDING/CONFIRMED) untuk layanan aktif
      const activeBookingServices = await prisma.bookingService.findMany({
        where: {
          serviceId: id,
          booking: { status: { in: ['PENDING', 'CONFIRMED'] } }
        },
        select: { bookingId: true }
      });
      if (activeBookingServices.length > 0) {
        console.warn(`Active service ${id} cannot be deleted due to active bookings: ${activeBookingServices.map(bs => bs.bookingId).join(', ')}`);
        return NextResponse.json(
          { error: 'Layanan tidak dapat dihapus karena masih digunakan dalam booking yang aktif (Pending/Confirmed).' },
          { status: 409 }
        );
      }

      // 2. Cek Riwayat Transaksi (Untuk layanan aktif, lebih baik dinonaktifkan daripada dihapus jika ada riwayat transaksi)
      const relatedTransactions = await prisma.transactionItem.findFirst({
        where: { serviceId: id },
        select: { transactionId: true }
      });

      if (relatedTransactions) {
        console.warn(`Active service ${id} should be deactivated instead of deleted because it exists in transaction history (ID: ${relatedTransactions.transactionId}).`);
        return NextResponse.json(
          { 
            error: 'Layanan tidak dapat dihapus karena sudah pernah digunakan dalam transaksi. Harap nonaktifkan saja.',
            suggestion: 'deactivate'
          },
          { status: 409 }
        );
      }
    }

    console.log(`Proceeding with deletion for service ${id}`);

    // Gunakan transaksi untuk menghapus semua relasi dan entitas terkait secara atomik
    await prisma.$transaction(async (tx) => {
      // 1. Hapus relasi di TherapistServiceCommission jika ada
      await tx.therapistServiceCommission?.deleteMany({ where: { serviceId: id } })
        .catch(err => console.log('No TherapistServiceCommission table or no records:', err));

      // 2. Nonaktifkan item transaksi terkait (tidak dihapus untuk menjaga integritas data historis)
      const transactionItems = await tx.transactionItem.findMany({
        where: { serviceId: id },
        select: { id: true }
      });
      
      if (transactionItems.length > 0) {
        // Kita tidak menghapus, tapi menandai sebagai inactive atau menghapus referensi
        console.log(`Found ${transactionItems.length} transaction items. Removing service reference...`);
        await tx.transactionItem.updateMany({
          where: { serviceId: id },
          data: { 
            serviceId: null,
            // Tambahkan metadata tentang layanan yang dihapus
            notes: `Service was deleted: ${existingService.name} (${id})`
          }
        });
      }
      
      // 3. Hapus relasi di BookingService jika ada
      await tx.bookingService.deleteMany({ where: { serviceId: id } });
      
      // 4. Hapus relasi di ServiceOutlet
      await tx.serviceOutlet.deleteMany({ where: { serviceId: id } });
      
      // 5. Hapus layanan
      await tx.service.delete({ where: { id } });
    });

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Log penghapusan layanan
    await logService(
      'delete',
      id,
      {
        name: existingService.name,
        description: existingService.description,
        duration: existingService.duration,
        price: existingService.price,
        commission: existingService.commission,
        isActive: existingService.isActive,
        outlets: existingService.outlets.map(o => o.outlet.name).join(', ')
      },
      undefined, // Ganti null dengan undefined
      userId // Gunakan userId dari token
    );

    return NextResponse.json({
      message: 'Layanan berhasil dihapus',
      service: existingService
    });
  } catch (error) {
    console.error('Error deleting service:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus layanan' },
      { status: 500 }
    );
  }
}