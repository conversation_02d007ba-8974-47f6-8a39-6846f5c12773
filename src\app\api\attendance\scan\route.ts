import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { qrData, scanType } = body;

    if (!qrData) {
      return NextResponse.json(
        { error: 'Data QR diperlukan' },
        { status: 400 }
      );
    }

    // Gunakan try-catch untuk mengatasi error parsing JSON
    let parsedData;
    try {
      parsedData = typeof qrData === 'string' ? JSON.parse(qrData) : qrData;
    } catch (error) {
      return NextResponse.json(
        { error: 'Format QR code tidak valid' },
        { status: 400 }
      );
    }

    const { type, id } = parsedData;

    if (!type || !id) {
      return NextResponse.json(
        { error: 'QR code tidak memiliki informasi yang diperlukan' },
        { status: 400 }
      );
    }

    if (!['therapist', 'user'].includes(type)) {
      return NextResponse.json(
        { error: 'Tipe QR code tidak valid' },
        { status: 400 }
      );
    }

    if (!['IN', 'OUT'].includes(scanType)) {
      return NextResponse.json(
        { error: 'Tipe scan tidak valid' },
        { status: 400 }
      );
    }

    let data: any = null;
    let outletId: string = '';
    
    // Ambil tanggal hari ini untuk pengecekan duplikat (reset jam ke 00:00:00)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Verifikasi data berdasarkan tipe
    if (type === 'therapist') {
      // Ambil data terapis
      const therapist = await prisma.therapist.findUnique({
        where: { id },
        include: { outlet: true }
      });
      
      if (!therapist) {
        return NextResponse.json(
          { error: 'Terapis tidak ditemukan' },
          { status: 404 }
        );
      }
      
      data = therapist;
      outletId = therapist.outletId;
      
      // Cek apakah sudah ada absensi hari ini dengan tipe yang sama
      const existingAttendance = await prisma.therapistAttendance.findFirst({
        where: {
          therapistId: id,
          attendanceType: scanType,
          createdAt: {
            gte: today,
            lt: tomorrow
          }
        }
      });
      
      if (existingAttendance) {
        return NextResponse.json({
          success: false,
          error: `Terapis sudah melakukan absensi ${scanType === 'IN' ? 'masuk' : 'keluar'} hari ini`,
          data: {
            id: therapist.id,
            name: therapist.name,
            type,
            scanType,
            timestamp: existingAttendance.createdAt.toISOString(),
            outlet: therapist.outlet?.name,
            isDuplicate: true
          }
        }, { status: 409 });  // 409 Conflict
      }
      
      // Simpan absensi terapis
      await prisma.therapistAttendance.create({
        data: {
          therapistId: id,
          outletId,
          attendanceType: scanType,
          notes: `Absensi ${scanType === 'IN' ? 'masuk' : 'keluar'} via QR code`
        }
      });
      
    } else if (type === 'user') {
      // Ambil data user
      const user = await prisma.user.findUnique({
        where: { id },
        include: { 
          allowedOutlets: {
            include: { outlet: true }
          }
        }
      });
      
      if (!user) {
        return NextResponse.json(
          { error: 'User tidak ditemukan' },
          { status: 404 }
        );
      }
      
      data = user;
      
      // Untuk user, ambil outlet pertama yang diakses
      if (user.allowedOutlets && user.allowedOutlets.length > 0) {
        outletId = user.allowedOutlets[0].outletId;
      } else {
        // Jika tidak ada akses outlet, gunakan outlet default
        const defaultOutlet = await prisma.outlet.findFirst({
          where: { isMain: true }
        });
        
        if (defaultOutlet) {
          outletId = defaultOutlet.id;
        } else {
          return NextResponse.json(
            { error: 'Tidak ada outlet default' },
            { status: 500 }
          );
        }
      }
      
      // Cek apakah sudah ada log absensi user hari ini dengan tipe yang sama
      const existingLog = await prisma.systemLog.findFirst({
        where: {
          userId: id,
          type: `USER_ATTENDANCE_${scanType}`,
          createdAt: {
            gte: today,
            lt: tomorrow
          }
        }
      });
      
      if (existingLog) {
        return NextResponse.json({
          success: false,
          error: `User sudah melakukan absensi ${scanType === 'IN' ? 'masuk' : 'keluar'} hari ini`,
          data: {
            id: user.id,
            name: user.name,
            type,
            scanType,
            timestamp: existingLog.createdAt.toISOString(),
            outlet: user.allowedOutlets[0]?.outlet?.name,
            isDuplicate: true
          }
        }, { status: 409 });  // 409 Conflict
      }
      
      // Simpan log sistem untuk absensi user
      await prisma.systemLog.create({
        data: {
          type: `USER_ATTENDANCE_${scanType}`,
          message: `User ${user.name} (${user.role}) ${scanType === 'IN' ? 'masuk' : 'keluar'} via QR code`,
          details: `User ${user.name} (${user.role}) ${scanType === 'IN' ? 'masuk' : 'keluar'} via QR code`,
          userId: id,
          outletId
        }
      });
    }

    // Format response data
    const responseData = {
      success: true,
      data: {
        id: data.id,
        name: data.name,
        type,
        scanType,
        timestamp: new Date().toISOString(),
        outlet: type === 'therapist' ? data.outlet?.name : (data.allowedOutlets && data.allowedOutlets[0]?.outlet?.name)
      }
    };

    return NextResponse.json(responseData);
    
  } catch (error) {
    console.error('Error processing scan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memproses QR code' },
      { status: 500 }
    );
  }
} 