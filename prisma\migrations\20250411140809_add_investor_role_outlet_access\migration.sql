-- AlterEnum
ALTER TYPE "Role" ADD VALUE 'INVESTOR';

-- CreateTable
CREATE TABLE "UserOutletAccess" (
    "id" TEXT NOT NULL,
    "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" TEXT NOT NULL,
    "outletId" TEXT NOT NULL,

    CONSTRAINT "UserOutletAccess_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "UserOutletAccess_userId_outletId_key" ON "UserOutletAccess"("userId", "outletId");

-- AddForeignKey
ALTER TABLE "UserOutletAccess" ADD CONSTRAINT "UserOutletAccess_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserOutletAccess" ADD CONSTRAINT "UserOutletAccess_outletId_fkey" FOREIGN KEY ("outletId") REFERENCES "Outlet"("id") ON DELETE CASCADE ON UPDATE CASCADE;
