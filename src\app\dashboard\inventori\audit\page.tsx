'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import {
  FiClipboard,
  FiPlus,
  FiSearch,
  FiFilter,
  FiEdit3,
  FiTrash2,
  FiEye,
  FiPlay,
  FiCheck,
  FiClock,
  FiAlertCircle,
  FiUser,
  FiCalendar,
  FiMapPin,
  FiX,
  FiSave
} from 'react-icons/fi';
import { useOutletContext } from '@/contexts/OutletContext';
// import PermissionGuard from '@/components/permission/PermissionGuard';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface InventoryAudit {
  id: string;
  title: string;
  description: string | null;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  scheduledDate: string;
  startedAt: string | null;
  completedAt: string | null;
  notes: string | null;
  createdAt: string;
  outlet: {
    id: string;
    name: string;
  };
  createdBy: {
    id: string;
    name: string;
  };

  _count: {
    auditItems: number;
  };
}



const AuditPage = () => {
  const router = useRouter();
  const { selectedOutletId } = useOutletContext();
  const [audits, setAudits] = useState<InventoryAudit[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [deletingAudit, setDeletingAudit] = useState<InventoryAudit | null>(null);
  const [editingAudit, setEditingAudit] = useState<InventoryAudit | null>(null);
  const [createForm, setCreateForm] = useState({
    title: '',
    description: '',
    scheduledDate: ''
  });
  const [editForm, setEditForm] = useState({
    title: '',
    description: '',
    scheduledDate: '',
    status: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [itemCount, setItemCount] = useState<number>(0);



  // Fetch audits
  const fetchAudits = async (page = 1) => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString()
      });
      
      if (selectedOutletId) {
        params.append('outletId', selectedOutletId);
      }
      
      if (statusFilter) {
        params.append('status', statusFilter);
      }

      const response = await fetch(`/api/inventory/audits?${params}`);
      
      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = 'Gagal mengambil data audit';
        
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch {
          console.error('Non-JSON error response:', errorText);
        }
        
        throw new Error(errorMessage);
      }

      const data = await response.json();
      setAudits(data.audits);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching audits:', error);
      toast.error(error instanceof Error ? error.message : 'Terjadi kesalahan saat mengambil data audit');
    }
  };

  // Fetch item count for selected outlet
  const fetchItemCount = async () => {
    if (!selectedOutletId) {
      setItemCount(0);
      return;
    }

    try {
      const response = await fetch(`/api/inventory/items?outletId=${selectedOutletId}&count=true`);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        setItemCount(0);
        return;
      }

      const data = await response.json();
      setItemCount(data.total || 0);
    } catch (error) {
      console.error('Error fetching item count:', error);
      setItemCount(0);
    }
  };

  // Handle create audit
  const handleCreateAudit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedOutletId) {
      toast.error('Pilih outlet terlebih dahulu');
      return;
    }

    try {
      const response = await fetch('/api/inventory/audits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...createForm,
          outletId: selectedOutletId
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast.success('Audit berhasil dibuat');
        setShowCreateModal(false);
        setCreateForm({
          title: '',
          description: '',
          scheduledDate: ''
        });
        await fetchAudits(pagination.page);
      } else {
        toast.error(data.error || 'Gagal membuat audit');
      }
    } catch (error) {
      console.error('Error creating audit:', error);
      toast.error('Terjadi kesalahan saat membuat audit');
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchAudits();
      setLoading(false);
    };
    
    loadData();
  }, [selectedOutletId, statusFilter]);

  // Fetch item count when outlet changes
  useEffect(() => {
    fetchItemCount();
  }, [selectedOutletId]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (showDeleteModal) {
          setShowDeleteModal(false);
          setDeletingAudit(null);
        }
        if (showEditModal) {
          setShowEditModal(false);
          setEditingAudit(null);
        }
        if (showCreateModal) {
          setShowCreateModal(false);
        }
      }
    };

    if (showDeleteModal || showEditModal || showCreateModal) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [showDeleteModal, showEditModal, showCreateModal]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'badge-info';
      case 'IN_PROGRESS':
        return 'badge-warning';
      case 'COMPLETED':
        return 'badge-success';
      case 'CANCELLED':
        return 'badge-error';
      default:
        return 'badge-ghost';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <FiClock className="w-4 h-4" />;
      case 'IN_PROGRESS':
        return <FiPlay className="w-4 h-4" />;
      case 'COMPLETED':
        return <FiCheck className="w-4 h-4" />;
      case 'CANCELLED':
        return <FiX className="w-4 h-4" />;
      default:
        return <FiClipboard className="w-4 h-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'Terjadwal';
      case 'IN_PROGRESS':
        return 'Sedang Berjalan';
      case 'COMPLETED':
        return 'Selesai';
      case 'CANCELLED':
        return 'Dibatalkan';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle start audit
  const handleStartAudit = async (auditId: string) => {
    try {
      const response = await fetch(`/api/inventory/audits/${auditId}/start`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast.success('Audit berhasil dimulai');
        await fetchAudits(pagination.page);
      } else {
        toast.error(data.error || 'Gagal memulai audit');
      }
    } catch (error) {
      console.error('Error starting audit:', error);
      toast.error('Terjadi kesalahan saat memulai audit');
    }
  };

  // Handle complete audit
  const handleCompleteAudit = async (auditId: string) => {
    try {
      const response = await fetch(`/api/inventory/audits/${auditId}/complete`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast.success('Audit berhasil diselesaikan');
        await fetchAudits(pagination.page);
      } else {
        toast.error(data.error || 'Gagal menyelesaikan audit');
      }
    } catch (error) {
      console.error('Error completing audit:', error);
      toast.error('Terjadi kesalahan saat menyelesaikan audit');
    }
  };

  // Handle delete audit
  const handleDeleteAudit = async () => {
    if (!deletingAudit) return;
    
    try {
      const response = await fetch(`/api/inventory/audits/${deletingAudit.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast.success('Audit berhasil dihapus');
        setShowDeleteModal(false);
        setDeletingAudit(null);
        
        // Jika ini audit terakhir di halaman dan bukan halaman pertama, kembali ke halaman sebelumnya
        const isLastItemOnPage = audits.length === 1;
        const isNotFirstPage = pagination.page > 1;
        const targetPage = isLastItemOnPage && isNotFirstPage ? pagination.page - 1 : pagination.page;
        
        await fetchAudits(targetPage);
      } else {
        toast.error(data.error || 'Gagal menghapus audit');
      }
    } catch (error) {
      console.error('Error deleting audit:', error);
      toast.error('Terjadi kesalahan saat menghapus audit');
    }
  };

  // Handle edit audit
  const handleEditAudit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingAudit) return;
    
    try {
      const response = await fetch(`/api/inventory/audits/${editingAudit.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: editForm.title.trim(),
          description: editForm.description.trim() || null,
          scheduledDate: editForm.scheduledDate,
          status: editForm.status
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast.success('Audit berhasil diperbarui');
        setShowEditModal(false);
        setEditingAudit(null);
        setEditForm({ title: '', description: '', scheduledDate: '', status: '' });
        await fetchAudits(pagination.page);
      } else {
        toast.error(data.error || 'Gagal memperbarui audit');
      }
    } catch (error) {
      console.error('Error updating audit:', error);
      toast.error('Terjadi kesalahan saat memperbarui audit');
    }
  };

  // Handle open edit modal
  const handleOpenEditModal = (audit: InventoryAudit) => {
    setEditingAudit(audit);
    // Format datetime untuk datetime-local input (YYYY-MM-DDTHH:mm)
    const scheduledDateTime = new Date(audit.scheduledDate);
    const formattedDateTime = scheduledDateTime.toISOString().slice(0, 16);
    
    setEditForm({
      title: audit.title,
      description: audit.description || '',
      scheduledDate: formattedDateTime,
      status: audit.status
    });
    setShowEditModal(true);
  };

  // Handle open delete modal
  const handleOpenDeleteModal = (audit: InventoryAudit) => {
    setDeletingAudit(audit);
    setShowDeleteModal(true);
  };

  // Handle change status
  const handleChangeStatus = async (auditId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/inventory/audits/${auditId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: newStatus
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        const statusText = getStatusText(newStatus);
        toast.success(`Status audit berhasil diubah ke ${statusText}`);
        await fetchAudits(pagination.page);
      } else {
        toast.error(data.error || 'Gagal mengubah status audit');
      }
    } catch (error) {
      console.error('Error changing audit status:', error);
      toast.error('Terjadi kesalahan saat mengubah status audit');
    }
  };

  // Handle back to inventory
  const handleBackToInventory = () => {
    router.push('/dashboard/inventori');
  };

  // Animation variants
  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 }
  };

  const stagger = {
    initial: {},
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <span className="loading loading-spinner loading-lg"></span>
      </div>
    );
  }

  return (
    <motion.div 
      className="space-y-6"
      variants={stagger}
      initial="initial"
      animate="animate"
    >
        {/* Header */}
        <motion.div variants={fadeInUp} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <button
                onClick={handleBackToInventory}
                className="btn btn-ghost btn-sm flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Kembali
              </button>
              <h1 className="text-2xl font-bold text-gray-900">
                Audit Inventori
              </h1>
            </div>
            <p className="text-gray-600">
              Kelola audit dan pemeriksaan inventori
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <div>
              <button
                onClick={() => setShowCreateModal(true)}
                className="btn btn-primary btn-sm flex items-center gap-2"
              >
                <FiPlus className="w-4 h-4" />
                Buat Audit
              </button>
            </div>
          </div>
        </motion.div>

        {/* Filters */}
        <motion.div variants={fadeInUp} className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Cari audit..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input input-bordered w-full pl-10"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="select select-bordered w-full"
          >
            <option value="">Semua Status</option>
            <option value="SCHEDULED">Terjadwal</option>
            <option value="IN_PROGRESS">Sedang Berjalan</option>
            <option value="COMPLETED">Selesai</option>
          </select>
        </motion.div>

        {/* Audits Grid */}
        <motion.div variants={fadeInUp} className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {audits.map((audit) => (
            <motion.div
              key={audit.id}
              variants={fadeInUp}
              className="card bg-base-100 shadow-sm border border-gray-200"
            >
              <div className="card-body">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="card-title text-base mb-2">
                      {audit.title}
                    </h3>
                    
                    <div className="dropdown dropdown-end">
                      <div 
                        tabIndex={0} 
                        role="button" 
                        className={`badge badge-sm gap-1 ${getStatusColor(audit.status)} cursor-pointer hover:brightness-110 transition-all`}
                        title="Klik untuk mengubah status"
                      >
                        {getStatusIcon(audit.status)}
                        {getStatusText(audit.status)}
                        <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                      <ul tabIndex={0} className="dropdown-content menu bg-base-100 rounded-box z-[1] w-48 p-2 shadow-lg border">
                        <li>
                          <button 
                            onClick={() => handleChangeStatus(audit.id, 'PENDING')}
                            className={`flex items-center gap-2 ${audit.status === 'PENDING' ? 'active' : ''}`}
                          >
                            <FiClock className="w-4 h-4" />
                            🕒 Terjadwal
                          </button>
                        </li>
                        <li>
                          <button 
                            onClick={() => handleChangeStatus(audit.id, 'IN_PROGRESS')}
                            className={`flex items-center gap-2 ${audit.status === 'IN_PROGRESS' ? 'active' : ''}`}
                          >
                            <FiPlay className="w-4 h-4" />
                            ⏳ Sedang Berlangsung
                          </button>
                        </li>
                        <li>
                          <button 
                            onClick={() => handleChangeStatus(audit.id, 'COMPLETED')}
                            className={`flex items-center gap-2 ${audit.status === 'COMPLETED' ? 'active' : ''}`}
                          >
                            <FiCheck className="w-4 h-4" />
                            ✅ Selesai
                          </button>
                        </li>
                        <li>
                          <button 
                            onClick={() => handleChangeStatus(audit.id, 'CANCELLED')}
                            className={`flex items-center gap-2 ${audit.status === 'CANCELLED' ? 'active' : ''}`}
                          >
                            <FiX className="w-4 h-4" />
                            ❌ Dibatalkan
                          </button>
                        </li>
                      </ul>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Link
                      href={`/dashboard/inventori/audit/${audit.id}`}
                      className="btn btn-ghost btn-sm btn-square"
                    >
                      <FiEye className="w-4 h-4" />
                    </Link>
                    
                    <div>
                      <button 
                        onClick={() => handleOpenEditModal(audit)}
                        className="btn btn-ghost btn-sm btn-square"
                        title="Edit Audit"
                      >
                        <FiEdit3 className="w-4 h-4" />
                      </button>
                    </div>
                    
                    <div>
                      {(audit.status === 'PENDING' || audit.status === 'CANCELLED') && (
                        <button 
                          onClick={() => handleOpenDeleteModal(audit)}
                          className="btn btn-ghost btn-sm btn-square text-error"
                          title="Hapus Audit"
                        >
                          <FiTrash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
                
                {audit.description && (
                  <p className="text-sm text-gray-600 mb-4">
                    {audit.description}
                  </p>
                )}
                
                <div className="space-y-3">
                  <div className="flex items-center gap-3 text-sm">
                    <FiMapPin className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Outlet:</span>
                    <span className="font-medium">
                      {audit.outlet.name}
                    </span>
                  </div>
                  

                  
                  <div className="flex items-center gap-3 text-sm">
                    <FiCalendar className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Terjadwal:</span>
                    <span className="font-medium">
                      {formatDate(audit.scheduledDate)}
                    </span>
                  </div>
                
                  {audit.startedAt && (
                    <div className="flex items-center gap-3 text-sm">
                      <FiPlay className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">Dimulai:</span>
                      <span className="font-medium">
                        {formatDateTime(audit.startedAt)}
                      </span>
                    </div>
                  )}
                  
                  {audit.completedAt && (
                    <div className="flex items-center gap-3 text-sm">
                      <FiCheck className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">Selesai:</span>
                      <span className="font-medium">
                        {formatDateTime(audit.completedAt)}
                      </span>
                    </div>
                  )}
                  
                  <div className="pt-3 border-t border-gray-200">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500">Total Item:</span>
                      <span className="font-semibold text-primary">
                        {audit._count.auditItems} item
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm mt-2">
                      <span className="text-gray-500">Dibuat oleh:</span>
                      <span className="font-medium">
                        {audit.createdBy.name}
                      </span>
                    </div>
                  </div>
                </div>
                
                {/* Action Buttons */}
                <div className="card-actions justify-end mt-4 pt-4 border-t border-gray-200">
                  <Link
                    href={`/dashboard/inventori/audit/${audit.id}`}
                    className="btn btn-primary btn-sm flex-1"
                  >
                    <FiEye className="w-4 h-4" />
                    Lihat Detail
                  </Link>
                  
                  {audit.status === 'PENDING' && (
                    <div>
                      <button 
                        onClick={() => handleStartAudit(audit.id)}
                        className="btn btn-success btn-sm"
                      >
                        <FiPlay className="w-4 h-4" />
                        Mulai
                      </button>
                    </div>
                  )}
                  
                  {audit.status === 'IN_PROGRESS' && (
                    <div>
                      <button 
                        onClick={() => handleCompleteAudit(audit.id)}
                        className="btn btn-success btn-sm"
                      >
                        <FiCheck className="w-4 h-4" />
                        Selesai
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Empty State */}
        {audits.length === 0 && (
          <motion.div variants={fadeInUp} className="text-center py-12">
            <FiClipboard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">
              Belum ada audit
            </h3>
            <p className="text-gray-600 mb-6">
              Buat audit inventori pertama untuk memulai pemeriksaan stok
            </p>
            <div>
              <button
                onClick={() => setShowCreateModal(true)}
                className="btn btn-primary gap-2"
              >
                <FiPlus className="w-4 h-4" />
                Buat Audit Pertama
              </button>
            </div>
          </motion.div>
        )}

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <motion.div variants={fadeInUp} className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Menampilkan {((pagination.page - 1) * pagination.limit) + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} dari {pagination.total} audit
            </div>
            
            <div className="join">
              <button
                onClick={() => fetchAudits(pagination.page - 1)}
                disabled={pagination.page === 1}
                className="join-item btn btn-sm"
              >
                Sebelumnya
              </button>
              
              <button className="join-item btn btn-sm btn-active">
                {pagination.page} / {pagination.totalPages}
              </button>
              
              <button
                onClick={() => fetchAudits(pagination.page + 1)}
                disabled={pagination.page === pagination.totalPages}
                className="join-item btn btn-sm"
              >
                Selanjutnya
              </button>
            </div>
          </motion.div>
        )}
        {/* Delete Confirmation Modal */}
        {showDeleteModal && deletingAudit && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <div className="flex items-center gap-4 mb-4">
                <div className="flex-shrink-0 w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                  <FiAlertCircle className="w-6 h-6 text-red-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Hapus Audit</h3>
                  <p className="text-sm text-gray-600">Apakah Anda yakin ingin menghapus audit ini?</p>
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Judul:</span>
                  <span className="text-sm text-gray-900">{deletingAudit.title}</span>
                </div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Outlet:</span>
                  <span className="text-sm text-gray-900">{deletingAudit.outlet.name}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Total Item:</span>
                  <span className="text-sm text-gray-900">{deletingAudit._count.auditItems} item</span>
                </div>
              </div>
              
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
                <div className="flex items-start gap-2">
                  <FiAlertCircle className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800">Peringatan!</p>
                    <p className="text-sm text-yellow-700">
                      Data audit dan semua item yang terkait akan dihapus secara permanen. 
                      Tindakan ini tidak dapat dibatalkan.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setDeletingAudit(null);
                  }}
                  className="btn btn-ghost flex-1"
                >
                  Batal
                </button>
                <button
                  onClick={handleDeleteAudit}
                  className="btn btn-error flex-1 gap-2"
                >
                  <FiTrash2 className="w-4 h-4" />
                  Hapus Audit
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Edit Audit Modal */}
        {showEditModal && editingAudit && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Edit Audit Inventori</h3>
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditingAudit(null);
                  }}
                  className="btn btn-ghost btn-sm btn-circle"
                >
                  ✕
                </button>
              </div>
              
              <form onSubmit={handleEditAudit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Judul Audit <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={editForm.title}
                    onChange={(e) => setEditForm(prev => ({ ...prev, title: e.target.value }))}
                    className="input input-bordered w-full"
                    required
                    placeholder="Masukkan judul audit"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Deskripsi
                  </label>
                  <textarea
                    value={editForm.description}
                    onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
                    className="textarea textarea-bordered w-full h-20"
                    placeholder="Deskripsi audit (opsional)"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Tanggal & Waktu Terjadwal <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="datetime-local"
                    value={editForm.scheduledDate}
                    onChange={(e) => setEditForm(prev => ({ ...prev, scheduledDate: e.target.value }))}
                    className="input input-bordered w-full"
                    required
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    Tentukan tanggal dan waktu mulai audit inventori
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Status Audit <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={editForm.status}
                    onChange={(e) => setEditForm(prev => ({ ...prev, status: e.target.value }))}
                    className="select select-bordered w-full"
                    required
                  >
                    <option value="PENDING">🕒 Terjadwal</option>
                    <option value="IN_PROGRESS">⏳ Sedang Berlangsung</option>
                    <option value="COMPLETED">✅ Selesai</option>
                    <option value="CANCELLED">❌ Dibatalkan</option>
                  </select>
                  <div className="text-xs text-gray-500 mt-1">
                    {editForm.status === 'PENDING' && 'Audit belum dimulai, masih dalam tahap perencanaan'}
                    {editForm.status === 'IN_PROGRESS' && 'Audit sedang berlangsung, item sedang diperiksa'}
                    {editForm.status === 'COMPLETED' && 'Audit telah selesai, hasil sudah final'}
                    {editForm.status === 'CANCELLED' && 'Audit dibatalkan dan tidak akan dilaksanakan'}
                  </div>
                </div>
                

                
                <div className="flex items-center gap-3 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowEditModal(false);
                      setEditingAudit(null);
                    }}
                    className="btn btn-ghost flex-1"
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary flex-1 gap-2"
                    disabled={!editForm.title.trim() || !editForm.scheduledDate || !editForm.status}
                  >
                    <FiSave className="w-4 h-4" />
                    Simpan Perubahan
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Create Audit Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-lg mx-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Buat Audit Inventori</h3>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="btn btn-ghost btn-sm btn-square"
                >
                  <FiX className="w-4 h-4" />
                </button>
              </div>
              
              <form onSubmit={handleCreateAudit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Judul Audit *
                  </label>
                  <input
                    type="text"
                    value={createForm.title}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, title: e.target.value }))}
                    className="input input-bordered w-full"
                    placeholder="Masukkan judul audit"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Deskripsi
                  </label>
                  <textarea
                    value={createForm.description}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, description: e.target.value }))}
                    className="textarea textarea-bordered w-full"
                    rows={3}
                    placeholder="Masukkan deskripsi audit"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tanggal Terjadwal *
                  </label>
                  <input
                    type="datetime-local"
                    value={createForm.scheduledDate}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, scheduledDate: e.target.value }))}
                    className="input input-bordered w-full"
                    required
                  />
                </div>

                {/* Info Outlet */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <FiMapPin className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">Outlet Terpilih</span>
                  </div>
                  <p className="text-sm text-blue-700 mb-2">
                    Audit akan dibuat untuk outlet yang sedang aktif dipilih. 
                    Item inventori yang akan diaudit adalah semua item yang tersedia di outlet tersebut.
                  </p>
                  {selectedOutletId && itemCount > 0 && (
                    <div className="flex items-center gap-2 text-sm text-blue-800 bg-blue-100 rounded-md px-2 py-1">
                      <FiClipboard className="w-4 h-4" />
                      <span><strong>{itemCount}</strong> item akan diaudit</span>
                    </div>
                  )}
                  {selectedOutletId && itemCount === 0 && (
                    <div className="flex items-center gap-2 text-sm text-amber-800 bg-amber-100 rounded-md px-2 py-1">
                      <FiAlertCircle className="w-4 h-4" />
                      <span>Tidak ada item tersedia untuk diaudit di outlet ini</span>
                    </div>
                  )}
                  {!selectedOutletId && (
                    <div className="flex items-center gap-2 mt-2 text-amber-700">
                      <FiAlertCircle className="w-4 h-4" />
                      <span className="text-sm">Silakan pilih outlet terlebih dahulu dari menu sidebar</span>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center gap-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowCreateModal(false)}
                    className="btn btn-ghost flex-1"
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary flex-1"
                    disabled={!selectedOutletId || itemCount === 0 || !createForm.title.trim() || !createForm.scheduledDate}
                  >
                    Buat Audit
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </motion.div>
  );
};

export default AuditPage;