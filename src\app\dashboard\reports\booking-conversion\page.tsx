'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiCalendar, FiFilter, FiBarChart2, FiPieChart,
  FiAlertTriangle, FiDownload, FiRefreshCw, FiChevronLeft,
  FiCheckCircle, FiXCircle, FiClock, FiInfo
} from 'react-icons/fi';
import { format } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { useOutletContext } from '@/contexts/OutletContext';
import { useThemeContext } from '@/contexts/ThemeContext';
import Link from 'next/link';

// Import komponen laporan
import { BookingConversionChart, ReportCard } from '@/components/reports';

// Format tanggal untuk tampilan
const formatDateDisplay = (date: Date | null): string => {
  if (!date) return '';
  return format(date, 'dd MMMM yyyy', { locale: idLocale });
};

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('id-ID', { 
    style: 'currency', 
    currency: 'IDR', 
    minimumFractionDigits: 0 
  }).format(value);
};

// Varian Animasi
const fadeInUp = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.5 } } };
const staggerContainer = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1 } } };

// Interface untuk data konversi booking
interface BookingConversionSummary {
  totalBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  noShowBookings: number;
  pendingBookings: number;
  conversionRate: number;
  cancellationRate: number;
  noShowRate: number;
}

interface DailyConversion {
  date: string;
  totalBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  noShowBookings: number;
  pendingBookings: number;
  conversionRate: number;
}

interface CancellationReason {
  reason: string;
  count: number;
}

interface ConversionStrategy {
  title: string;
  description: string;
  impact: string;
}

interface BookingConversionData {
  summary: BookingConversionSummary;
  dailyConversion: DailyConversion[];
  cancellationReasons: CancellationReason[];
  conversionStrategies: ConversionStrategy[];
}

interface Outlet {
  id: string;
  name: string;
}

export default function BookingConversionPage() {
  const { selectedOutletId: contextOutletId } = useOutletContext() ?? { selectedOutletId: null };
  const { theme } = useThemeContext();
  const [bookingConversionData, setBookingConversionData] = useState<BookingConversionData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [chartType, setChartType] = useState<'bar' | 'pie'>('bar');

  // Filters State
  const [startDate, setStartDate] = useState<Date | null>(() => {
    const today = new Date();
    return new Date(today.getFullYear(), today.getMonth(), 1); // Tanggal 1 bulan ini
  });
  const [endDate, setEndDate] = useState<Date | null>(new Date()); // Hari ini
  const [selectedOutletId, setSelectedOutletId] = useState<string>(contextOutletId || '');

  // Load outlets for filter dropdown
  useEffect(() => {
    const fetchOutlets = async () => {
      try {
        const response = await fetch('/api/outlets?simple=true');
        if (!response.ok) throw new Error('Gagal memuat outlet');
        const data = await response.json();
        setOutlets(data.outlets || []);

        // Hanya set state lokal dari context JIKA state lokal saat ini KOSONG ('')
        // DAN contextOutletId ada DAN valid (ada di daftar outlet).
        if (selectedOutletId === '' && contextOutletId && data.outlets.some((o: Outlet) => o.id === contextOutletId)) {
          setSelectedOutletId(contextOutletId);
        }
      } catch (err) {
        // Abaikan error fetching outlets
      }
    };
    fetchOutlets();
  }, [contextOutletId]);

  // Fetch booking conversion data
  const fetchBookingConversionData = async () => {
    if (!startDate || !endDate) {
      setError("Tanggal mulai dan tanggal selesai harus dipilih.");
      setBookingConversionData(null);
      return;
    }

    setIsLoading(true);
    setError(null);
    setBookingConversionData(null);

    // Format tanggal untuk API (YYYY-MM-DD)
    const formatLocalDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const formattedStartDate = formatLocalDate(startDate);
    const formattedEndDate = formatLocalDate(endDate);

    const params = new URLSearchParams({
      startDate: formattedStartDate,
      endDate: formattedEndDate,
    });

    if (selectedOutletId) {
      params.set('outletId', selectedOutletId);
    }

    const apiUrl = `/api/reports/booking-conversion?${params.toString()}`;

    try {
      const response = await fetch(apiUrl);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Gagal memuat data konversi booking: ${response.statusText}`);
      }

      const data = await response.json();
      setBookingConversionData(data.bookingConversion);
    } catch (err) {
      console.error('Error fetching booking conversion data:', err);
      setError(err instanceof Error ? err.message : "Terjadi kesalahan saat memuat data konversi booking.");
    } finally {
      setIsLoading(false);
    }
  };

  // Automatically fetch data when filters change
  useEffect(() => {
    fetchBookingConversionData();
  }, [startDate, endDate, selectedOutletId]);

  return (
    <motion.div
      className="container mx-auto p-4 md:p-6 space-y-6"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {/* Header with Back Button */}
      <motion.div variants={fadeInUp} className="flex items-center gap-2">
        <Link href="/dashboard/reports" className="btn btn-sm btn-ghost">
          <FiChevronLeft /> Kembali
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Laporan Konversi Booking</h1>
          <p className="text-gray-600">Analisis rasio booking yang berhasil menjadi transaksi</p>
        </div>
      </motion.div>

      {/* Filters Section */}
      <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Tanggal Mulai */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiCalendar className="text-gray-600" /> Tanggal Mulai
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline w-full justify-between overflow-hidden">
                <span className="truncate">{startDate ? formatDateDisplay(startDate) : 'Pilih tanggal mulai'}</span>
                <span className="text-xs">▼</span>
              </div>
              <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button className="btn btn-sm btn-ghost" onClick={() => setStartDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() - 1, 1) : null)}>
                        «
                      </button>
                      <div className="text-sm font-medium">
                        {startDate ? format(startDate, 'MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                      </div>
                      <button className="btn btn-sm btn-ghost" onClick={() => setStartDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() + 1, 1) : null)}>
                        »
                      </button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {/* Hari dalam seminggu */}
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}

                      {/* Tanggal */}
                      {startDate && Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(startDate.getFullYear(), startDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === startDate.getMonth();
                        const isSelected = startDate && date.getDate() === startDate.getDate() &&
                                          date.getMonth() === startDate.getMonth() &&
                                          date.getFullYear() === startDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected ? 'hover:bg-base-200' : ''}`}
                            onClick={() => setStartDate(new Date(date))}
                            disabled={!isCurrentMonth}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tanggal Akhir */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiCalendar className="text-gray-600" /> Tanggal Akhir
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline w-full justify-between overflow-hidden">
                <span className="truncate">{endDate ? formatDateDisplay(endDate) : 'Pilih tanggal akhir'}</span>
                <span className="text-xs">▼</span>
              </div>
              <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button className="btn btn-sm btn-ghost" onClick={() => setEndDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() - 1, 1) : null)}>
                        «
                      </button>
                      <div className="text-sm font-medium">
                        {endDate ? format(endDate, 'MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                      </div>
                      <button className="btn btn-sm btn-ghost" onClick={() => setEndDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() + 1, 1) : null)}>
                        »
                      </button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {/* Hari dalam seminggu */}
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}

                      {/* Tanggal */}
                      {endDate && Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(endDate.getFullYear(), endDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === endDate.getMonth();
                        const isSelected = endDate && date.getDate() === endDate.getDate() &&
                                          date.getMonth() === endDate.getMonth() &&
                                          date.getFullYear() === endDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();
                        const isDisabled = !isCurrentMonth || (startDate && date < startDate) || date > new Date();

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-base-200' : ''}
                                      ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={() => !isDisabled && setEndDate(new Date(date))}
                            disabled={isDisabled}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Filter Outlet */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiFilter className="text-gray-600" /> Outlet
            </label>
            <select
              className="select select-bordered select-sm w-full"
              value={selectedOutletId}
              onChange={(e) => setSelectedOutletId(e.target.value)}
            >
              <option value="">Semua Outlet</option>
              {outlets.map(outlet => (
                <option key={outlet.id} value={outlet.id}>{outlet.name}</option>
              ))}
            </select>
          </div>

          {/* Chart Controls */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiBarChart2 className="text-gray-600" /> Visualisasi
            </label>
            <div className="flex gap-2">
              <div className="join">
                <button
                  className={`btn btn-sm join-item ${chartType === 'bar' ? 'btn-active' : ''}`}
                  onClick={() => setChartType('bar')}
                  title="Bar Chart"
                >
                  <FiBarChart2 />
                </button>
                <button
                  className={`btn btn-sm join-item ${chartType === 'pie' ? 'btn-active' : ''}`}
                  onClick={() => setChartType('pie')}
                  title="Pie Chart"
                >
                  <FiPieChart />
                </button>
              </div>
              <button
                className="btn btn-sm btn-outline flex-1"
                onClick={fetchBookingConversionData}
              >
                <FiRefreshCw className="mr-1" /> Refresh Data
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Loading State */}
      {isLoading && (
        <motion.div variants={fadeInUp} className="text-center py-10">
          <span className="loading loading-spinner loading-lg text-primary"></span>
          <p className="mt-2 text-gray-600">Memuat data konversi booking...</p>
        </motion.div>
      )}

      {/* Error State */}
      {error && !isLoading && (
        <motion.div variants={fadeInUp} className="alert alert-error">
          <FiAlertTriangle />
          <span>Error: {error}</span>
        </motion.div>
      )}

      {/* Booking Conversion Data Display */}
      {!isLoading && !error && bookingConversionData && (
        <>
          {/* Summary Cards */}
          <motion.div variants={fadeInUp} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <ReportCard
              title="Total Booking"
              value={bookingConversionData.summary.totalBookings}
              type="number"
              icon="calendar"
            />
            <ReportCard
              title="Tingkat Konversi"
              value={bookingConversionData.summary.conversionRate}
              type="percentage"
              icon="calendar"
            />
            <ReportCard
              title="Tingkat Pembatalan"
              value={bookingConversionData.summary.cancellationRate}
              type="percentage"
              icon="calendar"
            />
            <ReportCard
              title="Tingkat Tidak Hadir"
              value={bookingConversionData.summary.noShowRate}
              type="percentage"
              icon="calendar"
            />
          </motion.div>

          {/* Status Breakdown */}
          <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700">
              <FiCheckCircle /> Status Booking
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <div className="flex items-center gap-2">
                  <FiCheckCircle className="text-green-500" />
                  <h3 className="font-semibold text-green-700">Selesai</h3>
                </div>
                <div className="mt-2 text-3xl font-bold text-green-700">{bookingConversionData.summary.completedBookings}</div>
                <div className="mt-1 text-sm text-green-600">
                  {((bookingConversionData.summary.completedBookings / bookingConversionData.summary.totalBookings) * 100).toFixed(1)}% dari total
                </div>
              </div>
              <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                <div className="flex items-center gap-2">
                  <FiXCircle className="text-red-500" />
                  <h3 className="font-semibold text-red-700">Dibatalkan</h3>
                </div>
                <div className="mt-2 text-3xl font-bold text-red-700">{bookingConversionData.summary.cancelledBookings}</div>
                <div className="mt-1 text-sm text-red-600">
                  {((bookingConversionData.summary.cancelledBookings / bookingConversionData.summary.totalBookings) * 100).toFixed(1)}% dari total
                </div>
              </div>
              <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                <div className="flex items-center gap-2">
                  <FiClock className="text-amber-500" />
                  <h3 className="font-semibold text-amber-700">Tidak Hadir</h3>
                </div>
                <div className="mt-2 text-3xl font-bold text-amber-700">{bookingConversionData.summary.noShowBookings}</div>
                <div className="mt-1 text-sm text-amber-600">
                  {((bookingConversionData.summary.noShowBookings / bookingConversionData.summary.totalBookings) * 100).toFixed(1)}% dari total
                </div>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2">
                  <FiInfo className="text-blue-500" />
                  <h3 className="font-semibold text-blue-700">Pending</h3>
                </div>
                <div className="mt-2 text-3xl font-bold text-blue-700">{bookingConversionData.summary.pendingBookings}</div>
                <div className="mt-1 text-sm text-blue-600">
                  {((bookingConversionData.summary.pendingBookings / bookingConversionData.summary.totalBookings) * 100).toFixed(1)}% dari total
                </div>
              </div>
            </div>
          </motion.div>

          {/* Conversion Chart */}
          <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700">
              <FiBarChart2 /> Visualisasi Konversi Booking
            </h2>
            <BookingConversionChart
              data={bookingConversionData.dailyConversion}
              type={chartType}
              height={400}
            />
          </motion.div>

          {/* Cancellation Reasons */}
          <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700">
              <FiXCircle /> Alasan Pembatalan
            </h2>
            <div className="overflow-x-auto">
              <table className="table table-zebra w-full">
                <thead>
                  <tr>
                    <th>Alasan</th>
                    <th className="text-right">Jumlah</th>
                    <th className="text-right">Persentase</th>
                  </tr>
                </thead>
                <tbody>
                  {bookingConversionData.cancellationReasons.map((reason, index) => (
                    <tr key={index}>
                      <td>{reason.reason}</td>
                      <td className="text-right">{reason.count}</td>
                      <td className="text-right">
                        {((reason.count / bookingConversionData.summary.cancelledBookings) * 100).toFixed(1)}%
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </motion.div>

          {/* Conversion Strategies */}
          <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700">
              <FiCheckCircle /> Strategi Meningkatkan Konversi
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {bookingConversionData.conversionStrategies.map((strategy, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <h3 className="font-semibold">{strategy.title}</h3>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                      strategy.impact === 'Tinggi' 
                        ? 'bg-green-100 text-green-800' 
                        : strategy.impact === 'Sedang'
                        ? 'bg-amber-100 text-amber-800'
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      Dampak: {strategy.impact}
                    </div>
                  </div>
                  <p className="mt-2 text-gray-700">{strategy.description}</p>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Daily Conversion Table */}
          <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700">
              <FiCalendar /> Konversi Booking Harian
            </h2>
            <div className="overflow-x-auto">
              <table className="table table-zebra w-full">
                <thead>
                  <tr>
                    <th>Tanggal</th>
                    <th className="text-right">Total</th>
                    <th className="text-right">Selesai</th>
                    <th className="text-right">Dibatalkan</th>
                    <th className="text-right">Tidak Hadir</th>
                    <th className="text-right">Pending</th>
                    <th className="text-right">Tingkat Konversi</th>
                  </tr>
                </thead>
                <tbody>
                  {bookingConversionData.dailyConversion.map((day) => (
                    <tr key={day.date}>
                      <td>{format(new Date(day.date), 'dd MMMM yyyy', { locale: idLocale })}</td>
                      <td className="text-right">{day.totalBookings}</td>
                      <td className="text-right">{day.completedBookings}</td>
                      <td className="text-right">{day.cancelledBookings}</td>
                      <td className="text-right">{day.noShowBookings}</td>
                      <td className="text-right">{day.pendingBookings}</td>
                      <td className={`text-right ${
                        day.conversionRate >= 70 ? 'text-green-600' : 
                        day.conversionRate >= 50 ? 'text-amber-600' : 
                        'text-red-600'
                      }`}>
                        {day.conversionRate.toFixed(1)}%
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </motion.div>
        </>
      )}

      {/* No Data State */}
      {!isLoading && !error && (!bookingConversionData || bookingConversionData.dailyConversion.length === 0) && (
        <motion.div variants={fadeInUp} className="bg-white p-8 rounded-lg shadow text-center">
          <FiCalendar className="mx-auto text-gray-400 mb-4" size={48} />
          <h3 className="text-lg font-medium text-gray-900">Tidak Ada Data Booking</h3>
          <p className="mt-2 text-gray-500">Tidak ada data booking untuk periode yang dipilih.</p>
        </motion.div>
      )}
    </motion.div>
  );
}
