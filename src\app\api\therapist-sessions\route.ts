import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserIdFromToken } from '@/lib/auth-utils';

// GET sesi terapis aktif
export async function GET(request: NextRequest) {
  try {
    // Ambil URL parameters
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');
    const therapistId = searchParams.get('therapistId');
    const active = searchParams.get('active');

    // Build query
    const where: any = {};

    // Filter berdasarkan outlet jika ada
    if (outletId) {
      where.therapist = {
        outletId
      };
    }

    // Filter berdasarkan terapis jika ada
    if (therapistId) {
      where.therapistId = therapistId;
    }

    // Filter hanya sesi aktif (endTime > waktu sekarang)
    if (active === 'true') {
      where.endTime = {
        gt: new Date()
      };
    }

    // Ambil semua sesi terapis aktif
    const sessions = await prisma.activeTherapistSession.findMany({
      where,
      include: {
        therapist: {
          select: {
            id: true,
            name: true,
            outletId: true,
            outlet: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        service: {
          select: {
            id: true,
            name: true,
            duration: true,
            price: true
          }
        },
        transaction: {
          select: {
            id: true,
            displayId: true,
            transactionDate: true,
            customer: {
              select: {
                id: true,
                name: true,
                phone: true
              }
            }
          }
        }
      },
      orderBy: {
        startTime: 'desc'
      }
    });

    return NextResponse.json({
      message: 'Data sesi terapis berhasil diambil',
      sessions
    });
  } catch (error) {
    console.error('Error fetching therapist sessions:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data sesi terapis' },
      { status: 500 }
    );
  }
}

// POST untuk membuat sesi terapis baru
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { therapistId, serviceId, transactionId, duration } = body;

    // Validasi input
    if (!therapistId || !serviceId || !transactionId || !duration) {
      return NextResponse.json(
        { error: 'ID terapis, ID layanan, ID transaksi, dan durasi diperlukan' },
        { status: 400 }
      );
    }

    // Hitung waktu akhir berdasarkan durasi (dalam menit)
    const startTime = new Date();
    const endTime = new Date(startTime.getTime() + duration * 60000);

    // Buat sesi terapis baru
    const newSession = await prisma.activeTherapistSession.create({
      data: {
        therapistId,
        serviceId,
        transactionId,
        startTime,
        endTime
      },
      include: {
        therapist: {
          select: {
            id: true,
            name: true
          }
        },
        service: {
          select: {
            id: true,
            name: true,
            duration: true
          }
        }
      }
    });

    return NextResponse.json({
      message: 'Sesi terapis berhasil dibuat',
      session: newSession
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating therapist session:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat sesi terapis' },
      { status: 500 }
    );
  }
}
