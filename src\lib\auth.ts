/**
 * File ini mengekspor konfigurasi dan utilitas autentikasi
 */

import { getUserIdFromToken } from './auth-utils';

// authOptions untuk next-auth getServerSession
export const authOptions = {
  providers: [],
  callbacks: {
    async session({ session, token }) {
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
      }
      return token;
    },
  },
  secret: process.env.JWT_SECRET || 'breaktime_session_secret_key_for_jwt_auth',
  session: {
    strategy: 'jwt',
  },
};

// Re-export fungsi autentikasi dari auth-utils
export { getUserIdFromToken };

// Fungsi helper untuk mendapatkan user tanpa next-auth
export const getUserFromRequest = getUserIdFromToken; 