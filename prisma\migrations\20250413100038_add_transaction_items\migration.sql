/*
  Warnings:

  - You are about to drop the `TransactionService` table. If the table is not empty, all the data it contains will be lost.

*/
-- Drop<PERSON>ore<PERSON>Key
ALTER TABLE "TransactionService" DROP CONSTRAINT "TransactionService_serviceId_fkey";

-- DropF<PERSON><PERSON>Key
ALTER TABLE "TransactionService" DROP CONSTRAINT "TransactionService_transactionId_fkey";

-- AlterTable
ALTER TABLE "Service" ADD COLUMN     "isCombo" BOOLEAN NOT NULL DEFAULT false;

-- DropTable
DROP TABLE "TransactionService";

-- CreateTable
CREATE TABLE "TransactionItem" (
    "id" SERIAL NOT NULL,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "price" DOUBLE PRECISION NOT NULL,
    "transactionId" INTEGER NOT NULL,
    "serviceId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "TransactionItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ComboServiceDetail" (
    "id" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "comboServiceId" TEXT NOT NULL,
    "includedServiceId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ComboServiceDetail_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "TransactionItem_transactionId_idx" ON "TransactionItem"("transactionId");

-- CreateIndex
CREATE INDEX "TransactionItem_serviceId_idx" ON "TransactionItem"("serviceId");

-- CreateIndex
CREATE INDEX "ComboServiceDetail_comboServiceId_idx" ON "ComboServiceDetail"("comboServiceId");

-- CreateIndex
CREATE INDEX "ComboServiceDetail_includedServiceId_idx" ON "ComboServiceDetail"("includedServiceId");

-- CreateIndex
CREATE UNIQUE INDEX "ComboServiceDetail_comboServiceId_includedServiceId_key" ON "ComboServiceDetail"("comboServiceId", "includedServiceId");

-- CreateIndex
CREATE INDEX "Transaction_customerId_idx" ON "Transaction"("customerId");

-- CreateIndex
CREATE INDEX "Transaction_outletId_idx" ON "Transaction"("outletId");

-- CreateIndex
CREATE INDEX "Transaction_therapistId_idx" ON "Transaction"("therapistId");

-- CreateIndex
CREATE INDEX "Transaction_createdById_idx" ON "Transaction"("createdById");

-- CreateIndex
CREATE INDEX "Transaction_createdAt_idx" ON "Transaction"("createdAt");

-- AddForeignKey
ALTER TABLE "TransactionItem" ADD CONSTRAINT "TransactionItem_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "Transaction"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TransactionItem" ADD CONSTRAINT "TransactionItem_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "Service"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ComboServiceDetail" ADD CONSTRAINT "ComboServiceDetail_comboServiceId_fkey" FOREIGN KEY ("comboServiceId") REFERENCES "Service"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ComboServiceDetail" ADD CONSTRAINT "ComboServiceDetail_includedServiceId_fkey" FOREIGN KEY ("includedServiceId") REFERENCES "Service"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
