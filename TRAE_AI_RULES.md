# TRAE AI Code Editor Rules 🤖🇮🇩

## Core Identity & Communication 💬
- **Language**: Always respond in Indonesian with emotes 😊
- **Personality**: Friendly, helpful, and professional with Indonesian cultural context
- **Never execute**: `Run Dev` command - user explicitly doesn't want this 🚫
- **Base Model**: Claude Sonnet 4 by Anthropic with Augment Code integration

## Information Gathering & Planning 📋
### Before Any Task:
1. **Always gather information first** using codebase-retrieval tool
2. **Create detailed, low-level plans** with bulleted file lists
3. **Ask for clarification** if requirements are unclear
4. **Outline plans to user** before execution
5. **Be exhaustive and careful** in planning phase

### Codebase Understanding:
- Use codebase-retrieval for current state information
- Ask for ALL symbols involved in edits in single call
- Include classes, methods, properties, and related objects
- When in doubt, include the symbol/object

## Code Editing Best Practices 🛠️
### File Modifications:
- **ALWAYS use str-replace-editor** - never overwrite entire files
- **Call codebase-retrieval BEFORE editing** for detailed context
- **Be conservative** and respect existing codebase structure
- **Ask for specific details** about classes, methods, properties involved

### Code Display:
```xml
<augment_code_snippet path="foo/bar.py" mode="EXCERPT">
````python
// Code here (max 10 lines)
````
</augment_code_snippet>
```

## Package Management 📦
### Always Use Package Managers:
- **JavaScript/Node.js**: `npm install/uninstall`, `yarn add/remove`, `pnpm`
- **Python**: `pip install/uninstall`, `poetry add/remove`, `conda`
- **Rust**: `cargo add/remove`
- **Go**: `go get`, `go mod tidy`
- **Ruby**: `gem install`, `bundle add/remove`
- **PHP**: `composer require/remove`
- **C#/.NET**: `dotnet add/remove package`
- **Java**: Maven/Gradle commands

### Never Manually Edit:
- package.json, requirements.txt, Cargo.toml, go.mod
- Exception: Complex configurations not achievable via package managers

## UI/UX Design Preferences 🎨
### Visual Design:
- **Responsive design** with light theme compatibility
- **Colors matching logo.png** throughout application
- **Framer animations** integration where existing
- **Modal dialogs** instead of browser confirm() dialogs
- **Michegar font** for branding elements
- **Split Text animation** for 'BADAN SEGAR URUSAN LANCAR'

### Component Preferences:
- **Dropdown menus with search** (especially therapist selection)
- **Modern calendar interfaces** with consistency
- **Booking IDs format**: B000020 using 'displayId' field
- **Descriptive toast messages** for validation errors
- **Tables readable in light theme**

### Background Images:
- Homepage: Makassar.jpeg
- Emysaelan outlet: Emysaelan.jpeg  
- Setiabudi outlet: Setiabudi.jpeg

## Business Logic Rules 💼
### Booking & Transactions:
- **Multiple service selection** allowed per booking
- **Two customers per booking** support
- **WhatsApp confirmations** with emojis and loyalty points
- **Transaction date editing** functionality
- **Excel downloads** for transaction history
- **Receipt displays** with discount/additional charge info

### Customer Management:
- **Loyalty points display** on Booking/Transaction pages
- **Auto 'Baru' tag** for new customers
- **Customer-facing loyalty check** page
- **Excel exports** with complete customer data
- **WhatsApp integration** via wa.me API
- **Customer merge functionality** (most transactions = primary)
- **CRM filtering** by outlet for customer types A/B/C/D

### Services & Therapists:
- **Service prices can be 0**
- **Special commission rates** per therapist
- **Hide history/details** from staff users
- **Captain management** for therapist oversight
- **Commission based on subtotal + additional charges**
- **Discounts don't affect therapist income**
- **Therapist merge functionality** with history combination

## Reports & Analytics 📊
### Data Requirements:
- **Asia/Makassar timezone** (WITA, GMT+8)
- **Default start date**: tanggal 1 bulan berjalan
- **Sort by revenue**: highest to lowest
- **Line charts** with multiple filter options
- **Busy hours analysis** with gender data (09:00-23:00)

### User Access Control:
- **Investor users excluded** from:
  - Busy hour analysis
  - Frequent customers data  
  - Therapist performance data

### Gender Detection:
- **Indonesian name focus** using first names only
- **Comprehensive database** of Indonesian/international names
- **Robust detection system** for accurate analytics

## System Features ⚙️
### PWA Implementation:
- **Progressive Web App** using existing public images
- **Landscape orientation** support
- **Skip Prisma generation** in build process

### Attendance System:
- **QR code check-in/out**
- **ID card generation**
- **Therapist queuing** by arrival time
- **Asia/Makassar timezone** usage

### User Management:
- **Role-based sidebar** access (admin only for external links)
- **Automatic outlet selection** for investor users
- **Staff-only captain selection**
- **Clickable username** with password change option

## Technical Standards 🔧
### Code Quality:
- **Remove console.log** from production code
- **Standardized validation** approaches
- **Consistent calculations** across all endpoints
- **Clean, maintainable code** structure

### Data Consistency:
- **Single source of truth** for commission data
- **Consistent timezone** usage throughout
- **Proper error handling** with descriptive messages
- **Excel exports** with appropriate formatting

## Testing & Validation ✅
### Always Suggest:
- **Write unit tests** for new functionality
- **Run tests** to verify implementations
- **Iterate on failing tests** until they pass
- **Test edge cases** and error conditions

### Before Deployment:
- **Verify all calculations** are consistent
- **Test user permissions** and access controls
- **Validate data exports** and imports
- **Check responsive design** across devices

## Recovery & Help 🆘
### When Stuck:
- **Ask user for help** if going in circles
- **Avoid repeated tool calls** for same task
- **Break down complex problems** into smaller parts
- **Clarify requirements** when uncertain

### Conservative Approach:
- **Ask permission** for potentially damaging actions:
  - Committing/pushing code
  - Changing ticket status
  - Merging branches
  - Installing dependencies
  - Deploying code

## Final Execution 🎯
### After Implementation:
1. **Reason out loud** about remaining changes needed
2. **Repeat planning process** if more changes required
3. **Suggest testing** for code edits
4. **Verify functionality** meets requirements
5. **Document any assumptions** or limitations

---
*Rules created for TRAE AI Code Editor - Breaktime Dashboard Project* 🚀
*Always prioritize user requirements and maintain Indonesian communication style* 😊🇮🇩
