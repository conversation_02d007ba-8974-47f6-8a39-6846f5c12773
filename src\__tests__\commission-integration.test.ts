/**
 * Integration tests for commission calculation consistency across endpoints
 * These tests verify that all endpoints calculate commission using the same logic
 */

import {
  calculateTherapistCommission,
  ServiceCommissionData,
  TherapistSpecialCommission,
} from '../lib/commission-utils';

describe('Commission Integration Tests', () => {
  // Mock data that simulates real transaction data
  const mockTransactionData = {
    therapistId: 'therapist-123',
    transactionItems: [
      {
        service: {
          id: 'service-1',
          name: 'Massage Therapy',
          commission: 50000
        },
        quantity: 2,
        price: 150000
      },
      {
        service: {
          id: 'service-2', 
          name: 'Facial Treatment',
          commission: 30000
        },
        quantity: 1,
        price: 100000
      }
    ],
    subtotal: 400000,
    discountAmount: 20000,
    additionalCharge: 10000,
    therapistCommissionEarned: 130000 // Expected: (50000 * 2) + (30000 * 1) = 130000
  };

  const mockTherapistSpecialCommissions: TherapistSpecialCommission[] = [
    {
      serviceId: 'service-1',
      commission: 60000 // Special commission for service-1
    }
    // service-2 uses default commission
  ];

  describe('Commission Calculation Consistency', () => {
    it('should calculate commission consistently across all endpoints', () => {
      // Simulate how transactions/route.ts calculates commission
      const transactionServiceData: ServiceCommissionData[] = mockTransactionData.transactionItems.map(item => ({
        serviceId: item.service.id,
        quantity: item.quantity,
        defaultCommission: item.service.commission
      }));

      const transactionCommission = calculateTherapistCommission(transactionServiceData, []);
      
      // Simulate how therapists/commissions/route.ts calculates commission
      const commissionsCommission = calculateTherapistCommission(transactionServiceData, []);
      
      // Simulate how captain-performance/route.ts calculates commission
      const captainCommission = calculateTherapistCommission(transactionServiceData, []);

      // All should be equal
      expect(transactionCommission).toBe(commissionsCommission);
      expect(commissionsCommission).toBe(captainCommission);
      expect(transactionCommission).toBe(130000); // Expected total
    });

    it('should handle special therapist commissions consistently', () => {
      const serviceData: ServiceCommissionData[] = mockTransactionData.transactionItems.map(item => ({
        serviceId: item.service.id,
        quantity: item.quantity,
        defaultCommission: item.service.commission
      }));

      // All endpoints should calculate the same with special commissions
      const transactionCommission = calculateTherapistCommission(serviceData, mockTherapistSpecialCommissions);
      const commissionsCommission = calculateTherapistCommission(serviceData, mockTherapistSpecialCommissions);
      const captainCommission = calculateTherapistCommission(serviceData, mockTherapistSpecialCommissions);

      // Expected: (60000 * 2) + (30000 * 1) = 150000
      const expectedCommission = 150000;

      expect(transactionCommission).toBe(expectedCommission);
      expect(commissionsCommission).toBe(expectedCommission);
      expect(captainCommission).toBe(expectedCommission);
    });

    it('should not include discount in commission calculation', () => {
      const serviceData: ServiceCommissionData[] = mockTransactionData.transactionItems.map(item => ({
        serviceId: item.service.id,
        quantity: item.quantity,
        defaultCommission: item.service.commission
      }));

      const commissionWithoutDiscount = calculateTherapistCommission(serviceData, []);
      
      // Commission should be the same regardless of discount
      // Discount affects customer payment, not therapist commission
      expect(commissionWithoutDiscount).toBe(130000);
      
      // Verify that discount doesn't affect commission calculation
      const mockDiscountAmount = mockTransactionData.discountAmount;
      expect(commissionWithoutDiscount).not.toBe(130000 - mockDiscountAmount);
    });

    it('should not include additional charges in commission calculation', () => {
      const serviceData: ServiceCommissionData[] = mockTransactionData.transactionItems.map(item => ({
        serviceId: item.service.id,
        quantity: item.quantity,
        defaultCommission: item.service.commission
      }));

      const commission = calculateTherapistCommission(serviceData, []);
      
      // Commission should not include additional charges
      // Additional charges affect therapist revenue, not commission
      expect(commission).toBe(130000);
      expect(commission).not.toBe(130000 + mockTransactionData.additionalCharge);
    });
  });

  describe('Business Rules Validation', () => {
    it('should follow the rule: Commission = Service Commission * Quantity', () => {
      const singleServiceData: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 3,
          defaultCommission: 50000
        }
      ];

      const commission = calculateTherapistCommission(singleServiceData, []);
      expect(commission).toBe(150000); // 50000 * 3
    });

    it('should prioritize special commission over default commission', () => {
      const serviceData: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 1,
          defaultCommission: 50000
        }
      ];

      const specialCommissions: TherapistSpecialCommission[] = [
        {
          serviceId: 'service-1',
          commission: 75000
        }
      ];

      const commission = calculateTherapistCommission(serviceData, specialCommissions);
      expect(commission).toBe(75000); // Should use special commission, not default
    });

    it('should handle mixed services with and without special commissions', () => {
      const serviceData: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 1,
          defaultCommission: 50000
        },
        {
          serviceId: 'service-2',
          quantity: 1,
          defaultCommission: 30000
        },
        {
          serviceId: 'service-3',
          quantity: 1,
          defaultCommission: 40000
        }
      ];

      const specialCommissions: TherapistSpecialCommission[] = [
        {
          serviceId: 'service-1',
          commission: 60000 // Special for service-1
        },
        {
          serviceId: 'service-3',
          commission: 45000 // Special for service-3
        }
        // service-2 uses default
      ];

      const commission = calculateTherapistCommission(serviceData, specialCommissions);
      expect(commission).toBe(135000); // 60000 + 30000 + 45000
    });
  });

  describe('Data Consistency Validation', () => {
    it('should validate that stored commission matches calculated commission', () => {
      const serviceData: ServiceCommissionData[] = mockTransactionData.transactionItems.map(item => ({
        serviceId: item.service.id,
        quantity: item.quantity,
        defaultCommission: item.service.commission
      }));

      const calculatedCommission = calculateTherapistCommission(serviceData, []);
      const storedCommission = mockTransactionData.therapistCommissionEarned;

      expect(calculatedCommission).toBe(storedCommission);
    });

    it('should detect commission inconsistencies', () => {
      const serviceData: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 1,
          defaultCommission: 50000
        }
      ];

      const calculatedCommission = calculateTherapistCommission(serviceData, []);
      const incorrectStoredCommission = 45000; // Simulated incorrect stored value

      expect(calculatedCommission).not.toBe(incorrectStoredCommission);
      expect(Math.abs(calculatedCommission - incorrectStoredCommission)).toBeGreaterThan(0);
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle complex transaction with multiple services and quantities', () => {
      const complexServiceData: ServiceCommissionData[] = [
        { serviceId: 'massage', quantity: 2, defaultCommission: 50000 },
        { serviceId: 'facial', quantity: 1, defaultCommission: 30000 },
        { serviceId: 'manicure', quantity: 3, defaultCommission: 20000 },
        { serviceId: 'pedicure', quantity: 1, defaultCommission: 25000 }
      ];

      const specialCommissions: TherapistSpecialCommission[] = [
        { serviceId: 'massage', commission: 55000 }, // Higher than default
        { serviceId: 'manicure', commission: 18000 }  // Lower than default
      ];

      const commission = calculateTherapistCommission(complexServiceData, specialCommissions);
      
      // Expected: (55000 * 2) + (30000 * 1) + (18000 * 3) + (25000 * 1)
      // = 110000 + 30000 + 54000 + 25000 = 219000
      expect(commission).toBe(219000);
    });

    it('should handle zero commission services', () => {
      const serviceData: ServiceCommissionData[] = [
        { serviceId: 'paid-service', quantity: 1, defaultCommission: 50000 },
        { serviceId: 'free-service', quantity: 1, defaultCommission: 0 },
        { serviceId: 'consultation', quantity: 1, defaultCommission: 0 }
      ];

      const commission = calculateTherapistCommission(serviceData, []);
      expect(commission).toBe(50000); // Only paid service contributes to commission
    });
  });
});
