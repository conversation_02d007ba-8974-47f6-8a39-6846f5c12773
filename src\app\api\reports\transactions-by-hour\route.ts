import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');
    const hour = searchParams.get('hour');
    const rawStartDate = searchParams.get('startDate');
    const rawEndDate = searchParams.get('endDate');

    // Validasi parameter yang diperlukan
    if (!hour || !rawStartDate || !rawEndDate) {
      return NextResponse.json(
        { error: 'Parameter jam, tanggal mulai, dan tanggal akhir diperlukan' },
        { status: 400 }
      );
    }

    // Parse tanggal
    const startDate = new Date(rawStartDate);
    const endDate = new Date(rawEndDate);
    const hourNumber = parseInt(hour, 10);

    // Validasi format
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime()) || isNaN(hourNumber)) {
      return NextResponse.json(
        { error: 'Format tanggal atau jam tidak valid' },
        { status: 400 }
      );
    }

    // Pastikan endDate diatur ke akhir hari
    endDate.setHours(23, 59, 59, 999);

    // Filter dasar untuk transaksi berdasarkan tanggal dan outlet terpilih
    const transactionWhere: Prisma.TransactionWhereInput = {
      transactionDate: { gte: startDate, lte: endDate },
      ...(outletId && { outletId: outletId }),
    };

    // Ambil semua transaksi dalam rentang tanggal
    const transactions = await prisma.transaction.findMany({
      where: transactionWhere,
      select: {
        id: true,
        displayId: true,
        transactionDate: true,
        totalAmount: true,
        customer: { 
          select: { 
            id: true, 
            name: true 
          } 
        },
        therapist: { 
          select: { 
            id: true, 
            name: true 
          } 
        },
        transactionItems: {
          select: {
            id: true,
            quantity: true,
            price: true,
            service: { 
              select: { 
                id: true, 
                name: true, 
                price: true 
              } 
            }
          }
        }
      },
    });

    // Filter transaksi berdasarkan jam (dengan koreksi zona waktu Asia/Makassar GMT+8)
    const filteredTransactions = transactions.filter(transaction => {
      try {
        if (transaction.transactionDate) {
          // Buat objek Date dari string ISO
          const transactionDate = new Date(transaction.transactionDate);
          
          // Konversi ke zona waktu Asia/Makassar (GMT+8)
          const utcHour = transactionDate.getUTCHours();
          const makassarHour = (utcHour + 8) % 24; // Tambahkan 8 jam dan pastikan dalam rentang 0-23
          
          return makassarHour === hourNumber;
        }
        return false;
      } catch (e) {
        console.error('Error parsing transaction date:', e);
        return false;
      }
    });

    // Hitung jumlah layanan yang terjadi pada jam tersebut
    const serviceCountMap = new Map<string, { count: number, name: string, totalAmount: number }>();
    
    filteredTransactions.forEach(transaction => {
      transaction.transactionItems.forEach(item => {
        if (item.service) {
          const serviceId = item.service.id;
          const serviceName = item.service.name;
          const amount = item.price * item.quantity;
          
          if (serviceCountMap.has(serviceId)) {
            const current = serviceCountMap.get(serviceId)!;
            serviceCountMap.set(serviceId, {
              count: current.count + item.quantity,
              name: serviceName,
              totalAmount: current.totalAmount + amount
            });
          } else {
            serviceCountMap.set(serviceId, {
              count: item.quantity,
              name: serviceName,
              totalAmount: amount
            });
          }
        }
      });
    });

    // Konversi Map ke array untuk response
    const serviceStats = Array.from(serviceCountMap.entries()).map(([id, data]) => ({
      id,
      name: data.name,
      count: data.count,
      totalAmount: data.totalAmount
    })).sort((a, b) => b.count - a.count); // Urutkan dari tertinggi ke terendah

    // Format transaksi untuk response
    const formattedTransactions = filteredTransactions.map(transaction => ({
      id: transaction.id,
      displayId: transaction.displayId,
      transactionDate: transaction.transactionDate,
      totalAmount: transaction.totalAmount,
      customerName: transaction.customer?.name || 'Unknown',
      therapistName: transaction.therapist?.name || 'Unknown',
      services: transaction.transactionItems.map(item => ({
        name: item.service?.name || 'Unknown Service',
        quantity: item.quantity,
        price: item.price
      }))
    }));

    return NextResponse.json({
      message: 'Data transaksi berdasarkan jam berhasil diambil',
      hour: hourNumber,
      transactionCount: filteredTransactions.length,
      serviceStats,
      transactions: formattedTransactions,
    });

  } catch (error: unknown) {
    console.error('[API GET /api/reports/transactions-by-hour] Error:', error);

    let errorMessage = 'Terjadi kesalahan saat mengambil data transaksi berdasarkan jam';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat mengambil data transaksi berdasarkan jam',
      hour: null,
      transactionCount: 0,
      serviceStats: [],
      transactions: []
    }, { status: statusCode });
  }
}
