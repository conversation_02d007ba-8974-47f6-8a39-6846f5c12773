import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET sesi terapis berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    if (!id) {
      return NextResponse.json(
        { error: 'ID sesi terapis diperlukan' },
        { status: 400 }
      );
    }

    // Ambil detail sesi terapis berdasarkan ID
    const session = await prisma.activeTherapistSession.findUnique({
      where: {
        id
      },
      include: {
        therapist: {
          select: {
            id: true,
            name: true,
            outletId: true,
            outlet: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        service: {
          select: {
            id: true,
            name: true,
            duration: true,
            price: true
          }
        },
        transaction: {
          select: {
            id: true,
            displayId: true,
            transactionDate: true,
            customer: {
              select: {
                id: true,
                name: true,
                phone: true
              }
            }
          }
        }
      }
    });

    if (!session) {
      return NextResponse.json(
        { error: 'Sesi terapis tidak ditemukan' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Data sesi terapis berhasil diambil',
      session
    });
  } catch (error) {
    console.error('Error fetching therapist session:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data sesi terapis' },
      { status: 500 }
    );
  }
}

// DELETE sesi terapis
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    if (!id) {
      return NextResponse.json(
        { error: 'ID sesi terapis diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah sesi terapis ada
    const session = await prisma.activeTherapistSession.findUnique({
      where: {
        id
      }
    });

    if (!session) {
      return NextResponse.json(
        { error: 'Sesi terapis tidak ditemukan' },
        { status: 404 }
      );
    }

    // Hapus sesi terapis
    await prisma.activeTherapistSession.delete({
      where: {
        id
      }
    });

    return NextResponse.json({
      message: 'Sesi terapis berhasil dihapus'
    });
  } catch (error) {
    console.error('Error deleting therapist session:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus sesi terapis' },
      { status: 500 }
    );
  }
}
