import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// Tambahkan konstanta untuk timeout dan caching
const REQUEST_TIMEOUT = 30000; // 30 detik
const DEFAULT_PAGE_SIZE = 25; // Kurangi default page size
const MAX_PAGE_SIZE = 100; // Batasi maksimal page size

// GET semua customer
export async function GET(request: Request) {
  const startTime = Date.now();
  
  try {
    // Ambil URL parameters
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const limit = searchParams.get('limit');
    const page = searchParams.get('page') || '1';
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const includeTransactionHistory = searchParams.get('includeTransactionHistory') === 'true';

    console.log(`🚀 [${Date.now()}] API Customers - Request started: search=${search}, startDate=${startDate}, endDate=${endDate}, page=${page}, limit=${limit}`);

    // Optimasi: Validasi dan sanitasi input
    const pageNumber = Math.max(1, parseInt(page));
    const pageSize = Math.min(
      Math.max(1, parseInt(limit || DEFAULT_PAGE_SIZE.toString())), 
      MAX_PAGE_SIZE
    );

    // Build query dengan optimasi
    const where: Record<string, any> = {
      isActive: true // Selalu filter aktif customer saja
    };

    // Filter berdasarkan pencarian (name, email, atau phone) dengan optimasi
    if (search && search.trim().length > 0) {
      const searchTerm = search.trim();
      where.OR = [
        { name: { contains: searchTerm, mode: 'insensitive' } },
        { email: { contains: searchTerm, mode: 'insensitive' } },
        { phone: { contains: searchTerm } }
      ];
    }

    // Ambil parameter jenis filter
    const filterType = searchParams.get('filterType') || 'registration';
    console.log(`📊 Filter type: ${filterType}`);

    // Optimasi: Filter berdasarkan tanggal dengan strategi yang berbeda
    if (startDate || endDate) {
      if (filterType === 'registration') {
        // Filter berdasarkan tanggal registrasi - OPTIMIZED
        where.registeredAt = {};

        if (startDate) {
          const startDateTime = new Date(startDate);
          startDateTime.setHours(0, 0, 0, 0);
          where.registeredAt.gte = startDateTime;
          console.log(`📅 Registration startDate filter: ${startDateTime.toISOString()}`);
        }

        if (endDate) {
          const endDateTime = new Date(endDate);
          endDateTime.setHours(23, 59, 59, 999);
          where.registeredAt.lte = endDateTime;
          console.log(`📅 Registration endDate filter: ${endDateTime.toISOString()}`);
        }
      } else if (filterType === 'transaction') {
        // OPTIMIZED: Untuk filter transaksi, gunakan subquery yang lebih efisien
        console.log(`🔄 Using optimized transaction date filter`);
        
        const transactionWhere: any = {};
        if (startDate) {
          const startDateTime = new Date(startDate);
          startDateTime.setHours(0, 0, 0, 0);
          transactionWhere.gte = startDateTime;
        }
        if (endDate) {
          const endDateTime = new Date(endDate);
          endDateTime.setHours(23, 59, 59, 999);
          transactionWhere.lte = endDateTime;
        }

        // Gunakan exists untuk optimasi query
        where.transactions = {
          some: {
            createdAt: Object.keys(transactionWhere).length > 0 ? transactionWhere : undefined
          }
        };
      }
    }

    console.log(`🔍 Final where clause:`, JSON.stringify(where, null, 2));

    // Optimasi: Hitung total dengan query yang dioptimalkan
    const countStartTime = Date.now();
    const totalCustomers = await prisma.customer.count({ where });
    const countDuration = Date.now() - countStartTime;
    console.log(`📊 Count query completed in ${countDuration}ms: ${totalCustomers} customers`);

    // Optimasi: Set pagination dengan bounds checking
    const skip = (pageNumber - 1) * pageSize;
    const totalPages = Math.ceil(totalCustomers / pageSize);

    // Validasi halaman
    if (pageNumber > totalPages && totalPages > 0) {
      return NextResponse.json({
        error: `Halaman ${pageNumber} tidak valid. Total halaman: ${totalPages}`,
        pagination: {
          total: totalCustomers,
          page: totalPages,
          pageSize,
          pageCount: totalPages
        }
      }, { status: 400 });
    }

    // Optimasi: Query data dengan select yang minimal
    const queryStartTime = Date.now();
    const customers = await prisma.customer.findMany({
      where,
      select: {
        id: true,
        name: true,
        phone: true,
        points: true,
        tags: true,
        address: true,
        registeredAt: true,

        // Conditional include untuk transaction history
        ...(includeTransactionHistory ? {
          transactions: {
            select: {
              id: true,
              createdAt: true,
              totalAmount: true,
              paymentMethod: true,
              displayId: true,
              therapist: { select: { id: true, name: true } },
              outlet: { select: { id: true, name: true } },
              transactionItems: {
                select: {
                  id: true,
                  price: true,
                  quantity: true,
                  service: { select: { id: true, name: true } }
                }
              },
              createdBy: { select: { id: true, name: true } }
            },
            orderBy: { createdAt: 'desc' },
            take: 10 // Limit transaction history untuk performance
          }
        } : {})
      },
      orderBy: search ? [
        { name: 'asc' },
        { registeredAt: 'desc' }
      ] : {
        registeredAt: 'desc'
      },
      skip,
      take: pageSize
    });

    const queryDuration = Date.now() - queryStartTime;
    console.log(`📊 Data query completed in ${queryDuration}ms: ${customers.length} customers retrieved`);

    // Optimasi: Hapus transaction data dari response jika tidak diperlukan untuk mengurangi bandwidth
    const optimizedCustomers = includeTransactionHistory ? customers : customers.map(customer => {
        const { transactions, ...rest } = customer as any;
        return rest;
      });

    const totalDuration = Date.now() - startTime;
    console.log(`✅ API Customers completed in ${totalDuration}ms (Count: ${countDuration}ms, Query: ${queryDuration}ms)`);

    // Response dengan performance metrics
    return NextResponse.json({
      message: 'Data customer berhasil diambil',
      customers: optimizedCustomers,
      pagination: {
        total: totalCustomers,
        page: pageNumber,
        pageSize,
        pageCount: totalPages
      },
      filter: {
        type: filterType,
        startDate,
        endDate
      },
      performance: {
        duration: totalDuration,
        countDuration,
        queryDuration,
        customerCount: customers.length
      }
    });

  } catch (error: unknown) {
    const totalDuration = Date.now() - startTime;
    console.error(`❌ Error fetching customers after ${totalDuration}ms:`, error);
    
    // Enhanced error response
    const errorMessage = error instanceof Error ? error.message : 'Terjadi kesalahan saat mengambil data customer';
    
    return NextResponse.json(
      { 
        error: errorMessage,
        duration: totalDuration,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// POST untuk membuat customer baru
export async function POST(request: Request) {
  try {
    const body = await request.json();
    // Ambil juga points dan tags dari body jika dikirim dari frontend
    const { name, email, phone, birthdate, address, gender, points = 0, tags = [] } = body;

    // Validasi input
    if (!name || !phone) {
      return NextResponse.json(
        { error: 'Nama dan nomor telepon diperlukan' },
        { status: 400 }
      );
    }

    // Cek duplikat email jika ada
    if (email) {
      const existingEmail = await prisma.customer.findFirst({
        where: { email }
      });

      if (existingEmail) {
        return NextResponse.json(
          {
            error: 'Email sudah terdaftar',
            existingCustomer: {
              name: existingEmail.name,
              email: existingEmail.email,
              id: existingEmail.id
            }
          },
          { status: 400 }
        );
      }
    }

    // Cek duplikat nomor telepon
    const existingPhone = await prisma.customer.findFirst({
      where: { phone }
    });

    if (existingPhone) {
      return NextResponse.json(
        {
          error: 'Nomor telepon sudah terdaftar',
          existingCustomer: {
            name: existingPhone.name,
            phone: existingPhone.phone,
            id: existingPhone.id
          }
        },
        { status: 400 }
      );
    }

    // --- Logika Generate ID Kustom ---
    let nextIdNumber = 1;
    try {
      // Cari customer terakhir berdasarkan ID custom (jika ada)
      // Ini asumsi format ID lama konsisten (P + angka)
      const lastCustomer = await prisma.customer.findFirst({
        where: {
          id: { startsWith: 'P' }
        },
        orderBy: { id: 'desc' } // Urutkan berdasarkan ID string
      });

      if (lastCustomer && lastCustomer.id.startsWith('P')) {
        const lastNumber = parseInt(lastCustomer.id.substring(1));
        if (!isNaN(lastNumber)) {
          nextIdNumber = lastNumber + 1;
        }
      }
    } catch(err){
       console.error("Error fetching last customer ID for sequence:", err);
       // Jika error, tetap gunakan 1, atau implementasikan strategi lain
    }
    const newCustomerId = `P${String(nextIdNumber).padStart(7, '0')}`;
    // --- End Logika Generate ID ---

    // Proses tag pelanggan
    let customerTags = Array.isArray(tags) ? [...tags] : [];

    // Jika pelanggan tidak memiliki tag, tambahkan tag 'Baru'
    // Jika sudah memiliki tag, pertahankan tag yang ada
    if (customerTags.length === 0) {
      customerTags = ['Baru'];
    } else if (!customerTags.includes('Baru')) {
      // Jika pelanggan memiliki tag tapi tidak memiliki tag 'Baru', tambahkan tag 'Baru'
      customerTags.push('Baru');
    }

    // Buat customer baru dengan ID kustom
    const newCustomer = await prisma.customer.create({
      data: {
        id: newCustomerId, // Gunakan ID yang baru dibuat
        name,
        email: email || null,
        phone,
        birthdate: birthdate ? new Date(birthdate) : null,
        address: address || null,
        gender: gender || null,
        points: Number(points) || 0, // Pastikan points adalah number
        tags: customerTags, // Gunakan tag yang sudah diproses
        // registeredAt diisi otomatis oleh Prisma
      }
    });

    return NextResponse.json({
      message: 'Customer berhasil dibuat',
      customer: newCustomer
    }, { status: 201 });

  } catch (error: unknown) {
    // Handle potensi error jika ID duplikat (meskipun seharusnya tidak terjadi dengan logika sequence)
    if ((error as any).code === 'P2002' && (error as any).meta?.target?.includes('id')) {
      console.error('Error creating customer: Duplicate ID', error);
      return NextResponse.json(
        { error: 'Gagal membuat ID unik untuk customer. Coba lagi.' },
        { status: 500 }
      );
    }

    console.error('Error creating customer:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat customer' },
      { status: 500 }
    );
  }
}