'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';
import { FiCheckCircle } from 'react-icons/fi';

export default function Features() {
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number = 1) => ({
      opacity: 1,
      y: 0,
      transition: { staggerChildren: 0.1, delayChildren: i * 0.1, duration: 0.5 }
    })
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const floatingAnimation = {
    y: [0, -10, 0],
    transition: {
      duration: 3,
      repeat: Infinity,
      repeatType: "reverse" as const,
      ease: "easeInOut"
    }
  };

  const features = [
    {
      title: "Terapis Berpengalaman",
      desc: "Tim terapis kami terlatih dengan sertifikasi profesional."
    },
    {
      title: "Ruangan yang <PERSON>yaman",
      desc: "Ruangan pijat yang bersih, tenang, dan dilengkapi musik relaksasi."
    },
    {
      title: "Produk Premium",
      desc: "Kami hanya menggunakan minyak pijat dan produk berkualitas tinggi."
    }
  ];

  return (
    <motion.section 
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      variants={staggerContainer}
      className="py-20 px-4 md:px-20 bg-white border-y border-gray-100"
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
        <motion.div variants={fadeIn}>
          <p className="text-secondary uppercase font-bold tracking-widest mb-2">KENAPA MEMILIH KAMI</p>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800">
            Pengalaman Pijat Yang Tak Terlupakan
          </h2>
          <p className="text-gray-500 mb-8">
            Kami berkomitmen untuk memberikan pengalaman relaksasi terbaik dengan terapis profesional dan fasilitas premium.
          </p>
          
          <div className="space-y-6">
            {features.map((item, i) => (
              <motion.div 
                key={i}
                variants={fadeIn}
                custom={i + 1}
                className="flex gap-4"
              >
                <div className="w-10 h-10 rounded-full bg-primary/10 flex-shrink-0 flex items-center justify-center">
                  <FiCheckCircle className="text-primary" size={20} />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">{item.title}</h3>
                  <p className="text-gray-500 text-sm">{item.desc}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
        
        <motion.div 
          variants={fadeIn}
          className="relative"
        >
          <div className="relative z-10">
            <Image
              src="https://images.unsplash.com/photo-1544161515-4ab6ce6db874?q=80&w=1080"
              alt="Massage therapy"
              width={600}
              height={500}
              className="rounded-3xl shadow-xl"
            />
            
            {/* Stats Card */}
            <motion.div 
              className="absolute top-1/2 -left-12 bg-white rounded-lg shadow-lg p-4 z-20"
              animate={floatingAnimation}
            >
              <Image
                src="https://images.unsplash.com/photo-1519823551278-64ac92734fb1?q=80&w=200"
                alt="Massage"
                width={100}
                height={100}
                className="rounded-lg mb-2"
              />
              <h4 className="font-bold">Pijat Refleksi</h4>
              <p className="text-xs text-gray-500">Layanan Populer</p>
            </motion.div>
          </div>
          
          {/* Decorative Elements */}
          <div className="absolute -z-10 top-1/4 right-1/4 bg-gradient-to-r from-primary to-secondary rounded-full w-64 h-64 blur-3xl opacity-20"></div>
        </motion.div>
      </div>
    </motion.section>
  );
}
