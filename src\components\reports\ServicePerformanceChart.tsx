'use client';

import React, { useRef } from 'react';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  Legend, Cell, PieChart, Pie
} from 'recharts';

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('id-ID', { 
    style: 'currency', 
    currency: 'IDR', 
    minimumFractionDigits: 0 
  }).format(value);
};

// Helper <PERSON><PERSON> (Gold & Teal)
const LOGO_COLORS = ['#FDBA74', '#2DD4BF', '#FCD34D', '#5EEAD4', '#FB923C', '#0D9488']; 

// Helper untuk label Pie Chart
const RADIAN = Math.PI / 180;
const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }: any) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central" fontSize={10}>
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

interface ServicePerformanceChartProps {
  data: Array<{
    name: string;
    count: number;
    revenue: number;
    profit: number;
    margin: number;
  }>;
  type?: 'bar' | 'pie';
  dataKey?: 'count' | 'revenue' | 'profit' | 'margin';
  title?: string;
  height?: number;
  className?: string;
}

const ServicePerformanceChart: React.FC<ServicePerformanceChartProps> = ({
  data,
  type = 'bar',
  dataKey = 'revenue',
  title = 'Performa Layanan',
  height = 300,
  className = ''
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  // Prepare chart data
  const chartData = data.map(item => ({
    name: item.name,
    value: item[dataKey],
    count: item.count,
    revenue: item.revenue,
    profit: item.profit,
    margin: item.margin
  }));

  // Get label for dataKey
  const getDataKeyLabel = () => {
    switch(dataKey) {
      case 'count': return 'Jumlah';
      case 'revenue': return 'Pendapatan';
      case 'profit': return 'Keuntungan';
      case 'margin': return 'Margin (%)';
      default: return 'Nilai';
    }
  };

  // Format value based on dataKey
  const formatValue = (value: number, key: string) => {
    switch(key) {
      case 'count': return value.toString();
      case 'margin': return `${value.toFixed(1)}%`;
      case 'revenue':
      case 'profit':
        return formatCurrency(value);
      default: return value.toString();
    }
  };

  return (
    <div className={`w-full ${className}`}>
      {title && <h3 className="text-lg font-semibold mb-2">{title}</h3>}
      <div ref={chartRef} style={{ width: '100%', height: `${height}px` }}>
        <ResponsiveContainer>
          {type === 'bar' ? (
            <BarChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
              <XAxis 
                dataKey="name" 
                tick={{ fill: '#6b7280' }} 
                tickLine={{ stroke: '#e0e0e0' }}
              />
              <YAxis 
                tickFormatter={(value) => {
                  if (dataKey === 'margin') return `${value}%`;
                  if (dataKey === 'count') return value.toString();
                  return new Intl.NumberFormat('id-ID', { 
                    notation: 'compact', 
                    compactDisplay: 'short' 
                  }).format(value);
                }} 
                tick={{ fill: '#6b7280' }}
                tickLine={{ stroke: '#e0e0e0' }}
              />
              <Tooltip 
                formatter={(value: number, name: string, props: any) => {
                  return [formatValue(value, dataKey), getDataKeyLabel()];
                }}
                labelFormatter={(label) => `Layanan: ${label}`}
              />
              <Legend />
              <Bar dataKey="value" name={getDataKeyLabel()}>
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={LOGO_COLORS[index % LOGO_COLORS.length]} />
                ))}
              </Bar>
            </BarChart>
          ) : (
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius="80%"
                innerRadius="40%"
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                paddingAngle={2}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={LOGO_COLORS[index % LOGO_COLORS.length]} />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value: number, name: string, props: any) => {
                  return [formatValue(value, dataKey), getDataKeyLabel()];
                }}
                labelFormatter={(label) => `Layanan: ${label}`}
              />
              <Legend />
            </PieChart>
          )}
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default ServicePerformanceChart;
