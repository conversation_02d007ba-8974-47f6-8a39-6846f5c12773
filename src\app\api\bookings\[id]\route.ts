import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import { BookingStatus } from '@/lib/types/prisma';
import { logBooking } from '@/lib/logger';
import { getUserIdFromToken } from '@/lib/auth-utils';

// Fungsi helper untuk parse query param 'include'
function parseIncludeQuery(includeQuery: string | null): Prisma.BookingInclude | undefined {
  if (!includeQuery) return undefined;

  const includes: Prisma.BookingInclude = {};
  const relations = includeQuery.split(',');

  relations.forEach(relation => {
    const parts = relation.trim().split('.');
    if (parts.length === 1) {
      // Relasi level 1 (e.g., customer, therapist, outlet)
      if (parts[0] === 'customer') includes.customer = true;
      if (parts[0] === 'therapist') includes.therapist = true;
      if (parts[0] === 'outlet') includes.outlet = true;
      if (parts[0] === 'transaction') includes.transaction = true;
      if (parts[0] === 'createdBy') includes.createdBy = true;
      // Tambahkan relasi level 1 lain jika perlu
    } else if (parts.length === 2 && parts[0] === 'bookingServices' && parts[1] === 'service') {
      // Relasi nested: bookingServices.service
      includes.bookingServices = { include: { service: true } };
    }
    // Tambahkan logika untuk relasi nested lain jika perlu
  });

  return Object.keys(includes).length > 0 ? includes : undefined;
}

// GET booking berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Await params untuk menghindari error
    const id = params.id;
    const { searchParams } = new URL(request.url);
    const includeQuery = searchParams.get('include');

    console.log(`API GET /bookings/${id} called with include: ${includeQuery}`);

    if (!id) {
      return NextResponse.json(
        { error: 'ID booking diperlukan' },
        { status: 400 }
      );
    }

    // Parse query param 'include'
    const includeObject = parseIncludeQuery(includeQuery);
    console.log("Parsed include object:", includeObject);

    // Ambil detail booking berdasarkan ID
    const booking = await prisma.booking.findUnique({
      where: {
        id
      },
      // Gunakan objek include dinamis yang sudah diparse
      include: includeObject
    });

    // Jika booking tidak ditemukan
    if (!booking) {
      console.error(`Booking with ID ${id} not found.`);
      return NextResponse.json(
        { error: 'Booking tidak ditemukan' },
        { status: 404 }
      );
    }

    console.log("Booking data fetched successfully:", booking);
    // Kembalikan data booking
    return NextResponse.json(booking); // <-- Kembalikan langsung objek booking

  } catch (error) {
    console.error('Error fetching booking details:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil detail booking' },
      { status: 500 }
    );
  }
}

// PUT untuk mengupdate booking
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Await params untuk menghindari error
    const id = params.id;
    const body = await request.json();
    const { therapistId, bookingDate, bookingTime, notes, status, services, customerIds } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID booking diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah booking ada
    const existingBooking = await prisma.booking.findUnique({
      where: {
        id
      },
      include: {
        bookingServices: {
          include: {
            service: {
              select: {
                id: true,
                outlets: {
                  select: {
                    outletId: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!existingBooking) {
      return NextResponse.json(
        { error: 'Booking tidak ditemukan' },
        { status: 404 }
      );
    }

    // Dapatkan outletId dari booking (jika service ada)
    const serviceOutlets = existingBooking.bookingServices?.[0]?.service?.outlets || [];
    const bookingOutletId = existingBooking.outletId || serviceOutlets[0]?.outletId;

    if (!bookingOutletId) {
       return NextResponse.json(
         { error: 'Outlet untuk booking ini tidak dapat ditentukan (tidak ada layanan terkait?)' },
         { status: 400 }
       );
    }

    // Persiapkan data untuk update
    const updateData: Prisma.BookingUpdateInput = {};

    // Update terapis jika ada
    if (therapistId && therapistId !== existingBooking.therapistId) {
      // Pastikan terapis ada dan aktif di outlet yang sama
      const therapist = await prisma.therapist.findFirst({
        where: {
          id: therapistId,
          outletId: bookingOutletId
        }
      });

      if (!therapist) {
        return NextResponse.json(
          { error: 'Terapis tidak ditemukan atau tidak berada di outlet yang sama' },
          { status: 404 }
        );
      }

      if (!therapist.isActive) {
        return NextResponse.json(
          { error: 'Terapis ini tidak tersedia' },
          { status: 400 }
        );
      }

      updateData.therapist = { connect: { id: therapistId } };
    }

    // Update tanggal dan waktu jika ada
    if (bookingDate || bookingTime) {
      const existingTimePart = existingBooking.bookingTime;

      const newTimePart = bookingTime || existingTimePart;

      updateData.bookingDate = bookingDate ? new Date(bookingDate) : undefined;
      updateData.bookingTime = newTimePart;

      // Cek ketersediaan terapis pada waktu yang baru
      const therapistIdToCheck = therapistId || existingBooking.therapistId;

      if (therapistIdToCheck && updateData.bookingDate) {
        const conflictingBooking = await prisma.booking.findFirst({
          where: {
            id: { not: id },
            therapistId: therapistIdToCheck,
            bookingDate: updateData.bookingDate,
            bookingTime: newTimePart,
            status: {
              in: ['PENDING', 'CONFIRMED']
            }
          }
        });

        if (conflictingBooking) {
          return NextResponse.json(
            { error: 'Terapis sudah memiliki booking pada waktu tersebut' },
            { status: 400 }
          );
        }
      }
    }

    // Update catatan jika ada
    if (notes !== undefined) {
      updateData.notes = notes;
    }

    // Update status jika ada (gunakan enum)
    if (status && Object.values(BookingStatus).includes(status as BookingStatus)) {
      updateData.status = status as BookingStatus;

      // Jika status diubah menjadi COMPLETED, update transaksi (sesuaikan field)
      if (status === 'COMPLETED') {
        await prisma.transaction.updateMany({
          where: {
            bookingId: id,
            paymentStatus: 'PENDING'
          },
          data: {
            paymentStatus: 'PAID'
          }
        });
      }

      // Jika status diubah menjadi CANCELLED, update transaksi (sesuaikan field)
      if (status === 'CANCELLED') {
        await prisma.transaction.updateMany({
          where: {
            bookingId: id,
            paymentStatus: 'PENDING'
          },
          data: {
            paymentStatus: 'FAILED'
          }
        });
      }
    }

    // Gunakan transaksi untuk memastikan semua operasi berhasil atau gagal bersama-sama
    const updatedBooking = await prisma.$transaction(async (tx) => {
      // Update booking dengan data dasar
      const booking = await tx.booking.update({
        where: {
          id
        },
        data: updateData,
        include: {
          customer: true,
          bookingServices: { include: { service: true } },
          therapist: true,
          transaction: true
        }
      });

      // Update services jika ada
      if (services && Array.isArray(services) && services.length > 0) {
        // Hapus semua booking services yang ada
        await tx.bookingService.deleteMany({
          where: {
            bookingId: id
          }
        });

        // Proses services (format dengan quantity)
        let serviceIdsToFetch: string[] = [];
        let serviceQuantities: Record<string, number> = {};

        // Ekstrak ID dan simpan quantity
        services.forEach(svc => {
          if (svc.id) {
            serviceIdsToFetch.push(svc.id);
            serviceQuantities[svc.id] = svc.quantity || 1;
          }
        });

        // Cek semua service yang dipilih
        const servicesData = await tx.service.findMany({
          where: {
            id: { in: serviceIdsToFetch },
            outlets: {
              some: {
                outletId: bookingOutletId
              }
            },
            isActive: true
          }
        });

        if (servicesData.length !== serviceIdsToFetch.length) {
          throw new Error('Satu atau lebih layanan tidak ditemukan, tidak aktif, atau tidak tersedia di outlet ini');
        }

        // Buat entri BookingService baru untuk setiap layanan dengan quantity
        const bookingServiceData = servicesData.map(service => {
          const quantity = serviceQuantities[service.id] || 1;
          return {
            bookingId: booking.id,
            serviceId: service.id,
            price: service.price,
            quantity: quantity
          };
        });

        // Buat entri satu per satu
        for (const serviceData of bookingServiceData) {
          await tx.bookingService.create({
            data: serviceData
          });
        }
      }

      // Update customers jika ada
      if (customerIds && Array.isArray(customerIds) && customerIds.length > 0) {
        // Hapus semua booking customers yang ada kecuali primary
        await tx.bookingCustomer.deleteMany({
          where: {
            bookingId: id,
            isPrimary: false
          }
        });

        // Dapatkan customer utama
        const primaryCustomerId = customerIds[0];
        
        // Update customer utama jika berbeda
        if (primaryCustomerId !== booking.customerId) {
          await tx.booking.update({
            where: { id },
            data: { customerId: primaryCustomerId }
          });
          
          await tx.bookingCustomer.updateMany({
            where: { 
              bookingId: id,
              isPrimary: true
            },
            data: { customerId: primaryCustomerId }
          });
        }

        // Tambahkan pelanggan tambahan
        const additionalCustomerIds = customerIds.slice(1);
        for (const customerId of additionalCustomerIds) {
          // Cek apakah sudah ada
          const existingCustomer = await tx.bookingCustomer.findFirst({
            where: {
              bookingId: id,
              customerId,
              isPrimary: false
            }
          });

          if (!existingCustomer) {
            await tx.bookingCustomer.create({
              data: {
                bookingId: id,
                customerId,
                isPrimary: false
              }
            });
          }
        }
      }

      // Ambil booking yang sudah diupdate dengan semua relasinya
      return await tx.booking.findUnique({
        where: { id },
        include: {
          customer: true,
          bookingServices: { include: { service: true } },
          therapist: true,
          transaction: true,
          bookingCustomers: {
            include: {
              customer: true
            }
          }
        }
      });
    });

    // Tambahkan array customers ke hasil
    const allCustomers = updatedBooking?.bookingCustomers?.map(bc => bc.customer) || [];
    (updatedBooking as any).customers = allCustomers;

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Log perubahan booking
    await logBooking(
      'update',
      id,
      {
        updatedFields: Object.keys(body),
        customerName: updatedBooking?.customer?.name,
        therapistName: updatedBooking?.therapist?.name,
        bookingDate: bookingDate,
        bookingTime: bookingTime
      },
      bookingOutletId,
      userId // Gunakan userId dari token
    );

    return NextResponse.json({
      message: 'Booking berhasil diupdate',
      booking: updatedBooking
    });
  } catch (error) {
    console.error('Error updating booking:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengupdate booking' },
      { status: 500 }
    );
  }
}

// DELETE booking
export async function DELETE(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const { id } = context.params;

    if (!id) {
      return NextResponse.json(
        { error: 'ID booking diperlukan' },
        { status: 400 }
      );
    }

    // Cek dulu apakah ada transaksi terkait
    const relatedTransaction = await prisma.transaction.findFirst({
      where: { bookingId: id }
    });

    if (relatedTransaction) {
      // Pilihan 1: Larang hapus jika sudah ada transaksi
      return NextResponse.json(
        { error: 'Tidak dapat menghapus booking yang sudah memiliki transaksi terkait.' },
        { status: 400 }
      );
    }

    // Ambil data booking sebelum dihapus untuk logging
    const bookingToDelete = await prisma.booking.findUnique({
      where: { id },
      include: {
        customer: true,
        therapist: true,
        outlet: true
      }
    });

    // Hapus booking (Prisma akan handle cascade delete untuk BookingService)
    await prisma.booking.delete({
      where: { id },
    });

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Log penghapusan booking
    if (bookingToDelete) {
      await logBooking(
        'delete',
        id,
        {
          customerName: bookingToDelete.customer?.name,
          therapistName: bookingToDelete.therapist?.name,
          bookingDate: bookingToDelete.bookingDate,
          bookingTime: bookingToDelete.bookingTime,
          status: bookingToDelete.status
        },
        bookingToDelete.outletId,
        userId // Gunakan userId dari token
      );
    }

    return NextResponse.json({ message: 'Booking berhasil dihapus' });

  } catch (error) {
    // Handle error jika booking tidak ditemukan saat delete
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Booking tidak ditemukan' },
        { status: 404 }
      );
    }
    console.error('Error deleting booking:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus booking' },
      { status: 500 }
    );
  }
}

// PATCH untuk mengupdate status booking
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { status } = body;

    // 1. Validasi ID
    if (!id) {
      return NextResponse.json({ error: 'ID booking diperlukan' }, { status: 400 });
    }

    // 2. Validasi Status Baru
    if (!status || !Object.values(BookingStatus).includes(status as BookingStatus)) {
      return NextResponse.json(
        { error: 'Status booking baru tidak valid atau tidak disediakan' },
        { status: 400 }
      );
    }

    // 3. Cek Booking yang Ada
    const existingBooking = await prisma.booking.findUnique({
      where: { id },
    });

    if (!existingBooking) {
      return NextResponse.json({ error: 'Booking tidak ditemukan' }, { status: 404 });
    }

    // (Opsional) Tambahkan logika validasi transisi status jika perlu
    // Misalnya, tidak bisa kembali dari COMPLETED/CANCELLED ke PENDING
    const allowedTransitions: Record<string, string[]> = {
      PENDING: ['CONFIRMED', 'CANCELLED'],
      CONFIRMED: ['COMPLETED', 'CANCELLED', 'NO_SHOW'],
      // Status akhir (COMPLETED, CANCELLED, NO_SHOW) tidak bisa diubah lagi
    };

    const currentStatus = existingBooking.status;
    if (allowedTransitions[currentStatus] && !allowedTransitions[currentStatus].includes(status)) {
      return NextResponse.json(
        { error: `Tidak dapat mengubah status dari ${currentStatus} ke ${status}` },
        { status: 400 }
      );
    }
    if (!allowedTransitions[currentStatus] && currentStatus !== status) {
       return NextResponse.json(
        { error: `Status ${currentStatus} tidak dapat diubah.` },
        { status: 400 }
      );
    }

    // 4. Update Status Booking
    const updatedBooking = await prisma.booking.update({
      where: { id },
      data: { status: status as BookingStatus },
      // Include data yang relevan jika perlu dikembalikan ke frontend
      include: {
        customer: true,
        therapist: true,
        bookingServices: { include: { service: true } },
        outlet: true
      }
    });

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Log perubahan status booking
    await logBooking(
      status.toLowerCase(),
      id,
      {
        previousStatus: currentStatus,
        newStatus: status,
        customerName: updatedBooking.customer?.name,
        therapistName: updatedBooking.therapist?.name
      },
      updatedBooking.outletId,
      userId // Gunakan userId dari token
    );

    // TODO: Tambahkan logika untuk update transaksi terkait jika status berubah
    // (misal: jika CANCELLED -> refund transaction, jika COMPLETED -> mark transaction PAID)

    return NextResponse.json({
      message: 'Status booking berhasil diperbarui',
      booking: updatedBooking
    });

  } catch (error) {
    console.error('Error updating booking status:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui status booking' },
      { status: 500 }
    );
  }
}