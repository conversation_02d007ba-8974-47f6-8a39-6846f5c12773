'use client';

import { motion } from 'framer-motion';
import { FiCheckCircle } from 'react-icons/fi';

export default function Services() {
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number = 1) => ({
      opacity: 1,
      y: 0,
      transition: { staggerChildren: 0.1, delayChildren: i * 0.1, duration: 0.5 }
    })
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const services = [
    {
      title: "Pijat Swedia",
      desc: "Relaksasi klasik untuk melemaskan otot dan meningkatkan sirkulasi.",
      color: "from-primary/10 to-primary-light/5",
      iconBg: "bg-primary/20"
    },
    {
      title: "Pijat Aromaterapi",
      desc: "Kombinasi pijat dengan minyak esensial untuk relaksasi mendalam.",
      color: "from-secondary/10 to-secondary-light/5",
      iconBg: "bg-secondary/20"
    },
    {
      title: "Pijat Refleksi",
      desc: "Stimulasi titik-titik refleksi pada kaki untuk keseimbangan tubuh.",
      color: "from-primary/10 to-primary-light/5",
      iconBg: "bg-primary/20"
    },
    {
      title: "Pijat Jaringan Dalam",
      desc: "Teknik intensif untuk meredakan ketegangan otot kronis.",
      color: "from-secondary/10 to-secondary-light/5", 
      iconBg: "bg-secondary/20"
    }
  ];

  return (
    <motion.section 
      id="layanan"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      variants={staggerContainer}
      className="py-16 px-4 md:px-20"
    >
      <motion.div variants={fadeIn} className="text-center mb-16">
        <p className="text-secondary uppercase font-bold tracking-widest mb-2">LAYANAN KAMI</p>
        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800">
          Kami Menawarkan Layanan <br className="hidden md:block" />Pijat Terbaik
        </h2>
        <p className="text-gray-500 max-w-lg mx-auto">
          Pilih dari berbagai layanan pijat premium kami yang dirancang untuk memenuhi kebutuhan relaksasi Anda
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
        {services.map((service, i) => (
          <motion.div 
            key={i} 
            variants={fadeIn}
            custom={i + 1}
            className={`card hover:shadow-xl transition-all duration-300 hover:-translate-y-2 bg-gradient-to-br ${service.color} overflow-hidden group border border-gray-100 rounded-3xl p-6`}
          >
            <div className={`${service.iconBg} w-16 h-16 rounded-2xl flex items-center justify-center mb-5 group-hover:scale-110 transition-transform duration-300`}>
              <FiCheckCircle className={i % 2 === 0 ? "text-primary" : "text-secondary"} size={28} />
            </div>
            <h3 className="text-xl font-bold mb-3">{service.title}</h3>
            <p className="text-gray-500 text-sm">{service.desc}</p>
          </motion.div>
        ))}
      </div>
    </motion.section>
  );
}
