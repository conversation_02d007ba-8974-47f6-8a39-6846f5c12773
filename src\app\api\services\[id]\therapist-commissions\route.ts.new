import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserIdFromToken } from '@/lib/auth-utils';
import { logService } from '@/lib/logger';

// GET komisi terapis khusus untuk layanan tertentu
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const serviceId = params.id;

    if (!serviceId) {
      return NextResponse.json(
        { error: 'ID layanan diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah layanan ada
    const service = await prisma.service.findUnique({
      where: { id: serviceId }
    });

    if (!service) {
      return NextResponse.json(
        { error: 'Layanan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Untuk sementara, kembalikan data kosong
    return NextResponse.json({
      message: 'Data komisi terapis berhasil diambil',
      therapistCommissions: [],
      availableTherapists: [],
      defaultCommission: service.commission
    });
  } catch (error) {
    console.error('Error fetching therapist commissions:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data komisi terapis' },
      { status: 500 }
    );
  }
}

// POST untuk menambahkan komisi terapis khusus
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const serviceId = params.id;
    const body = await request.json();
    const { therapistId, commission } = body;

    if (!serviceId || !therapistId || commission === undefined) {
      return NextResponse.json(
        { error: 'ID layanan, ID terapis, dan komisi diperlukan' },
        { status: 400 }
      );
    }

    // Validasi tipe data
    if (typeof commission !== 'number' || commission < 0) {
      return NextResponse.json(
        { error: 'Komisi harus berupa angka dan tidak boleh negatif' },
        { status: 400 }
      );
    }

    // Cek apakah layanan ada
    const service = await prisma.service.findUnique({
      where: { id: serviceId }
    });

    if (!service) {
      return NextResponse.json(
        { error: 'Layanan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Cek apakah terapis ada
    const therapist = await prisma.therapist.findUnique({
      where: { id: therapistId }
    });

    if (!therapist) {
      return NextResponse.json(
        { error: 'Terapis tidak ditemukan' },
        { status: 404 }
      );
    }

    // Untuk sementara, kembalikan respons sukses tanpa membuat data
    return NextResponse.json({
      message: 'Komisi terapis khusus berhasil ditambahkan (simulasi)',
      therapistCommission: {
        id: 'temp-id',
        therapistId,
        serviceId,
        commission,
        therapist: {
          name: therapist.name,
          outlet: {
            id: therapist.outletId,
            name: 'Outlet'
          }
        },
        service: {
          name: service.name
        }
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating therapist commission:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menambahkan komisi terapis khusus' },
      { status: 500 }
    );
  }
}
