'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';
import { FiCheckCircle, FiStar, FiChevronDown } from 'react-icons/fi';
import Button from '../ui/Button';

export default function Hero() {
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number = 1) => ({
      opacity: 1,
      y: 0,
      transition: { staggerChildren: 0.1, delayChildren: i * 0.1, duration: 0.5 }
    })
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const bounceAnimation = {
    y: [0, -8, 0],
    opacity: [0.5, 1, 0.5],
    transition: {
      duration: 2,
      repeat: Infinity,
      repeatType: "loop" as const
    }
  };

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="relative h-screen pt-16 flex items-center overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 z-0 transform scale-110 motion-safe:animate-subtle-zoom">
        <Image
          src="https://images.unsplash.com/photo-1600334129128-685c5582fd35?q=80&w=2070"
          alt="Massage therapy background"
          fill
          className="object-cover filter brightness-75"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/60"></div>
        
        {/* Decorative Elements untuk hero */}
        <div className="absolute inset-0 bg-pattern-dot opacity-10 mix-blend-soft-light"></div>
        <div className="absolute left-0 bottom-0 w-full h-24 bg-gradient-to-t from-black/40 to-transparent"></div>
        <div className="absolute -bottom-10 -left-10 w-80 h-80 bg-primary/30 blur-[100px] rounded-full opacity-40 mix-blend-multiply"></div>
        <div className="absolute -top-10 -right-10 w-80 h-80 bg-secondary/30 blur-[100px] rounded-full opacity-40 mix-blend-multiply"></div>
      </div>

      <div className="container mx-auto px-4 md:px-20 relative z-10 text-white">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="relative z-10"
          >
            <motion.p 
              variants={fadeIn} 
              custom={1} 
              className="text-secondary uppercase font-bold tracking-widest mb-2 drop-shadow-md"
            >
              Badan Segar Urusan Lancar
            </motion.p>
            <motion.h1 
              variants={fadeIn} 
              custom={2} 
              className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6 drop-shadow-lg"
            >
              Temukan <span className="text-primary-light relative inline-block">
                <span className="relative z-10">Relaksasi</span>
                <span className="absolute -inset-1 bg-primary-light/20 blur-md -z-10 rounded-lg"></span>
              </span> Sejati Bersama Kami
            </motion.h1>
            <motion.p 
              variants={fadeIn} 
              custom={3} 
              className="text-base text-gray-200 mb-8 max-w-md drop-shadow-md"
            >
              Lepaskan penat dan pulihkan energi Anda dengan berbagai layanan pijat premium yang disesuaikan dengan kebutuhan Anda
            </motion.p>
            <motion.div variants={fadeIn} custom={4} className="flex flex-wrap gap-4">
              <Button variant="primary" size="lg">Pesan Sekarang</Button>
              <Button variant="ghost" size="lg">
                <div className="w-8 h-8 rounded-full bg-secondary/70 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                </div>
                <span>Tonton Video</span>
              </Button>
            </motion.div>
            
            {/* Floating Card */}
            <motion.div 
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
              className="mt-12 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-4 max-w-xs"
            >
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-full bg-primary/60 flex items-center justify-center">
                  <FiCheckCircle className="text-white" size={24} />
                </div>
                <div>
                  <p className="font-semibold text-white">Kepuasan Terjamin</p>
                  <div className="flex items-center gap-1 mt-1">
                    {[...Array(5)].map((_, i) => 
                      <FiStar key={i} className="text-secondary" size={14} />
                    )}
                    <span className="text-xs text-gray-200 ml-1">5.0 Rating</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
      
      {/* Scroll Down Indicator */}
      <motion.div 
        className="absolute bottom-8 left-1/2 -translate-x-1/2 text-white cursor-pointer z-10"
        animate={bounceAnimation}
        onClick={() => scrollToSection('layanan')}
      >
        <div className="flex flex-col items-center">
          <span className="text-sm mb-2">Scroll Down</span>
          <FiChevronDown size={24} />
        </div>
      </motion.div>
    </div>
  );
}
