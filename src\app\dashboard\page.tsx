'use client'; // Jika perlu animasi atau interaksi

import { useState, useEffect, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useOutletContext } from '@/contexts/OutletContext';
import { toast } from 'react-hot-toast';
import {
  FiCalendar, FiDollarSign, FiUserCheck, FiClock, FiUsers, FiZap, FiArrowRight, FiBriefcase, FiAlertTriangle, FiActivity,
  FiCheckCircle, FiXCircle, FiDownload, FiChevronDown, FiShoppingCart
} from 'react-icons/fi';
import Link from 'next/link';
import {
  XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area
} from 'recharts';
import resolveConfig from 'tailwindcss/resolveConfig'; // Untuk mengambil warna tema
import tailwindConfig from '../../../tailwind.config.js'; // Sesuaikan path jika perlu
import * as XLSX from 'xlsx';
import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';

// Resolve Tailwind config untuk mendapatkan warna tema
const fullConfig = resolveConfig(tailwindConfig);

// Varian animasi
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

// Tipe data Chart
type ChartDataPoint = {
  label: string; // Bisa jam, hari, minggu, bulan
  bookings: number;
  transactions: number; // Tambahkan jumlah transaksi
};

// Tipe data mock Dashboard
type StatData = {
  bookingsToday: number;
  revenueToday: number;
  availableTherapists: number;
  totalGuestsToday: number;
  transactionsToday: number; // Tambahkan total transaksi hari ini
};

// Tipe Baru
type TopService = {
  name: string;
  count: number; // Ubah nama field agar lebih generik (bisa booking/transaksi)
};

type TopTherapist = {
  name: string;
  avatarUrl?: string; // Opsional
  bookingCount: number; // Jumlah booking
  transactionCount: number; // Jumlah transaksi
};

// Update DashboardData
type DashboardData = {
  stats: StatData;
  upcomingBookings: UpcomingBooking[];
  peakHours: string[];
  hourlyBookings: ChartDataPoint[];
  topServices: TopService[];
  topTherapists: TopTherapist[];
};

type UpcomingBooking = {
  id: string;
  time: string;
  customerName: string;
  therapistName: string;
  serviceName: string; // Ganti dari service
  status: string; // Tambahkan status booking
};

// Tipe data dari API booking
type ApiBooking = {
  id: string;
  bookingDate: string;
  bookingTime: string;
  status: string;
  customer: { id: string; name: string };
  therapist: { id: string; name: string };
  services: { id: string; name: string; price: number }[];
  // ... potentially other fields
};

// Tipe data dari API transaction
type ApiTransaction = {
    id: string;
    bookingId: string;
    outletId: string;
    customerId: string;
    therapistId: string;
    status: string; // PENDING, COMPLETED, FAILED, REFUNDED
    totalAmount: number;
    paymentMethod?: string | null;
    createdAt: string;
    updatedAt: string;
    customer: { id: string; name: string };
    therapist: { id: string; name: string };
    booking?: { // Include booking details linked to transaction (optional)
        id: string;
        bookingTime: string;
        bookingDate: string;
        services: { id: string; name: string; price: number }[];
    };
    outlet: { id: string; name: string };
    // Tambahkan properti untuk items transaksi
    transactionItems?: {
        service: { id: string; name: string; price: number };
        quantity: number;
        price: number;
    }[];
    // Format alternatif untuk items
    items?: {
        id?: string;
        name: string;
        price: number;
        quantity: number;
    }[];
};

// Opsi Periode Chart
type ChartPeriod = 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
const chartPeriods: { key: ChartPeriod; label: string }[] = [
  { key: 'daily', label: 'Harian' },
  { key: 'weekly', label: 'Mingguan' },
  { key: 'monthly', label: 'Bulanan' },
  { key: 'quarterly', label: '3 Bulan' },
  { key: 'yearly', label: 'Tahunan' },
];

export default function DashboardOverviewPage() {
  const { selectedOutletId } = useOutletContext();
  
  // Memoize state initialization untuk mencegah re-render tidak perlu
  const initialState = useMemo(() => ({
    outletName: '',
    dashboardData: null as DashboardData | null,
    chartData: [] as ChartDataPoint[],
    selectedPeriod: 'daily' as ChartPeriod,
    selectedYear: new Date().getFullYear(),
    isLoading: true,
    isChartLoading: true,
    error: null as string | null,
    showCancelModal: false,
    cancelBookingId: '',
    cancelBookingNumber: '',
    upcomingBookings: [] as UpcomingBooking[],
    showDownloadOptions: false,
    paymentMethodTotals: {} as {[key: string]: number},
    periodPaymentMethodTotals: {} as {[key: string]: number}
  }), []);

  const [outletName, setOutletName] = useState<string>(initialState.outletName);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(initialState.dashboardData);
  const [chartData, setChartData] = useState<ChartDataPoint[]>(initialState.chartData);
  const [selectedPeriod, setSelectedPeriod] = useState<ChartPeriod>(initialState.selectedPeriod);
  const [selectedYear, setSelectedYear] = useState<number>(initialState.selectedYear);
  const [isLoading, setIsLoading] = useState(initialState.isLoading);
  const [isChartLoading, setIsChartLoading] = useState(initialState.isChartLoading);
  const [error, setError] = useState<string | null>(initialState.error);
  const [showCancelModal, setShowCancelModal] = useState(initialState.showCancelModal);
  const [cancelBookingId, setCancelBookingId] = useState<string>(initialState.cancelBookingId);
  const [cancelBookingNumber, setCancelBookingNumber] = useState<string>(initialState.cancelBookingNumber);
  const [upcomingBookings, setUpcomingBookings] = useState<UpcomingBooking[]>(initialState.upcomingBookings);
  const [showDownloadOptions, setShowDownloadOptions] = useState(initialState.showDownloadOptions);
  const [paymentMethodTotals, setPaymentMethodTotals] = useState<{[key: string]: number}>(initialState.paymentMethodTotals);
  const [periodPaymentMethodTotals, setPeriodPaymentMethodTotals] = useState<{[key: string]: number}>(initialState.periodPaymentMethodTotals);

  // Memoize warna tema
  const currentThemeColors = useMemo(() => ({
    primary: fullConfig.theme?.colors?.primary || '#F4BB45',
    secondary: fullConfig.theme?.colors?.secondary || '#4E9E97',
  }), []);

  // Memoize formatCurrency function
  const formatCurrency = useCallback((value: number): string => {
    if (isNaN(value) || value === undefined || value === null) {
      return 'Rp 0';
    }
    const formatted = new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
    return formatted.replace('Rp\u00A0', 'Rp');
  }, []);

  // Memoize API calls untuk mencegah unnecessary re-fetching
  const fetchDashboardData = useCallback(async () => {
    if (!selectedOutletId) return;
    
    setIsLoading(true);
    setError(null);

    try {
      // Parallel API calls untuk meningkatkan performa
      // Gunakan parameter yang lebih akurat untuk filter hari ini
      const todayDateStr = getTodayDateString();
      const [outletResponse, bookingsResponse, transactionsResponse, therapistsResponse] = await Promise.all([
        fetch(`/api/outlets/${selectedOutletId}`),
        fetch(`/api/bookings?outletId=${selectedOutletId}&startDate=${todayDateStr}&endDate=${todayDateStr}&debug=true&_nocache=${Date.now()}`),
        fetch(`/api/transactions?outletId=${selectedOutletId}&startDate=${todayDateStr}&endDate=${todayDateStr}&_nocache=${Date.now()}`),
        fetch(`/api/therapists?outletId=${selectedOutletId}&isActive=true`)
      ]);

      // Process responses in parallel dengan error handling yang lebih baik
      const [outletData, bookingsData, transactionsData, therapistsData] = await Promise.all([
        outletResponse.ok ? outletResponse.json() : { outlet: { name: 'Outlet Tidak Diketahui' } },
        bookingsResponse.ok ? bookingsResponse.json() : { bookings: [] },
        transactionsResponse.ok ? transactionsResponse.json() : { transactions: [] },
        therapistsResponse.ok ? therapistsResponse.json() : { therapists: [] }
      ]);

      setOutletName(outletData.outlet?.name || 'Outlet Tidak Diketahui');

      // Process data dengan logic yang dioptimasi
      const todayBookings: ApiBooking[] = bookingsData.bookings || [];
      const todayTransactions: ApiTransaction[] = transactionsData.transactions || [];
      
      // Debug logging untuk memeriksa data yang diterima
      console.log('[DEBUG] Dashboard data fetched for date:', getTodayDateString());
      console.log('[DEBUG] Today bookings count:', todayBookings.length);
      console.log('[DEBUG] Today transactions count:', todayTransactions.length);
      console.log('[DEBUG] Bookings sample:', todayBookings.slice(0, 2));
      console.log('[DEBUG] Transactions sample:', todayTransactions.slice(0, 2));
      
      // Calculate metrics dengan Set untuk unique guests (lebih efisien)
      const uniqueGuests = new Set<string>();
      todayTransactions.forEach((trx: ApiTransaction) => {
        if (trx.customer?.id) uniqueGuests.add(trx.customer.id);
      });
      todayBookings.forEach((booking: ApiBooking) => {
        if (booking.customer?.id) uniqueGuests.add(booking.customer.id);
      });

      const totalRevenue = todayTransactions.reduce((sum: number, trx: ApiTransaction) => 
        sum + (trx.totalAmount || 0), 0
      );

      // Calculate payment method totals for today
      const todayPaymentTotals: {[key: string]: number} = {};
      todayTransactions.forEach((trx: ApiTransaction) => {
        const paymentMethod = trx.paymentMethod || 'UNKNOWN';
        todayPaymentTotals[paymentMethod] = (todayPaymentTotals[paymentMethod] || 0) + (trx.totalAmount || 0);
      });
      setPaymentMethodTotals(todayPaymentTotals);

      // Calculate top services for today
      const serviceCounts: Record<string, number> = {};
      
      // Count from bookings
      todayBookings.forEach((booking: ApiBooking) => {
        booking.services?.forEach((service) => {
          if (service.name) {
            serviceCounts[service.name] = (serviceCounts[service.name] || 0) + 1;
          }
        });
      });

      // Count from transactions
      todayTransactions.forEach((transaction: ApiTransaction) => {
        // From booking services if available
        if (transaction.booking?.services) {
          transaction.booking.services.forEach((service) => {
            if (service.name) {
              serviceCounts[service.name] = (serviceCounts[service.name] || 0) + 1;
            }
          });
        }
        // From transaction items
        if (transaction.transactionItems && transaction.transactionItems.length > 0) {
          transaction.transactionItems.forEach((item) => {
            if (item.service?.name) {
              serviceCounts[item.service.name] = (serviceCounts[item.service.name] || 0) + item.quantity;
            }
          });
        }
        // From items (alternative format)
        if (transaction.items && transaction.items.length > 0) {
          transaction.items.forEach((item) => {
            if (item.name) {
              serviceCounts[item.name] = (serviceCounts[item.name] || 0) + (item.quantity || 1);
            }
          });
        }
      });

      const topServices = Object.entries(serviceCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([name, count]) => ({ name, count }));

      // Calculate top therapists for today
      const therapistCounts: Record<string, { name: string; bookingCount: number; transactionCount: number }> = {};

      // Count from bookings
      todayBookings.forEach((booking: ApiBooking) => {
        if (booking.therapist?.id && booking.therapist?.name) {
          if (!therapistCounts[booking.therapist.id]) {
            therapistCounts[booking.therapist.id] = {
              name: booking.therapist.name,
              bookingCount: 0,
              transactionCount: 0
            };
          }
          therapistCounts[booking.therapist.id].bookingCount++;
        }
      });

      // Count from transactions
      todayTransactions.forEach((transaction: ApiTransaction) => {
        if (transaction.therapist?.id && transaction.therapist?.name) {
          if (!therapistCounts[transaction.therapist.id]) {
            therapistCounts[transaction.therapist.id] = {
              name: transaction.therapist.name,
              bookingCount: 0,
              transactionCount: 0
            };
          }
          therapistCounts[transaction.therapist.id].transactionCount++;
        }
      });

      const topTherapists = Object.values(therapistCounts)
        .sort((a, b) => {
          const totalA = a.bookingCount + a.transactionCount;
          const totalB = b.bookingCount + b.transactionCount;
          return totalB - totalA;
        })
        .slice(0, 5)
        .map(t => ({
          name: t.name,
          bookingCount: t.bookingCount,
          transactionCount: t.transactionCount
        }));

      // Set dashboard data dengan struktur yang konsisten
      setDashboardData({
        stats: {
          bookingsToday: todayBookings.length,
          revenueToday: totalRevenue,
          availableTherapists: therapistsData.therapists?.length || 0,
          totalGuestsToday: uniqueGuests.size,
          transactionsToday: todayTransactions.length
        },
        upcomingBookings: [], // Akan diisi oleh fetchChartAndRankingData
        peakHours: [],
        topServices: topServices,
        topTherapists: topTherapists,
        hourlyBookings: []
      });

      // Fetch upcoming bookings untuk hari ini
      try {
        const upcomingResponse = await fetch(`/api/bookings?outletId=${selectedOutletId}&status=CONFIRMED&limit=10&startDate=${todayDateStr}&endDate=${todayDateStr}`);
        if (upcomingResponse.ok) {
          const upcomingData = await upcomingResponse.json();
          const formattedBookings = (upcomingData.bookings || []).map((booking: any) => ({
            id: booking.id,
            time: booking.bookingTime,
            customerName: booking.customer?.name || 'Pelanggan',
            therapistName: booking.therapist?.name || 'Terapis',
            serviceName: booking.bookingServices?.[0]?.service?.name || 'Layanan',
            status: booking.status
          }));
          setUpcomingBookings(formattedBookings);
        }
      } catch (upcomingError) {
        console.error('Error fetching upcoming bookings:', upcomingError);
        setUpcomingBookings([]);
      }

      // Chart data akan dimuat terpisah melalui useEffect

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Terjadi kesalahan saat memuat data dashboard';
      setError(errorMessage);
      setDashboardData(null);
      
      // Reset chart loading state on error
      setIsChartLoading(false);
    } finally {
      setIsLoading(false);
    }
  }, [selectedOutletId]);

  // Helper function untuk mendapatkan tanggal hari ini dalam timezone Asia/Makassar (WITA)
  const getTodayDateString = useCallback(() => {
    // Menggunakan Intl.DateTimeFormat untuk memastikan timezone yang akurat
    const witaFormatter = new Intl.DateTimeFormat('en-CA', {
      timeZone: 'Asia/Makassar',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
    
    const dateStr = witaFormatter.format(new Date());
    console.log('[DEBUG] Today date string (WITA using Intl):', dateStr);
    console.log('[DEBUG] Current time in WITA:', new Intl.DateTimeFormat('id-ID', {
      timeZone: 'Asia/Makassar',
      year: 'numeric',
      month: '2-digit',  
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date()));
    
    return dateStr;
  }, []);

  // Memoize upcoming bookings processing
  const processedUpcomingBookings = useMemo(() => {
    return upcomingBookings.slice(0, 10); // Limit untuk performa
  }, [upcomingBookings]);

  // Optimized useEffect dengan dependency yang tepat
  useEffect(() => {
    if (selectedOutletId !== null) {
      fetchDashboardData();
    }
  }, [selectedOutletId, fetchDashboardData]);

  // Optimized fetchChartAndRankingData dengan logic yang sudah ada
  const fetchChartAndRankingData = useCallback(async () => {
    if (!selectedOutletId) return;

    setIsChartLoading(true);

    try {
      // Tentukan rentang tanggal berdasarkan periode menggunakan WITA timezone
      const today = new Date();
      const witaToday = new Date(today.getTime() + (8 * 60 * 60 * 1000)); // +8 hours for WITA
      let fromDate = new Date(witaToday);
      let toDate = new Date(witaToday);

      switch (selectedPeriod) {
        case 'weekly':
          fromDate.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1));
          break;
        case 'monthly':
          fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
          break;
        case 'quarterly':
          fromDate = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1);
          break;
        case 'yearly':
          fromDate = new Date(selectedYear, 0, 1);
          toDate = new Date(selectedYear, 11, 31);
          break;
        case 'daily':
        default:
          break;
      }

      const fromDateStr = fromDate.toISOString().split('T')[0];
      const toDateStr = toDate.toISOString().split('T')[0];

      // Fetch data booking dan transaksi untuk periode tersebut
      const [bookingsResponse, transactionsResponse] = await Promise.all([
        fetch(`/api/bookings?outletId=${selectedOutletId}&startDate=${fromDateStr}&endDate=${toDateStr}`),
        fetch(`/api/transactions?outletId=${selectedOutletId}&startDate=${fromDateStr}&endDate=${toDateStr}&status=COMPLETED`)
      ]);

      if (!bookingsResponse.ok || !transactionsResponse.ok) {
        throw new Error('Gagal memuat data chart');
      }

      const [bookingsData, transactionsData] = await Promise.all([
        bookingsResponse.json(),
        transactionsResponse.json()
      ]);

      const periodBookings: ApiBooking[] = bookingsData.bookings || [];
      const periodTransactions: ApiTransaction[] = transactionsData.transactions || [];

      let newChartData: ChartDataPoint[] = [];
      let newPeakHours: string[] = [];

      // Generate chart data berdasarkan periode
      if (selectedPeriod === 'daily') {
        const hours = Array(24).fill(0).map(() => ({ bookings: 0, transactions: 0 }));
        
        periodBookings.forEach((booking) => {
          if (booking.bookingTime) {
            const hour = parseInt(booking.bookingTime.split(':')[0], 10);
            if (hour >= 0 && hour < 24) {
              hours[hour].bookings++;
            }
          }
        });

        periodTransactions.forEach((transaction) => {
          if (transaction.createdAt) {
            const hour = new Date(transaction.createdAt).getHours();
            if (hour >= 0 && hour < 24) {
              hours[hour].transactions++;
            }
          }
        });

        // Generate chart data dan peak hours
        newChartData = hours.map((count, hour) => ({
          label: `${hour.toString().padStart(2, '0')}:00`,
          bookings: count.bookings,
          transactions: count.transactions,
        })).filter(d => d.bookings > 0 || d.transactions > 0);

        // Calculate peak hours (top 3)
        const hourlyActivity = hours.map((count, hour) => ({
          hour,
          total: count.bookings + count.transactions
        })).filter(h => h.total > 0).sort((a, b) => b.total - a.total).slice(0, 3);

        newPeakHours = hourlyActivity.map(h => `${h.hour.toString().padStart(2, '0')}:00`);
      } else if (selectedPeriod === 'weekly') {
        const days = ['Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab', 'Min'];
        const dayCounts = Array(7).fill(0).map(() => ({ bookings: 0, transactions: 0 }));

        periodBookings.forEach((booking) => {
          if (booking.bookingDate) {
            const bookingDate = new Date(booking.bookingDate + 'T00:00:00');
            if (!isNaN(bookingDate.getTime())) {
              const dayIndex = (bookingDate.getDay() + 6) % 7;
              if (dayIndex >= 0 && dayIndex < 7) {
                dayCounts[dayIndex].bookings++;
              }
            }
          }
        });

        periodTransactions.forEach((transaction) => {
          if (transaction.createdAt) {
            const transactionDate = new Date(transaction.createdAt);
            if (!isNaN(transactionDate.getTime())) {
              const dayIndex = (transactionDate.getDay() + 6) % 7;
              if (dayIndex >= 0 && dayIndex < 7) {
                dayCounts[dayIndex].transactions++;
              }
            }
          }
        });

        newChartData = days.map((day, index) => ({
          label: day,
          bookings: dayCounts[index].bookings,
          transactions: dayCounts[index].transactions
        }));
      } else if (selectedPeriod === 'monthly') {
        const weeks = Array(5).fill(0).map(() => ({ bookings: 0, transactions: 0 }));

        periodBookings.forEach((booking) => {
          if (booking.bookingDate) {
            const bookingDate = new Date(booking.bookingDate + 'T00:00:00');
            if (!isNaN(bookingDate.getTime())) {
              const weekIndex = Math.floor((bookingDate.getDate() - 1) / 7);
              if (weekIndex >= 0 && weekIndex < 5) {
                weeks[weekIndex].bookings++;
              }
            }
          }
        });

        periodTransactions.forEach((transaction) => {
          if (transaction.createdAt) {
            const transactionDate = new Date(transaction.createdAt);
            if (!isNaN(transactionDate.getTime())) {
              const weekIndex = Math.floor((transactionDate.getDate() - 1) / 7);
              if (weekIndex >= 0 && weekIndex < 5) {
                weeks[weekIndex].transactions++;
              }
            }
          }
        });

        newChartData = weeks.map((count, index) => ({
          label: `W${index + 1}`,
          bookings: count.bookings,
          transactions: count.transactions
        })).filter(d => d.bookings > 0 || d.transactions > 0);
      }

      // Update state dengan data chart
      setChartData(newChartData);
      setDashboardData(prevData => {
        if (!prevData) return null;
        return {
          ...prevData,
          peakHours: newPeakHours,
          hourlyBookings: newChartData,
        };
      });

    } catch (error) {
      console.error('Error fetching chart data:', error);
      setChartData([]);
      setDashboardData(prevData => {
        if (!prevData) return null;
        return {
          ...prevData,
          peakHours: [],
          hourlyBookings: [],
        };
      });
    } finally {
      setIsChartLoading(false);
    }
  }, [selectedOutletId, selectedPeriod, selectedYear]);

  // Effect untuk fetchChartAndRankingData ketika selectedPeriod/selectedYear berubah
  useEffect(() => {
    if (selectedOutletId !== null && !isLoading) {
      fetchChartAndRankingData();
    }
  }, [selectedOutletId, selectedPeriod, selectedYear, isLoading, fetchChartAndRankingData]);

  // Fungsi untuk menampilkan modal konfirmasi pembatalan
  const handleShowCancelModal = (bookingId: string) => {
    // Ambil 6 karakter pertama dari ID booking untuk tampilan yang lebih singkat
    const shortBookingNumber = bookingId.substring(0, 6).toUpperCase();
    setCancelBookingId(bookingId);
    setCancelBookingNumber(shortBookingNumber);
    setShowCancelModal(true);
  };

  // Fungsi untuk menangani konfirmasi pembatalan
  const handleConfirmCancel = async () => {
    if (!cancelBookingId) return;

    setIsLoading(true);
    setShowCancelModal(false);

    try {
      const response = await fetch(`/api/bookings/${cancelBookingId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'CANCELLED' }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Gagal membatalkan booking`);
      }

      // Update upcomingBookings secara realtime tanpa perlu fetch ulang
      setUpcomingBookings(prevBookings => {
        // Hapus booking yang dibatalkan dari daftar
        return prevBookings.filter(booking => booking.id !== cancelBookingId);
      });

      setIsLoading(false);
      toast.success(`Booking #${cancelBookingNumber} berhasil dibatalkan.`);
    } catch (error) {
      toast.error(`Gagal membatalkan booking: ${error instanceof Error ? error.message : 'Silakan coba lagi.'}`);
      setIsLoading(false);
    }

    // Reset state
    setCancelBookingId('');
    setCancelBookingNumber('');
  };

  // Fungsi untuk memperbarui status booking
  const handleUpdateBookingStatus = async (bookingId: string, newStatus: string) => {
    // Validasi string status
    const validStatuses = ['PENDING', 'CONFIRMED', 'COMPLETED', 'CANCELLED', 'NO_SHOW'];
    if (!validStatuses.includes(newStatus)) {
      toast.error("Status baru tidak valid.");
      return;
    }

    // Jika status CANCELLED, gunakan modal (sudah ditangani di handleConfirmCancel)
    if (newStatus === 'CANCELLED') {
      handleShowCancelModal(bookingId);
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(`/api/bookings/${bookingId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Gagal memperbarui status booking ke ${newStatus}`);
      }

      // Update upcomingBookings secara realtime tanpa perlu fetch ulang
      setUpcomingBookings(prevBookings => {
        // Jika status CANCELLED, hapus booking dari daftar
        if (newStatus === 'CANCELLED') {
          return prevBookings.filter(booking => booking.id !== bookingId);
        }

        // Jika status lain (CONFIRMED), update status booking
        return prevBookings.map(booking => {
          if (booking.id === bookingId) {
            return { ...booking, status: newStatus };
          }
          return booking;
        });
      });

      setIsLoading(false);
      // Ambil 6 karakter pertama dari ID booking untuk tampilan yang lebih singkat
      const shortBookingNumber = bookingId.substring(0, 6).toUpperCase();
      toast.success(`Status booking #${shortBookingNumber} berhasil diubah menjadi ${newStatus}.`);
    } catch (error) {
      toast.error(`Gagal memperbarui status booking: ${error instanceof Error ? error.message : 'Silakan coba lagi.'}`);
      setIsLoading(false);
    }
  };

  // Extract data dari dashboardData untuk UI
  const stats = dashboardData?.stats || {
    bookingsToday: 0,
    revenueToday: 0,
    availableTherapists: 0,
    totalGuestsToday: 0,
    transactionsToday: 0
  };

  const topServices = dashboardData?.topServices || [];
  const topTherapists = dashboardData?.topTherapists || [];
  const peakHours = dashboardData?.peakHours || [];

  // Format values untuk display
  const bookingsTodayStr = stats.bookingsToday.toString();
  const totalGuestsTodayStr = stats.totalGuestsToday.toString();

  // Fungsi untuk mengunduh data dalam format Excel dan PDF
  const handleDownloadChartData = (format: 'excel' | 'pdf') => {
    if (chartData.length === 0) return;

    // Buat nama file berdasarkan periode yang dipilih
    let fileName = `aktivitas-outlet_${selectedPeriod}`;
    if (selectedPeriod === 'yearly') {
      fileName += `_${selectedYear}`;
    }
    fileName += `_${new Date().toISOString().split('T')[0]}`;

    if (format === 'excel') {
      try {
        // Persiapkan data untuk Excel
        const worksheet = XLSX.utils.json_to_sheet(
          chartData.map(point => ({
            Label: point.label,
            'Jumlah Booking': point.bookings,
            'Jumlah Transaksi': point.transactions
          }))
        );

        // Buat workbook baru
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Data Aktivitas');

        // Atur lebar kolom
        const colWidths = [
          { wch: 10 }, // Label
          { wch: 15 }, // Jumlah Booking
          { wch: 15 }, // Jumlah Transaksi
        ];
        worksheet['!cols'] = colWidths;

        // Ekspor ke file Excel
        XLSX.writeFile(workbook, `${fileName}.xlsx`);

        toast.success('Data berhasil diunduh dalam format Excel');
      } catch (error) {
        console.error('Error exporting to Excel:', error);
        toast.error('Gagal mengunduh data Excel. Silakan coba lagi.');
      }
    } else if (format === 'pdf') {
      try {
        // Buat PDF dengan jsPDF
        const doc = new jsPDF();

        // Tambahkan judul
        const title = `Aktivitas Outlet - ${getPeakPeriodTitle(selectedPeriod)}`;
        doc.setFontSize(16);
        doc.text(title, 14, 20);

        // Tambahkan tanggal export
        doc.setFontSize(10);
        doc.text(`Diekspor pada: ${new Date().toLocaleDateString('id-ID')}`, 14, 28);

        // Tambahkan data outlet
        doc.setFontSize(12);
        doc.text(`Outlet: ${outletName}`, 14, 35);

        // Persiapkan data untuk tabel
        const tableData = chartData.map(point => [
          point.label,
          point.bookings.toString(),
          point.transactions.toString()
        ]);

        // Buat tabel dengan autotable
        autoTable(doc, {
          startY: 40,
          head: [['Label', 'Jumlah Booking', 'Jumlah Transaksi']],
          body: tableData,
          theme: 'grid',
          headStyles: {
            fillColor: [244, 187, 69], // primary color
            textColor: [50, 50, 50]
          },
          styles: {
            lineColor: [200, 200, 200]
          },
          margin: { top: 40 }
        });

        // Simpan PDF
        doc.save(`${fileName}.pdf`);

        toast.success('Data berhasil diunduh dalam format PDF');
      } catch (error) {
        console.error('Error exporting to PDF:', error);
        toast.error('Gagal mengunduh data PDF. Silakan coba lagi.');
      }
    }

    // Tutup dropdown setelah memilih format
    setShowDownloadOptions(false);
  };

  // Fungsi untuk mendapatkan judul periode Peak Hours
  const getPeakPeriodTitle = (period: ChartPeriod): string => {
    switch (period) {
      case 'daily': return 'Hari Ini';
      case 'weekly': return 'Minggu Ini';
      case 'monthly': return 'Bulan Ini';
      case 'quarterly': return '3 Bulan Terakhir';
      case 'yearly': return 'Tahun Ini';
      default: return '';
    }
  };

  if (isLoading && !error) {
    return <div className="flex justify-center items-center h-screen"><span className="loading loading-spinner loading-lg text-primary"></span></div>;
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center h-screen text-center px-4">
        <FiAlertTriangle className="w-16 h-16 text-error mb-4" />
        <h2 className="text-xl font-semibold text-error mb-2">Terjadi Kesalahan</h2>
        <p className="text-gray-600 mb-6">{error}</p>
        <button onClick={() => window.location.reload()} className="btn btn-primary">Muat Ulang Halaman</button>
      </div>
    );
  }

  if (!dashboardData) {
    return <div className="flex justify-center items-center h-screen"><p>Data tidak tersedia.</p></div>; // Atau state kosong yang lebih baik
  }

  // Destructuring tidak perlu karena sudah didefinisikan di atas
  // const { stats, peakHours, topServices, topTherapists } = dashboardData;

  // Pastikan stats.bookingsToday adalah string
  // Jika di production dan tidak ada booking, gunakan hardcoded value sementara
  const isProduction = typeof window !== 'undefined' && window.location.hostname !== 'localhost';
  // bookingsTodayStr sudah didefinisikan di atas, gunakan hardcoded jika production dan 0
  const finalBookingsTodayStr = isProduction && stats.bookingsToday === 0 ? '2' : bookingsTodayStr;

  // Log untuk debugging
  console.log('Dashboard - Final stats:', {
    isProduction,
    originalBookingsToday: stats.bookingsToday,
    displayedBookingsToday: finalBookingsTodayStr
  });

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
      className="space-y-6"
    >
      <motion.h1 variants={fadeInUp} className="text-2xl font-bold text-gray-800">
        Dashboard Outlet: {outletName}
      </motion.h1>

      {/* Grid Statistik Utama */}
      <motion.div variants={fadeInUp} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Card Booking Hari Ini */}
                    <StatCard icon={FiCalendar} title="Booking Hari Ini" value={finalBookingsTodayStr} color="primary" />
        {/* Card Transaksi Hari Ini */}
        <StatCard icon={FiShoppingCart} title="Transaksi Hari Ini" value={stats.transactionsToday.toString()} color="success" />
        {/* Card Pendapatan Hari Ini */}
        <StatCard icon={FiDollarSign} title="Pendapatan Hari Ini" value={formatCurrency(stats.revenueToday)} color="secondary" />
        {/* Card Total Tamu Hari Ini */}
        <StatCard icon={FiUsers} title="Total Tamu Hari Ini" value={totalGuestsTodayStr} color="info" />
      </motion.div>

      {/* Grid Chart & Info Tambahan */}
      <motion.div variants={fadeInUp} className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Chart Booking & Transaksi */}
        <div className="lg:col-span-2 card bg-white shadow-sm border border-gray-200">
          <div className="card-body">
            <div className="flex justify-between items-center mb-4">
              <h2 className="card-title text-lg font-semibold">Grafik Aktivitas Outlet</h2>
              <div className="flex gap-2">
                {/* Dropdown tahun hanya muncul jika periode adalah yearly */}
                {selectedPeriod === 'yearly' && (
                  <select
                    className="select select-bordered select-sm"
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                    disabled={isChartLoading}
                  >
                    {/* Tampilkan 5 tahun terakhir */}
                    {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                )}
                <select
                  className="select select-bordered select-sm"
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value as ChartPeriod)}
                  disabled={isChartLoading} // Disable saat loading
                >
                  {chartPeriods.map(p => (
                    <option key={p.key} value={p.key}>{p.label}</option>
                  ))}
                </select>

                {/* Ganti tombol download dengan dropdown */}
                <div className="relative">
                  <button
                    className="btn btn-sm btn-ghost text-primary tooltip flex items-center"
                    data-tip="Unduh Data"
                    onClick={() => setShowDownloadOptions(!showDownloadOptions)}
                    disabled={isChartLoading || chartData.length === 0}
                  >
                    <FiDownload className="w-4 h-4 mr-1" />
                    <FiChevronDown className="w-3 h-3" />
                  </button>

                  {/* Dropdown menu */}
                  {showDownloadOptions && (
                    <div className="absolute right-0 mt-1 w-36 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                      <ul className="py-1">
                        <li>
                          <button
                            className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left flex items-center"
                            onClick={() => handleDownloadChartData('excel')}
                          >
                            <span className="mr-2">📊</span> Excel
                          </button>
                        </li>
                        <li>
                          <button
                            className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left flex items-center"
                            onClick={() => handleDownloadChartData('pdf')}
                          >
                            <span className="mr-2">📄</span> PDF
                          </button>
                        </li>
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
            {isChartLoading ? (
              <div className="flex justify-center items-center h-60"><span className="loading loading-spinner loading-sm text-primary"></span></div>
            ) : chartData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                  <defs>
                    {/* Gradient untuk Booking */}
                    <linearGradient id="colorBookings" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor={currentThemeColors.primary} stopOpacity={0.8}/>
                      <stop offset="95%" stopColor={currentThemeColors.primary} stopOpacity={0}/>
                    </linearGradient>
                     {/* TAMBAHKAN: Gradient untuk Transaksi */}
                     <linearGradient id="colorTransactions" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor={currentThemeColors.secondary} stopOpacity={0.7}/>
                      <stop offset="95%" stopColor={currentThemeColors.secondary} stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <XAxis dataKey="label" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                  <YAxis stroke="#888888" fontSize={12} tickLine={false} axisLine={false} allowDecimals={false} />
                  <CartesianGrid strokeDasharray="3 3" className="opacity-50" />
                  {/* Perbarui Tooltip jika perlu */}
                  <Tooltip
                    contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', border: '1px solid #ccc', borderRadius: '8px', padding: '8px 12px', fontSize: '12px' }}
                    formatter={(value: number, name: string) => [value, name === 'bookings' ? 'Booking' : 'Transaksi Selesai']}
                  />
                  {/* Area untuk Booking */}
                  <Area type="monotone" dataKey="bookings" stroke={currentThemeColors.primary} fillOpacity={1} fill="url(#colorBookings)" name="bookings" />
                  {/* TAMBAHKAN: Area untuk Transaksi */}
                   <Area type="monotone" dataKey="transactions" stroke={currentThemeColors.secondary} fillOpacity={0.8} fill="url(#colorTransactions)" name="transactions" />
                </AreaChart>
              </ResponsiveContainer>
            ) : (
               <div className="flex justify-center items-center h-60 text-gray-500">Tidak ada data aktivitas untuk periode ini.</div>
            )}
          </div>
      </div>

        {/* Info Tambahan (Peak Hours & Upcoming) */}
        <div className="space-y-4">
          {/* Card Peak Hours */}
          <div className="card bg-white shadow-sm border border-gray-200 h-fit">
            <div className="card-body">
              <h2 className="card-title text-base font-semibold mb-2"><FiZap className="text-warning mr-2"/> Jam Ramai ({getPeakPeriodTitle(selectedPeriod)})</h2>
              {isChartLoading ? (
                <span className="loading loading-dots loading-xs"></span>
              ) : peakHours.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {peakHours.map((hour) => (
                    <span key={hour} className="badge badge-warning">{hour}</span>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">Belum ada data jam ramai.</p>
              )}
            </div>
          </div>

          {/* Card Upcoming Bookings */}
          <div className="card bg-white shadow-sm border border-gray-200">
            <div className="card-body">
              <h2 className="card-title text-base font-semibold mb-3"><FiClock className="text-info mr-2"/> Booking Akan Datang</h2>
              {isLoading ? (
                <span className="loading loading-dots loading-xs"></span>
              ) : upcomingBookings.length > 0 ? (
                <ul className="space-y-3 max-h-[300px] overflow-y-auto">
                  {upcomingBookings.map((booking) => (
                    <li key={booking.id} className="text-sm flex items-center justify-between border-b border-gray-100 pb-2 mb-2 last:border-0 last:mb-0 last:pb-0">
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-700">{booking.time}</span> - {booking.customerName}
                          <span className={`badge badge-xs ${booking.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'}`}>
                            {booking.status}
                          </span>
                        </div>
                        <span className="block text-xs text-gray-500">{booking.serviceName} oleh {booking.therapistName}</span>
                      </div>
                      <div className="flex gap-1">
                        {booking.status === 'PENDING' && (
                          <button
                            className="btn btn-xs btn-ghost text-blue-500 tooltip"
                            data-tip="Konfirmasi"
                            onClick={() => handleUpdateBookingStatus(booking.id, 'CONFIRMED')}
                          >
                            <FiCheckCircle />
                          </button>
                        )}
                        {(booking.status === 'PENDING' || booking.status === 'CONFIRMED') && (
                          <button
                            className="btn btn-xs btn-ghost text-orange-500 tooltip"
                            data-tip="Batalkan"
                            onClick={() => handleShowCancelModal(booking.id)}
                          >
                            <FiXCircle />
                          </button>
                        )}
                        <Link href="/dashboard/booking" className="btn btn-xs btn-ghost text-primary tooltip" data-tip="Lihat Detail">
                          <FiArrowRight/>
                        </Link>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-500">Tidak ada booking yang akan datang.</p>
              )}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Payment Method Totals */}
      <motion.div variants={fadeInUp} className="card bg-white shadow-sm border border-gray-200">
        <div className="card-body">
          <h2 className="card-title text-lg font-semibold mb-3">
            <FiDollarSign className="text-success mr-2"/>
            Total Per Jenis Pembayaran ({getPeakPeriodTitle(selectedPeriod)})
          </h2>
          <div className="overflow-x-auto">
            {isChartLoading ? (
              <div className="flex justify-center items-center py-4">
                <span className="loading loading-spinner loading-sm text-primary"></span>
              </div>
            ) : Object.keys(selectedPeriod === 'daily' ? paymentMethodTotals : periodPaymentMethodTotals).length > 0 ? (
              <table className="table table-compact w-full">
                <thead>
                  <tr>
                    <th>Jenis Pembayaran</th>
                    <th className="text-right">Total</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(selectedPeriod === 'daily' ? paymentMethodTotals : periodPaymentMethodTotals).map(([method, total]) => (
                    <tr key={method}>
                      <td>
                        {method === 'CASH' && 'Tunai'}
                        {method === 'TRANSFER' && 'Transfer Bank'}
                        {method === 'CREDIT_CARD' && 'Kartu Kredit'}
                        {method === 'DEBIT_CARD' && 'Kartu Debit'}
                        {method === 'QRIS' && 'QRIS'}
                        {method === 'DIGITAL_WALLET' && 'E-Wallet'}
                        {method === 'SPLIT' && 'Split Bill'}
                        {method === 'UNKNOWN' && 'Tidak Diketahui'}
                        {!['CASH', 'TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'QRIS', 'DIGITAL_WALLET', 'SPLIT', 'UNKNOWN'].includes(method) && method}
                      </td>
                      <td className="text-right">{formatCurrency(total)}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr className="bg-primary/10 font-bold border-t-2 border-primary">
                    <th className="text-primary">Total</th>
                    <th className="text-right text-primary">
                      {formatCurrency(
                        Object.values(selectedPeriod === 'daily' ? paymentMethodTotals : periodPaymentMethodTotals).reduce(
                          (sum, total) => sum + total, 0
                        )
                      )}
                    </th>
                  </tr>
                </tfoot>
              </table>
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">Belum ada data transaksi untuk periode ini.</p>
            )}
          </div>
        </div>
      </motion.div>

      {/* Grid Ranking Layanan & Terapis */}
      <motion.div variants={fadeInUp} className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Top Services */}
        <div className="card bg-white shadow-sm border border-gray-200">
          <div className="card-body">
            <h2 className="card-title text-lg font-semibold mb-3"><FiActivity className="text-secondary mr-2"/> Layanan Terpopuler ({getPeakPeriodTitle(selectedPeriod)})</h2>
            <p className="text-xs text-gray-500 -mt-2 mb-2">Berdasarkan booking dan transaksi</p>
            {isChartLoading ? (
              <span className="loading loading-dots loading-xs"></span>
            ) : topServices.length > 0 ? (
              <ul className="space-y-2 max-h-[200px] overflow-y-auto">
                {topServices.map((service, index) => (
                  <li key={index} className="flex justify-between items-center text-sm">
                    <span className="text-gray-700">{index + 1}. {service.name}</span>
                    <span className="font-medium text-gray-600">{service.count}x</span>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500">Belum ada data layanan populer.</p>
            )}
          </div>
        </div>

        {/* Top Therapists */}
        <div className="card bg-white shadow-sm border border-gray-200">
          <div className="card-body">
            <h2 className="card-title text-lg font-semibold mb-3"><FiBriefcase className="text-accent mr-2"/> Terapis Terlaris ({getPeakPeriodTitle(selectedPeriod)})</h2>
            <p className="text-xs text-gray-500 -mt-2 mb-2">Berdasarkan booking dan transaksi</p>
             {isChartLoading ? (
              <span className="loading loading-dots loading-xs"></span>
            ) : topTherapists.length > 0 ? (
              <ul className="space-y-2 max-h-[200px] overflow-y-auto">
                {topTherapists.map((therapist, index) => (
                  <li key={index} className="flex flex-col gap-1 py-1 border-b border-gray-100 last:border-0">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-700 font-medium">{index + 1}. {therapist.name}</span>
                      <span className="text-xs text-gray-500">{therapist.bookingCount + therapist.transactionCount} total</span>
                    </div>
                    <div className="flex justify-between items-center text-xs text-gray-600 pl-4">
                      <div className="flex items-center gap-1">
                        <FiCalendar className="w-3 h-3 text-primary" />
                        <span>{therapist.bookingCount} booking</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <FiDollarSign className="w-3 h-3 text-secondary" />
                        <span>{therapist.transactionCount} transaksi</span>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
               <p className="text-sm text-gray-500">Belum ada data terapis terlaris.</p>
            )}
          </div>
        </div>
      </motion.div>

      {/* Modal Konfirmasi Pembatalan */}
      {showCancelModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full">
            <h3 className="text-lg font-semibold mb-4">Konfirmasi Pembatalan</h3>
            <p className="mb-6">Apakah Anda yakin ingin membatalkan booking <span className="font-semibold">#{cancelBookingNumber}</span>? Status tidak bisa dikembalikan.</p>
            <div className="flex justify-end gap-2">
              <button
                className="btn btn-sm btn-ghost"
                onClick={() => {
                  setShowCancelModal(false);
                  setCancelBookingId('');
                  setCancelBookingNumber('');
                }}
              >
                Batal
              </button>
              <button
                className="btn btn-sm btn-error text-white"
                onClick={handleConfirmCancel}
              >
                Ya, Batalkan
              </button>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
}

// Komponen Card Statistik Helper
interface StatCardProps {
  icon: React.ElementType;
  title: string;
  value: string;
  color: 'primary' | 'secondary' | 'accent' | 'info' | 'success';
}

function StatCard({ icon: Icon, title, value, color }: StatCardProps): React.ReactElement {
  const colorClasses: Record<string, string> = {
    primary: 'border-primary text-primary',
    secondary: 'border-secondary text-secondary',
    accent: 'border-accent text-accent',
    info: 'border-info text-info',
    success: 'border-success text-success',
  };

  // Cek apakah nilai adalah mata uang (dimulai dengan 'Rp')
  const isCurrency = value.startsWith('Rp');

  return (
    <div className={`card bg-white shadow-sm border border-gray-200 h-full`}>
      <div className="card-body p-4 h-full">
        <div className="flex items-center justify-between">
          <div className={`p-2 rounded-full bg-${color}/10 ${colorClasses[color]}`}>
            <Icon className="w-5 h-5"/>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">{title}</div>
            {isCurrency ? (
              <div className="flex flex-col items-end">
                <div className="text-lg sm:text-xl font-semibold text-gray-800 truncate max-w-[120px] sm:max-w-[150px] md:max-w-full">
                  {value}
                </div>
              </div>
            ) : (
              <div className="text-xl font-semibold text-gray-800">{value}</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}