'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function MigrateServicesPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const runMigration = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/migrate-services');
      const data = await response.json();

      setResult(data);

      if (!data.success) {
        setError(data.error || 'Terjadi kesalahan saat migrasi');
      }
    } catch (err) {
      console.error('Error running migration:', err);
      setError('Terjadi kesalahan saat menjalankan migrasi');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <h1 className="text-2xl font-bold mb-4"><PERSON><PERSON><PERSON> Layanan</h1>
      <p className="mb-4">
        <PERSON>aman ini akan memigrasikan layanan dari struktur lama ke struktur baru.
        Gunakan halaman ini jika layanan lama tidak muncul setelah migrasi database.
      </p>

      <div className="card bg-base-100 shadow-xl mb-4">
        <div className="card-body">
          <h2 className="card-title">Jalankan Migrasi</h2>
          <p>Klik tombol di bawah untuk memulai migrasi layanan.</p>
          <div className="card-actions justify-end mt-4">
            <button
              className="btn btn-primary"
              onClick={runMigration}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  Memproses...
                </>
              ) : 'Jalankan Migrasi'}
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="alert alert-error mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
          <span>{error}</span>
        </div>
      )}

      {result && (
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Hasil Migrasi</h2>
            <div className="overflow-x-auto">
              <table className="table w-full">
                <tbody>
                  <tr>
                    <td className="font-semibold">Status</td>
                    <td>
                      <span className={`badge ${result.success ? 'badge-success' : 'badge-error'}`}>
                        {result.success ? 'Berhasil' : 'Gagal'}
                      </span>
                    </td>
                  </tr>
                  {result.totalServices !== undefined && (
                    <tr>
                      <td className="font-semibold">Total Layanan</td>
                      <td>{result.totalServices}</td>
                    </tr>
                  )}
                  {result.migratedCount !== undefined && (
                    <tr>
                      <td className="font-semibold">Layanan Dimigrasikan</td>
                      <td>{result.migratedCount}</td>
                    </tr>
                  )}
                  {result.message && (
                    <tr>
                      <td className="font-semibold">Pesan</td>
                      <td>{result.message}</td>
                    </tr>
                  )}
                  {result.details && (
                    <tr>
                      <td className="font-semibold">Detail</td>
                      <td className="text-error">{result.details}</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {result.migratedServices && result.migratedServices.length > 0 && (
              <div className="mt-4">
                <h3 className="font-semibold mb-2">Layanan yang Dimigrasikan:</h3>
                <div className="overflow-x-auto">
                  <table className="table w-full">
                    <thead>
                      <tr>
                        <th>Nama Layanan</th>
                        <th>Outlet</th>
                      </tr>
                    </thead>
                    <tbody>
                      {result.migratedServices.map((service, index) => (
                        <tr key={index}>
                          <td>{service.name}</td>
                          <td>{service.outletName}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            <div className="card-actions justify-end mt-4">
              <button
                className="btn btn-primary"
                onClick={() => router.push('/dashboard/layanan')}
              >
                Kembali ke Halaman Layanan
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
