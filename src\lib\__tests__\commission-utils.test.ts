import {
  calculateTherapistCommission,
  ServiceCommissionData,
  TherapistSpecialCommission,
  validateCommissionConsistency,
  validateCommissionConsistencyLegacy,
  calculateTherapistSalesTotal,
  getCommissionPerItem,
  formatCommissionLog
} from '../commission-utils';

describe('Commission Utils', () => {
  describe('calculateTherapistCommission', () => {
    it('should calculate basic commission correctly', () => {
      const serviceData: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 1,
          defaultCommission: 50000
        }
      ];

      const result = calculateTherapistCommission(serviceData, []);
      expect(result).toBe(50000);
    });

    it('should calculate commission with multiple quantity', () => {
      const serviceData: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 3,
          defaultCommission: 50000
        }
      ];

      const result = calculateTherapistCommission(serviceData, []);
      expect(result).toBe(150000); // 50000 * 3
    });

    it('should calculate commission for multiple services', () => {
      const serviceData: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 1,
          defaultCommission: 50000
        },
        {
          serviceId: 'service-2',
          quantity: 2,
          defaultCommission: 30000
        }
      ];

      const result = calculateTherapistCommission(serviceData, []);
      expect(result).toBe(110000); // 50000 + (30000 * 2)
    });

    it('should use special commission when available', () => {
      const serviceData: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 1,
          defaultCommission: 50000
        }
      ];

      const specialCommissions: TherapistSpecialCommission[] = [
        {
          serviceId: 'service-1',
          commission: 75000 // Special commission higher than default
        }
      ];

      const result = calculateTherapistCommission(serviceData, specialCommissions);
      expect(result).toBe(75000);
    });

    it('should handle mixed special and default commissions', () => {
      const serviceData: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 1,
          defaultCommission: 50000
        },
        {
          serviceId: 'service-2',
          quantity: 1,
          defaultCommission: 30000
        }
      ];

      const specialCommissions: TherapistSpecialCommission[] = [
        {
          serviceId: 'service-1',
          commission: 75000 // Only service-1 has special commission
        }
      ];

      const result = calculateTherapistCommission(serviceData, specialCommissions);
      expect(result).toBe(105000); // 75000 + 30000
    });

    it('should handle zero commission', () => {
      const serviceData: ServiceCommissionData[] = [
        {
          serviceId: 'free-service',
          quantity: 1,
          defaultCommission: 0
        }
      ];

      const result = calculateTherapistCommission(serviceData, []);
      expect(result).toBe(0);
    });

    it('should handle empty service data', () => {
      const result = calculateTherapistCommission([], []);
      expect(result).toBe(0);
    });
  });

  describe('validateCommissionConsistency', () => {
    it('should validate commission calculation consistency with service data', () => {
      const serviceData: ServiceCommissionData[] = [
        { serviceId: 'service-1', quantity: 2, defaultCommission: 50000 }
      ];
      const expectedCommission = 100000; // 50000 * 2
      
      const result = validateCommissionConsistency(serviceData, [], expectedCommission);
      expect(result).toBe(true);
    });

    it('should detect inconsistency in commission calculation', () => {
      const serviceData: ServiceCommissionData[] = [
        { serviceId: 'service-1', quantity: 2, defaultCommission: 50000 }
      ];
      const wrongCommission = 150000; // Incorrect amount
      
      const result = validateCommissionConsistency(serviceData, [], wrongCommission);
      expect(result).toBe(false);
    });

    it('should handle special commissions in validation', () => {
      const serviceData: ServiceCommissionData[] = [
        { serviceId: 'service-1', quantity: 2, defaultCommission: 50000 }
      ];
      const specialCommissions: TherapistSpecialCommission[] = [
        { serviceId: 'service-1', commission: 60000 }
      ];
      const expectedCommission = 120000; // 60000 * 2
      
      const result = validateCommissionConsistency(serviceData, specialCommissions, expectedCommission);
      expect(result).toBe(true);
    });
  });

  describe('validateCommissionConsistencyLegacy', () => {
    it('should return true for identical values', () => {
      const result = validateCommissionConsistencyLegacy(100000, 100000);
      expect(result).toBe(true);
    });

    it('should return true for values within tolerance', () => {
      const result = validateCommissionConsistencyLegacy(100000.50, 100000.00, 1.00);
      expect(result).toBe(true);
    });

    it('should return false for values outside tolerance', () => {
      const result = validateCommissionConsistencyLegacy(100000.50, 100000.00, 0.01);
      expect(result).toBe(false);
    });

    it('should use default tolerance of 0.01', () => {
      const result1 = validateCommissionConsistencyLegacy(100000.005, 100000.000);
      const result2 = validateCommissionConsistencyLegacy(100000.02, 100000.000);
      
      expect(result1).toBe(true);  // Within 0.01
      expect(result2).toBe(false); // Outside 0.01
    });
  });

  describe('calculateTherapistSalesTotal', () => {
    it('should calculate sales total correctly', () => {
      const result = calculateTherapistSalesTotal(100000, 5000, 10000);
      expect(result).toBe(105000); // subtotal + additionalCharge (discount doesn't affect therapist income)
    });

    it('should handle zero additional charge', () => {
      const result = calculateTherapistSalesTotal(100000, 0, 10000);
      expect(result).toBe(100000);
    });

    it('should handle zero discount', () => {
      const result = calculateTherapistSalesTotal(100000, 5000, 0);
      expect(result).toBe(105000);
    });
  });

  describe('getCommissionPerItem', () => {
    it('should return default commission when no special commission', () => {
      const result = getCommissionPerItem('service-1', 50000, []);
      expect(result).toBe(50000);
    });

    it('should return special commission when available', () => {
      const specialCommissions: TherapistSpecialCommission[] = [
        {
          serviceId: 'service-1',
          commission: 75000
        }
      ];

      const result = getCommissionPerItem('service-1', 50000, specialCommissions);
      expect(result).toBe(75000);
    });

    it('should return default commission for non-matching service', () => {
      const specialCommissions: TherapistSpecialCommission[] = [
        {
          serviceId: 'service-2',
          commission: 75000
        }
      ];

      const result = getCommissionPerItem('service-1', 50000, specialCommissions);
      expect(result).toBe(50000);
    });
  });

  describe('formatCommissionLog', () => {
    it('should format commission log correctly', () => {
      const result = formatCommissionLog('service-1', 50000, 2, false);
      expect(result).toBe('Service service-1: 50000 (default) x 2 = 100000');
    });

    it('should format special commission log correctly', () => {
      const result = formatCommissionLog('service-1', 75000, 1, true);
      expect(result).toBe('Service service-1: 75000 (special) x 1 = 75000');
    });
  });

  describe('Performance Tests', () => {
    it('should handle large dataset efficiently', () => {
      const largeServiceData: ServiceCommissionData[] = [];
      for (let i = 0; i < 1000; i++) {
        largeServiceData.push({
          serviceId: `service-${i}`,
          quantity: Math.floor(Math.random() * 5) + 1,
          defaultCommission: Math.floor(Math.random() * 100000) + 10000
        });
      }

      const startTime = performance.now();
      const commission = calculateTherapistCommission(largeServiceData, []);
      const endTime = performance.now();

      expect(commission).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(100); // Should complete in less than 100ms
    });
  });

  describe('Edge Cases', () => {
    it('should handle negative quantity gracefully', () => {
      const serviceData: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: -1,
          defaultCommission: 50000
        }
      ];

      const result = calculateTherapistCommission(serviceData, []);
      expect(result).toBe(0); // Negative quantities should be ignored
    });

    it('should handle very large numbers', () => {
      const serviceData: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 1,
          defaultCommission: Number.MAX_SAFE_INTEGER
        }
      ];

      const result = calculateTherapistCommission(serviceData, []);
      expect(result).toBe(Number.MAX_SAFE_INTEGER);
    });

    it('should handle zero quantity gracefully', () => {
      const serviceData: ServiceCommissionData[] = [
        {
          serviceId: 'service-1',
          quantity: 0,
          defaultCommission: 50000
        }
      ];

      const result = calculateTherapistCommission(serviceData, []);
      expect(result).toBe(0); // Zero quantities should be ignored
    });
  });
});
