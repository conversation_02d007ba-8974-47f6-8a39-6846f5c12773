'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { FiSettings, FiSave, FiGlobe, FiPhone, FiCreditCard, FiPlus, FiInfo } from 'react-icons/fi';
import LoadingSpinner from '@/components/dashboard/LoadingSpinner';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import SystemLogs from '@/components/dashboard/SystemLogs';

// Tipe untuk opsi pengaturan
interface SettingOption {
  label: string;
  value: string;
}

// Tipe untuk data pengaturan
interface Setting {
  id: string;
  key: string;
  value: string;
  category: string;
  label: string;
  type: string;
  options: SettingOption[] | null;
  isSystem: boolean;
  createdAt: string;
  updatedAt: string;
}

// Group pengaturan berdasarkan kategori
interface GroupedSettings {
  [key: string]: Setting[];
}

export default function SettingsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [settings, setSettings] = useState<GroupedSettings>({});
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'logs', atau nama kategori
  const [isInitializing, setIsInitializing] = useState(false);

  // Fungsi untuk memuat data pengaturan
  const fetchSettings = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/settings');

      if (!response.ok) {
        throw new Error('Gagal memuat data pengaturan');
      }

      const data = await response.json();
      setSettings(data.settings || {});
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError('Terjadi kesalahan saat memuat data pengaturan');
      toast.error('Gagal memuat data pengaturan');
    } finally {
      setIsLoading(false);
    }
  };

  // Fungsi untuk inisialisasi pengaturan awal
  const initializeSettings = async () => {
    setIsInitializing(true);

    try {
      const response = await fetch('/api/settings/init');

      if (!response.ok) {
        throw new Error('Gagal menginisialisasi pengaturan');
      }

      const data = await response.json();
      toast.success(`${data.count} pengaturan berhasil diinisialisasi`);
      // Segarkan data
      await fetchSettings();
    } catch (err) {
      console.error('Error initializing settings:', err);
      toast.error('Gagal menginisialisasi pengaturan');
    } finally {
      setIsInitializing(false);
    }
  };

  // Muat data pengaturan saat mount
  useEffect(() => {
    fetchSettings();
  }, []);

  // Fungsi untuk menyimpan perubahan pengaturan
  const saveSetting = async (setting: Setting) => {
    try {
      const response = await fetch(`/api/settings/${setting.key}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          value: setting.value,
          category: setting.category,
          label: setting.label,
          type: setting.type,
          options: setting.options,
        }),
      });

      if (!response.ok) {
        throw new Error('Gagal menyimpan pengaturan');
      }

      toast.success('Pengaturan berhasil disimpan');
      // Segarkan data
      fetchSettings();
    } catch (err) {
      console.error('Error saving setting:', err);
      toast.error('Gagal menyimpan pengaturan');
    }
  };

  // Handle perubahan nilai pengaturan
  const handleSettingChange = (key: string, category: string, value: string) => {
    // Clone settings untuk mencegah mutasi langsung
    const newSettings = { ...settings };

    // Cari pengaturan dan update nilainya
    if (newSettings[category]) {
      const settingIndex = newSettings[category].findIndex(s => s.key === key);
      if (settingIndex !== -1) {
        newSettings[category][settingIndex] = {
          ...newSettings[category][settingIndex],
          value
        };
        setSettings(newSettings);
      }
    }
  };

  // Render komponen input berdasarkan tipe pengaturan
  const renderSettingInput = (setting: Setting) => {
    switch (setting.type) {
      case 'BOOLEAN':
        return (
          <div className="form-control">
            <label className="cursor-pointer label justify-start gap-2">
              <input
                type="checkbox"
                className="checkbox checkbox-primary"
                checked={setting.value === 'true'}
                onChange={(e) => handleSettingChange(setting.key, setting.category, e.target.checked ? 'true' : 'false')}
                disabled={setting.isSystem}
              />
              <span className="label-text">{setting.label}</span>
            </label>
          </div>
        );

      case 'SELECT':
        return (
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">{setting.label}</span>
            </label>
            <select
              className="select select-bordered w-full"
              value={setting.value}
              onChange={(e) => handleSettingChange(setting.key, setting.category, e.target.value)}
              disabled={setting.isSystem}
            >
              {setting.options && Array.isArray(setting.options) && setting.options.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        );

      case 'COLOR':
        return (
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">{setting.label}</span>
            </label>
            <div className="flex flex-col sm:flex-row gap-2">
              <input
                type="color"
                className="w-full sm:w-12 h-12 rounded cursor-pointer mb-2 sm:mb-0"
                value={setting.value}
                onChange={(e) => handleSettingChange(setting.key, setting.category, e.target.value)}
                disabled={setting.isSystem}
              />
              <input
                type="text"
                className="input input-bordered w-full flex-1"
                value={setting.value}
                onChange={(e) => handleSettingChange(setting.key, setting.category, e.target.value)}
                disabled={setting.isSystem}
              />
            </div>
          </div>
        );

      case 'RICHTEXT':
        return (
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">{setting.label}</span>
            </label>
            <textarea
              className="textarea textarea-bordered h-24"
              value={setting.value}
              onChange={(e) => handleSettingChange(setting.key, setting.category, e.target.value)}
              disabled={setting.isSystem}
            />
          </div>
        );

      case 'NUMBER':
        return (
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">{setting.label}</span>
            </label>
            <input
              type="number"
              className="input input-bordered w-full"
              value={setting.value}
              onChange={(e) => handleSettingChange(setting.key, setting.category, e.target.value)}
              disabled={setting.isSystem}
            />
          </div>
        );

      // Default untuk tipe TEXT dan lainnya
      default:
        return (
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">{setting.label}</span>
            </label>
            <input
              type="text"
              className="input input-bordered w-full"
              value={setting.value}
              onChange={(e) => handleSettingChange(setting.key, setting.category, e.target.value)}
              disabled={setting.isSystem}
            />
          </div>
        );
    }
  };

  // Fungsi untuk render kategori pengaturan
  const renderSettingCategory = (category: string, settings: Setting[]) => {
    // Abaikan kategori yang tidak memiliki pengaturan
    if (!settings || settings.length === 0) {
      return null;
    }

    // Tampilkan label kategori
    const getCategoryLabel = (category: string) => {
      switch (category) {
        case 'landing-page':
          return 'Landing Page';
        case 'cta':
          return 'Call to Action';
        case 'payment-types':
          return 'Jenis Pembayaran';
        case 'system':
          return 'Sistem';
        default:
          return category.charAt(0).toUpperCase() + category.slice(1);
      }
    };

    // Tampilkan ikon kategori
    const getCategoryIcon = (category: string) => {
      switch (category) {
        case 'landing-page':
          return <FiGlobe className="w-5 h-5 mr-2" />;
        case 'cta':
          return <FiPhone className="w-5 h-5 mr-2" />;
        case 'payment-types':
          return <FiCreditCard className="w-5 h-5 mr-2" />;
        case 'system':
          return <FiSettings className="w-5 h-5 mr-2" />;
        default:
          return <FiSettings className="w-5 h-5 mr-2" />;
      }
    };

    return (
      <div className="bg-white rounded-lg shadow-sm mb-8">
        <div className="p-4 sm:p-6 border-b">
          <h3 className="text-lg font-medium flex items-center flex-wrap">
            <span className="flex items-center mr-2">
              {getCategoryIcon(category)}
            </span>
            <span>{getCategoryLabel(category)}</span>
          </h3>
          <p className="text-sm text-gray-500 mt-1">
            Kelola pengaturan untuk {getCategoryLabel(category).toLowerCase()}
          </p>
        </div>
        <div className="p-4 sm:p-6">
          <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {settings.map((setting) => (
              <div key={setting.key} className="col-span-1 p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                {renderSettingInput(setting)}
                <div className="mt-2 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                  <div className="text-xs text-gray-400 break-all">{setting.key}</div>
                  <button
                    className="btn btn-sm btn-primary w-full sm:w-auto"
                    onClick={() => saveSetting(setting)}
                    disabled={setting.isSystem}
                  >
                    <FiSave className="w-4 h-4 mr-1" />
                    Simpan
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Tampilkan loading spinner jika masih memuat
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[500px]">
        <LoadingSpinner />
        <p className="mt-4 text-gray-500">Memuat pengaturan...</p>
      </div>
    );
  }

  // Tampilkan pesan error jika ada
  if (error) {
    return (
      <div className="p-8 text-center">
        <div className="text-red-500 mb-4">{error}</div>
        <button
          className="btn btn-primary"
          onClick={() => fetchSettings()}
        >
          Coba Lagi
        </button>
      </div>
    );
  }

  // Daftar kategori yang tersedia
  const categories = Object.keys(settings);
  const hasNoSettings = categories.length === 0;

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-6">
      <DashboardHeader
        title="Pengaturan"
        description="Kelola pengaturan sistem, tampilan, dan fitur aplikasi"
        icon={<FiSettings className="w-6 h-6" />}
        actions={
          <button
            className="btn btn-primary btn-sm md:btn-md"
            onClick={initializeSettings}
            disabled={isInitializing}
          >
            {isInitializing ? (
              <>
                <span className="loading loading-spinner loading-xs"></span>
                <span className="hidden sm:inline">Menginisialisasi...</span>
                <span className="sm:hidden">Loading...</span>
              </>
            ) : (
              <>
                <FiPlus className="w-4 h-4 mr-1" />
                <span className="hidden sm:inline">Inisialisasi Pengaturan</span>
                <span className="sm:hidden">Inisialisasi</span>
              </>
            )}
          </button>
        }
      />

      {hasNoSettings ? (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center mt-6">
          <FiSettings className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium mb-2">Belum Ada Pengaturan</h3>
          <p className="text-gray-500 mb-6">
            Anda belum memiliki pengaturan. Klik tombol di bawah untuk menginisialisasi pengaturan awal untuk landing page, CTA, dan jenis pembayaran.
          </p>
          <button
            className="btn btn-primary w-full sm:w-auto"
            onClick={initializeSettings}
            disabled={isInitializing}
          >
            {isInitializing ? (
              <>
                <span className="loading loading-spinner loading-xs"></span>
                <span className="hidden xs:inline">Menginisialisasi...</span>
                <span className="xs:hidden">Loading...</span>
              </>
            ) : (
              <>
                <FiPlus className="w-4 h-4 mr-1" />
                <span className="hidden xs:inline">Inisialisasi Pengaturan</span>
                <span className="xs:hidden">Inisialisasi</span>
              </>
            )}
          </button>
        </div>
      ) : (
        <div className="mt-6">
          <div className="overflow-x-auto pb-2">
            <div className="tabs tabs-boxed mb-8 flex flex-nowrap min-w-max md:flex-wrap">
              <a
                className={`tab ${activeTab === 'all' ? 'tab-active' : ''}`}
                onClick={() => setActiveTab('all')}
              >
                Semua
              </a>
              <a
                className={`tab ${activeTab === 'logs' ? 'tab-active' : ''}`}
                onClick={() => setActiveTab('logs')}
              >
                <FiInfo className="mr-1" /> Log Sistem
              </a>
              {categories.map((category) => (
                <a
                  key={category}
                  className={`tab ${activeTab === category ? 'tab-active' : ''}`}
                  onClick={() => setActiveTab(category)}
                >
                  {(() => {
                    switch (category) {
                      case 'landing-page':
                        return 'Landing Page';
                      case 'cta':
                        return 'Call to Action';
                      case 'payment-types':
                        return 'Jenis Pembayaran';
                      case 'system':
                        return 'Sistem';
                      default:
                        return category.charAt(0).toUpperCase() + category.slice(1);
                    }
                  })()}
                </a>
              ))}
            </div>
          </div>

          {activeTab === 'logs' ? (
            <SystemLogs />
          ) : activeTab === 'all' ? (
            categories.map((category) => (
              <div key={`category-${category}`}>
                {renderSettingCategory(category, settings[category])}
              </div>
            ))
          ) : (
            renderSettingCategory(activeTab, settings[activeTab])
          )}
        </div>
      )}
    </div>
  );
}