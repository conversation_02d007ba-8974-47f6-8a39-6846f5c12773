'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  FiCalendar, <PERSON>Filter, FiBarChart2, FiAlertTriangle,
  FiRefreshCw, FiChevronLeft, FiClock, FiSun, FiUsers
} from 'react-icons/fi';
import { format } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { useOutletContext } from '@/contexts/OutletContext';
import Link from 'next/link';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';
const DEFAULT_CHART_COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ef4444', '#6b7280'];

// Interfaces for API data structure (adjust based on actual API response)
interface HourlyVisitData {
  hour: number; // 0-23
  visitCount: number;
}

interface DailyVisitData {
  dayOfWeek: number; // 0 (Sunday) - 6 (Saturday)
  visitCount: number;
}

interface VisitPatternData {
  peakHours: HourlyVisitData[];
  peakDays: DailyVisitData[];
  avgVisitsPerDay: number;
  avgVisitsPerHour: number;
  totalVisits: number;
}

interface Outlet {
  id: string;
  name: string;
}

// Fungsi format tanggal untuk tombol dropdown
const formatDateDisplay = (date: Date | null): string => {
  if (!date) return '';
  return format(date, 'dd MMMM yyyy', { locale: idLocale });
};

const formatDayOfWeek = (day: number): string => {
  const days = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
  return days[day] || 'Tidak Diketahui';
};

// Varian Animasi
const fadeInUp = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.5 } } };
const staggerContainer = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1 } } };

export default function VisitPatternsPage() {
  const { selectedOutletId: contextOutletId } = useOutletContext() ?? { selectedOutletId: null };
  const [visitPatternsData, setVisitPatternsData] = useState<VisitPatternData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [outlets, setOutlets] = useState<Outlet[]>([]);

  // Filters State
  const [startDate, setStartDate] = useState<Date | null>(() => {
    const today = new Date();
    return new Date(today.getFullYear(), today.getMonth(), 1); // Default: Start of current month
  });
  const [endDate, setEndDate] = useState<Date | null>(new Date()); // Default: Today
  const [selectedOutletId, setSelectedOutletId] = useState<string>(contextOutletId || '');

  // Load outlets for filter dropdown
  useEffect(() => {
    const fetchOutlets = async () => {
      try {
        const response = await fetch('/api/outlets?simple=true');
        if (!response.ok) throw new Error('Gagal memuat outlet');
        const data = await response.json();
        const fetchedOutlets = data.outlets || [];
        setOutlets(fetchedOutlets);

        // Set local state from context only if local state is empty and context ID is valid
        if (selectedOutletId === '' && contextOutletId && fetchedOutlets.some((o: Outlet) => o.id === contextOutletId)) {
          setSelectedOutletId(contextOutletId);
        } else if (selectedOutletId === '' && fetchedOutlets.length > 0) {
           // Optionally default to the first outlet if no context is set
           // setSelectedOutletId(fetchedOutlets[0].id);
        }
      } catch (err) {
        console.error("Failed to fetch outlets:", err);
        // Handle error appropriately, maybe set an error state
      }
    };
    fetchOutlets();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contextOutletId]); // Dependency on contextOutletId

  // Fetch visit patterns data
  const fetchVisitPatternsData = async () => {
    if (!startDate || !endDate) {
      setError("Tanggal mulai dan tanggal selesai harus dipilih.");
      setVisitPatternsData(null);
      return;
    }

    setIsLoading(true);
    setError(null);
    setVisitPatternsData(null);

    const formatLocalDate = (date: Date) => date.toISOString().split('T')[0]; // YYYY-MM-DD

    const params = new URLSearchParams({
      startDate: formatLocalDate(startDate),
      endDate: formatLocalDate(endDate),
    });

    if (selectedOutletId) {
      params.set('outletId', selectedOutletId);
    }

    const apiUrl = `/api/reports/visit-patterns?${params.toString()}`;

    try {
      const response = await fetch(apiUrl);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Gagal memuat data pola kunjungan: ${response.statusText}`);
      }
      const data = await response.json();
      setVisitPatternsData(data.visitPatterns); // Adjust based on actual API response key
    } catch (err) {
      console.error('Error fetching visit patterns data:', err);
      setError(err instanceof Error ? err.message : "Terjadi kesalahan saat memuat data pola kunjungan.");
    } finally {
      setIsLoading(false);
    }
  };

  // Automatically fetch data when filters change
  useEffect(() => {
    fetchVisitPatternsData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startDate, endDate, selectedOutletId]);

  // Memoized chart data preparation
  const peakHoursChartData = useMemo(() => {
    return visitPatternsData?.peakHours
      ?.map(item => ({ ...item, hourLabel: `${String(item.hour).padStart(2, '0')}:00` }))
      .sort((a, b) => a.hour - b.hour) || [];
  }, [visitPatternsData]);

  const peakDaysChartData = useMemo(() => {
    return visitPatternsData?.peakDays
      ?.map(item => ({ ...item, dayName: formatDayOfWeek(item.dayOfWeek) }))
      .sort((a, b) => a.dayOfWeek - b.dayOfWeek) || [];
  }, [visitPatternsData]);

  return (
    <motion.div
      className="container mx-auto p-4 md:p-6 space-y-6"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {/* Header */}
      <motion.div variants={fadeInUp} className="flex items-center gap-2">
        <Link href="/dashboard/reports" className="btn btn-sm btn-ghost">
          <FiChevronLeft /> Kembali
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Laporan Pola Kunjungan</h1>
          <p className="text-gray-600">Analisis waktu ramai dan hari sibuk pelanggan.</p>
        </div>
      </motion.div>

      {/* Filters Section */}
      <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
          {/* Tanggal Mulai Dropdown Calendar */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiCalendar className="text-gray-600" /> Tanggal Mulai
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline w-full justify-between overflow-hidden">
                <span className="truncate">{startDate ? formatDateDisplay(startDate) : 'Pilih tanggal'}</span>
                <span className="text-xs">▼</span>
              </div>
              <div tabIndex={0} className="dropdown-content z-[10] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button className="btn btn-sm btn-ghost" onClick={() => setStartDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() - 1, 1) : null)} aria-label="Bulan sebelumnya">«</button>
                      <div className="text-sm font-medium">
                        {startDate ? format(startDate, 'MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                      </div>
                      <button className="btn btn-sm btn-ghost" onClick={() => setStartDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() + 1, 1) : null)} aria-label="Bulan berikutnya">»</button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}
                      {startDate && Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(startDate.getFullYear(), startDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === startDate.getMonth();
                        const isSelected = startDate && date.getDate() === startDate.getDate() && date.getMonth() === startDate.getMonth() && date.getFullYear() === startDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();
                        const isDisabled = !isCurrentMonth || !!(endDate && date > endDate);

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-base-200' : ''}
                                      ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={() => !isDisabled && setStartDate(new Date(date))}
                            disabled={isDisabled}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tanggal Selesai Dropdown Calendar */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiCalendar className="text-gray-600" /> Tanggal Selesai
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline w-full justify-between overflow-hidden">
                <span className="truncate">{endDate ? formatDateDisplay(endDate) : 'Pilih tanggal'}</span>
                <span className="text-xs">▼</span>
              </div>
              <div tabIndex={0} className="dropdown-content z-[10] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button className="btn btn-sm btn-ghost" onClick={() => setEndDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() - 1, 1) : null)} aria-label="Bulan sebelumnya">«</button>
                      <div className="text-sm font-medium">
                        {endDate ? format(endDate, 'MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                      </div>
                      <button className="btn btn-sm btn-ghost" onClick={() => setEndDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() + 1, 1) : null)} aria-label="Bulan berikutnya">»</button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}
                      {endDate && Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(endDate.getFullYear(), endDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === endDate.getMonth();
                        const isSelected = endDate && date.getDate() === endDate.getDate() && date.getMonth() === endDate.getMonth() && date.getFullYear() === endDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();
                        const isDisabled = !isCurrentMonth || !!(startDate && date < startDate) || date > new Date();

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-base-200' : ''}
                                      ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={() => !isDisabled && setEndDate(new Date(date))}
                            disabled={isDisabled}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Filter Outlet */}
          <div className="flex flex-col gap-1">
            <label htmlFor="outlet-filter" className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiFilter className="text-gray-600" /> Outlet
            </label>
            <select
              id="outlet-filter"
              className="select select-bordered select-sm w-full"
              value={selectedOutletId}
              onChange={(e) => setSelectedOutletId(e.target.value)}
              aria-label="Outlet Filter"
            >
              <option value="">Semua Outlet</option>
              {outlets.map(outlet => (
                <option key={outlet.id} value={outlet.id}>{outlet.name}</option>
              ))}
            </select>
          </div>

           {/* Refresh Button */}
           <div className="flex justify-end">
             <button
               className="btn btn-sm btn-primary btn-outline"
               onClick={fetchVisitPatternsData}
               disabled={isLoading}
               title="Refresh Data"
             >
               <FiRefreshCw className={isLoading ? 'animate-spin' : ''} />
               {isLoading ? 'Memuat...' : 'Terapkan'}
             </button>
           </div>
        </div>
      </motion.div>

      {/* Loading State */}
      {isLoading && (
        <motion.div variants={fadeInUp} className="text-center py-10">
          <span className="loading loading-spinner loading-lg text-primary"></span>
          <p className="mt-2 text-gray-600">Memuat data pola kunjungan...</p>
        </motion.div>
      )}

      {/* Error State */}
      {error && !isLoading && (
        <motion.div variants={fadeInUp} className="alert alert-error">
          <FiAlertTriangle />
          <span>Error: {error}</span>
        </motion.div>
      )}

      {/* Data Display */}
      {!isLoading && !error && visitPatternsData && (
        <>
          {/* Summary Cards - Add optional chaining and default values */}
          <motion.div variants={fadeInUp} className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white p-4 rounded-lg shadow flex items-center gap-4">
              <FiUsers className="text-blue-500 text-3xl" />
              <div>
                <div className="text-gray-500 text-sm">Total Kunjungan</div>
                <div className="text-2xl font-bold">{visitPatternsData?.totalVisits?.toLocaleString('id-ID') ?? 0}</div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow flex items-center gap-4">
              <FiSun className="text-orange-500 text-3xl" />
              <div>
                <div className="text-gray-500 text-sm">Rata-rata / Hari</div>
                <div className="text-2xl font-bold">{visitPatternsData?.avgVisitsPerDay?.toFixed(1) ?? '0.0'}</div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow flex items-center gap-4">
              <FiClock className="text-purple-500 text-3xl" />
              <div>
                <div className="text-gray-500 text-sm">Rata-rata / Jam Buka</div>
                <div className="text-2xl font-bold">{visitPatternsData?.avgVisitsPerHour?.toFixed(1) ?? '0.0'}</div>
              </div>
            </div>
          </motion.div>

          {/* Charts Section */}
          <motion.div variants={fadeInUp} className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Peak Hours Chart */}
            <div className="bg-white p-4 rounded-lg shadow">
              <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700">
                <FiClock /> Kunjungan per Jam
              </h2>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={peakHoursChartData} margin={{ top: 5, right: 5, left: -20, bottom: 5 }}>
                   <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                   <XAxis dataKey="hourLabel" tick={{ fontSize: 12 }} />
                   <YAxis allowDecimals={false} tick={{ fontSize: 12 }} />
                   <Tooltip
                    formatter={(value: number) => [`${value} kunjungan`, 'Jumlah']}
                    labelFormatter={(label) => `Jam: ${label}`}
                  />
                  <Bar dataKey="visitCount" name="Jumlah Kunjungan">
                    {peakHoursChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={DEFAULT_CHART_COLORS[index % DEFAULT_CHART_COLORS.length]} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Peak Days Chart */}
            <div className="bg-white p-4 rounded-lg shadow">
              <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700">
                <FiCalendar /> Kunjungan per Hari
              </h2>
               <ResponsiveContainer width="100%" height={300}>
                 <BarChart data={peakDaysChartData} margin={{ top: 5, right: 5, left: -20, bottom: 5 }}>
                   <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                   <XAxis dataKey="dayName" tick={{ fontSize: 12 }} />
                   <YAxis allowDecimals={false} tick={{ fontSize: 12 }}/>
                   <Tooltip
                    formatter={(value: number) => [`${value} kunjungan`, 'Jumlah']}
                    labelFormatter={(label) => `Hari: ${label}`}
                  />
                   <Bar dataKey="visitCount" name="Jumlah Kunjungan">
                     {peakDaysChartData.map((entry, index) => (
                       <Cell key={`cell-${index}`} fill={DEFAULT_CHART_COLORS[(index + 3) % DEFAULT_CHART_COLORS.length]} /> // Offset color
                     ))}
                   </Bar>
                 </BarChart>
               </ResponsiveContainer>
            </div>
          </motion.div>
        </>
      )}

      {/* No Data State */}
      {!isLoading && !error && !visitPatternsData && (
        <motion.div variants={fadeInUp} className="bg-white p-8 rounded-lg shadow text-center">
          <FiBarChart2 className="mx-auto text-gray-400 mb-4" size={48} />
          <h3 className="text-lg font-medium text-gray-900">Tidak Ada Data Pola Kunjungan</h3>
          <p className="mt-2 text-gray-500">Tidak ada data kunjungan untuk periode atau outlet yang dipilih.</p>
        </motion.div>
      )}
    </motion.div>
  );
} 