import { useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Hook untuk melakukan fetch dengan automatic handling untuk token expired (401)
 * Akan otomatis redirect ke login jika token expired
 */
export function useAuthenticatedFetch() {
  const router = useRouter();
  const { logout } = useAuth();

  const authenticatedFetch = useCallback(async (url: string, options: RequestInit = {}) => {
    const defaultOptions: RequestInit = {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);
      
      // Jika response 401 (Unauthorized), token kemungkinan expired
      if (response.status === 401) {
        console.log('🔒 Session expired detected in fetch, redirecting to login...');
        
        // Hapus token dari cookie
        document.cookie = 'user_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        
        // Gunakan logout function dari AuthContext untuk cleanup yang proper
        try {
          await logout();
        } catch (logoutError) {
          console.error('Error during logout:', logoutError);
          // Tetap lanjutkan redirect meskipun logout gagal
        }
        
        // Redirect ke login dengan current path sebagai redirect parameter
        const currentPath = window.location.pathname + window.location.search;
        router.push(`/auth/login?redirect=${encodeURIComponent(currentPath)}`);
        
        throw new Error('Session expired. Redirecting to login...');
      }
      
      return response;
    } catch (error) {
      // Re-throw error untuk handling di level komponen
      throw error;
    }
  }, [router, logout]);
  
  return authenticatedFetch;
}

/**
 * Hook untuk melakukan fetch dengan error handling yang lebih lengkap
 * Termasuk handling untuk berbagai status code dan automatic retry
 */
export function useApiRequest() {
  const authenticatedFetch = useAuthenticatedFetch();
  
  const apiRequest = useCallback(async <T = any>(
    url: string, 
    options: RequestInit = {},
    showErrorAlert = true
  ): Promise<T> => {
    try {
      const response = await authenticatedFetch(url, options);
      
      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.message || errorMessage;
        } catch {
          // Jika tidak bisa parse JSON, gunakan status text
        }
        
        if (showErrorAlert && typeof window !== 'undefined') {
          alert(`❌ Error: ${errorMessage}`);
        }
        
        throw new Error(errorMessage);
      }
      
      // Parse response sebagai JSON jika memungkinkan
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      }
      
      // Jika bukan JSON, return response object
      return response as any;
    } catch (error) {
      console.error('API Request Error:', error);
      throw error;
    }
  }, [authenticatedFetch]);
  
  return apiRequest;
}