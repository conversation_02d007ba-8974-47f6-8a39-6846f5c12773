import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// Konstanta untuk batasan jumlah pelanggan yang diproses per batch
const BATCH_SIZE = 50; // Mengurangi ukuran batch untuk menghindari timeout
const MAX_CUSTOMERS_PER_REQUEST = 500; // Batasi jumlah pelanggan yang diproses per request

export async function POST(req: NextRequest) {
  try {
    // Ambil kriteria dari request body
    const body = await req.json();
    const {
      tagACriteria = { minTransactions: 2, maxIntervalDays: 30 },
      tagBCriteria = { minDays: 31, maxDays: 60 },
      tagCCriteria = { minDays: 61, maxDays: 180 },
      tagDCriteria = { minDays: 181 },
      startIndex = 0, // Parameter baru untuk pagination
      processAll = false // Parameter untuk memproses semua atau hanya sebagian
    } = body;

    console.log(`Auto-tag request: startIndex=${startIndex}, processAll=${processAll}`);

    // Ambil total jumlah pelanggan aktif
    const totalCustomers = await prisma.customer.count({
      where: {
        isActive: true
      }
    });

    console.log(`Total active customers: ${totalCustomers}`);

    // Jika tidak ada pelanggan, kembalikan respons kosong
    if (totalCustomers === 0) {
      return NextResponse.json({
        message: 'Tidak ada pelanggan aktif untuk diproses',
        totalCustomers: 0,
        processedCustomers: 0,
        updatedCustomers: 0,
        tagCounts: [],
        isComplete: true,
        nextStartIndex: 0,
        progress: 100
      });
    }

    // Statistik untuk hasil
    const tagCounts: Record<string, number> = {
      'A': 0,
      'B': 0,
      'C': 0,
      'D': 0,
      'Baru': 0
    };

    let updatedCustomers = 0;
    let processedCustomers = 0;

    // Tentukan jumlah pelanggan yang akan diproses
    const endIndex = processAll ? totalCustomers : Math.min(startIndex + MAX_CUSTOMERS_PER_REQUEST, totalCustomers);

    console.log(`Processing customers from ${startIndex} to ${endIndex} (total: ${totalCustomers})`);

    // Proses pelanggan dalam batch untuk menghindari timeout
    for (let skip = startIndex; skip < endIndex; skip += BATCH_SIZE) {
      const take = Math.min(BATCH_SIZE, endIndex - skip);
      console.log(`Processing batch: skip=${skip}, take=${take}`);

      // Ambil batch pelanggan dengan transaksi mereka
      const customerBatch = await prisma.customer.findMany({
        where: {
          isActive: true // Hanya pelanggan aktif
        },
        include: {
          transactions: {
            select: {
              id: true,
              transactionDate: true // Gunakan transactionDate bukan createdAt
            },
            orderBy: {
              transactionDate: 'asc'
            }
          }
        },
        skip: skip,
        take: take
      });

      console.log(`Retrieved ${customerBatch.length} customers in batch`);

      // Persiapkan array untuk bulk update
      const updateOperations = [];

      // Proses setiap pelanggan dalam batch
      for (const customer of customerBatch) {
        processedCustomers++;
        const transactions = customer.transactions;
        let newTags: string[] = [];

        // Ambil tag yang sudah ada dan pertahankan
        const currentTags = Array.isArray(customer.tags) ? customer.tags : [];
        
        // Normalisasi tag yang ada (konversi 'BARU' ke 'Baru' jika ada)
        const normalizedCurrentTags = currentTags.map(tag =>
          tag === 'BARU' ? 'Baru' : tag
        );
        
        // Pisahkan tag kategori (A, B, C, D, Baru) dari tag lainnya
        const categoryTags = ['A', 'B', 'C', 'D', 'Baru'];
        const existingCategoryTags = normalizedCurrentTags.filter(tag => categoryTags.includes(tag));
        const nonCategoryTags = normalizedCurrentTags.filter(tag => !categoryTags.includes(tag));
        
        // Jika sudah ada tag kategori (A, B, C, D, atau Baru), pertahankan yang sudah ada
        // TIDAK melakukan auto-tagging untuk pelanggan yang sudah memiliki tag kategori
        if (existingCategoryTags.length > 0) {
          console.log(`Customer ${customer.id} sudah memiliki tag kategori: ${existingCategoryTags.join(', ')}. Melewati auto-tagging.`);
          newTags = normalizedCurrentTags; // Pertahankan semua tag yang sudah ada
        } else {
          // Hanya lakukan auto-tagging jika belum ada tag kategori
          let newCategoryTag = '';
          
          // Tentukan tag kategori berdasarkan transaksi
          // Jika tidak ada transaksi, tag sebagai Baru
          if (transactions.length === 0) {
            newCategoryTag = 'Baru';
            tagCounts['Baru']++;
          }
          // Jika hanya ada 1 transaksi, tag sebagai Baru
          else if (transactions.length === 1) {
            newCategoryTag = 'Baru';
            tagCounts['Baru']++;
          }
          // Jika ada 2+ transaksi, periksa interval
          else {
            // Periksa kriteria Tag A (interval antar transaksi)
            let hasShortInterval = false;

            // Periksa interval antar transaksi berurutan
            for (let i = 1; i < transactions.length; i++) {
              const prevDate = new Date(transactions[i-1].transactionDate);
              const currDate = new Date(transactions[i].transactionDate);

              // Hitung interval dalam hari
              const intervalDays = Math.floor((currDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24));

              // Jika ada interval ≤ 30 hari, pelanggan mendapat tag A
              if (intervalDays <= tagACriteria.maxIntervalDays) {
                hasShortInterval = true;
                break;
              }
            }

            if (hasShortInterval && transactions.length >= tagACriteria.minTransactions) {
              newCategoryTag = 'A';
              tagCounts['A']++;
            } else {
              // Jika tidak memenuhi kriteria A, periksa interval sejak transaksi terakhir
              const lastTransactionDate = new Date(transactions[transactions.length - 1].transactionDate);
              const today = new Date();
              const daysSinceLastTransaction = Math.floor((today.getTime() - lastTransactionDate.getTime()) / (1000 * 60 * 60 * 24));

              if (daysSinceLastTransaction >= tagBCriteria.minDays && daysSinceLastTransaction <= tagBCriteria.maxDays) {
                newCategoryTag = 'B';
                tagCounts['B']++;
              } else if (daysSinceLastTransaction >= tagCCriteria.minDays && daysSinceLastTransaction <= tagCCriteria.maxDays) {
                newCategoryTag = 'C';
                tagCounts['C']++;
              } else if (daysSinceLastTransaction >= tagDCriteria.minDays) {
                newCategoryTag = 'D';
                tagCounts['D']++;
              } else {
                newCategoryTag = 'Baru';
                tagCounts['Baru']++;
              }
            }
          }
          
          // Gabungkan tag non-kategori dengan tag kategori baru
          newTags = [...nonCategoryTags, newCategoryTag];
        }
        
        // Bandingkan dengan tag yang ada untuk menentukan apakah perlu update
        const needsUpdate = !arraysEqual(normalizedCurrentTags, newTags);

        // Tambahkan ke operasi update jika perlu
        if (needsUpdate) {
          console.log(`Customer ${customer.id} needs tag update: ${JSON.stringify(normalizedCurrentTags)} -> ${JSON.stringify(newTags)}`);
          updateOperations.push({
            id: customer.id,
            tags: newTags
          });
        }
      }

      // Lakukan bulk update untuk batch ini
      if (updateOperations.length > 0) {
        console.log(`Performing ${updateOperations.length} updates in batch`);

        // Gunakan prisma transaction untuk bulk update
        await prisma.$transaction(
          updateOperations.map(op =>
            prisma.customer.update({
              where: { id: op.id },
              data: { tags: op.tags }
            })
          )
        );

        updatedCustomers += updateOperations.length;
      }
    }

    // Konversi tagCounts menjadi array untuk respons
    const tagCountsArray = Object.entries(tagCounts).map(([tag, count]) => ({ tag, count }));

    // Tentukan apakah proses sudah selesai atau masih ada yang perlu diproses
    const isComplete = endIndex >= totalCustomers;
    const nextStartIndex = isComplete ? 0 : endIndex;

    return NextResponse.json({
      message: isComplete ? 'Tag pelanggan berhasil diperbarui' : 'Sebagian tag pelanggan berhasil diperbarui',
      totalCustomers: totalCustomers,
      processedCustomers: processedCustomers,
      updatedCustomers,
      tagCounts: tagCountsArray,
      isComplete,
      nextStartIndex,
      progress: Math.round((endIndex / totalCustomers) * 100)
    });
  } catch (error) {
    console.error('Error in auto-tag process:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengatur tag otomatis' },
      { status: 500 }
    );
  }
}

// Helper function to compare arrays
function arraysEqual(a: string[], b: string[]): boolean {
  if (a.length !== b.length) return false;
  const sortedA = [...a].sort();
  const sortedB = [...b].sort();
  return sortedA.every((val, idx) => val === sortedB[idx]);
}
