import { NextResponse } from 'next/server';

export async function POST() {
  try {
    // Buat response dengan pesan sukses
    const response = NextResponse.json({
      message: 'Logout berhasil'
    });
    
    // Hapus cookie token dengan mengatur expired date-nya ke masa lalu
    response.cookies.set({
      name: 'user_token',
      value: '',
      expires: new Date(0), // Set expired date ke masa lalu
      httpOnly: true,
      path: '/',
    });
    
    return response;
  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { error: 'Ter<PERSON><PERSON> kesalahan saat logout' },
      { status: 500 }
    );
  }
} 