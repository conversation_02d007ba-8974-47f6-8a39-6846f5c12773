// Script untuk menjalankan migrasi pelanggan
const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Menjalankan script migrasi pelanggan...');

try {
  // Jalankan script migrasi dengan Node.js
  execSync('node scripts/migrasi-pelanggan.js', { stdio: 'inherit' });
  
  console.log('✅ Migrasi berhasil dijalankan!');
} catch (error) {
  console.error('❌ Terjadi kesalahan saat menjalankan migrasi:', error.message);
  process.exit(1);
}
