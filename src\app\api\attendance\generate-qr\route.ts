import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import QRCode from 'qrcode';

export async function GET(request: NextRequest) {
  try {
    // Ambil parameter dari query string
    const { searchParams } = new URL(request.url);
    const therapistId = searchParams.get('therapistId');
    const userId = searchParams.get('userId');
    const type = searchParams.get('type') || 'therapist'; // default ke therapist
    
    if (!therapistId && !userId) {
      return NextResponse.json(
        { error: 'Id terapis atau user diperlukan' },
        { status: 400 }
      );
    }

    let data: any = null;
    let qrData = '';

    // Ambil data berdasarkan tipe
    if (type === 'therapist' && therapistId) {
      data = await prisma.therapist.findUnique({
        where: { id: therapistId },
        include: { outlet: true }
      });
      
      if (!data) {
        return NextResponse.json(
          { error: 'Terapis tidak ditemukan' },
          { status: 404 }
        );
      }
      
      // Format data untuk QR code
      qrData = JSON.stringify({
        type: 'therapist',
        id: data.id,
        name: data.name,
        outletId: data.outletId
      });
    } else if (type === 'user' && userId) {
      data = await prisma.user.findUnique({
        where: { id: userId }
      });
      
      if (!data) {
        return NextResponse.json(
          { error: 'User tidak ditemukan' },
          { status: 404 }
        );
      }
      
      // Format data untuk QR code
      qrData = JSON.stringify({
        type: 'user',
        id: data.id,
        name: data.name,
        role: data.role
      });
    }

    // Generate QR code dengan kualitas tinggi
    const qrCodeOptions = {
      errorCorrectionLevel: 'H',
      type: 'image/png',
      quality: 1.0,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#ffffff'
      }
    };
    
    const qrImageData = await QRCode.toDataURL(qrData, qrCodeOptions);

    // Format nama outlet jika ada (hapus prefix 'breaktime')
    if (data?.outlet?.name) {
      // Hapus kata 'breaktime' dan 'palu' jika ada dalam nama outlet
      let outletName = data.outlet.name.toLowerCase();
      outletName = outletName.replace(/breaktime/i, '').trim();
      outletName = outletName.replace(/palu/i, '').trim();
      data.outlet.name = outletName.charAt(0).toUpperCase() + outletName.slice(1);
    }
    
    return NextResponse.json({
      success: true,
      data,
      qrCode: qrImageData
    });
  } catch (error) {
    console.error('Error generating QR code:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat generate QR code' },
      { status: 500 }
    );
  }
} 