import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: Request) {
  try {
    // Ambil URL parameters
    const { searchParams } = new URL(request.url);
    const phone = searchParams.get('phone');

    // Validasi parameter
    if (!phone) {
      return NextResponse.json(
        { error: 'Parameter phone diperlukan' },
        { status: 400 }
      );
    }

    console.log(`API Customers by phone - Searching for phone: ${phone}`);

    // Cari pelanggan berdasarkan nomor telepon
    const customer = await prisma.customer.findFirst({
      where: {
        phone: {
          equals: phone,
          // mode: 'insensitive' // Tidak perlu case insensitive untuk nomor telepon
        },
        isActive: true // Hanya ambil pelanggan aktif
      },
      select: {
        id: true,
        name: true,
        phone: true,
        email: true,
        points: true,
        address: true,
        registeredAt: true,
      }
    });

    // Log detail customer jika ditemukan
    if (customer) {
      console.log(`API Customers by phone - Customer details: name=${customer.name}, points=${customer.points ?? 0}`);
      console.log(`API Customers by phone - Found customer: ${customer.id}`);
      return NextResponse.json({ 
        customer: {
          ...customer,
          points: customer.points ?? 0 // Pastikan points tidak null
        }
      });
    } else {
      console.log(`API Customers by phone - No customer found with phone: ${phone}`);
      return NextResponse.json({ customer: null });
    }
  } catch (error) {
    console.error('API Customers by phone - Error:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mencari pelanggan' },
      { status: 500 }
    );
  }
} 