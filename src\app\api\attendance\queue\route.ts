import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserIdFromToken } from '@/lib/auth-utils';
import { startOfDay, endOfDay } from 'date-fns';

export async function GET(request: NextRequest) {
  try {
    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ambil parameter URL
    const searchParams = request.nextUrl.searchParams;
    const outletId = searchParams.get('outletId');
    
    // Tanggal hari ini
    const today = startOfDay(new Date());
    const endToday = endOfDay(new Date());
    
    // Query untuk mendapatkan terapis yang sudah check-in tapi belum check-out
    const activeTherapists = await prisma.therapistAttendance.findMany({
      where: {
        createdAt: {
          gte: today,
          lte: endToday
        },
        attendanceType: 'IN',
        ...(outletId ? { outletId } : {}), // Filter berdasarkan outlet jika ada
      },
      include: {
        therapist: true,
        outlet: true
      },
      orderBy: {
        createdAt: 'asc' // Urutkan berdasarkan waktu check-in
      }
    });
    
    // Temukan terapis yang sudah check-out hari ini
    const checkedOutTherapists = await prisma.therapistAttendance.findMany({
      where: {
        createdAt: {
          gte: today,
          lte: endToday
        },
        attendanceType: 'OUT',
        ...(outletId ? { outletId } : {})
      },
      select: {
        therapistId: true,
        createdAt: true
      }
    });
    
    // Buat set dari ID terapis yang sudah check-out
    const checkedOutTherapistIds = new Set(
      checkedOutTherapists.map(record => record.therapistId)
    );
    
    // Filter terapis yang belum check-out
    const activeTherapistsQueue = activeTherapists.filter(
      record => !checkedOutTherapistIds.has(record.therapistId)
    ).map(record => ({
      id: record.id,
      therapistId: record.therapistId,
      name: record.therapist.name,
      outletId: record.outletId,
      outletName: record.outlet.name,
      checkInTime: record.createdAt,
      waitingTime: Math.floor((new Date().getTime() - new Date(record.createdAt).getTime()) / 60000), // Waktu tunggu dalam menit
    }));

    // Dapatkan daftar outlet untuk filter
    const outlets = await prisma.outlet.findMany({
      select: {
        id: true,
        name: true,
      },
      orderBy: {
        name: 'asc',
      },
    });
    
    return NextResponse.json({
      success: true,
      data: activeTherapistsQueue,
      filters: {
        outlets
      }
    });
    
  } catch (error) {
    console.error('Error fetching therapist queue:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data antrian' },
      { status: 500 }
    );
  }
} 