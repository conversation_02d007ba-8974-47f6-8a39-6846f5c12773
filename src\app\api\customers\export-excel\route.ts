import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import * as XLSX from 'xlsx';

export async function GET(req: NextRequest) {
  try {
    console.log('Memulai ekspor data pelanggan ke Excel...');

    // Ambil parameter filter dari URL
    const { searchParams } = new URL(req.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    console.log(`Ekspor Excel - Parameter filter: startDate=${startDate}, endDate=${endDate}`);

    // Ambil semua outlet untuk referensi
    const outlets = await prisma.outlet.findMany({
      select: { id: true, name: true }
    });

    console.log(`Berhasil mengambil ${outlets.length} outlet`);

    // Buat filter untuk query
    const whereFilter: Record<string, any> = {
      isActive: true // Hanya pelanggan aktif
    };

    // Parameter tambahan untuk menentukan jenis filter tanggal
    const filterType = searchParams.get('filterType') || 'registration'; // Default: filter berdasarkan tanggal registrasi
    console.log(`Ekspor Excel - Filter type: ${filterType}`);

    if (startDate || endDate) {
      if (filterType === 'transaction') {
        // Filter berdasarkan tanggal transaksi
        console.log('Ekspor Excel - Filtering by transaction date');

        // Kita tidak bisa langsung filter by transaction date di level customer query
        // Akan difilter setelah data diambil
      } else {
        // Filter berdasarkan tanggal registrasi (default)
        whereFilter.registeredAt = {};

        if (startDate) {
          // Konversi ke tanggal awal hari (00:00:00)
          const startDateTime = new Date(startDate);
          startDateTime.setHours(0, 0, 0, 0);
          whereFilter.registeredAt.gte = startDateTime;
          console.log(`Ekspor Excel - Filtering by registration startDate: ${startDateTime.toISOString()}`);
        }

        if (endDate) {
          // Konversi ke tanggal akhir hari (23:59:59)
          const endDateTime = new Date(endDate);
          endDateTime.setHours(23, 59, 59, 999);
          whereFilter.registeredAt.lte = endDateTime;
          console.log(`Ekspor Excel - Filtering by registration endDate: ${endDateTime.toISOString()}`);
        }
      }
    }

    // Hitung total pelanggan aktif untuk logging berdasarkan filter
    const totalCustomers = await prisma.customer.count({
      where: whereFilter
    });

    console.log(`Total pelanggan aktif sesuai filter: ${totalCustomers}`);

    // Ambil semua pelanggan dengan transaksi mereka (termasuk informasi outlet dan item transaksi)
    const customers = await prisma.customer.findMany({
      where: whereFilter,
      orderBy: {
        name: 'asc' // Urutkan berdasarkan nama
      },
      include: {
        transactions: {
          select: {
            id: true,
            displayId: true,
            createdAt: true,
            totalAmount: true,
            outletId: true,
            outlet: {
              select: {
                id: true,
                name: true
              }
            },
            transactionItems: {
              select: {
                quantity: true,
                price: true,
                service: {
                  select: {
                    name: true
                  }
                }
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    });

    // Log jumlah data yang diambil untuk debugging
    console.log(`Total customers fetched for Excel export: ${customers.length}`);

    // Filter pelanggan berdasarkan tanggal transaksi jika diperlukan
    let filteredCustomers = customers;

    if ((startDate || endDate) && filterType === 'transaction') {
      console.log('Filtering customers by transaction date...');

      // Konversi tanggal untuk perbandingan dengan penanganan zona waktu yang lebih baik
      let startDateTime = null;
      let endDateTime = null;

      if (startDate) {
        // Pastikan tanggal dimulai dari awal hari (00:00:00.000)
        startDateTime = new Date(startDate);
        startDateTime.setHours(0, 0, 0, 0);
        console.log(`Original startDate: ${startDate}, Converted to: ${startDateTime.toISOString()}`);
      }

      if (endDate) {
        // Pastikan tanggal berakhir di akhir hari (23:59:59.999)
        endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        console.log(`Original endDate: ${endDate}, Converted to: ${endDateTime.toISOString()}`);
      }

      console.log(`Filtering transactions from: ${startDateTime?.toISOString() || 'any date'}`);
      console.log(`Filtering transactions until: ${endDateTime?.toISOString() || 'any date'}`);

      // Filter pelanggan yang memiliki transaksi dalam rentang tanggal
      filteredCustomers = customers.filter(customer => {
        // Jika tidak ada transaksi, tidak termasuk dalam filter
        if (customer.transactions.length === 0) return false;

        // Cek apakah ada transaksi dalam rentang tanggal
        const hasTransactionInRange = customer.transactions.some(trx => {
          // Gunakan createdAt sebagai pengganti transactionDate
          const trxDate = new Date(trx.createdAt);

          // Log untuk debugging
          if (startDate === endDate && startDate === '2023-05-01') {
            console.log(`Excel export - Transaction date: ${trxDate.toISOString()}, Customer: ${customer.id}`);
          }

          // Cek apakah tanggal transaksi dalam rentang
          const afterStart = !startDateTime || trxDate >= startDateTime;
          const beforeEnd = !endDateTime || trxDate <= endDateTime;

          const isInRange = afterStart && beforeEnd;

          // Log untuk debugging tanggal 1 Mei
          if (startDate === endDate && startDate === '2023-05-01' && isInRange) {
            console.log(`EXCEL EXPORT MATCH! Transaction date: ${trxDate.toISOString()}, Customer: ${customer.id}`);
          }

          return isInRange;
        });

        return hasTransactionInRange;
      });

      console.log(`Filtered customers by transaction date: ${filteredCustomers.length} of ${customers.length}`);
      console.log(`After date filtering: ${filteredCustomers.length} customers have transactions in the selected period`);

      // Log beberapa contoh pelanggan yang terfilter untuk debugging
      if (filteredCustomers.length > 0) {
        console.log('Sample filtered customers:');
        filteredCustomers.slice(0, 3).forEach((customer, index) => {
          console.log(`Customer ${index + 1}: ${customer.name}, Transactions: ${customer.transactions.length}`);
          if (customer.transactions.length > 0) {
            console.log(`  Latest transaction date: ${new Date(customer.transactions[0].createdAt).toISOString()}`);
          }
        });
      } else {
        console.log('No customers found with transactions in the selected date range');
      }
    }

    // Siapkan data untuk Excel dalam format JSON
    console.log(`Processing ${filteredCustomers.length} customers for Excel export`);
    const excelData = filteredCustomers.map((customer, index) => {
      // Hitung total transaksi
      const totalSpent = customer.transactions.reduce((sum, trx) => sum + (trx.totalAmount || 0), 0);

      // Format tanggal registrasi
      const registeredAt = customer.registeredAt ? new Date(customer.registeredAt) : null;

      // Format tanggal transaksi terakhir
      const lastTransaction = customer.transactions.length > 0
        ? new Date(customer.transactions[0].createdAt)
        : null;

      // Buat objek dasar
      const customerData = {
        'No': index + 1,
        'Nama': customer.name,
        'No. Telepon': customer.phone,
        'Alamat': customer.address || '-',
        'Poin': customer.points || 0,
        'Tag': (customer.tags || []).join(', '),
        'Tanggal Registrasi': registeredAt ? registeredAt.toLocaleDateString('id-ID') : '-',
        'Jumlah Transaksi': customer.transactions.length,
        'Total Transaksi (Rp)': totalSpent,
        'Transaksi Terakhir': lastTransaction ? lastTransaction.toLocaleDateString('id-ID') : '-'
      };

      // Tambahkan data transaksi per outlet
      outlets.forEach(outlet => {
        // Hitung total transaksi untuk outlet ini
        const outletTransactions = customer.transactions
          .filter(trx => trx.outletId === outlet.id);

        const outletTotal = outletTransactions
          .reduce((sum, trx) => sum + (trx.totalAmount || 0), 0);

        // Tambahkan ke objek data
        customerData[`Transaksi di ${outlet.name} (Rp)`] = outletTotal;

        // Tambahkan riwayat transaksi untuk outlet ini
        if (outletTransactions.length > 0) {
          // Format riwayat transaksi: tanggal - ID - layanan (jumlah)
          const transactionHistory = outletTransactions.map(trx => {
            const date = new Date(trx.createdAt).toLocaleDateString('id-ID');
            const id = trx.displayId || trx.id.substring(0, 8);

            // Gabungkan semua layanan dengan jumlahnya
            const services = trx.transactionItems.map(item => {
              const serviceName = item.service?.name || 'Layanan';
              return item.quantity > 1
                ? `${serviceName} (${item.quantity}x)`
                : serviceName;
            }).join(', ');

            return `${date} - ${id} - ${services}`;
          }).join('\n');

          customerData[`Riwayat di ${outlet.name}`] = transactionHistory;
        } else {
          customerData[`Riwayat di ${outlet.name}`] = '-';
        }
      });

      return customerData;
    });

    // Buat worksheet dari data JSON dengan opsi
    const worksheet = XLSX.utils.json_to_sheet(excelData, {
      header: Object.keys(excelData[0] || {}),
      skipHeader: false
    });

    // Styling untuk header
    const headerRange = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    for (let C = headerRange.s.c; C <= headerRange.e.c; C++) {
      const headerCell = XLSX.utils.encode_cell({ r: 0, c: C });
      if (!worksheet[headerCell]) continue;

      // Tambahkan style ke header
      worksheet[headerCell].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "4F81BD" } },
        alignment: { horizontal: "center", vertical: "center" }
      };
    }

    // Atur lebar kolom untuk riwayat transaksi
    const colInfo = [];

    // Dapatkan semua nama kolom
    const cols = Object.keys(excelData[0] || {});

    // Atur lebar kolom berdasarkan nama kolom
    cols.forEach((col, index) => {
      // Sesuaikan lebar kolom berdasarkan jenis data
      switch (col) {
        case 'No':
          colInfo[index] = { wch: 5 }; // Sangat sempit
          break;
        case 'Nama':
          colInfo[index] = { wch: 25 }; // Lebar untuk nama
          break;
        case 'No. Telepon':
          colInfo[index] = { wch: 15 }; // Cukup untuk nomor telepon
          break;
        case 'Alamat':
          colInfo[index] = { wch: 35 }; // Lebar untuk alamat
          break;
        case 'Poin':
          colInfo[index] = { wch: 8 }; // Sempit untuk angka
          break;
        case 'Tag':
          colInfo[index] = { wch: 15 }; // Sedang untuk tag
          break;
        case 'Tanggal Registrasi':
        case 'Transaksi Terakhir':
          colInfo[index] = { wch: 18 }; // Cukup untuk tanggal
          break;
        case 'Jumlah Transaksi':
          colInfo[index] = { wch: 10 }; // Sempit untuk angka
          break;
        case 'Total Transaksi (Rp)':
          colInfo[index] = { wch: 18 }; // Cukup untuk angka dengan format
          break;
        default:
          // Untuk kolom dinamis (outlet)
          if (col.startsWith('Riwayat di')) {
            colInfo[index] = { wch: 60 }; // Lebih sempit dari sebelumnya
          } else if (col.startsWith('Transaksi di')) {
            colInfo[index] = { wch: 18 }; // Cukup untuk angka dengan format
          } else {
            colInfo[index] = { wch: 15 }; // Default
          }
      }
    });

    worksheet['!cols'] = colInfo;

    // Tambahkan format untuk angka dan tanggal
    if (excelData.length > 0) {
      // Dapatkan range data (tidak termasuk header)
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');

      // Iterasi melalui semua kolom
      for (let C = range.s.c; C <= range.e.c; C++) {
        const colName = cols[C];

        // Terapkan format berdasarkan jenis kolom
        if (colName === 'Poin' || colName === 'Jumlah Transaksi' || colName.startsWith('Transaksi di')) {
          // Format angka dengan pemisah ribuan
          for (let R = range.s.r + 1; R <= range.e.r; R++) {
            const cellRef = XLSX.utils.encode_cell({ r: R, c: C });
            if (worksheet[cellRef]) {
              worksheet[cellRef].z = '#,##0';
            }
          }
        } else if (colName === 'Total Transaksi (Rp)') {
          // Format mata uang
          for (let R = range.s.r + 1; R <= range.e.r; R++) {
            const cellRef = XLSX.utils.encode_cell({ r: R, c: C });
            if (worksheet[cellRef]) {
              worksheet[cellRef].z = '#,##0.00';
            }
          }
        }
      }
    }

    // Tambahkan autofilter untuk semua kolom
    if (excelData.length > 0) {
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
      worksheet['!autofilter'] = { ref: XLSX.utils.encode_range(range.s, range.e) };

      // Bekukan baris header
      worksheet['!freeze'] = { xSplit: 0, ySplit: 1, topLeftCell: 'A2', activePane: 'bottomLeft' };
    }

    // Buat workbook baru
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Data Pelanggan');

    // Konversi ke array buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Buat response dengan buffer
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="Data_Pelanggan.xlsx"'
      }
    });
  } catch (error) {
    console.error('Error saat ekspor Excel:', error);

    // Tampilkan detail error untuk debugging
    const errorMessage = error instanceof Error
      ? `${error.name}: ${error.message}`
      : 'Terjadi kesalahan yang tidak diketahui';

    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengekspor data pelanggan', details: errorMessage },
      { status: 500 }
    );
  }
}
