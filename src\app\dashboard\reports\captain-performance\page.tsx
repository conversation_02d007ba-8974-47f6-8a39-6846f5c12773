'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  FiCalendar, FiFilter, FiBarChart2, FiUsers,
  FiAlertTriangle, FiRefreshCw, FiChevronLeft,
  FiDollarSign, FiUserCheck, FiChevronDown, FiChevronUp
} from 'react-icons/fi';
import { format, subDays, isValid } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { useOutletContext } from '@/contexts/OutletContext';
import Link from 'next/link';

// Format tanggal untuk tampilan
const formatDateDisplay = (date: Date | null): string => {
  if (!date) return '';
  return format(date, 'dd MMMM yyyy', { locale: idLocale });
};

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(value);
};

// Animasi
const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
};

// Interface untuk data kinerja kapten
interface TherapistPerformance {
  therapistId: string;
  therapistName: string;
  outletId: string;
  outletName: string;
  totalSales: number;
  totalCommission: number;
  transactionCount: number;
  additionalCharge?: number; // Biaya tambahan (opsional)
  discountAmount?: number;   // Jumlah diskon (opsional)
}

interface CaptainPerformance {
  captainId: string;
  captainName: string;
  captainUsername: string;
  teamSize: number;
  totalSales: number;
  totalCommission: number;
  therapists: TherapistPerformance[];
  additionalCharge?: number; // Biaya tambahan (opsional)
  discountAmount?: number;   // Jumlah diskon (opsional)
}

interface CaptainPerformanceData {
  captainPerformance: CaptainPerformance[];
  filters: {
    startDate: string;
    endDate: string;
    outletId: string | null;
  };
}

const CaptainPerformancePage = () => {
  // State untuk data dan loading
  const [captainData, setCaptainData] = useState<CaptainPerformanceData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // State untuk filter
  const [startDate, setStartDate] = useState<Date>(() => {
    // Tanggal 1 bulan berjalan
    const today = new Date();
    return new Date(today.getFullYear(), today.getMonth(), 1);
  });
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [selectedOutletId, setSelectedOutletId] = useState<string>('');

  // State untuk navigasi kalender
  const [calendarMonth, setCalendarMonth] = useState<{start: Date, end: Date}>({
    start: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    end: new Date()
  });

  // State untuk expanded rows
  const [expandedCaptains, setExpandedCaptains] = useState<Record<string, boolean>>({});

  // Context untuk outlet
  const { outlets = [] } = useOutletContext() || {};

  // Fetch data kapten
  const fetchCaptainData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Buat parameter untuk API
      const params = new URLSearchParams();
      params.append('startDate', startDate.toISOString());
      params.append('endDate', endDate.toISOString());
      if (selectedOutletId) {
        params.append('outletId', selectedOutletId);
      }

      // Fetch data dari API
      const response = await fetch(`/api/reports/captain-performance?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();

      setCaptainData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Terjadi kesalahan saat mengambil data');
    } finally {
      setIsLoading(false);
    }
  };

  // Re-fetch data saat filter berubah
  useEffect(() => {
    fetchCaptainData();
  }, [startDate, endDate, selectedOutletId]);

  // Toggle expanded state untuk kapten
  const toggleCaptainExpanded = (captainId: string) => {
    setExpandedCaptains(prev => ({
      ...prev,
      [captainId]: !prev[captainId]
    }));
  };

  // Render
  return (
    <motion.div
      className="container mx-auto p-4 md:p-6 space-y-6"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {/* Header with Back Button */}
      <motion.div variants={fadeInUp} className="flex items-center gap-2">
        <Link href="/dashboard/reports" className="btn btn-sm btn-ghost">
          <FiChevronLeft /> Kembali
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Laporan Kinerja Kapten</h1>
          <p className="text-gray-600">Analisis kinerja kapten dan tim terapis mereka</p>
        </div>
      </motion.div>

      {/* Filter Section */}
      <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-end flex-wrap">
          <div className="form-control w-full md:w-64">
            <label className="label">
              <span className="label-text text-gray-700 flex items-center gap-1 mb-1">
                <FiCalendar className="text-gray-600" /> Tanggal Mulai
              </span>
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-outline w-full justify-between">
                <span className="truncate">{startDate ? formatDateDisplay(startDate) : 'Pilih tanggal mulai'}</span>
                <FiCalendar className="ml-2" />
              </div>
              <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button
                        type="button"
                        className="btn btn-sm btn-ghost"
                        onClick={(e) => {
                          e.preventDefault();
                          const newMonth = new Date(calendarMonth.start);
                          newMonth.setMonth(newMonth.getMonth() - 1);
                          setCalendarMonth({
                            ...calendarMonth,
                            start: newMonth
                          });
                        }}
                      >
                        «
                      </button>
                      <div className="text-sm font-medium">
                        {format(calendarMonth.start, 'MMMM yyyy', { locale: idLocale })}
                      </div>
                      <button
                        type="button"
                        className="btn btn-sm btn-ghost"
                        onClick={(e) => {
                          e.preventDefault();
                          const newMonth = new Date(calendarMonth.start);
                          newMonth.setMonth(newMonth.getMonth() + 1);
                          setCalendarMonth({
                            ...calendarMonth,
                            start: newMonth
                          });
                        }}
                      >
                        »
                      </button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {/* Hari dalam seminggu */}
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}

                      {/* Tanggal */}
                      {Array.from({ length: 42 }, (_, i) => {
                        const currentDate = calendarMonth.start;
                        const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === currentDate.getMonth();
                        const isSelected = startDate && date.getDate() === startDate.getDate() &&
                                          date.getMonth() === startDate.getMonth() &&
                                          date.getFullYear() === startDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();

                        return (
                          <button
                            type="button"
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all
                                      ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected ? 'hover:bg-base-200' : ''}
                                      ${!isCurrentMonth ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={(e) => {
                              e.preventDefault();
                              if (isCurrentMonth) {
                                // Set tanggal mulai langsung dan refresh data
                                setStartDate(new Date(date));
                              }
                            }}
                            disabled={!isCurrentMonth}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="form-control w-full md:w-64">
            <label className="label">
              <span className="label-text text-gray-700 flex items-center gap-1 mb-1">
                <FiCalendar className="text-gray-600" /> Tanggal Akhir
              </span>
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-outline w-full justify-between">
                <span className="truncate">{endDate ? formatDateDisplay(endDate) : 'Pilih tanggal akhir'}</span>
                <FiCalendar className="ml-2" />
              </div>
              <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button
                        type="button"
                        className="btn btn-sm btn-ghost"
                        onClick={(e) => {
                          e.preventDefault();
                          const newMonth = new Date(calendarMonth.end);
                          newMonth.setMonth(newMonth.getMonth() - 1);
                          setCalendarMonth({
                            ...calendarMonth,
                            end: newMonth
                          });
                        }}
                      >
                        «
                      </button>
                      <div className="text-sm font-medium">
                        {format(calendarMonth.end, 'MMMM yyyy', { locale: idLocale })}
                      </div>
                      <button
                        type="button"
                        className="btn btn-sm btn-ghost"
                        onClick={(e) => {
                          e.preventDefault();
                          const newMonth = new Date(calendarMonth.end);
                          newMonth.setMonth(newMonth.getMonth() + 1);
                          setCalendarMonth({
                            ...calendarMonth,
                            end: newMonth
                          });
                        }}
                      >
                        »
                      </button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {/* Hari dalam seminggu */}
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}

                      {/* Tanggal */}
                      {Array.from({ length: 42 }, (_, i) => {
                        const currentDate = calendarMonth.end;
                        const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === currentDate.getMonth();
                        const isSelected = endDate && date.getDate() === endDate.getDate() &&
                                          date.getMonth() === endDate.getMonth() &&
                                          date.getFullYear() === endDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();
                        const isBeforeStartDate = startDate && date < startDate;
                        const isDisabled = !isCurrentMonth || isBeforeStartDate;

                        return (
                          <button
                            type="button"
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all
                                      ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-base-200' : ''}
                                      ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={(e) => {
                              e.preventDefault();
                              if (!isDisabled) {
                                // Set tanggal akhir langsung dan refresh data
                                setEndDate(new Date(date));
                              }
                            }}
                            disabled={isDisabled}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="form-control w-full md:w-64">
            <label className="label">
              <span className="label-text text-gray-700">Outlet</span>
            </label>
            <select
              className="select select-bordered w-full h-10"
              value={selectedOutletId}
              onChange={(e) => setSelectedOutletId(e.target.value)}
              aria-label="Pilih Outlet"
            >
              <option value="">Semua Outlet</option>
              {outlets.map((outlet) => (
                <option key={outlet.id} value={outlet.id}>
                  {outlet.name}
                </option>
              ))}
            </select>
          </div>

          <button
            type="button"
            className="btn btn-primary gap-2 w-full md:w-auto h-10 mt-8 md:mt-0"
            onClick={fetchCaptainData}
            disabled={isLoading}
          >
            {isLoading ? <FiRefreshCw className="animate-spin" /> : <FiFilter />}
            {isLoading ? 'Memuat...' : 'Refresh Data'}
          </button>
        </div>
      </motion.div>

      {/* Error Display */}
      {error && (
        <motion.div variants={fadeInUp} className="alert alert-error">
          <FiAlertTriangle />
          <span>{error}</span>
        </motion.div>
      )}

      {/* Loading State */}
      {isLoading && (
        <motion.div variants={fadeInUp} className="flex justify-center py-8">
          <span className="loading loading-spinner loading-lg text-primary"></span>
        </motion.div>
      )}

      {/* Captain Performance Data */}
      {!isLoading && !error && captainData && (
        <>
          {/* Summary Cards */}
          <motion.div variants={fadeInUp} className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Total Kapten</h3>
              <p className="text-3xl font-bold text-primary">
                {captainData?.captainPerformance?.length || 0}
              </p>
            </div>

            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Total Pendapatan Terapis</h3>
              <p className="text-3xl font-bold text-primary">
                {formatCurrency(
                  captainData?.captainPerformance?.reduce(
                    (sum, captain) => sum + captain.totalSales,
                    0
                  ) || 0
                )}
              </p>
            </div>

            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Total Komisi</h3>
              <p className="text-3xl font-bold text-primary">
                {formatCurrency(
                  captainData?.captainPerformance?.reduce(
                    (sum, captain) => sum + captain.totalCommission,
                    0
                  ) || 0
                )}
              </p>
            </div>
          </motion.div>

          {/* Captain Performance Table */}
          <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow overflow-x-auto">
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Kinerja Kapten</h2>
            <p className="text-sm text-gray-600 mb-4">Menampilkan data kinerja kapten dan terapis aktif. Terapis yang telah digabungkan atau dinonaktifkan tidak ditampilkan.</p>

            {!captainData?.captainPerformance || captainData.captainPerformance.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                Tidak ada data kapten untuk periode ini
              </div>
            ) : (
              <table className="table table-zebra w-full">
                <thead>
                  <tr>
                    <th aria-label="Expand/Collapse">Aksi</th>
                    <th className="w-10">#</th>
                    <th>Nama Kapten</th>
                    <th className="text-center">Jumlah Terapis</th>
                    <th className="text-right">
                      Total Pendapatan Terapis
                      <span className="ml-1 text-primary" title="Diurutkan dari tertinggi">▼</span>
                    </th>
                    <th className="text-right">Total Komisi</th>
                  </tr>
                </thead>
                <tbody>
                  {captainData.captainPerformance.map((captain, index) => (
                    <React.Fragment key={captain.captainId}>
                      <tr className="hover cursor-pointer" onClick={() => toggleCaptainExpanded(captain.captainId)}>
                        <td>
                          {expandedCaptains[captain.captainId] ? (
                            <FiChevronUp className="text-primary" />
                          ) : (
                            <FiChevronDown className="text-primary" />
                          )}
                        </td>
                        <td className="font-medium text-center">{index + 1}</td>
                        <td className="font-medium">{captain.captainName}</td>
                        <td className="text-center">{captain.teamSize}</td>
                        <td className="text-right">
                          <div className="flex flex-col items-end">
                            <div>{formatCurrency((() => {
                              // Hitung total pendapatan terapis (harga layanan asli + biaya tambahan)
                              let baseAmount = captain.totalSales;

                              // Jika ada diskon, tambahkan kembali ke total untuk mendapatkan harga asli
                              if (captain.discountAmount && captain.discountAmount > 0) {
                                baseAmount += captain.discountAmount;
                              }

                              return baseAmount;
                            })())}</div>
                            {/* Jika ada biaya tambahan, tampilkan sebagai catatan */}
                            {captain.additionalCharge && captain.additionalCharge > 0 && (
                              <div className="text-xs text-info">
                                +Biaya: {formatCurrency(captain.additionalCharge)}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="text-right">{formatCurrency(captain.totalCommission)}</td>
                      </tr>

                      {/* Expanded Row for Therapist Details */}
                      {expandedCaptains[captain.captainId] && (
                        <tr>
                          <td colSpan={6} className="p-0">
                            <div className="bg-base-200 p-4 rounded-lg m-2">
                              <h4 className="font-medium text-gray-700 mb-2">
                                Tim Terapis {captain.captainName}
                              </h4>
                              <table className="table table-sm w-full">
                                <thead>
                                  <tr>
                                    <th className="w-10">#</th>
                                    <th>Nama Terapis</th>
                                    <th>Outlet</th>
                                    <th className="text-center">Jumlah Transaksi</th>
                                    <th className="text-right">
                                      Total Pendapatan Terapis
                                      <span className="ml-1 text-primary" title="Diurutkan dari tertinggi">▼</span>
                                    </th>
                                    <th className="text-right">Total Komisi</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {captain.therapists.map((therapist, index) => (
                                    <tr key={therapist.therapistId}>
                                      <td className="font-medium text-center">{index + 1}</td>
                                      <td>{therapist.therapistName}</td>
                                      <td>{therapist.outletName}</td>
                                      <td className="text-center">{therapist.transactionCount}</td>
                                      <td className="text-right">
                                        <div className="flex flex-col items-end">
                                          <div>{formatCurrency((() => {
                                            // Hitung total pendapatan terapis (harga layanan asli + biaya tambahan)
                                            let baseAmount = therapist.totalSales;

                                            // Jika ada diskon, tambahkan kembali ke total untuk mendapatkan harga asli
                                            if (therapist.discountAmount && therapist.discountAmount > 0) {
                                              baseAmount += therapist.discountAmount;
                                            }

                                            return baseAmount;
                                          })())}</div>
                                          {/* Jika ada biaya tambahan, tampilkan sebagai catatan */}
                                          {therapist.additionalCharge && therapist.additionalCharge > 0 && (
                                            <div className="text-xs text-info">
                                              +Biaya: {formatCurrency(therapist.additionalCharge)}
                                            </div>
                                          )}
                                        </div>
                                      </td>
                                      <td className="text-right">{formatCurrency(therapist.totalCommission)}</td>
                                    </tr>
                                  ))}
                                  {/* Baris total untuk terapis */}
                                  <tr className="font-medium bg-base-200">
                                    <td colSpan={3} className="text-right">Total Tim ({captain.therapists.length} terapis):</td>
                                    <td className="text-center">{captain.therapists.reduce((sum, t) => sum + t.transactionCount, 0)}</td>
                                    <td className="text-right">{formatCurrency(captain.therapists.reduce((sum, t) => {
                                      // Hitung total pendapatan terapis (harga layanan asli + biaya tambahan)
                                      let baseAmount = t.totalSales;

                                      // Jika ada diskon, tambahkan kembali ke total untuk mendapatkan harga asli
                                      if (t.discountAmount && t.discountAmount > 0) {
                                        baseAmount += t.discountAmount;
                                      }

                                      return sum + baseAmount;
                                    }, 0))}</td>
                                    <td className="text-right">{formatCurrency(captain.therapists.reduce((sum, t) => sum + t.totalCommission, 0))}</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            )}
          </motion.div>
        </>
      )}
    </motion.div>
  );
};

export default CaptainPerformancePage;
