import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserIdFromToken } from '@/lib/auth-utils';
import { logService } from '@/lib/logger';

// PUT untuk mengupdate komisi terapis khusus
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string, commissionId: string } }
) {
  try {
    const serviceId = params.id;
    const commissionId = params.commissionId;
    const body = await request.json();
    const { commission } = body;

    if (!serviceId || !commissionId || commission === undefined) {
      return NextResponse.json(
        { error: 'ID layanan, ID komisi, dan nilai komisi diperlukan' },
        { status: 400 }
      );
    }

    // Validasi nilai komisi
    if (commission === undefined || commission === null) {
      return NextResponse.json(
        { error: 'Nilai komisi harus diisi' },
        { status: 400 }
      );
    }

    // Pastikan nilai komisi adalah angka
    if (typeof commission !== 'number') {
      return NextResponse.json(
        { error: 'Nilai komisi harus berupa angka' },
        { status: 400 }
      );
    }

    // Pastikan nilai komisi tidak negatif
    if (commission < 0) {
      return NextResponse.json(
        { error: 'Nilai komisi tidak boleh negatif' },
        { status: 400 }
      );
    }

    // Cek apakah komisi terapis khusus ada
    const existingCommission = await prisma.therapistServiceCommission.findUnique({
      where: { id: commissionId },
      include: {
        therapist: {
          select: {
            name: true,
            outlet: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        service: {
          select: {
            name: true
          }
        }
      }
    });

    if (!existingCommission) {
      return NextResponse.json(
        { error: 'Komisi terapis khusus tidak ditemukan' },
        { status: 404 }
      );
    }

    // Verifikasi bahwa komisi ini memang untuk layanan yang dimaksud
    if (existingCommission.serviceId !== serviceId) {
      return NextResponse.json(
        { error: 'Komisi terapis khusus ini bukan untuk layanan yang dimaksud' },
        { status: 400 }
      );
    }

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Update komisi terapis khusus
    const updatedCommission = await prisma.therapistServiceCommission.update({
      where: { id: commissionId },
      data: { commission },
      include: {
        therapist: {
          select: {
            name: true,
            outlet: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        service: {
          select: {
            name: true
          }
        }
      }
    });

    // Log update komisi terapis khusus
    await logService(
      'update',
      serviceId,
      {
        action: 'update_therapist_commission',
        therapistId: existingCommission.therapistId,
        therapistName: existingCommission.therapist.name,
        serviceName: existingCommission.service.name,
        oldCommission: existingCommission.commission,
        newCommission: commission
      },
      existingCommission.therapist.outlet.id,
      userId
    );

    return NextResponse.json({
      message: 'Komisi terapis khusus berhasil diupdate',
      therapistCommission: updatedCommission
    });
  } catch (error) {
    console.error('Error updating therapist commission:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengupdate komisi terapis khusus' },
      { status: 500 }
    );
  }
}

// DELETE untuk menghapus komisi terapis khusus
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string, commissionId: string } }
) {
  try {
    const serviceId = params.id;
    const commissionId = params.commissionId;

    if (!serviceId || !commissionId) {
      return NextResponse.json(
        { error: 'ID layanan dan ID komisi diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah komisi terapis khusus ada
    const existingCommission = await prisma.therapistServiceCommission.findUnique({
      where: { id: commissionId },
      include: {
        therapist: {
          select: {
            name: true,
            outlet: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        service: {
          select: {
            name: true
          }
        }
      }
    });

    if (!existingCommission) {
      return NextResponse.json(
        { error: 'Komisi terapis khusus tidak ditemukan' },
        { status: 404 }
      );
    }

    // Verifikasi bahwa komisi ini memang untuk layanan yang dimaksud
    if (existingCommission.serviceId !== serviceId) {
      return NextResponse.json(
        { error: 'Komisi terapis khusus ini bukan untuk layanan yang dimaksud' },
        { status: 400 }
      );
    }

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Hapus komisi terapis khusus
    await prisma.therapistServiceCommission.delete({
      where: { id: commissionId }
    });

    // Log penghapusan komisi terapis khusus
    await logService(
      'update',
      serviceId,
      {
        action: 'delete_therapist_commission',
        therapistId: existingCommission.therapistId,
        therapistName: existingCommission.therapist.name,
        serviceName: existingCommission.service.name,
        commission: existingCommission.commission
      },
      existingCommission.therapist.outlet.id,
      userId
    );

    return NextResponse.json({
      message: 'Komisi terapis khusus berhasil dihapus'
    });
  } catch (error) {
    console.error('Error deleting therapist commission:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus komisi terapis khusus' },
      { status: 500 }
    );
  }
}
