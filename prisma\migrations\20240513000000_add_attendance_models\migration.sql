-- CreateTable
CREATE TABLE "TherapistAttendance" (
    "id" TEXT NOT NULL,
    "therapistId" TEXT NOT NULL,
    "outletId" TEXT NOT NULL,
    "attendanceType" TEXT NOT NULL,
    "attendanceTime" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "notes" TEXT,

    CONSTRAINT "TherapistAttendance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserAttendance" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "attendanceType" TEXT NOT NULL,
    "attendanceTime" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "notes" TEXT,

    CONSTRAINT "UserAttendance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AttendanceQueue" (
    "id" TEXT NOT NULL,
    "therapistId" TEXT NOT NULL,
    "outletId" TEXT NOT NULL,
    "queuePosition" INTEGER NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'WAITING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AttendanceQueue_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "TherapistAttendance_therapistId_idx" ON "TherapistAttendance"("therapistId");

-- CreateIndex
CREATE INDEX "TherapistAttendance_outletId_idx" ON "TherapistAttendance"("outletId");

-- CreateIndex
CREATE INDEX "TherapistAttendance_attendanceTime_idx" ON "TherapistAttendance"("attendanceTime");

-- CreateIndex
CREATE INDEX "UserAttendance_userId_idx" ON "UserAttendance"("userId");

-- CreateIndex
CREATE INDEX "UserAttendance_attendanceTime_idx" ON "UserAttendance"("attendanceTime");

-- CreateIndex
CREATE INDEX "AttendanceQueue_therapistId_idx" ON "AttendanceQueue"("therapistId");

-- CreateIndex
CREATE INDEX "AttendanceQueue_outletId_idx" ON "AttendanceQueue"("outletId");

-- CreateIndex
CREATE INDEX "AttendanceQueue_queuePosition_idx" ON "AttendanceQueue"("queuePosition");

-- CreateIndex
CREATE UNIQUE INDEX "AttendanceQueue_therapistId_outletId_key" ON "AttendanceQueue"("therapistId", "outletId");

-- AddForeignKey
ALTER TABLE "TherapistAttendance" ADD CONSTRAINT "TherapistAttendance_therapistId_fkey" FOREIGN KEY ("therapistId") REFERENCES "Therapist"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TherapistAttendance" ADD CONSTRAINT "TherapistAttendance_outletId_fkey" FOREIGN KEY ("outletId") REFERENCES "Outlet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserAttendance" ADD CONSTRAINT "UserAttendance_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AttendanceQueue" ADD CONSTRAINT "AttendanceQueue_therapistId_fkey" FOREIGN KEY ("therapistId") REFERENCES "Therapist"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AttendanceQueue" ADD CONSTRAINT "AttendanceQueue_outletId_fkey" FOREIGN KEY ("outletId") REFERENCES "Outlet"("id") ON DELETE CASCADE ON UPDATE CASCADE;
