'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useState, useEffect } from 'react';

// Interface untuk permission
interface Permission {
  module: string;
  canCreate: boolean;
  canRead: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

// Interface untuk user dengan permissions
interface UserWithPermissions {
  id: string;
  name: string;
  email: string;
  role: string;
  permissions?: Permission[];
}

export function usePermission() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isLoadingPermissions, setIsLoadingPermissions] = useState(true);
  const [permissionCache, setPermissionCache] = useState<Record<string, boolean>>({});
  const [hasInitialPermissions, setHasInitialPermissions] = useState(false);

  // Gunakan localStorage untuk caching permission
  useEffect(() => {
    // Admin selalu punya akses penuh, tidak perlu load permissions
    if (user?.role === 'ADMIN') {
      setIsLoadingPermissions(false);
      setHasInitialPermissions(true);
      return;
    }

    // Coba ambil dari localStorage dulu untuk mengurangi waktu loading
    if (user?.id) {
      try {
        const cachedPermissions = localStorage.getItem(`user_permissions_${user.id}`);
        if (cachedPermissions) {
          const parsedPermissions = JSON.parse(cachedPermissions);
          setPermissions(parsedPermissions);
          // Tetap set loading false karena sudah ada data dari cache
          setIsLoadingPermissions(false);
          setHasInitialPermissions(true);
        }
      } catch (error) {
        console.error('Error reading cached permissions:', error);
      }
    }
  }, [user?.id, user?.role]);

  useEffect(() => {
    const fetchUserPermissions = async () => {
      // Jika user adalah ADMIN, tidak perlu fetch permissions
      if (user?.role === 'ADMIN') {
        setIsLoadingPermissions(false);
        setHasInitialPermissions(true);
        return;
      }

      // Jika sudah ada initial permissions, tidak perlu fetch lagi
      if (hasInitialPermissions) {
        setIsLoadingPermissions(false);
        return;
      }

      if (!isAuthenticated || !user || isLoading) {
        setIsLoadingPermissions(false);
        return;
      }

      try {
        // Fetch user dengan permissions
        const response = await fetch(`/api/users/${user.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch user permissions');
        }

        const data = await response.json();
        const userWithPermissions = data.user as UserWithPermissions;

        if (userWithPermissions.permissions) {
          setPermissions(userWithPermissions.permissions);

          // Simpan ke localStorage untuk caching
          try {
            localStorage.setItem(
              `user_permissions_${user.id}`,
              JSON.stringify(userWithPermissions.permissions)
            );
          } catch (error) {
            console.error('Error caching permissions:', error);
          }

          // Set hasInitialPermissions ke true karena sudah ada data
          setHasInitialPermissions(true);
        } else {
          // Jika tidak ada permissions, set array kosong
          setPermissions([]);
          setHasInitialPermissions(true); // Tetap set true karena proses sudah selesai
        }
      } catch (error) {
        console.error('Error fetching permissions:', error);
        // Set permissions kosong jika terjadi error
        setPermissions([]);
        setHasInitialPermissions(true); // Tetap set true karena proses sudah selesai
      } finally {
        setIsLoadingPermissions(false);
      }
    };

    fetchUserPermissions();
  }, [user, isAuthenticated, isLoading, hasInitialPermissions]);

  // Fungsi untuk mengecek permission dengan caching
  const checkPermission = (module: string, action: 'create' | 'read' | 'update' | 'delete'): boolean => {
    // Admin selalu punya akses penuh
    if (user?.role === 'ADMIN') {
      return true;
    }

    // Optimasi untuk role lain
    // STAFF selalu punya akses read ke dashboard
    if (user?.role === 'STAFF' && module === 'dashboard' && action === 'read') {
      return true;
    }

    // MANAGER punya akses penuh ke banyak modul kecuali users dan settings
    if (user?.role === 'MANAGER') {
      if (action === 'read') return true; // MANAGER selalu bisa read semua modul
      if (!['users', 'settings'].includes(module)) {
        return true; // MANAGER punya akses penuh ke modul lain
      }
    }

    // INVESTOR hanya punya akses read ke dashboard dan reports
    if (user?.role === 'INVESTOR') {
      if (action === 'read' && ['dashboard', 'reports'].includes(module)) {
        return true;
      }
      return false; // INVESTOR tidak punya akses lain
    }

    // Jika masih loading, return false
    if (isLoadingPermissions || !isAuthenticated) {
      return false;
    }

    // Cek cache dulu untuk performa
    const cacheKey = `${module}_${action}`;
    if (permissionCache[cacheKey] !== undefined) {
      return permissionCache[cacheKey];
    }

    // Cari permission untuk module yang diminta
    const modulePermission = permissions.find(p => p.module === module);
    if (!modulePermission) {
      // Simpan ke cache
      setPermissionCache(prev => ({ ...prev, [cacheKey]: false }));
      return false;
    }

    // Cek permission berdasarkan action
    let result = false;
    switch (action) {
      case 'create':
        result = modulePermission.canCreate;
        break;
      case 'read':
        result = modulePermission.canRead;
        break;
      case 'update':
        result = modulePermission.canUpdate;
        break;
      case 'delete':
        result = modulePermission.canDelete;
        break;
    }

    // Simpan ke cache
    setPermissionCache(prev => ({ ...prev, [cacheKey]: result }));
    return result;
  };

  return {
    checkPermission,
    isLoadingPermissions,
    permissions,
    user,
    hasInitialPermissions,
  };
}
