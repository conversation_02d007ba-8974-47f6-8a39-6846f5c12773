const { PrismaClient } = require('../prisma/generated/client');

const prisma = new PrismaClient();

async function seedInventorySetiaBudi() {
  const outletId = 'palu-setiabudi'; // ID outlet Setia Budi
  
  console.log('🏪 Memulai seeding inventori untuk outlet Setia Budi...');
  console.log(`📍 Outlet ID: ${outletId}`);
  
  // Cari admin user untuk createdById
  const adminUser = await prisma.user.findFirst({
    where: { role: 'ADMIN' }
  });
  
  if (!adminUser) {
    throw new Error('Admin user tidak ditemukan. Pastikan seeding user sudah dijalankan.');
  }
  
  console.log(`👤 Menggunakan admin user: ${adminUser.email}`);
  
  let totalCategories = 0;
  let totalItems = 0;

  try {
    // Buat kategori inventori berdasarkan area
    const categories = [
      { name: 'Area Depan/Parkiran', description: 'Item-item yang berada di area depan dan parkiran outlet' },
      { name: '<PERSON><PERSON>gu/Kasir', description: 'Item-item yang berada di area ruang tunggu dan kasir' },
      { name: '<PERSON><PERSON> Cowok', description: 'Item-item yang berada di ruang treatment pria' },
      { name: 'Ruang Cewek', description: 'Item-item yang berada di ruang treatment wanita' },
      { name: 'Ruang AO', description: 'Item-item yang berada di ruang AO (Area Operasional)' },
      { name: 'Ruang Office', description: 'Item-item yang berada di ruang kantor' },
      { name: 'Lantai 2', description: 'Item-item yang berada di lantai 2' }
    ];

    console.log('Membuat kategori inventori...');
    const createdCategories = {};
    
    for (const category of categories) {
      const createdCategory = await prisma.inventoryCategory.upsert({
        where: { name: category.name },
        update: { description: category.description },
        create: {
          name: category.name,
          description: category.description,
          isActive: true
        }
      });
      createdCategories[category.name] = createdCategory;
      console.log(`✅ Kategori "${category.name}" berhasil dibuat`);
    }

    // Data inventori berdasarkan area
    const inventoryData = {
      'Area Depan/Parkiran': [
        { name: 'LAMPU', quantity: 3 },
        { name: 'PAPAN BANNER', quantity: 1 },
        { name: 'NEON BOX', quantity: 1 },
        { name: 'LAMPU SOROT', quantity: 1 },
        { name: 'RAK HELM', quantity: 1 },
        { name: 'SAPU LIDI', quantity: 1 },
        { name: 'SKOP SAMPAH', quantity: 1 },
        { name: 'CCTV', quantity: 1 },
        { name: 'KESET', quantity: 2 }
      ],
      'Ruang Tunggu/Kasir': [
        { name: 'AC', quantity: 1 },
        { name: 'KURSI TUNGGU', quantity: 1 },
        { name: 'KURSI PLASTIK', quantity: 8 },
        { name: 'MEJA KASIR', quantity: 1 },
        { name: 'TONG SAMPAH', quantity: 2 },
        { name: 'BELL', quantity: 1 },
        { name: 'VAS BUNGA', quantity: 2 },
        { name: 'TEMPAT TISSU', quantity: 1 },
        { name: 'JAM DINDING', quantity: 1 },
        { name: 'MESIN EDC ABC', quantity: 1 },
        { name: 'CERMIN', quantity: 3 },
        { name: 'TEMPAT PULPEN', quantity: 1 },
        { name: 'GUNTING', quantity: 1 },
        { name: 'RAK TEMPAT UANG', quantity: 1 },
        { name: 'KERANJANG MINUMAN', quantity: 1 },
        { name: 'TEKO JAHE', quantity: 1 },
        { name: 'OPEN/CLOSE', quantity: 1 },
        { name: 'KANEBO', quantity: 1 },
        { name: 'HIASAN DINDING', quantity: 4 },
        { name: 'KERANJANG KASIR', quantity: 3 },
        { name: 'DIFUSHER', quantity: 1 },
        { name: 'LIST HARGA', quantity: 1 },
        { name: 'BINGKAI LIST PROMO', quantity: 1 },
        { name: 'CCTV', quantity: 2 },
        { name: 'LAMPU', quantity: 6 },
        { name: 'PELUBANG KERTAS', quantity: 1 },
        { name: 'WIFI', quantity: 1 },
        { name: 'COLOKAN', quantity: 3 },
        { name: 'TALENAN SEGI PANJANG', quantity: 1 }
      ],
      'Ruang Cowok': [
        { name: 'BED', quantity: 5 },
        { name: 'KASUR', quantity: 5 },
        { name: 'BUSA', quantity: 5 },
        { name: 'BANTAL', quantity: 5 },
        { name: 'KURSI PLASTIK', quantity: 5 },
        { name: 'KERANJANG TEMPAT SEKAPAN', quantity: 5 },
        { name: 'ANTI NYAMUK ELECTRIK', quantity: 1 },
        { name: 'AC', quantity: 1 },
        { name: 'LAMPU PUTIH', quantity: 2 },
        { name: 'LAMPU KUNING', quantity: 2 },
        { name: 'GORDEN BED', quantity: 4 },
        { name: 'BESI GORDEN', quantity: 4 },
        { name: 'SOUND SYTEM', quantity: 1 },
        { name: 'SALON', quantity: 1 },
        { name: 'JAM DINDING', quantity: 1 },
        { name: 'GANTUNGAN BAJU TAMU', quantity: 5 },
        { name: 'COLOKAN', quantity: 3 }
      ],
      'Ruang Cewek': [
        { name: 'BED', quantity: 5 },
        { name: 'KASUR', quantity: 5 },
        { name: 'BUSA', quantity: 5 },
        { name: 'BANTAL', quantity: 5 },
        { name: 'KURSI PLASTIK', quantity: 5 },
        { name: 'KERANJANG TEMPAT SEKAPAN', quantity: 5 },
        { name: 'ANTI NYAMUK ELECTRIK', quantity: 1 },
        { name: 'AC', quantity: 1 },
        { name: 'LAMPU PUTIH', quantity: 1 },
        { name: 'LAMPU KUNING', quantity: 3 },
        { name: 'GORDEN BED', quantity: 4 },
        { name: 'BESI GORDEN', quantity: 4 },
        { name: 'SALON', quantity: 1 },
        { name: 'GORDEN PINTU', quantity: 2 },
        { name: 'JAM DINDING', quantity: 1 },
        { name: 'GANTUNGAN BAJU TAMU', quantity: 5 },
        { name: 'COLOKAN', quantity: 1 }
      ],
      'Ruang AO': [
        { name: 'KAIN BALI', quantity: 31 },
        { name: 'HANDUK PIJAT', quantity: 97 },
        { name: 'WASHLAP', quantity: 9 },
        { name: 'HANDUK KRETEK', quantity: 5 },
        { name: 'KAIN SREI', quantity: 18 },
        { name: 'KAIN PENGALAS LUBANG BED', quantity: 23 },
        { name: 'BANDOW', quantity: 6 },
        { name: 'SARUNG BANTAL', quantity: 30 },
        { name: 'TERMOS', quantity: 4 },
        { name: 'CEREK', quantity: 1 },
        { name: 'KOMPOR', quantity: 1 },
        { name: 'GAS', quantity: 1 },
        { name: 'GALON', quantity: 6 },
        { name: 'CERMIN', quantity: 2 },
        { name: 'TONG SAMPAH', quantity: 3 },
        { name: 'SETRIKA', quantity: 1 },
        { name: 'MESIN CUCI', quantity: 1 },
        { name: 'RENDAMAN KAKI', quantity: 9 },
        { name: 'LOYANG KECIL', quantity: 13 },
        { name: 'SERBET', quantity: 2 },
        { name: 'MANGKUK KRIM', quantity: 11 },
        { name: 'SAPU IJUK', quantity: 2 },
        { name: 'SIKAT WC', quantity: 2 },
        { name: 'GAYUNG', quantity: 2 },
        { name: 'ALAT PELL', quantity: 1 },
        { name: 'KERANJANG MINYAK', quantity: 24 },
        { name: 'GELAS TAKAR', quantity: 1 },
        { name: 'FLUGER', quantity: 1 },
        { name: 'GANTUNGAN BAJU TAMU WC', quantity: 1 },
        { name: 'TEMPAT KRIM', quantity: 1 },
        { name: 'KERANJANG PAKAIAN', quantity: 1 },
        { name: 'BOTOL PARFUM RUANGAN', quantity: 1 },
        { name: 'BOTOL PARFUM LAUNDRY', quantity: 1 },
        { name: 'KESET', quantity: 2 },
        { name: 'JAS HUJAN', quantity: 3 },
        { name: 'TAS HS', quantity: 4 },
        { name: 'PAYUNG', quantity: 1 },
        { name: 'KOTAK P3K', quantity: 1 },
        { name: 'LAMPU', quantity: 4 },
        { name: 'SISIR', quantity: 1 },
        { name: 'TEMPAT LULUR', quantity: 2 },
        { name: 'KEMBEN PLISKET', quantity: 23 },
        { name: 'TALENAN SEGI PANJANG', quantity: 1 },
        { name: 'TALENAN SEGI EMPAT', quantity: 1 },
        { name: 'COLOKAN', quantity: 3 },
        { name: 'LOYANG BESAR', quantity: 2 },
        { name: 'SAPU KASUR', quantity: 2 },
        { name: 'DAP AIR', quantity: 1 },
        { name: 'LEMARI', quantity: 2 },
        { name: 'BOTOL SABUN TEMPAT CUCI TANGAN', quantity: 1 },
        { name: 'DISPENSER', quantity: 1 },
        { name: 'TEMPAT MINYAK', quantity: 41 },
        { name: 'TEMAPT HS', quantity: 3 },
        { name: 'HT', quantity: 2 }
      ],
      'Ruang Office': [
        { name: 'MEJA RAPAT', quantity: 2 },
        { name: 'KURSI PLASTIK', quantity: 10 },
        { name: 'KURSI RAPAT', quantity: 7 },
        { name: 'AC', quantity: 2 },
        { name: 'GANTUNGAN BAJU WC', quantity: 1 },
        { name: 'BRANGKAS', quantity: 1 },
        { name: 'LAMPU', quantity: 4 },
        { name: 'TEMPAT TISSU', quantity: 1 },
        { name: 'KIPAS', quantity: 1 },
        { name: 'COLOKAN', quantity: 3 },
        { name: 'PRIUT', quantity: 1 },
        { name: 'BEANBAG', quantity: 4 },
        { name: 'RUMPUT SLUTESIS', quantity: 4 },
        { name: 'POMPA GALON/DISPENSER', quantity: 1 },
        { name: 'TANGGA', quantity: 1 },
        { name: 'RAK BUKU', quantity: 6 },
        { name: 'KERANJANG SUSUN', quantity: 1 },
        { name: 'TV', quantity: 1 },
        { name: 'MEJA KANTOR HITAM', quantity: 2 },
        { name: 'TONG SAMPAH', quantity: 4 },
        { name: 'GAYUNG', quantity: 1 },
        { name: 'JAM DINDING', quantity: 1 },
        { name: 'BOTOL SABUN CUCI TANGAN', quantity: 1 },
        { name: 'PAPAN TULIS', quantity: 2 },
        { name: 'LEMARI BARANG', quantity: 1 }
      ],
      'Lantai 2': [
        { name: 'KASUR', quantity: 2 },
        { name: 'BUSA', quantity: 2 },
        { name: 'BED', quantity: 2 },
        { name: 'GANTUNGAN BAJU TAMU', quantity: 2 },
        { name: 'LAMPU KUNING', quantity: 2 },
        { name: 'KURSI PLASTIK', quantity: 2 },
        { name: 'BANTAL', quantity: 2 }
      ]
    };

    // Buat item inventori untuk setiap kategori
    console.log('Membuat item inventori...');
    let totalItems = 0;

    for (const [categoryName, items] of Object.entries(inventoryData)) {
      const category = createdCategories[categoryName];
      console.log(`\n📦 Membuat item untuk kategori: ${categoryName}`);

      for (const item of items) {
        // Buat item inventori
        const createdItem = await prisma.inventoryItem.create({
          data: {
            name: item.name,
            description: `${item.name} yang berada di area ${categoryName}`,
            categoryId: category.id,
            outletId: outletId,
            totalQuantity: item.quantity,
            goodCondition: item.quantity, // Asumsikan semua dalam kondisi baik
            damagedCondition: 0,
            lostCondition: 0,
            remainingQuantity: item.quantity,
            minStockLevel: Math.max(1, Math.floor(item.quantity * 0.2)), // 20% dari quantity sebagai minimum stock
            maxStockLevel: item.quantity * 2,
            unitPrice: 0,
            notes: `Item inventori di area ${categoryName}`,
            createdById: adminUser.id // Menggunakan admin sebagai creator
          }
        });

        // Buat movement record untuk stock awal
        await prisma.inventoryMovement.create({
          data: {
            itemId: createdItem.id,
            movementType: 'IN',
            quantity: item.quantity,
            reason: 'INITIAL_STOCK',
            notes: `Stock awal - ${item.name}`,
            createdById: adminUser.id
          }
        });

        console.log(`  ✅ ${item.name} (${item.quantity} pcs)`);
        totalItems++;
      }
    }

    console.log(`\n🎉 Seeding inventori berhasil!`);
    console.log(`📊 Total kategori dibuat: ${Object.keys(createdCategories).length}`);
    console.log(`📦 Total item dibuat: ${totalItems}`);
    console.log(`🏢 Outlet: Breaktime Palu Setia Budi (${outletId})`);

  } catch (error) {
    console.error('❌ Error saat seeding inventori:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Jalankan seeding
seedInventorySetiaBudi()
  .catch((e) => {
    console.error('❌ Seeding gagal:', e);
    process.exit(1);
  });