'use client';

import React, { useRef, useEffect } from 'react';
import {
  AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  Legend, LineChart, Line
} from 'recharts';
import { format, parseISO } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';

// Format tanggal untuk tampilan
const formatDateDisplay = (dateStr: string): string => {
  try {
    const date = parseISO(dateStr);
    return format(date, 'dd MMM', { locale: idLocale });
  } catch (e) {
    return dateStr;
  }
};

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('id-ID', { 
    style: 'currency', 
    currency: 'IDR', 
    minimumFractionDigits: 0 
  }).format(value);
};

interface RevenueChartProps {
  data: Array<{
    date: string;
    revenue: number;
    prediction?: number;
    previousPeriod?: number;
  }>;
  showPrediction?: boolean;
  showComparison?: boolean;
  title?: string;
  height?: number;
  className?: string;
}

const RevenueChart: React.FC<RevenueChartProps> = ({
  data,
  showPrediction = false,
  showComparison = false,
  title = 'Pendapatan',
  height = 300,
  className = ''
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  // Prepare chart data
  const chartData = data.map(item => ({
    date: item.date,
    Pendapatan: item.revenue,
    Prediksi: showPrediction ? item.prediction : undefined,
    'Periode Sebelumnya': showComparison ? item.previousPeriod : undefined,
    // Format tanggal untuk tooltip
    formattedDate: formatDateDisplay(item.date)
  }));

  return (
    <div className={`w-full ${className}`}>
      {title && <h3 className="text-lg font-semibold mb-2">{title}</h3>}
      <div ref={chartRef} style={{ width: '100%', height: `${height}px` }}>
        <ResponsiveContainer>
          <AreaChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <defs>
              <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
              </linearGradient>
              {showPrediction && (
                <linearGradient id="colorPrediction" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                </linearGradient>
              )}
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
            <XAxis 
              dataKey="formattedDate" 
              tick={{ fill: '#6b7280' }} 
              tickLine={{ stroke: '#e0e0e0' }}
            />
            <YAxis 
              tickFormatter={(value) => new Intl.NumberFormat('id-ID', { 
                notation: 'compact', 
                compactDisplay: 'short' 
              }).format(value)} 
              tick={{ fill: '#6b7280' }}
              tickLine={{ stroke: '#e0e0e0' }}
            />
            <Tooltip 
              formatter={(value: number) => [formatCurrency(value), '']}
              labelFormatter={(label) => `Tanggal: ${label}`}
            />
            <Legend />
            <Area 
              type="monotone" 
              dataKey="Pendapatan" 
              stroke="#10b981" 
              fillOpacity={1}
              fill="url(#colorRevenue)" 
              activeDot={{ r: 8 }}
            />
            {showPrediction && (
              <Area 
                type="monotone" 
                dataKey="Prediksi" 
                stroke="#3b82f6" 
                fillOpacity={0.5}
                fill="url(#colorPrediction)" 
                strokeDasharray="5 5"
              />
            )}
            {showComparison && (
              <Line 
                type="monotone" 
                dataKey="Periode Sebelumnya" 
                stroke="#f59e0b" 
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 6 }}
              />
            )}
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default RevenueChart;
