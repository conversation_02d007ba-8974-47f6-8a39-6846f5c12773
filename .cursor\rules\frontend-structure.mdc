---
description:
globs:
alwaysApply: true
---
# Panduan Struktur Frontend 🖥️

Kode frontend utama berada di direktori `src`.

- **<PERSON><PERSON> (Pages)**: <PERSON><PERSON> utama aplikasi (menggunakan App Router) ada di `src/app/`.
    - <PERSON><PERSON> Login: `[login/page.tsx](mdc:src/app/auth/login/page.tsx)`
    - Dashboard utama: `[page.tsx](mdc:src/app/dashboard/page.tsx)`
    - Sub-halaman dashboard (contoh): `[booking/page.tsx](mdc:src/app/dashboard/booking/page.tsx)`, `[pelanggan/page.tsx](mdc:src/app/dashboard/pelanggan/page.tsx)`
    - Halaman pemilihan outlet: `[page.tsx](mdc:src/app/outlet-selection/page.tsx)`
    - Halaman cetak struk: `[receipt/page.tsx](mdc:src/app/print/receipt/page.tsx)`
- **<PERSON>mponen (Components)**: Komponen UI yang dapat digunakan kembali ada di `src/components/`.
    - Komponen UI dasar: `src/components/ui/`
    - Komponen layout: `src/components/layout/`
    - Komponen spesifik dashboard: `src/components/dashboard/`
- **Konteks (Contexts)**: React Contexts untuk manajemen state global ada di `src/contexts/`.
- **Hooks**: Custom React Hooks ada di `src/hooks/`.
- **Library/Utils**: Fungsi utilitas dan logika bisnis bersama ada di `src/lib/`.
- **Styles**: Gaya global atau tema ada di `src/styles/`.