import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// PUT untuk mengupdate poin customer
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Await params untuk menghindari error
    const id = params.id;

    // <PERSON>ani kemung<PERSON> body kosong atau error parsing JSON
    let body;
    try {
      body = await request.json();
    } catch (e) {
      console.error("Error parsing JSON body:", e);
      return NextResponse.json(
        { error: 'Format JSON tidak valid' },
        { status: 400 }
      );
    }

    const { points } = body || {};

    // Log untuk debugging
    console.log('Request body:', body);
    console.log('ID:', id);
    console.log('Points:', points);

    if (!id) {
      return NextResponse.json(
        { error: 'ID customer diperlukan' },
        { status: 400 }
      );
    }

    if (points === undefined || isNaN(Number(points))) {
      return NextResponse.json(
        { error: 'Poin harus berupa angka yang valid' },
        { status: 400 }
      );
    }

    // Cek apakah customer ada
    const existingCustomer = await prisma.customer.findUnique({
      where: {
        id
      }
    });

    if (!existingCustomer) {
      return NextResponse.json(
        { error: 'Customer tidak ditemukan' },
        { status: 404 }
      );
    }

    // Update poin customer dengan try/catch terpisah
    try {
      const updatedCustomer = await prisma.customer.update({
        where: {
          id
        },
        data: {
          points: Number(points)
        }
      });

      return NextResponse.json({
        message: 'Poin customer berhasil diupdate',
        customer: updatedCustomer
      });
    } catch (updateError) {
      console.error('Error in prisma update:', updateError);
      return NextResponse.json(
        { error: 'Gagal memperbarui poin customer di database' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error updating customer points:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengupdate poin customer' },
      { status: 500 }
    );
  }
}