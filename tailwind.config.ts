import type { Config } from "tailwindcss";

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      screens: {
        'xs': '480px',
      },
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        primary: "#4e9e97",
        secondary: "#f4bb45",
        "primary-light": "#65b5ae",
        "primary-dark": "#3a7a75",
        "secondary-light": "#ffd06b",
        "secondary-dark": "#d99a20",
      },
    },
  },
  plugins: [require("daisyui")],
  daisyui: {
    themes: [
      {
        breaktime: {
          "primary": "#4e9e97",
          "primary-content": "#ffffff",
          "secondary": "#f4bb45",
          "secondary-content": "#ffffff",
          "accent": "#65b5ae",
          "neutral": "#374151",
          "base-100": "#ffffff",
          "base-200": "#f5f7f8",
          "base-300": "#e8edef",
          "base-content": "#374151",
          "info": "#3abff8",
          "success": "#36d399",
          "warning": "#fbbd23",
          "error": "#f87272",
        }
      },
      "light",
      "dark",
    ],
  },
} satisfies Config;
