import React from 'react';
import Image from 'next/image';
import './receipt.css'; // Import style khusus untuk receipt
import './print-receipt.css'; // Import style khusus untuk pencetakan

// Update tipe data untuk memasukkan diskon, charge, dan split bill
interface SplitPaymentData {
  firstMethod: string;
  secondMethod: string;
  cashAmount: number;
  changeAmount: number;
}

export interface ReceiptProps {
  outletName: string;
  outletAddress?: string | null;
  outletPhone?: string | null;
  transactionId: string;
  dateTime: string;
  customerName: string;
  customers?: { id: string; name: string; phone: string }[]; // Tambahkan daftar pelanggan (opsional)
  therapistName: string;
  items: { name: string; price: number; quantity: number }[];
  subtotal: number;
  discountType: 'percentage' | 'fixed' | 'none';
  discountValue: number;
  discountAmount: number;
  additionalCharge: number;
  tax?: number;
  total: number;
  paymentMethod: string;
  splitPayment?: SplitPaymentData; // Data split bill (opsional)
  note?: string;
  createdByName?: string; // <PERSON>a kasir yang membuat transaksi
}

// Gunakan forwardRef agar react-to-print bisa mendapatkan ref DOM
const ReceiptComponent = React.forwardRef<HTMLDivElement, ReceiptProps>((
  {
    outletName,
    outletAddress,
    outletPhone,
    transactionId, dateTime, customerName, customers = [],
    therapistName, items, subtotal,
    discountType, discountValue, discountAmount, additionalCharge,
    tax = 0, total, paymentMethod, splitPayment, note, createdByName
  },
  ref
) => {
  return (
    <div
      ref={ref}
      id="receipt-to-print"
      className="receipt-container p-2 bg-white text-black text-xs font-mono"
      style={{ width: '58mm', maxWidth: '58mm', margin: '0 auto' }}
    >
      {/* Header */}
      <div className="text-center mb-2">
        <Image
          src="/logo.png"
          alt="Breaktime Logo"
          width={30}
          height={30}
          className="mx-auto mb-1"
          priority
        />
        <h2 className="text-sm font-bold uppercase">{outletName}</h2>
        <p className="text-[0.65rem] leading-tight">
          {outletAddress || 'Alamat Outlet Tidak Tersedia'}
        </p>
        <p className="text-[0.65rem] leading-tight">
          {outletPhone ? `Telp: ${outletPhone}` : 'Telepon Outlet Tidak Tersedia'}
        </p>
      </div>

      {/* Info Transaksi */}
      <div className="mb-2 text-[0.7rem] border-t border-b border-dashed border-black py-1">
        <div className="flex justify-between">
          <span>No: {transactionId}</span>
          <span>{dateTime}</span>
        </div>
        <div className="flex justify-between">
          <span>Kasir: {createdByName || 'Admin'}</span>
          <span>Plg: {customerName}</span>
        </div>
        <div>Terapis: {therapistName}</div>

        {customers && customers.length > 1 && (
          <div className="mt-1 border-t border-dotted border-black pt-1">
            <div>Pelanggan:</div>
            {customers.map((customer, idx) => (
              <div key={customer.id} className="pl-2 text-[0.65rem]">
                {idx + 1}. {customer.name}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Item List */}
      <div className="mb-2 text-[0.7rem]">
        {items && items.length > 0 ? (
          <>
            <div className="border-b border-dotted border-black pb-1 mb-2">
              <div className="flex justify-between font-bold">
                <span>Layanan</span>
                <span>Harga</span>
              </div>
            </div>
            {items.map((item, index) => (
              <div key={`item-${index}`} className="mb-1">
                <div className="flex justify-between">
                  <span className="pr-1">{item.name}</span>
                  <span>{(item.price * item.quantity).toLocaleString('id-ID')}</span>
                </div>
                <div className="text-xs text-gray-600 pl-2">
                  {item.quantity} x {item.price.toLocaleString('id-ID')}
                </div>
              </div>
            ))}
          </>
        ) : (
          <div className="text-center text-gray-500 py-2">Tidak ada detail item</div>
        )}
      </div>

      {/* Total (MODIFIED) */}
      <div className="border-t border-dashed border-black pt-1 text-[0.7rem]">
        {/* Subtotal - harga layanan sebelum diskon dan biaya tambahan */}
        <div className="flex justify-between">
          <span>Subtotal</span>
          <span>{subtotal.toLocaleString('id-ID')}</span>
        </div>

        {/* Diskon - hanya tampilkan jika ada */}
        {discountAmount > 0 && (
           <div className="flex justify-between">
             <span>Diskon {discountType === 'percentage' ? `(${discountValue}%)` : ''}</span>
             <span>- {discountAmount.toLocaleString('id-ID')}</span>
           </div>
        )}

        {/* Biaya Tambahan - hanya tampilkan jika ada */}
        {additionalCharge > 0 && (
           <div className="flex justify-between">
             <span>Biaya Tambahan</span>
             <span>+ {additionalCharge.toLocaleString('id-ID')}</span>
           </div>
        )}

        {/* Pajak - hanya tampilkan jika ada */}
        {tax > 0 && (
          <div className="flex justify-between">
            <span>Pajak</span>
            <span>{tax.toLocaleString('id-ID')}</span>
          </div>
        )}

        {/* Garis pemisah sebelum total */}
        {(discountAmount > 0 || additionalCharge > 0 || tax > 0) && (
           <div className="border-t border-dotted border-black my-1"></div>
        )}

        {/* Total akhir */}
        <div className="flex justify-between font-bold">
          <span>TOTAL</span>
          <span>{total.toLocaleString('id-ID')}</span>
        </div>
      </div>

      {/* Info Pembayaran */}
      <div className="border-t border-dashed border-black mt-2 pt-1 text-[0.7rem]">
        {splitPayment ? (
          <>
            <div className="flex justify-between">
              <span>Metode Bayar</span>
              <span>SPLIT BILL</span>
            </div>
            <div className="pl-2 mt-1">
              <div className="flex justify-between">
                <span>- {splitPayment.firstMethod}</span>
                <span>{(splitPayment.firstMethod === 'CASH' ? splitPayment.cashAmount : total - (splitPayment.secondMethod === 'CASH' ? splitPayment.cashAmount : 0)).toLocaleString('id-ID')}</span>
              </div>
              <div className="flex justify-between">
                <span>- {splitPayment.secondMethod}</span>
                <span>{(splitPayment.secondMethod === 'CASH' ? splitPayment.cashAmount : total - (splitPayment.firstMethod === 'CASH' ? splitPayment.cashAmount : 0)).toLocaleString('id-ID')}</span>
              </div>
              {(splitPayment.firstMethod === 'CASH' || splitPayment.secondMethod === 'CASH') && (
                <>
                  <div className="flex justify-between mt-1 border-t border-dotted border-black pt-1">
                    <span>Tunai Diterima</span>
                    <span>{splitPayment.cashAmount.toLocaleString('id-ID')}</span>
                  </div>
                  {splitPayment.changeAmount > 0 && (
                    <div className="flex justify-between">
                      <span>Kembalian</span>
                      <span>{splitPayment.changeAmount.toLocaleString('id-ID')}</span>
                    </div>
                  )}
                </>
              )}
            </div>
          </>
        ) : (
          <div className="flex justify-between">
            <span>Metode Bayar</span>
            <span>{paymentMethod}</span>
          </div>
        )}
      </div>

      {/* Footer Struk */}
      <div className="text-center mt-4 text-[0.65rem] border-t border-black pt-1">
        <p>{note || "Terima kasih atas kunjungan Anda!"}</p>
        <p>Badan Segar Urusan Lancar</p>
      </div>
    </div>
  );
});

ReceiptComponent.displayName = 'ReceiptComponent';
export default ReceiptComponent;