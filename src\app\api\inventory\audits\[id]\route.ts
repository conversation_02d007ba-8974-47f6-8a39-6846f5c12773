import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserIdFromToken } from '@/lib/auth-utils';

// GET - Ambil audit inventori berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const audit = await prisma.inventoryAudit.findUnique({
      where: { id },
      include: {
        outlet: true,
        createdBy: {
          select: {
            id: true,
            name: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true
          }
        },
        auditItems: {
          include: {
            item: {
              include: {
                category: true
              }
            },
            checkedBy: {
              select: {
                id: true,
                name: true
              }
            }
          },
          orderBy: {
            item: {
              name: 'asc'
            }
          }
        }
      }
    });

    if (!audit) {
      return NextResponse.json(
        { error: 'Audit inventori tidak ditemukan' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Audit inventori berhasil diambil',
      audit
    });
  } catch (error) {
    console.error('Error fetching inventory audit:', error);
    return NextResponse.json(
      { error: '<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mengambil audit inventori' },
      { status: 500 }
    );
  }
}

// PUT - Update audit inventori
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const userId = getUserIdFromToken(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const body = await request.json();
    const {
      title,
      description,
      scheduledDate,
      status,
      notes
    } = body;

    // Cek apakah audit ada
    const existingAudit = await prisma.inventoryAudit.findUnique({
      where: { id }
    });

    if (!existingAudit) {
      return NextResponse.json(
        { error: 'Audit inventori tidak ditemukan' },
        { status: 404 }
      );
    }

    // Validasi input
    if (title && title.trim() === '') {
      return NextResponse.json(
        { error: 'Judul audit harus diisi' },
        { status: 400 }
      );
    }

    // Validasi tanggal jika diubah
    let scheduledDateTime;
    if (scheduledDate) {
      scheduledDateTime = new Date(scheduledDate);
      const now = new Date();
      now.setHours(0, 0, 0, 0);
      
      if (scheduledDateTime < now && existingAudit.status === 'PENDING') {
        return NextResponse.json(
          { error: 'Tanggal audit tidak boleh di masa lalu' },
          { status: 400 }
        );
      }
    }

    // Update data
    const updateData: any = {};
    
    if (title !== undefined) updateData.title = title.trim();
    if (description !== undefined) updateData.description = description?.trim() || null;
    if (notes !== undefined) updateData.notes = notes?.trim() || null;
    if (scheduledDateTime) updateData.scheduledDate = scheduledDateTime;
    
    // Handle status changes
    if (status !== undefined && status !== existingAudit.status) {
      updateData.status = status;
      
      if (status === 'IN_PROGRESS' && existingAudit.status === 'PENDING') {
        updateData.startedAt = new Date();
      } else if (status === 'COMPLETED' && existingAudit.status === 'IN_PROGRESS') {
        updateData.completedAt = new Date();
      }
    }

    const audit = await prisma.inventoryAudit.update({
      where: { id },
      data: updateData,
      include: {
        outlet: true,
        createdBy: {
          select: {
            id: true,
            name: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true
          }
        },
        auditItems: {
          include: {
            item: {
              include: {
                category: true
              }
            },
            checkedBy: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      message: 'Audit inventori berhasil diperbarui',
      audit
    });
  } catch (error) {
    console.error('Error updating inventory audit:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui audit inventori' },
      { status: 500 }
    );
  }
}

// DELETE - Hapus audit inventori
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const userId = getUserIdFromToken(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const { id } = await params;
    
    // Cek apakah audit ada
    const existingAudit = await prisma.inventoryAudit.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            auditItems: true
          }
        }
      }
    });

    if (!existingAudit) {
      return NextResponse.json(
        { error: 'Audit inventori tidak ditemukan' },
        { status: 404 }
      );
    }

    // Hanya boleh hapus audit dengan status PENDING atau CANCELLED
    if (existingAudit.status !== 'PENDING' && existingAudit.status !== 'CANCELLED') {
      return NextResponse.json(
        { error: 'Hanya audit dengan status "Terjadwal" atau "Dibatalkan" yang dapat dihapus' },
        { status: 400 }
      );
    }

    // Hapus audit items terlebih dahulu jika ada
    if (existingAudit._count.auditItems > 0) {
      await prisma.inventoryAuditItem.deleteMany({
        where: { auditId: id }
      });
    }

    // Hapus audit
    await prisma.inventoryAudit.delete({
      where: { id }
    });

    return NextResponse.json({
      message: 'Audit inventori berhasil dihapus'
    });
  } catch (error) {
    console.error('Error deleting inventory audit:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus audit inventori' },
      { status: 500 }
    );
  }
}