import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET pengaturan berdasarkan key
export async function GET(
  request: Request,
  { params }: { params: { key: string } }
) {
  try {
    const { key } = params;

    if (!key) {
      return NextResponse.json(
        { error: 'Key pengaturan diperlukan' },
        { status: 400 }
      );
    }

    // Ambil pengaturan berdasarkan key
    const setting = await prisma.setting.findUnique({
      where: {
        key: key
      }
    });

    // Jika pengaturan tidak ditemukan
    if (!setting) {
      return NextResponse.json(
        { error: 'Pengaturan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Kembalikan data pengaturan
    return NextResponse.json({
      message: 'Data pengaturan berhasil diambil',
      setting
    });
  } catch (error) {
    console.error('Error fetching setting:', error);
    return NextResponse.json(
      { error: '<PERSON><PERSON><PERSON><PERSON> kesalahan saat mengambil data pengaturan' },
      { status: 500 }
    );
  }
}

// PATCH untuk memperbarui pengaturan
export async function PATCH(
  request: Request,
  { params }: { params: { key: string } }
) {
  try {
    const { key } = params;
    const body = await request.json();
    const { value, category, label, type, options, isSystem } = body;

    if (!key) {
      return NextResponse.json(
        { error: 'Key pengaturan diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah pengaturan ada
    const existingSetting = await prisma.setting.findUnique({
      where: { key }
    });

    if (!existingSetting) {
      return NextResponse.json(
        { error: 'Pengaturan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Validasi jika pengaturan sistem dan mencoba mengubah isSystem
    if (existingSetting.isSystem && isSystem === false) {
      return NextResponse.json(
        { error: 'Tidak dapat mengubah status sistem dari pengaturan sistem' },
        { status: 403 }
      );
    }

    // Siapkan data yang akan diupdate dengan cara yang lebih langsung
    const data: Record<string, any> = {};
    if (value !== undefined) data.value = value;
    if (category !== undefined) data.category = category;
    if (label !== undefined) data.label = label;
    if (type !== undefined) data.type = type;
    if (options !== undefined) data.options = options;
    if (isSystem !== undefined && !existingSetting.isSystem) data.isSystem = isSystem;

    // Update pengaturan
    const updatedSetting = await prisma.setting.update({
      where: { key },
      data
    });

    return NextResponse.json({
      message: 'Pengaturan berhasil diperbarui',
      setting: updatedSetting
    });
  } catch (error) {
    console.error('Error updating setting:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui pengaturan' },
      { status: 500 }
    );
  }
}

// DELETE untuk menghapus pengaturan
export async function DELETE(
  request: Request,
  { params }: { params: { key: string } }
) {
  try {
    const { key } = params;

    if (!key) {
      return NextResponse.json(
        { error: 'Key pengaturan diperlukan' },
        { status: 400 }
      );
    }

    // Cek apakah pengaturan ada
    const existingSetting = await prisma.setting.findUnique({
      where: { key }
    });

    if (!existingSetting) {
      return NextResponse.json(
        { error: 'Pengaturan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Jangan hapus pengaturan sistem
    if (existingSetting.isSystem) {
      return NextResponse.json(
        { error: 'Tidak dapat menghapus pengaturan sistem' },
        { status: 403 }
      );
    }

    // Hapus pengaturan
    await prisma.setting.delete({
      where: { key }
    });

    return NextResponse.json({
      message: 'Pengaturan berhasil dihapus'
    });
  } catch (error) {
    console.error('Error deleting setting:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus pengaturan' },
      { status: 500 }
    );
  }
} 