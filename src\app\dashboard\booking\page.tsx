'use client';

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { motion } from 'framer-motion';
import { useOutletContext } from '@/contexts/OutletContext';
import { useAuth } from '@/contexts/AuthContext';
import {
  FiCalendar, FiUsers, FiUserCheck, FiScissors,
  FiPlus, FiSearch, FiCheckCircle, FiInbox, FiEdit, FiTrash2, FiAlertCircle, FiXCircle, FiStar,
  FiMessageSquare, FiShare2
} from 'react-icons/fi';
import { createWhatsAppUrl, createBookingConfirmationMessage } from '@/lib/whatsapp';
import { addMinutes, format, parse, isValid, startOfDay, parseISO } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { toast } from 'sonner';

// --- Type Outlet (jika belum ada atau berbeda dari context) ---
interface Outlet {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  operationalHours?: string;
  isOpen?: boolean;
  isMain?: boolean;
}

// --- Interfaces ---
interface Service {
  id: string;
  name: string;
  duration: number;
  price: number;
  description?: string;
  isActive: boolean;
}

interface Therapist {
  id: string;
  name: string;
  specialization?: string;
  isActive: boolean;
}

interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  points?: number;
}

interface BookingService {
  id: string;
  serviceId: string;
  bookingId: string;
  price: number;
  quantity?: number; // Tambahkan field quantity
  service: Service;
}

interface Booking {
  id: string;
  displayId?: string;
  bookingDate: string;
  bookingTime: string;
  customerId: string;
  therapistId: string;
  outletId: string;
  createdById: string;
  status: string;
  notes?: string;
  customer: Customer;
  customers?: Customer[]; // Array pelanggan untuk mendukung beberapa pelanggan
  therapist: Therapist;
  bookingServices: BookingService[];
  outlet?: Outlet; // Tambahkan outlet untuk digunakan di pesan WhatsApp
}

// Kunci localStorage untuk fallback
const BOOKING_HISTORY_KEY = 'bookingHistory';

// Format tanggal untuk tampilan
const formatDateDisplay = (date: Date | null): string => {
  if (!date) return '';
  return format(date, 'dd MMMM yyyy', { locale: idLocale });
};

// Varian Animasi
const fadeInUp = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.5 } } };
const staggerContainer = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1 } } };

// Helper: Parse Operating Hours String (contoh: "10:00 - 22:00")
const parseOperatingHours = (hoursString?: string): { start: Date | null, end: Date | null } => {

  if (!hoursString) {
    return { start: null, end: null };
  }
  // Ubah split menjadi hanya '-' dan trim hasilnya
  const parts = hoursString.split('-').map(part => part.trim());
  if (parts.length !== 2) {
    return { start: null, end: null };
  }

  const baseDate = startOfDay(new Date()); // Gunakan awal hari ini sebagai basis

  const startTime = parse(parts[0], 'HH:mm', baseDate);
  const endTime = parse(parts[1], 'HH:mm', baseDate);

  if (!isValid(startTime) || !isValid(endTime)) {
    return { start: null, end: null };
  }

  return { start: startTime, end: endTime };
};

// Helper: Generate Time Slots (dimodifikasi)
const generateTimeSlots = (startTime: Date, endTime: Date, intervalMinutes: number): string[] => {
  const slots: string[] = [];
  let currentTime = startTime;

  // Pastikan waktu akhir juga dimasukkan
  while (currentTime <= endTime) {
    // Tambahkan slot waktu saat ini
    slots.push(format(currentTime, 'HH:mm'));

    // Pindah ke slot berikutnya
    currentTime = addMinutes(currentTime, intervalMinutes);
  }

  // Pastikan slot terakhir adalah 22:00 jika endTime adalah 22:00
  const endTimeStr = format(endTime, 'HH:mm');
  if (endTimeStr === '22:00' && !slots.includes('22:00')) {
    slots.push('22:00');
  }

  return slots;
};

// Fungsi untuk mendapatkan tanggal hari ini (selalu real-time) dengan zona waktu lokal
const getTodayDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0'); // Bulan dimulai dari 0
  const day = String(today.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export default function BookingPage() {
  const { selectedOutletId, setSelectedOutletId } = useOutletContext();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State Data dari API
  const [availableServices, setAvailableServices] = useState<Service[]>([]);
  const [availableTherapists, setAvailableTherapists] = useState<Therapist[]>([]);
  const [bookingHistory, setBookingHistory] = useState<Booking[]>([]);
  const [allOutlets, setAllOutlets] = useState<Outlet[]>([]);
  const [isOutletLoading, setIsOutletLoading] = useState(true);
  const [outletError, setOutletError] = useState<string | null>(null);
  const [isHistoryLoading, setIsHistoryLoading] = useState(true);

  // State Form Booking
  const [selectedServices, setSelectedServices] = useState<Service[]>([]);
  const [selectedTherapist, setSelectedTherapist] = useState<Therapist | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>(() => getTodayDate());
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [bookingNote, setBookingNote] = useState('');

  // State untuk pencarian layanan
  const [serviceSearchTerm, setServiceSearchTerm] = useState('');
  // State untuk pencarian terapis
  const [therapistSearchTerm, setTherapistSearchTerm] = useState('');
  const therapistDropdownRef = useRef<HTMLDivElement>(null);

  // State Pelanggan
  const [selectedCustomers, setSelectedCustomers] = useState<Customer[]>([]);
  const [customerSearchTerm, setCustomerSearchTerm] = useState('');
  const [customerSearchResults, setCustomerSearchResults] = useState<Customer[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [showAddCustomerModal, setShowAddCustomerModal] = useState(false);
  const [newCustomerName, setNewCustomerName] = useState('');
  const [newCustomerPhone, setNewCustomerPhone] = useState('');
  const [newCustomerAddress, setNewCustomerAddress] = useState('');

  // State Konfirmasi
  const [isBookingComplete, setIsBookingComplete] = useState(false);
  const [bookingDetails, setBookingDetails] = useState<Booking | null>(null);

  // Fungsi untuk mengirim pesan WhatsApp
  const handleSendWhatsAppMessage = (booking: Booking) => {
    if (!booking || !booking.customer || !booking.customer.phone) {
      toast.error('Tidak dapat mengirim pesan: Data pelanggan tidak lengkap');
      return;
    }

    try {
      // Format tanggal booking untuk pesan
      const bookingDate = new Date(booking.bookingDate);
      const formattedDate = format(bookingDate, 'EEEE, dd MMMM yyyy', { locale: idLocale });

      // Hitung total durasi dan harga
      const totalDuration = booking.bookingServices.reduce(
        (sum, bs) => sum + (bs.service?.duration || 0),
        0
      );

      const totalPrice = booking.bookingServices.reduce(
        (sum, bs) => sum + (bs.price || 0),
        0
      );

      // Siapkan data untuk pesan
      let customerNameText = booking.customer.name || 'Pelanggan';

      // Jika ada pelanggan tambahan, tambahkan ke nama pelanggan
      if (booking.customers && booking.customers.length > 1) {
        const additionalCustomers = booking.customers.filter(c => c.id !== booking.customer.id);
        if (additionalCustomers.length > 0) {
          customerNameText += ` dan ${additionalCustomers.map(c => c.name || 'Pelanggan').join(', ')}`;
        }
      }

      const messageData = {
        customerName: customerNameText,
        outletName: booking.outlet?.name || 'BreakTime',
        bookingDate: formattedDate,
        bookingTime: booking.bookingTime,
        services: booking.bookingServices.map(bs => ({
          name: bs.service?.name || 'Layanan',
          duration: bs.service?.duration || 0,
          price: bs.price || 0,
          quantity: bs.quantity || 1 // Tambahkan quantity
        })),
        totalDuration,
        totalPrice,
        bookingId: booking.displayId || booking.id.substring(0, 8),
        customerPoints: booking.customer.points || 0
      };

      // Buat pesan dan URL WhatsApp
      const message = createBookingConfirmationMessage(messageData);
      const whatsappUrl = createWhatsAppUrl(booking.customer.phone, message);

      // Buka URL WhatsApp di tab baru
      window.open(whatsappUrl, '_blank');
    } catch (error) {
      console.error('Error sending WhatsApp message:', error);
      toast.error('Gagal membuat pesan WhatsApp');
    }
  };

  // State untuk mode Edit
  const [isEditing, setIsEditing] = useState(false);
  const [editingBookingId, setEditingBookingId] = useState<string | null>(null);

  // State untuk slot waktu yang tersedia
  const [availableTimeSlots, setAvailableTimeSlots] = useState<Array<{ time: string; isAvailable: boolean }>>([]);

  // State untuk pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10); // 10 booking per halaman

  // Kalkulasi jumlah layanan yang dipilih (untuk UI)
  const serviceCounts = useMemo(() => {
    return selectedServices.reduce((acc, service) => {
      acc[service.id] = (acc[service.id] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [selectedServices]);

  // Kalkulasi grup layanan yang dipilih (untuk UI ringkasan)
  const serviceGroups = useMemo(() => {
    return selectedServices.reduce((acc, service) => {
      const existing = acc.find(item => item.id === service.id);
      if (existing) {
        existing.quantity += 1;
      } else {
        acc.push({ ...service, quantity: 1 });
      }
      return acc;
    }, [] as Array<Service & { quantity: number }>);
  }, [selectedServices]);

  // Get today's date string YYYY-MM-DD - selalu real-time
  const todayDateStr = useMemo(() => getTodayDate(), []);

  // Kalkulasi Ringkasan
  const totalDuration = useMemo(() => selectedServices.reduce((sum, s) => sum + s.duration, 0), [selectedServices]);
  const totalPrice = useMemo(() => selectedServices.reduce((sum, s) => sum + s.price, 0), [selectedServices]);

  // Kalkulasi pagination
  const totalBookings = bookingHistory.length;
  const totalPages = Math.ceil(totalBookings / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentBookings = bookingHistory.slice(startIndex, endIndex);

  // Reset halaman ketika data booking berubah
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(1);
    }
  }, [currentPage, totalPages]);

  // Filter layanan berdasarkan kata kunci pencarian
  const filteredServices = useMemo(() => {
    if (!serviceSearchTerm.trim()) return availableServices;
    return availableServices.filter(service =>
      service.name.toLowerCase().includes(serviceSearchTerm.toLowerCase()) ||
      (service.description && service.description.toLowerCase().includes(serviceSearchTerm.toLowerCase()))
    );
  }, [availableServices, serviceSearchTerm]);

  // Filter terapis berdasarkan kata kunci pencarian
  const filteredTherapists = useMemo(() => {
    if (!therapistSearchTerm.trim()) return availableTherapists;
    return availableTherapists.filter(therapist =>
      therapist.name.toLowerCase().includes(therapistSearchTerm.toLowerCase())
    );
  }, [availableTherapists, therapistSearchTerm]);

  // --> State untuk Modal Konfirmasi Hapus Booking <--
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [bookingToDelete, setBookingToDelete] = useState<Booking | null>(null);

  // --- Kalkulasi Slot Waktu Tersedia ---
  const calculateAvailableSlots = useMemo(() => {
    if (!selectedDate || !selectedOutletId || !allOutlets.length) {
      return [];
    }

    const selectedOutlet = allOutlets.find(o => o.id === selectedOutletId);
    // Pastikan jam operasional maksimal sampai jam 22:00
    let { start: opStart, end: opEnd } = parseOperatingHours(selectedOutlet?.operationalHours);

    if (!opStart || !opEnd) {
      return []; // Kembalikan array kosong jika jam operasional tidak valid
    }

    // Tetapkan batas maksimal jam operasional sampai 22:00
    const maxEndTime = parse('22:00', 'HH:mm', startOfDay(new Date()));

    // Jika jam operasional outlet melebihi 22:00, batasi hingga 22:00
    if (opEnd > maxEndTime) {
      opEnd = maxEndTime;
    }

    // Jika jam operasional outlet kurang dari 22:00, tetapkan ke 22:00
    // untuk memastikan slot waktu tersedia hingga 22:00
    if (opEnd < maxEndTime) {
      opEnd = maxEndTime;
    }

    // Generate semua slot dalam jam operasional
    const baseTimeSlots = generateTimeSlots(opStart, opEnd, 30);

    // Filter booking history untuk tanggal terpilih
    const bookingsOnSelectedDate = bookingHistory.filter(b => b.bookingDate === selectedDate);

    // Hitung total durasi layanan yang dipilih
    const currentBookingDuration = selectedServices.reduce((sum, s) => sum + s.duration, 0);

    // Dapatkan waktu saat ini
    const now = new Date();
    const today = startOfDay(now);

    // Parse tanggal yang dipilih dengan format yang benar
    const selectedDateObj = parse(selectedDate, 'yyyy-MM-dd', new Date());

    // Buat salinan objek tanggal untuk perbandingan
    const todayForCompare = new Date(today);
    const selectedDateForCompare = new Date(selectedDateObj);

    // Reset jam ke 00:00:00 untuk perbandingan yang akurat
    todayForCompare.setHours(0, 0, 0, 0);
    selectedDateForCompare.setHours(0, 0, 0, 0);

    // Cek apakah tanggal yang dipilih adalah hari ini
    const isToday = selectedDateForCompare.getTime() === todayForCompare.getTime();

    // Cek apakah tanggal yang dipilih adalah di masa depan
    const isFutureDate = selectedDateForCompare.getTime() > todayForCompare.getTime();



    const slotsWithAvailability = baseTimeSlots.map(slot => {
      // Default: slot tersedia
      let isAvailable = true;
      const slotStartTime = parse(`${selectedDate} ${slot}`, 'yyyy-MM-dd HH:mm', new Date());
      const slotEndTime = addMinutes(slotStartTime, currentBookingDuration);

      // LANGKAH 1: Cek apakah slot berada dalam jam operasional
      // Buat salinan objek waktu untuk perbandingan yang akurat
      const slotTimeOnly = parse(slot, 'HH:mm', startOfDay(new Date()));
      const opStartTimeOnly = parse(format(opStart, 'HH:mm'), 'HH:mm', startOfDay(new Date()));
      const opEndTimeOnly = parse(format(opEnd, 'HH:mm'), 'HH:mm', startOfDay(new Date()));

      // Bandingkan hanya waktu (jam:menit), bukan tanggal+waktu
      const isWithinOperatingHours =
        slotTimeOnly >= opStartTimeOnly &&
        slotTimeOnly <= opEndTimeOnly;

      if (!isWithinOperatingHours) {
        isAvailable = false;
      }

      // LANGKAH 2: Untuk tanggal hari ini, nonaktifkan slot yang sudah lewat
      if (isToday && slotStartTime < now) {
        isAvailable = false;
      }

      // LANGKAH 3: Untuk tanggal masa depan, aktifkan semua slot dalam jam operasional
      if (isFutureDate && isWithinOperatingHours) {
        // Aktifkan slot untuk tanggal masa depan (akan dicek konflik booking di langkah berikutnya)
        isAvailable = true;
      }

      // LANGKAH 4: Cek konflik dengan booking yang sudah ada
      let hasConflict = false;
      for (const existingBooking of bookingsOnSelectedDate) {
        try {
          const existingStartTime = parse(`${existingBooking.bookingDate} ${existingBooking.bookingTime}`, 'yyyy-MM-dd HH:mm', new Date());
          // Hitung durasi booking yang ada
          const existingDuration = existingBooking.bookingServices.reduce((sum, bs) => sum + bs.service.duration, 0);
          const existingEndTime = addMinutes(existingStartTime, existingDuration);

          // Cek tumpang tindih (overlap check)
          // Kondisi overlap: (StartA < EndB) and (EndA > StartB)
          if (slotStartTime < existingEndTime && slotEndTime > existingStartTime) {
            hasConflict = true;
            break; // Jika sudah konflik, tidak perlu cek booking lain
          }
        } catch (parseError) {
          // Anggap slot tidak tersedia jika ada error parsing waktu booking yg ada
          hasConflict = true;
          break;
        }
      }

      // Jika ada konflik, slot tidak tersedia
      if (hasConflict) {
        isAvailable = false;
      }

      // HASIL AKHIR
      return { time: slot, isAvailable };
    });

    return slotsWithAvailability;

  }, [selectedDate, selectedOutletId, allOutlets, bookingHistory, selectedServices]);

  // Update state ketika hasil kalkulasi berubah
  useEffect(() => {
    setAvailableTimeSlots(calculateAvailableSlots);
  }, [calculateAvailableSlots]);

  // Load Data dari API
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      setIsHistoryLoading(true);

      if (selectedOutletId) {
        try {
          const [servicesResponse, therapistsResponse, customersResponse, bookingsResponse] = await Promise.all([
            fetch(`/api/services?outletId=${selectedOutletId}`),
            fetch(`/api/therapists?outletId=${selectedOutletId}`),
            fetch('/api/customers'),
            fetch(`/api/bookings?outletId=${selectedOutletId}&fromDate=${todayDateStr}`)
          ]);

          if (!servicesResponse.ok) throw new Error('Gagal memuat data layanan');
          if (!therapistsResponse.ok) throw new Error('Gagal memuat data terapis');
          if (!customersResponse.ok) throw new Error('Gagal memuat data pelanggan');
          if (!bookingsResponse.ok) throw new Error('Gagal memuat data booking');

          const [servicesData, therapistsData, customersData, bookingsData] = await Promise.all([
            servicesResponse.json(),
            therapistsResponse.json(),
            customersResponse.json(),
            bookingsResponse.json()
          ]);

          console.log('Data pelanggan dari API:', customersData);

          setAvailableServices(servicesData.services?.filter((service: Service) => service.isActive) || []);
          setAvailableTherapists(therapistsData.therapists?.filter((therapist: Therapist) => therapist.isActive) || []);
          setCustomers(customersData.customers || []);
          console.log('Jumlah pelanggan yang dimuat:', customersData.customers?.length || 0);
          setBookingHistory(bookingsData.bookings || []);
        } catch (error) {
          setError('Terjadi kesalahan saat memuat data. Silakan coba lagi.');

          setAvailableServices([]);
          setAvailableTherapists([]);
          setCustomers([]);
          setBookingHistory([]);
        } finally {
          setIsLoading(false);
          setIsHistoryLoading(false);
        }
      } else {
        setIsLoading(false);
        setIsHistoryLoading(false);
      }
    };

    fetchData();
  }, [selectedOutletId, todayDateStr]);

  useEffect(() => {
    const fetchOutlets = async () => {
      setIsOutletLoading(true);
      setOutletError(null);
      try {
        const response = await fetch('/api/outlets');
        if (!response.ok) throw new Error('Gagal memuat data booking');
        const data = await response.json();
        setAllOutlets(data.outlets || []);
      } catch (error) {
        setOutletError('Gagal memuat daftar outlet.');
        setAllOutlets([]);
      } finally {
        setIsOutletLoading(false);
      }
    };
    fetchOutlets();
  }, []);

  // Pencarian pelanggan dengan debounce
  useEffect(() => {
    // Hanya lakukan pencarian jika ada minimal 2 karakter
    if (customerSearchTerm.length > 1) {
      // Gunakan timeout untuk debounce (mengurangi jumlah pencarian)
      const searchTimeout = setTimeout(async () => {
        console.log('Mencari pelanggan:', customerSearchTerm);

        try {
          // Coba cari dari API langsung untuk hasil yang lebih akurat
          const response = await fetch(`/api/customers?search=${encodeURIComponent(customerSearchTerm)}&limit=10`);
          if (response.ok) {
            const data = await response.json();
            console.log('Hasil pencarian dari API:', data);
            if (data.customers && Array.isArray(data.customers)) {
              setCustomerSearchResults(data.customers);
              console.log('Hasil pencarian dari API:', data.customers.length);
              return;
            }
          }
        } catch (error) {
          console.error('Error saat mencari pelanggan dari API:', error);
        }

        // Fallback ke pencarian lokal jika API gagal
        const results = customers.filter(c =>
          c.name.toLowerCase().includes(customerSearchTerm.toLowerCase()) ||
          c.phone.includes(customerSearchTerm)
        );
        console.log('Hasil pencarian lokal:', results.length);
        setCustomerSearchResults(results);
      }, 300); // Tunggu 300ms setelah pengguna berhenti mengetik

      // Cleanup timeout jika komponen di-unmount atau customerSearchTerm berubah
      return () => clearTimeout(searchTimeout);
    } else {
      setCustomerSearchResults([]);
    }
  }, [customerSearchTerm, customers]);

  // Effect untuk menutup dropdown terapis saat mengklik di luar
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (therapistDropdownRef.current && !therapistDropdownRef.current.contains(event.target as Node)) {
        // Menutup dropdown dengan menghapus class dropdown-open
        const dropdown = therapistDropdownRef.current.querySelector('.dropdown') as HTMLElement;
        if (dropdown) dropdown.classList.remove('dropdown-open');
      }
    }

    // Tambahkan event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Cleanup event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSelectCustomer = (customer: Customer) => {
    // Cek apakah pelanggan sudah ada dalam daftar
    const isCustomerSelected = selectedCustomers.some(c => c.id === customer.id);

    // Jika belum ada dan jumlah pelanggan < 2, tambahkan
    if (!isCustomerSelected && selectedCustomers.length < 2) {
      setSelectedCustomers([...selectedCustomers, customer]);
      setCustomerSearchTerm('');
      setCustomerSearchResults([]);
    } else if (isCustomerSelected) {
      // Jika sudah ada, tampilkan pesan
      toast.info('Pelanggan sudah dipilih');
    } else {
      // Jika sudah ada 2 pelanggan, tampilkan pesan
      toast.info('Maksimal 2 pelanggan dapat dipilih');
    }
  };

  // Fungsi untuk menghapus pelanggan dari daftar yang dipilih
  const handleRemoveCustomer = (customerId: string) => {
    setSelectedCustomers(selectedCustomers.filter(c => c.id !== customerId));
  };

  const handleAddNewCustomer = async () => {
    if (!newCustomerName || !newCustomerPhone) {
      return toast.warning('Nama dan No. HP wajib diisi.');
    }

    if (!/^[0-9]{10,}$/.test(newCustomerPhone)) {
      return toast.warning('Format No. HP tidak valid.');
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newCustomerName,
          phone: newCustomerPhone,
          address: newCustomerAddress.trim() || undefined,
          points: 0,
          // Tag 'Baru' akan ditambahkan otomatis di API jika tidak ada tag lain
          tags: []
        }),
      });

      if (!response.ok) {
        throw new Error('Gagal menyimpan data pelanggan baru');
      }

      const data = await response.json();
      const newCustomer = data.customer;

      setCustomers(prevCustomers => [...prevCustomers, newCustomer]);

      // Tambahkan pelanggan baru ke daftar pelanggan yang dipilih
      // Hanya jika belum ada 2 pelanggan yang dipilih
      if (selectedCustomers.length < 2) {
        setSelectedCustomers(prev => [...prev, newCustomer]);
      } else {
        toast.info('Maksimal 2 pelanggan dapat dipilih. Pelanggan baru ditambahkan ke database.');
      }

      setShowAddCustomerModal(false);
      setNewCustomerName('');
      setNewCustomerPhone('');
      setNewCustomerAddress('');
    } catch (error) {
      toast.error('Gagal menambahkan pelanggan baru. Silakan coba lagi.');

      const newCust = {
        id: `c${Date.now()}`,
        name: newCustomerName,
        phone: newCustomerPhone,
      };

      setCustomers(prevCustomers => [...prevCustomers, newCust]);

      // Tambahkan pelanggan baru ke daftar pelanggan yang dipilih
      // Hanya jika belum ada 2 pelanggan yang dipilih
      if (selectedCustomers.length < 2) {
        setSelectedCustomers(prev => [...prev, newCust]);
      } else {
        toast.info('Maksimal 2 pelanggan dapat dipilih. Pelanggan baru ditambahkan ke database.');
      }

      setShowAddCustomerModal(false);
      setNewCustomerName('');
      setNewCustomerPhone('');
      setNewCustomerAddress('');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddService = (service: Service) => {
    setSelectedServices([...selectedServices, service]);
  };

  const handleRemoveService = (serviceId: string) => {
    const indexToRemove = selectedServices.findIndex(s => s.id === serviceId);
    if (indexToRemove !== -1) {
      const newSelectedServices = [...selectedServices];
      newSelectedServices.splice(indexToRemove, 1);
      setSelectedServices(newSelectedServices);
    }
  };

  const handleSelectTherapist = (therapistId: string) => {
    const therapist = availableTherapists.find(t => t.id === therapistId);
    setSelectedTherapist(therapist || null);
  };

  // --- Handler Update Booking ---
  const handleUpdateBooking = async () => {
    if (!editingBookingId) {
      return toast.error('ID Booking yang diedit tidak ditemukan.');
    }
    // Validasi sama seperti create
    if (selectedServices.length === 0) {
      return toast.warning('Pilih minimal satu layanan.');
    }
    if (!selectedTherapist) {
      return toast.warning('Pilih terapis.');
    }
    if (selectedCustomers.length === 0) {
      return toast.warning('Pilih atau tambahkan pelanggan.');
    }
    if (!selectedDate) {
      return toast.warning('Pilih tanggal booking.');
    }
    if (!selectedTime) {
      return toast.warning('Pilih waktu booking.');
    }

    setIsLoading(true);

    try {
      // Hitung jumlah layanan yang sama dan kirim sebagai array objek dengan quantity
      const serviceQuantities = selectedServices.reduce((acc, service) => {
        const existingService = acc.find(s => s.id === service.id);
        if (existingService) {
          existingService.quantity += 1;
        } else {
          acc.push({ id: service.id, quantity: 1 });
        }
        return acc;
      }, [] as { id: string; quantity: number }[]);

      // Log data yang akan dikirim untuk debugging
      console.log('Update booking data:', {
        id: editingBookingId,
        therapistId: selectedTherapist.id,
        customerId: selectedCustomers[0].id, // Pelanggan utama
        customerIds: selectedCustomers.map(c => c.id), // Semua pelanggan
        bookingDate: selectedDate,
        bookingTime: selectedTime,
        notes: bookingNote,
        services: serviceQuantities // Tambahkan services
      });

      const response = await fetch(`/api/bookings/${editingBookingId}`, { // Target ID spesifik
        method: 'PUT', // Method PUT
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          // Kirim data yang bisa diupdate sesuai dengan API
          therapistId: selectedTherapist.id,
          customerId: selectedCustomers[0].id, // Pelanggan utama
          customerIds: selectedCustomers.map(c => c.id), // Semua pelanggan
          bookingDate: selectedDate, // Gunakan bookingDate, bukan scheduledDate
          bookingTime: selectedTime, // Gunakan bookingTime, bukan scheduledTime
          notes: bookingNote,
          services: serviceQuantities // Tambahkan services
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Gagal memperbarui booking');
      }

      const data = await response.json();
      const updatedBooking = data.booking;

      // Log respons untuk debugging
      console.log('Update booking response:', data);

      // Update history state
      setBookingHistory(prev => prev.map(b => b.id === editingBookingId ? updatedBooking : b));

      toast.success('Booking berhasil diperbarui!');
      // Keluar dari mode edit
      setIsEditing(false);
      setEditingBookingId(null);
      // Reset form?
      handleNewBooking(); // Gunakan handleNewBooking untuk reset

    } catch (error) {
      toast.error('Gagal memperbarui booking. Silakan coba lagi.');
      // Tetap dalam mode edit jika gagal?
    } finally {
      setIsLoading(false);
    }
  };

  // --- Handler Cancel Edit ---
  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditingBookingId(null);

    // Reset form ke nilai default
    setSelectedServices([]);
    setSelectedTherapist(null);
    setSelectedCustomers([]);

    // Tidak perlu mengubah tanggal yang dipilih saat membatalkan edit
    // Biarkan pengguna tetap pada tanggal yang sudah dipilih

    setSelectedTime('');
    setBookingNote('');
    setCustomerSearchTerm('');
    setIsBookingComplete(false);
    setBookingDetails(null);

    // Scroll ke atas halaman
    window.scrollTo({ top: 0, behavior: 'smooth' });

    toast.info('Edit booking dibatalkan');
  };

  const handleCreateBooking = async () => {
    if (selectedServices.length === 0) {
      return toast.warning('Pilih minimal satu layanan.');
    }
    if (!selectedTherapist) {
      return toast.warning('Pilih terapis.');
    }
    if (selectedCustomers.length === 0) {
      return toast.warning('Pilih atau tambahkan pelanggan.');
    }
    if (!selectedDate) {
      return toast.warning('Pilih tanggal booking.');
    }
    if (!selectedTime) {
      return toast.warning('Pilih waktu booking.');
    }

    // Tambahkan validasi user
    if (!user || !user.id) {
      return toast.error('Gagal mendapatkan ID pengguna. Silakan login ulang.');
    }

    setIsLoading(true);

    try {
      // Siapkan payload sesuai ekspektasi backend
      // Hitung jumlah layanan yang sama dan kirim sebagai array objek dengan quantity
      const serviceQuantities = selectedServices.reduce((acc, service) => {
        const existingService = acc.find(s => s.id === service.id);
        if (existingService) {
          existingService.quantity += 1;
        } else {
          acc.push({ id: service.id, quantity: 1 });
        }
        return acc;
      }, [] as { id: string; quantity: number }[]);

      const payload = {
        outletId: selectedOutletId,
        customerId: selectedCustomers[0].id, // Pelanggan utama
        customerIds: selectedCustomers.map(c => c.id), // Semua pelanggan
        therapistId: selectedTherapist.id,
        createdById: user.id,
        date: selectedDate,
        time: selectedTime,
        services: serviceQuantities, // Kirim array objek dengan quantity
        notes: bookingNote,
        status: 'PENDING'
      };



      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        // Coba baca error dari response body jika ada
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Gagal membuat booking');
      }

      const data = await response.json();
      const booking = data.booking;

      // Pastikan booking memiliki data customer yang lengkap
      // Jika tidak ada, gunakan data dari selectedCustomers
      const completeBooking = {
        ...booking,
        customer: booking.customer || selectedCustomers[0] || { name: 'Pelanggan' },
        customers: booking.customers || selectedCustomers || [],
        therapist: booking.therapist || selectedTherapist || { name: 'Terapis' },
        bookingServices: booking.bookingServices || selectedServices.map(s => ({ service: s })) || []
      };

      setBookingHistory(prev => [completeBooking, ...prev]);
      setBookingDetails(completeBooking);
      setIsBookingComplete(true);
      toast.success('Booking berhasil dibuat!'); // Tambah notifikasi sukses

    } catch (error) {
      // Tampilkan pesan error yang lebih spesifik jika ada
      toast.error(`Gagal membuat booking: ${error instanceof Error ? error.message : 'Silakan coba lagi.'}`);

      // ... (logika fallback tetap sama) ...
      // Hanya buat fallback jika outlet, customer, dan therapist terpilih
      if (selectedOutletId && selectedCustomers.length > 0 && selectedTherapist) {
        // Generate ID booking dengan format yang benar (B0000xxx)
        const randomNum = Math.floor(Math.random() * 1000);
        const fallbackDisplayId = `B${String(randomNum).padStart(7, '0')}`;

        const details: Booking = {
          id: `${Date.now().toString()}`,
          displayId: fallbackDisplayId, // Gunakan format yang benar
          bookingDate: selectedDate,
          bookingTime: selectedTime,
          customerId: selectedCustomers[0].id,
          therapistId: selectedTherapist.id,
          outletId: selectedOutletId, // Pastikan tidak null di sini
          createdById: 'u1',
          status: 'PENDING', // Status fallback
          notes: bookingNote,
          customer: selectedCustomers[0], // Pelanggan utama
          customers: selectedCustomers, // Semua pelanggan
          therapist: selectedTherapist,
          bookingServices: selectedServices.map(s => ({
            id: `bs${Date.now()}-${s.id}`,
            serviceId: s.id,
            bookingId: `${Date.now().toString()}`,
            price: s.price,
            service: s
          }))
        };

        setBookingDetails(details);

        // Simpan ke localStorage sebagai fallback
        try {
          const currentHistory: Partial<Booking>[] = JSON.parse(localStorage.getItem(BOOKING_HISTORY_KEY) || '[]');
          // Filter dengan asumsi item di localStorage memiliki 'id'
          const updatedHistory = currentHistory.filter((b) => b.id !== details.id);
          // Tambahkan booking baru ke history
          updatedHistory.push(details);
          localStorage.setItem(BOOKING_HISTORY_KEY, JSON.stringify(updatedHistory));

          // Update state dengan data fallback
          setBookingHistory(prev => [details, ...prev]);
          setIsBookingComplete(true);
        } catch (localError) {
          // Gagal menyimpan ke localStorage, tapi tidak perlu menampilkan error
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewBooking = () => {
    setSelectedServices([]);
    setSelectedTherapist(null);
    setSelectedCustomers([]);
    // Tidak perlu mengubah tanggal yang dipilih saat membuat booking baru
    // Biarkan pengguna tetap pada tanggal yang sudah dipilih
    setSelectedTime('');
    setBookingNote('');
    setCustomerSearchTerm('');
    setIsBookingComplete(false);
    setBookingDetails(null);
  };

  // --- Handlers untuk Modal Hapus Booking ---
  const handleOpenDeleteModal = (booking: Booking) => {
    setBookingToDelete(booking);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setBookingToDelete(null);
    setIsDeleteModalOpen(false);
  };

  const confirmDeleteBooking = async () => {
    if (!bookingToDelete) return;

    setIsLoading(true);
    const bookingId = bookingToDelete.id;
    handleCloseDeleteModal(); // Tutup modal

    try {
      const response = await fetch(`/api/bookings/${bookingId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        // Cek pesan error spesifik dari API (jika ada, misal booking tidak bisa dihapus krn status)
        throw new Error(errorData.error || 'Gagal menghapus booking');
      }

      // Refresh data booking
      await fetchBookings(); // Panggil fungsi fetch

      toast.success(`Booking (ID: ${bookingId}) berhasil dihapus.`);
    } catch (err: unknown) {
      toast.error(`Gagal menghapus booking: ${err instanceof Error ? err.message : 'Terjadi kesalahan'}`);
    } finally {
      setIsLoading(false);
    }
  };
  // --- Akhir Handlers Modal Hapus Booking ---

  // Modifikasi handleDeleteBooking: Hanya buka modal
  const handleDeleteBooking = (booking: Booking) => {
    // Mungkin perlu cek status dulu sebelum membuka modal?
    // Contoh: if (booking.status === 'COMPLETED' || booking.status === 'CANCELLED') { ... }
    handleOpenDeleteModal(booking);
  };

  // State untuk menyimpan tanggal hari ini
  const [todayDate, setTodayDate] = useState(getTodayDate());

  // Fungsi untuk memperbarui tanggal hari ini
  const refreshTodayDate = useCallback(() => {
    const newTodayDate = getTodayDate();

    // Selalu perbarui todayDate untuk validasi
    setTodayDate(newTodayDate);

    // Tidak perlu memperbarui selectedDate secara otomatis
    // Biarkan pengguna memilih tanggal yang diinginkan
  }, []);

  // Effect untuk memperbarui tanggal hari ini setiap kali komponen di-render
  useEffect(() => {
    refreshTodayDate();

    // Juga perbarui setiap 1 menit untuk menangani kasus pergantian hari
    const intervalId = setInterval(refreshTodayDate, 60000);
    return () => clearInterval(intervalId);
  }, [refreshTodayDate]);

  // Effect khusus untuk memperbarui selectedDate ke tanggal hari ini saat halaman pertama kali dimuat
  // tapi hanya jika selectedDate belum diatur
  useEffect(() => {
    // Hanya jalankan sekali saat komponen pertama kali dimuat
    // dan hanya jika selectedDate belum diatur
    if (!selectedDate) {
      const todayDate = getTodayDate();
      setSelectedDate(todayDate);
    }
  }, []);

  // --- Fungsi Fetch Bookings (Rekonstruksi) ---
  const fetchBookings = useCallback(async () => {
    setIsLoading(true);
    try {
      let url = '/api/bookings';
      if (selectedOutletId) {
        url += `?outletId=${selectedOutletId}`;
      }
      // Tambahkan filter status jika perlu, misal:
      // if (filterStatus !== 'ALL') {
      //   url += `${selectedOutletId ? '&' : '?'}status=${filterStatus}`;
      // }
      const response = await fetch(url);
      if (!response.ok) throw new Error(`Error: ${response.status}`);
      const data = await response.json();
      setBookingHistory(data.bookings || []);
    } catch (error) {
      setBookingHistory([]);
      toast.error("Gagal memuat data booking.");
    } finally {
      setIsLoading(false);
    }
    // Tambahkan selectedOutletId dan filterStatus ke dependency jika dipakai
  }, [selectedOutletId]);

  // ... useEffect fetchBookings ...
  useEffect(() => {
    fetchBookings();
  }, [fetchBookings]);

  // ... filteredBookings ...

  // --- Handler Outlet Change (Rekonstruksi) ---
  const handleOutletChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newOutletId = event.target.value;
    setSelectedOutletId(newOutletId || null); // Update context
    // Reset state form booking jika perlu
    setIsEditing(false);
    setEditingBookingId(null);
    setSelectedServices([]);
    setSelectedTherapist(null);
    // Tidak perlu mengubah tanggal yang dipilih saat mengganti outlet
    // Biarkan pengguna tetap pada tanggal yang sudah dipilih
    setSelectedTime('');
    setSelectedCustomers([]);
    setBookingNote('');
    // Reset pagination
    setCurrentPage(1);
    // Reset filter status?
    // setFilterStatus('ALL');
  };

  // --- Handler Update Status (Rekonstruksi) ---
  const handleUpdateBookingStatus = async (bookingId: string, newStatus: string) => {
    // Validasi string status (opsional tapi bagus)
    const validStatuses = ['PENDING', 'CONFIRMED', 'COMPLETED', 'CANCELLED', 'NO_SHOW'];
    if (!validStatuses.includes(newStatus)) {
      toast.error("Status baru tidak valid.");
      return;
    }
    if (newStatus === 'CANCELLED' && !confirm(`Apakah Anda yakin ingin membatalkan booking ${bookingId}? Status tidak bisa dikembalikan.`)) {
      return;
    }
    setIsLoading(true);
    try {
      const response = await fetch(`/api/bookings/${bookingId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus }), // Kirim status sebagai string
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Gagal memperbarui status booking ke ${newStatus}`);
      }
      await fetchBookings(); // Refresh data
      toast.success(`Status booking ${bookingId} berhasil diubah menjadi ${newStatus}.`);
    } catch (error) {
      toast.error(`Gagal memperbarui status booking: ${error instanceof Error ? error.message : 'Silakan coba lagi.'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // --- Handler Edit Booking (Rekonstruksi) ---
  const handleEditBooking = (booking: Booking) => {
    // Pastikan outlet yg diedit sama
    if (booking.outletId !== selectedOutletId) {
      toast.warning('Silakan pilih outlet yang sesuai dengan booking yang akan diedit.');
      return;
    }

    // Nonaktifkan interval refresh untuk mencegah perubahan tanggal
    // Akan diaktifkan kembali saat mode edit selesai

    // Set mode edit terlebih dahulu agar refreshTodayDate tidak mengubah selectedDate
    setIsEditing(true);
    setEditingBookingId(booking.id);

    // Ambil service, filter null
    setSelectedServices(booking.bookingServices.map(bs => bs.service).filter((s): s is Service => !!s));
    setSelectedTherapist(booking.therapist);

    // Tambahkan pelanggan ke array pelanggan yang dipilih
    if (booking.customer) {
      // Jika booking memiliki array customers, gunakan itu
      if (booking.customers && Array.isArray(booking.customers)) {
        setSelectedCustomers(booking.customers);
      } else {
        // Jika tidak, gunakan pelanggan utama
        setSelectedCustomers([booking.customer]);
      }
    } else {
      setSelectedCustomers([]);
    }

    // Format tanggal booking ke yyyy-MM-dd
    // Pastikan format tanggal yang benar dengan menangani berbagai kemungkinan format
    let bookingDateFormatted;
    try {
      // Coba parse sebagai ISO string
      bookingDateFormatted = format(parseISO(booking.bookingDate as string), 'yyyy-MM-dd');
      console.log('Parsed booking date (ISO):', bookingDateFormatted);
    } catch (error) {
      try {
        // Jika gagal, coba parse sebagai Date object
        const dateObj = new Date(booking.bookingDate as any);
        bookingDateFormatted = format(dateObj, 'yyyy-MM-dd');
        console.log('Parsed booking date (Date object):', bookingDateFormatted);
      } catch (innerError) {
        // Fallback ke tanggal hari ini jika semua gagal
        bookingDateFormatted = getTodayDate();
        console.error('Failed to parse booking date, using today:', bookingDateFormatted);
      }
    }

    // Simpan tanggal booking yang akan diedit
    // Gunakan setTimeout untuk memastikan state isEditing sudah diperbarui
    setTimeout(() => {
      setSelectedDate(bookingDateFormatted);
      console.log('Selected date set to:', bookingDateFormatted);
    }, 0);

    setSelectedTime(booking.bookingTime);
    setBookingNote(booking.notes || '');
    window.scrollTo({ top: 0, behavior: 'smooth' });
    // Tutup modal lain jika perlu
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-screen"><span className="loading loading-spinner loading-lg text-teal-500"></span></div>;
  }

  return (
    <motion.div
      className="w-full max-w-[1600px] mx-auto p-2 md:p-4"
      data-theme="breaktime"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      <motion.h1 variants={fadeInUp} className="text-2xl md:text-3xl font-bold text-gray-800 mb-6">Buat & Kelola Booking</motion.h1>

      {error && (
        <motion.div variants={fadeInUp} className="alert alert-error mb-6">
          <FiAlertCircle className="w-6 h-6" />
          <span>{error}</span>
        </motion.div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 md:gap-4">
        <motion.div className="lg:col-span-3 mb-3 md:mb-4" variants={fadeInUp}>
          <div className="card bg-base-100 shadow-lg">
            <div className="card-body p-3 md:p-4">
              <h3 className="card-title text-lg font-semibold mb-2">Pilih Outlet</h3>
              {isOutletLoading ? (
                <span className="loading loading-dots loading-md"></span>
              ) : outletError ? (
                <div className="text-error flex items-center">
                  <FiAlertCircle className="mr-2"/> {outletError}
                </div>
              ) : (
                <select
                  className="select select-bordered w-full"
                  value={selectedOutletId || ''}
                  onChange={handleOutletChange}
                  disabled={isOutletLoading || isLoading || isEditing}
                >
                  <option value="" disabled={!!selectedOutletId}>-- Pilih Lokasi Outlet --</option>
                  {allOutlets.map(outlet => (
                    <option key={outlet.id} value={outlet.id}>{outlet.name}</option>
                  ))}
                </select>
              )}
            </div>
          </div>
        </motion.div>

        {!isBookingComplete && (
          <>
            <motion.div className="lg:col-span-2 space-y-3 md:space-y-4" variants={staggerContainer}>
              <motion.div variants={fadeInUp} className="card bg-white shadow border border-gray-200">
                 <div className="card-body p-3 md:p-4">
                   <h2 className="card-title text-gray-800"><FiScissors className="mr-2"/> Pilih Layanan</h2>

                   {/* Input Pencarian Layanan */}
                   <div className="relative mt-2 mb-4">
                     <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                       <FiSearch className="text-gray-500" />
                     </div>
                     <input
                       type="text"
                       className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                       placeholder="Cari layanan..."
                       value={serviceSearchTerm}
                       onChange={(e) => setServiceSearchTerm(e.target.value)}
                     />
                   </div>

                   {filteredServices.length > 0 ? (
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 mt-3">
                         {filteredServices.map(service => {
                            const count = serviceCounts[service.id] || 0;
                            return (
                              <div key={service.id} className={`card card-compact border ${count > 0 ? 'border-teal-500 bg-teal-50' : 'border-gray-200 bg-white hover:bg-gray-50'}`}>
                                <div className="card-body text-center p-2">
                                  <h3 className="text-sm font-semibold text-gray-800">{service.name}</h3>
                                  <p className="text-xs text-gray-500">{service.duration}mnt</p>
                                  <p className="text-xs text-gray-700 font-medium">Rp {service.price.toLocaleString('id-ID')}</p>
                                  <div className="card-actions justify-center items-center mt-2 space-x-2">
                                   {count > 0 && (
                                       <button onClick={() => handleRemoveService(service.id)} className="btn btn-xs btn-circle border-red-500 text-red-500 hover:bg-red-50 btn-outline">-</button>
                                    )}
                                    {count > 0 && (
                                       <span className="text-sm font-medium text-gray-700 tabular-nums">{count}x</span>
                                    )}
                                    <button onClick={() => handleAddService(service)} className={`btn btn-xs ${count > 0 ? 'btn-circle bg-teal-500 hover:bg-teal-600 text-white' : 'bg-teal-500 hover:bg-teal-600 text-white'}`}>{count > 0 ? '+' : '+ Tambah'}</button>
                                  </div>
                                </div>
                              </div>
                            );
                         })}
                      </div>
                    ) : serviceSearchTerm ? (
                      <div className="text-center py-4">
                        <FiSearch className="mx-auto text-3xl text-gray-400 mb-2" />
                        <p className="text-gray-500">Tidak ada layanan yang sesuai dengan pencarian.</p>
                        <button onClick={() => setServiceSearchTerm('')} className="btn btn-sm btn-outline mt-2">Reset Pencarian</button>
                      </div>
                    ) : (
                      <p className="text-gray-500 text-center py-4">Layanan tidak tersedia.</p>
                    )}
                 </div>
              </motion.div>

              <motion.div variants={fadeInUp} className="card bg-white shadow border border-gray-200">
                 <div className="card-body p-3 md:p-4">
                    <h2 className="card-title text-gray-800"><FiUserCheck className="mr-2"/> Pilih Terapis</h2>

                    {/* Dropdown dengan fitur search untuk terapis */}
                    <div className="form-control w-full" ref={therapistDropdownRef}>
                      <div className="dropdown dropdown-bottom w-full">
                        <div className="relative w-full">
                          <input
                            type="text"
                            placeholder="Cari terapis..."
                            className="input input-bordered w-full text-gray-700 border-gray-300 pr-16"
                            value={therapistSearchTerm}
                            onChange={(e) => setTherapistSearchTerm(e.target.value)}
                            onClick={(e) => {
                              // Prevent dropdown from closing when clicking on input
                              e.stopPropagation();
                              const dropdown = e.currentTarget.closest('.dropdown') as HTMLElement;
                              if (dropdown) dropdown.classList.add('dropdown-open');
                            }}
                          />
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                            {therapistSearchTerm && (
                              <button
                                className="mr-4 text-gray-400 hover:text-gray-600"
                                onClick={() => setTherapistSearchTerm('')}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                </svg>
                              </button>
                            )}
                            <FiSearch className="text-gray-500 pointer-events-none" />
                          </div>
                        </div>
                        <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-full max-h-60 overflow-y-auto mt-1" onClick={(e) => e.stopPropagation()}>
                          {filteredTherapists.length > 0 ? (
                            filteredTherapists.map(t => (
                              <li key={t.id}>
                                <a
                                  onClick={() => {
                                    handleSelectTherapist(t.id);
                                    setTherapistSearchTerm(t.name);
                                  }}
                                  className={selectedTherapist?.id === t.id ? 'bg-teal-50 text-teal-700' : ''}
                                >
                                  {t.name} {t.specialization ? `(${t.specialization})` : ''}
                                </a>
                              </li>
                            ))
                          ) : therapistSearchTerm ? (
                            <li className="text-center py-2">
                              <span className="text-gray-500 text-sm">Tidak ada terapis yang sesuai dengan pencarian.</span>
                              <button onClick={() => setTherapistSearchTerm('')} className="btn btn-xs btn-outline mt-1">Reset Pencarian</button>
                            </li>
                          ) : (
                            <li className="text-center py-2">
                              <span className="text-gray-500 text-sm">Pilih terapis</span>
                            </li>
                          )}
                        </ul>
                      </div>
                    </div>

                    {/* Menampilkan terapis yang dipilih */}
                    {selectedTherapist && (
                      <div className="mt-2 p-2 bg-teal-50 rounded-md border border-teal-100 flex justify-between items-center">
                        <div>
                          <span className="font-medium text-teal-700">{selectedTherapist.name}</span>
                          {selectedTherapist.specialization && (
                            <span className="text-xs text-teal-600 ml-1">({selectedTherapist.specialization})</span>
                          )}
                        </div>
                        <button
                          className="btn btn-xs btn-ghost text-teal-700"
                          onClick={() => {
                            setSelectedTherapist(null);
                            setTherapistSearchTerm('');
                          }}
                        >
                          Ganti
                        </button>
                      </div>
                    )}
                 </div>
              </motion.div>

              <motion.div variants={fadeInUp} className="card bg-white shadow border border-gray-200">
                 <div className="card-body p-3 md:p-4">
                   <h2 className="card-title text-gray-800 mb-4"><FiUsers className="mr-2"/> Pelanggan</h2>
                   {selectedCustomers.length > 0 ? (
                      <div className="space-y-2">
                        {selectedCustomers.map((customer, index) => (
                          <div key={customer.id} className="alert bg-gray-100 text-gray-700">
                            <FiCheckCircle className="text-green-500"/>
                            <div>
                              <h3 className="font-bold">{customer.name} {index === 0 ? '(Utama)' : ''}</h3>
                              <div className="flex flex-col xs:flex-row xs:items-center gap-1">
                                <div className="text-xs">{customer.phone}</div>
                                {customer.points !== undefined && (
                                  <>
                                    <span className="hidden xs:inline text-xs">•</span>
                                    <div className="flex items-center text-xs">
                                      <FiStar className="text-amber-500 mr-1 h-3 w-3" />
                                      <span>{customer.points} Poin</span>
                                    </div>
                                  </>
                                )}
                              </div>
                            </div>
                            <button
                              type="button"
                              className="btn btn-sm btn-ghost"
                              onClick={() => handleRemoveCustomer(customer.id)}
                            >
                              Hapus
                            </button>
                          </div>
                        ))}
                        {selectedCustomers.length < 2 && (
                          <div className="space-y-2">
                            <div className="form-control">
                              <div className="relative w-full">
                                <input
                                  type="text"
                                  placeholder="Cari Pelanggan Lain..."
                                  className="input input-bordered w-full placeholder:text-gray-400 text-gray-700 border-gray-300 pr-10"
                                  value={customerSearchTerm}
                                  onChange={(e) => setCustomerSearchTerm(e.target.value)}
                                />
                                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                  <FiSearch className="text-gray-500"/>
                                </div>
                              </div>
                            </div>
                            {customerSearchTerm.length > 1 && (
                              <div className="mt-2 border border-gray-200 rounded-lg shadow-sm">
                                {customerSearchResults.length > 0 ? (
                                  <ul className="menu bg-white rounded-box max-h-48 overflow-y-auto p-2 divide-y divide-gray-100">
                                    {customerSearchResults.map(cust => (
                                      <li key={cust.id}>
                                        <button
                                          type="button"
                                          onClick={() => handleSelectCustomer(cust)}
                                          className="w-full text-left text-sm text-gray-700 hover:bg-gray-100 py-2 px-3 rounded"
                                        >
                                          <div>
                                            <div className="font-medium">{cust.name}</div>
                                            <div className="text-xs text-gray-500">{cust.phone}</div>
                                          </div>
                                        </button>
                                      </li>
                                    ))}
                                  </ul>
                                ) : (
                                  <div className="p-4 text-center">
                                    <p className="text-gray-500 text-sm">Tidak ada pelanggan yang ditemukan</p>
                                    <p className="text-xs text-gray-400 mt-1">Coba kata kunci lain atau tambahkan pelanggan baru</p>
                                  </div>
                                )}
                              </div>
                            )}
                            <button
                              type="button"
                              className="btn btn-outline btn-sm w-full text-gray-700 border-gray-300 hover:bg-gray-100"
                              onClick={() => setShowAddCustomerModal(true)}
                            >
                              <FiPlus className="mr-1"/> Tambah Pelanggan Baru
                            </button>
                          </div>
                        )}
                      </div>
                   ) : (
                      <div className="space-y-2">
                          <div className="form-control">
                             <div className="relative w-full">
                                <input
                                  type="text"
                                  placeholder="Cari Nama/No. HP Pelanggan..."
                                  className="input input-bordered w-full placeholder:text-gray-400 text-gray-700 border-gray-300 pr-10"
                                  value={customerSearchTerm}
                                  onChange={(e) => setCustomerSearchTerm(e.target.value)}
                                />
                                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                  <FiSearch className="text-gray-500"/>
                                </div>
                             </div>
                          </div>
                          {customerSearchTerm.length > 1 && (
                             <div className="mt-2 border border-gray-200 rounded-lg shadow-sm">
                               {customerSearchResults.length > 0 ? (
                                 <ul className="menu bg-white rounded-box max-h-48 overflow-y-auto p-2 divide-y divide-gray-100">
                                    {customerSearchResults.map(cust => (
                                       <li key={cust.id}>
                                          <button
                                            type="button"
                                            onClick={() => handleSelectCustomer(cust)}
                                            className="w-full text-left text-sm text-gray-700 hover:bg-gray-100 py-2 px-3 rounded"
                                          >
                                            <div>
                                              <div className="font-medium">{cust.name}</div>
                                              <div className="text-xs text-gray-500">{cust.phone}</div>
                                            </div>
                                          </button>
                                        </li>
                                    ))}
                                 </ul>
                               ) : (
                                 <div className="p-4 text-center">
                                   <p className="text-gray-500 text-sm">Tidak ada pelanggan yang ditemukan</p>
                                   <p className="text-xs text-gray-400 mt-1">Coba kata kunci lain atau tambahkan pelanggan baru</p>
                                 </div>
                               )}
                             </div>
                          )}
                           <button className="btn btn-outline btn-sm w-full text-gray-700 border-gray-300 hover:bg-gray-100" onClick={() => setShowAddCustomerModal(true)}>
                             <FiPlus className="mr-1"/> Tambah Pelanggan Baru
                           </button>
                      </div>
                   )}
                 </div>
               </motion.div>
            </motion.div>

            <motion.div className="lg:col-span-1 space-y-3 md:space-y-4" variants={fadeInUp}>
               <motion.div variants={fadeInUp} className="card bg-white shadow border border-gray-200">
                  <div className="card-body p-3 md:p-4">
                     <h2 className="card-title text-gray-800 mb-4"><FiCalendar className="mr-2"/> Tanggal & Waktu</h2>
                      <div className="form-control mb-4">
                        <label className="label pb-1">
                          <span className="label-text text-gray-600">Tanggal</span>
                          <span className="label-text-alt text-xs text-teal-600">Tersedia hingga 30 hari ke depan</span>
                        </label>
                        <div className="dropdown dropdown-end w-full">
                          <div tabIndex={0} role="button" className="btn btn-outline w-full justify-between">
                            {selectedDate ? format(new Date(selectedDate), 'dd MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                            <FiCalendar className="ml-2" />
                          </div>
                          <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-64">
                            <div className="card-body p-2">
                              <div className="w-full max-w-[300px] mx-auto">
                                <div className="flex justify-between items-center py-2 mb-2">
                                  <button
                                    className="btn btn-sm btn-ghost"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      const currentDate = selectedDate ? new Date(selectedDate) : new Date();
                                      currentDate.setMonth(currentDate.getMonth() - 1);
                                      setSelectedDate(format(currentDate, 'yyyy-MM-dd'));
                                    }}
                                  >
                                    «
                                  </button>
                                  <div className="text-sm font-medium">
                                    {selectedDate ? format(new Date(selectedDate), 'MMMM yyyy', { locale: idLocale }) : format(new Date(), 'MMMM yyyy', { locale: idLocale })}
                                  </div>
                                  <button
                                    className="btn btn-sm btn-ghost"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      const currentDate = selectedDate ? new Date(selectedDate) : new Date();
                                      currentDate.setMonth(currentDate.getMonth() + 1);
                                      setSelectedDate(format(currentDate, 'yyyy-MM-dd'));
                                    }}
                                  >
                                    »
                                  </button>
                                </div>
                                <div className="grid grid-cols-7 gap-1">
                                  {/* Hari dalam seminggu */}
                                  {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                                    <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                                  ))}

                                  {/* Tanggal */}
                                  {Array.from({ length: 42 }, (_, i) => {
                                    const currentDate = selectedDate ? new Date(selectedDate) : new Date();
                                    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                                    const startingDayOfWeek = firstDayOfMonth.getDay();
                                    const day = i - startingDayOfWeek + 1;
                                    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
                                    const isCurrentMonth = date.getMonth() === currentDate.getMonth();
                                    const isSelected = selectedDate && date.getDate() === new Date(selectedDate).getDate() &&
                                                      date.getMonth() === new Date(selectedDate).getMonth() &&
                                                      date.getFullYear() === new Date(selectedDate).getFullYear();

                                    // Bandingkan tanggal tanpa waktu untuk menghindari masalah dengan jam
                                    // Gunakan format yang sama untuk perbandingan yang konsisten
                                    const dateYear = date.getFullYear();
                                    const dateMonth = String(date.getMonth() + 1).padStart(2, '0');
                                    const dateDay = String(date.getDate()).padStart(2, '0');
                                    const dateStr = `${dateYear}-${dateMonth}-${dateDay}`;

                                    // Gunakan format yang sama dengan getTodayDate() untuk menentukan isToday
                                    const isToday = dateStr === getTodayDate();
                                    // Gunakan format yang sama untuk menentukan isFutureDate
                                    const isFutureDate = dateStr > getTodayDate();

                                    // Tanggal sebelum hari ini dianggap sudah lewat
                                    const isPastDate = dateStr < getTodayDate();
                                    // Hitung tanggal 30 hari dari sekarang dengan format yang sama
                                    const future30Date = new Date();
                                    future30Date.setDate(future30Date.getDate() + 30);
                                    const future30Year = future30Date.getFullYear();
                                    const future30Month = String(future30Date.getMonth() + 1).padStart(2, '0');
                                    const future30Day = String(future30Date.getDate()).padStart(2, '0');
                                    const future30Str = `${future30Year}-${future30Month}-${future30Day}`;

                                    // Bandingkan string tanggal
                                    const isWithin30Days = dateStr <= future30Str;
                                    // Jika dalam mode edit, tanggal yang dipilih tidak boleh dinonaktifkan
                                    // meskipun tanggal tersebut sudah lewat
                                    const isEditingThisDate = isEditing && dateStr === selectedDate;
                                    const isDisabled = !isCurrentMonth || (isPastDate && !isEditingThisDate) || !isWithin30Days;

                                    return (
                                      <button
                                        key={i}
                                        className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all
                                                  ${isCurrentMonth ? '' : 'text-gray-300'}
                                                  ${isSelected ? 'bg-primary text-primary-content' : ''}
                                                  ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                                  ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-base-200' : ''}
                                                  ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                                        onClick={(e) => {
                                          e.preventDefault();
                                          if (!isDisabled) {
                                            // Gunakan dateStr yang sudah dihitung untuk konsistensi
                                            setSelectedDate(dateStr);
                                          }
                                        }}
                                        disabled={isDisabled}
                                      >
                                        {date.getDate()}
                                      </button>
                                    );
                                  })}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="form-control">
                        <label className="label pb-1">
                          <span className="label-text text-gray-600">Waktu Tersedia</span>
                          <span className="label-text-alt text-xs text-teal-600">Tersedia hingga 22:00</span>
                        </label>
                        {selectedDate ? (
                          <div className="grid grid-cols-4 gap-1 md:gap-2">
                            {/* 1. Cek jika layanan belum dipilih */}
                            {selectedServices.length === 0 ? (
                              <p className="col-span-4 text-xs text-center text-gray-500 py-2">Pilih layanan untuk melihat slot waktu.</p>
                            /* 2. Cek jika slot kosong setelah loading selesai */
                            ) : availableTimeSlots.length === 0 && !isLoading && !isOutletLoading && !isHistoryLoading ? (
                              <p className="col-span-4 text-xs text-center text-gray-500 py-2">Tidak ada slot tersedia/sesuai pada tanggal ini.</p>
                            /* 3. Tampilkan loading jika slot kosong tapi data masih dimuat */
                            ) : availableTimeSlots.length === 0 && (isLoading || isOutletLoading || isHistoryLoading) ? (
                              <span className="loading loading-dots loading-sm col-span-4 mx-auto"></span>
                            /* 4. Tampilkan slot jika ada */
                            ) : (
                              <>
                                {/* Tampilkan pesan berdasarkan tanggal yang dipilih */}
                                {selectedDate && (
                                  <div className="col-span-4 mb-2">
                                    {/* Gunakan format yang konsisten untuk perbandingan tanggal */}
                                    {selectedDate === getTodayDate() ? (
                                      <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded-md flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        Slot waktu yang sudah lewat tidak tersedia
                                      </div>
                                    ) : selectedDate > getTodayDate() ? (
                                      <div className="text-xs text-teal-600 bg-teal-50 p-2 rounded-md flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        Semua slot waktu tersedia untuk tanggal ini
                                      </div>
                                    ) : null}
                                  </div>
                                )}

                                {availableTimeSlots.map(slotInfo => (
                                  <button
                                    key={slotInfo.time}
                                    className={`btn btn-xs md:btn-sm ${selectedTime === slotInfo.time ? 'bg-teal-500 hover:bg-teal-600 text-white' : slotInfo.isAvailable ? 'btn-outline border-gray-300 text-gray-700' : 'btn-disabled bg-gray-100 text-gray-400'}`}
                                    onClick={() => slotInfo.isAvailable && setSelectedTime(slotInfo.time)}
                                    disabled={!slotInfo.isAvailable}
                                  >
                                    {slotInfo.time}
                                  </button>
                                ))}
                              </>
                            )}
                          </div>
                        ) : (
                           /* Jika tanggal belum dipilih */
                           <p className="text-xs text-center text-gray-500 py-2">Pilih tanggal terlebih dahulu.</p>
                        )}
                      </div>
                  </div>
               </motion.div>

               <div className="card bg-white shadow border border-gray-200 sticky top-20">
                 <div className="card-body p-3 md:p-4">
                   <h2 className="card-title text-gray-800 mb-4">Ringkasan Booking</h2>
                   <div className="space-y-2 text-sm border-b border-gray-200 pb-4 mb-4">
                     {serviceGroups.length > 0 ? (
                       serviceGroups.map(group => (
                         <div key={group.id} className="pl-2">
                           <div className="flex justify-between">
                             <span className="text-gray-700">
                               {group.name}
                               {group.quantity > 1 && <span className="text-xs text-gray-500 ml-1"> (x{group.quantity})</span>}
                             </span>
                             <span className="text-gray-600">{group.duration * group.quantity} mnt</span>
                           </div>
                           <div className="flex justify-between text-xs text-gray-500 pl-2">
                             <span>Rp {group.price.toLocaleString('id-ID')} {group.quantity > 1 && `× ${group.quantity}`}</span>
                             <span className="font-medium">Rp {(group.price * group.quantity).toLocaleString('id-ID')}</span>
                           </div>
                         </div>
                       ))
                     ) : (<p className="text-xs text-gray-500 pl-2">Belum ada layanan</p>)}

                     <div className="flex justify-between pt-2">
                       <span className="text-gray-600">Terapis:</span>
                       <span className="font-medium text-gray-700">{selectedTherapist?.name || '-'}</span>
                     </div>
                     <div className="flex justify-between">
                       <span className="text-gray-600">Pelanggan:</span>
                       <span className="font-medium text-gray-700">
                         {selectedCustomers.length > 0
                           ? (selectedCustomers.length === 1
                              ? selectedCustomers[0].name
                              : `${selectedCustomers[0].name} (+${selectedCustomers.length - 1})`)
                           : '-'}
                       </span>
                     </div>
                     <div className="flex justify-between">
                       <span className="text-gray-600">Jadwal:</span>
                       <span className="font-medium text-gray-700">
                         {selectedDate ? (() => {
                           try {
                             // Parse tanggal dengan benar dan gunakan UTC untuk konsistensi
                             const [year, month, day] = selectedDate.split('-').map(Number);
                             const dateObj = new Date(Date.UTC(year, month - 1, day));
                             return dateObj.toLocaleDateString('id-ID', {
                               weekday: 'short',
                               day: 'numeric',
                               month: 'short',
                               timeZone: 'UTC' // Penting untuk konsistensi
                             });
                           } catch (error) {
                             console.error('Error formatting selected date:', error);
                             return selectedDate; // Fallback ke string asli
                           }
                         })() : '-'}
                         {selectedTime ? `, ${selectedTime}` : ''}
                        </span>
                     </div>
                   </div>
                    <div className="flex justify-between text-sm">
                       <span className="text-gray-600">Estimasi Durasi</span>
                       <span className="font-medium text-gray-700">{totalDuration} menit</span>
                    </div>
                    <div className="flex justify-between text-base font-medium">
                       <span className="text-gray-700">Estimasi Harga</span>
                       <span className="text-gray-800">Rp {totalPrice.toLocaleString('id-ID')}</span>
                    </div>
                 </div>
               </div>

                <div className="card bg-white shadow border border-gray-200">
                   <div className="card-body p-3 md:p-4">
                     <h2 className="card-title text-gray-800 text-sm mb-2">Catatan Booking (Opsional)</h2>
                      <textarea
                        className="textarea textarea-bordered w-full h-20 text-gray-700 placeholder:text-gray-400 border-gray-300"
                        placeholder="Preferensi ruangan, permintaan khusus, dll."
                        value={bookingNote}
                        onChange={(e) => setBookingNote(e.target.value)}
                      ></textarea>
                   </div>
                </div>

               <div className="flex gap-2 mt-6">
                 {isEditing && (
                   <button
                     type="button"
                     className="btn btn-ghost bg-gray-200 hover:bg-gray-300 text-gray-700 flex-1"
                     onClick={handleCancelEdit}
                     disabled={isLoading}
                   >
                     Batal Edit
                   </button>
                 )}
                 <button
                    type="button"
                    className="btn bg-teal-500 hover:bg-teal-600 text-white flex-1 disabled:bg-gray-200 disabled:text-gray-400 disabled:border-gray-300"
                    onClick={isEditing ? handleUpdateBooking : handleCreateBooking}
                    disabled={selectedServices.length === 0 || !selectedTherapist || selectedCustomers.length === 0 || !selectedDate || !selectedTime || isLoading}
                 >
                    {isLoading ? <span className="loading loading-spinner loading-xs"></span> : (isEditing ? 'Simpan Perubahan' : 'Buat Booking')}
                 </button>
               </div>
            </motion.div>
          </>
        )}
      </div>

      {!isBookingComplete && (
        <motion.div variants={fadeInUp} className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-2">
            <h2 className="text-xl md:text-2xl font-bold text-gray-800">Daftar Booking Mendatang</h2>
            {totalBookings > 0 && (
              <div className="text-sm text-gray-600">
                Menampilkan {startIndex + 1} - {Math.min(endIndex, totalBookings)} dari {totalBookings} booking
              </div>
            )}
          </div>
           {isHistoryLoading ? (
             <div className="flex justify-center items-center h-40"><span className="loading loading-spinner text-teal-500"></span></div>
           ) : (
             <div className="bg-white shadow border border-gray-200 rounded-lg">
                {/* Tabel untuk layar medium dan di atasnya */}
                <div className="hidden md:block overflow-x-auto">
                  <table className="table table-zebra w-full">
                    <thead className="bg-gray-100 text-gray-700">
                      <tr>
                        <th>ID</th>
                        <th>Pelanggan</th>
                        <th>Tanggal</th>
                        <th>Waktu</th>
                        <th>Terapis</th>
                        <th>Status</th>
                        <th>Layanan</th>
                        <th className="text-center">Aksi</th>
                      </tr>
                    </thead>
                    <tbody>
                      {currentBookings.length === 0 && !isHistoryLoading && (
                         <tr><td colSpan={8} className="text-center text-gray-500 py-10">
                            <FiInbox className="w-10 h-10 mx-auto mb-2 text-gray-300"/>
                            {totalBookings === 0 ? 'Tidak ada booking mendatang.' : 'Tidak ada booking di halaman ini.'}
                         </td></tr>
                      )}
                      {currentBookings.map((booking) => (
                        <tr key={booking.id} className="hover">
                          <td className="text-xs font-mono text-gray-600">{booking.displayId || booking.id.substring(0, 8)}</td>
                          <td className="text-gray-700 font-medium">
                            {booking.customer.name}
                            {booking.customers && booking.customers.length > 1 && (
                              <span className="text-xs text-gray-500 block">
                                +{booking.customers.filter(c => c.id !== booking.customer.id).map(c => c.name).join(', ')}
                              </span>
                            )}
                          </td>
                          <td className="text-sm text-gray-600">
                            {(() => {
                              try {
                                // Coba parse tanggal dengan benar
                                const dateObj = new Date(booking.bookingDate);
                                // Tambahkan pengecekan apakah tanggal valid
                                if (isNaN(dateObj.getTime())) {
                                  console.error('Invalid date:', booking.bookingDate);
                                  return booking.bookingDate; // Tampilkan string asli jika tidak valid
                                }
                                // Gunakan toLocaleDateString dengan opsi timeZone: 'UTC' untuk menghindari masalah zona waktu
                                return dateObj.toLocaleDateString('id-ID', {
                                  day: 'numeric',
                                  month: 'short',
                                  year: 'numeric',
                                  timeZone: 'UTC' // Penting untuk konsistensi
                                });
                              } catch (error) {
                                console.error('Error formatting date:', error);
                                return booking.bookingDate; // Fallback ke string asli
                              }
                            })()}
                          </td>
                          <td className="text-sm text-gray-600">{booking.bookingTime}</td>
                          <td className="text-sm text-gray-700">{booking.therapist.name}</td>
                          <td className="text-xs">
                            <span className={`badge ${
                              booking.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                              booking.status === 'CONFIRMED' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                              booking.status === 'COMPLETED' ? 'bg-green-100 text-green-800 border-green-200' :
                              booking.status === 'CANCELLED' ? 'bg-red-100 text-red-800 border-red-200' :
                              'bg-gray-100 text-gray-800 border-gray-200'
                            }`}>
                              {booking.status}
                            </span>
                          </td>
                          <td className="text-xs text-gray-600">
                             {booking.bookingServices.length > 0 && (
                               <>
                                 {booking.bookingServices[0]?.service?.name}
                                 {booking.bookingServices[0]?.quantity > 1 && ` (${booking.bookingServices[0]?.quantity}x)`}
                                 {booking.bookingServices.length > 1 ? ` (+${booking.bookingServices.length - 1})` : ''}
                               </>
                             )}
                          </td>
                          <td className="text-center space-x-1">
                            {/* Tombol Konfirmasi (jika PENDING) */}
                            {booking.status === 'PENDING' && (
                              <button
                                type="button"
                                className="btn btn-xs btn-ghost text-blue-500 tooltip"
                                data-tip="Konfirmasi Booking"
                                onClick={() => handleUpdateBookingStatus(booking.id, 'CONFIRMED')}
                                aria-label="Konfirmasi Booking"
                              >
                                <FiCheckCircle />
                              </button>
                            )}
                            {/* Tombol Selesaikan (jika CONFIRMED) */}
                            {booking.status === 'CONFIRMED' && (
                              <button
                                type="button"
                                className="btn btn-xs btn-ghost text-green-500 tooltip"
                                data-tip="Selesaikan Booking"
                                onClick={() => handleUpdateBookingStatus(booking.id, 'COMPLETED')}
                                aria-label="Selesaikan Booking"
                              >
                                <FiCheckCircle />
                              </button>
                            )}
                            {/* Tombol WhatsApp */}
                            {(booking.status === 'PENDING' || booking.status === 'CONFIRMED') && booking.customer?.phone && (
                              <button
                                type="button"
                                className="btn btn-xs btn-ghost text-green-600 tooltip"
                                data-tip="Kirim WhatsApp"
                                onClick={() => handleSendWhatsAppMessage(booking)}
                                aria-label="Kirim konfirmasi via WhatsApp"
                              >
                                <FiMessageSquare />
                              </button>
                            )}
                            {/* Tombol Edit (selalu ada atau kondisional) */}
                            <button
                               type="button"
                               className="btn btn-xs btn-ghost text-amber-500 tooltip"
                               data-tip="Edit Booking"
                               onClick={() => handleEditBooking(booking)}
                               aria-label="Edit Booking"
                             >
                                <FiEdit />
                             </button>
                             {/* Tombol Batalkan (jika PENDING atau CONFIRMED) */}
                            {(booking.status === 'PENDING' || booking.status === 'CONFIRMED') && (
                               <button
                                 type="button"
                                 className="btn btn-xs btn-ghost text-orange-500 tooltip"
                                 data-tip="Batalkan Booking"
                                 onClick={() => handleUpdateBookingStatus(booking.id, 'CANCELLED')}
                                 aria-label="Batalkan Booking"
                               >
                                  <FiXCircle />
                               </button>
                             )}
                            {/* Tombol Hapus (selalu ada atau kondisional) */}
                            <button
                               type="button"
                               className="btn btn-xs btn-ghost text-red-500 tooltip"
                               data-tip="Hapus Booking"
                               onClick={() => handleDeleteBooking(booking)}
                               aria-label="Hapus Booking"
                             >
                                <FiTrash2 />
                             </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Tampilan kartu untuk layar kecil */}
                <div className="md:hidden">
                  {currentBookings.length === 0 && !isHistoryLoading ? (
                    <div className="text-center text-gray-500 py-10">
                      <FiInbox className="w-10 h-10 mx-auto mb-2 text-gray-300"/>
                      {totalBookings === 0 ? 'Tidak ada booking mendatang.' : 'Tidak ada booking di halaman ini.'}
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 gap-4 p-4">
                      {currentBookings.map((booking) => {
                        // Format tanggal
                        let formattedDate = '';
                        try {
                          const dateObj = new Date(booking.bookingDate);
                          if (!isNaN(dateObj.getTime())) {
                            formattedDate = dateObj.toLocaleDateString('id-ID', {
                              day: 'numeric',
                              month: 'short',
                              year: 'numeric',
                              timeZone: 'UTC'
                            });
                          } else {
                            formattedDate = booking.bookingDate as string;
                          }
                        } catch (error) {
                          formattedDate = booking.bookingDate as string;
                        }

                        // Status badge class
                        const statusClass =
                          booking.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                          booking.status === 'CONFIRMED' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                          booking.status === 'COMPLETED' ? 'bg-green-100 text-green-800 border-green-200' :
                          booking.status === 'CANCELLED' ? 'bg-red-100 text-red-800 border-red-200' :
                          'bg-gray-100 text-gray-800 border-gray-200';

                        // Layanan dengan quantity
                        let serviceText = '-';
                        if (booking.bookingServices.length > 0) {
                          const firstService = booking.bookingServices[0];
                          serviceText = firstService?.service?.name || '';

                          // Tambahkan quantity jika lebih dari 1
                          if (firstService?.quantity > 1) {
                            serviceText += ` (${firstService.quantity}x)`;
                          }

                          // Tambahkan indikator layanan tambahan
                          if (booking.bookingServices.length > 1) {
                            serviceText += ` (+${booking.bookingServices.length - 1})`;
                          }
                        }

                        return (
                          <div key={booking.id} className="card bg-base-100 shadow-sm border border-gray-200">
                            <div className="card-body p-4">
                              <div className="flex justify-between items-start mb-2">
                                <div>
                                  <h3 className="card-title text-sm font-medium text-gray-700">
                                    {booking.customer.name}
                                    {booking.customers && booking.customers.length > 1 && (
                                      <span className="text-xs text-gray-500 block font-normal">
                                        +{booking.customers.filter(c => c.id !== booking.customer.id).map(c => c.name).join(', ')}
                                      </span>
                                    )}
                                  </h3>
                                  <p className="text-xs font-mono text-gray-500">{booking.displayId || booking.id.substring(0, 8)}</p>
                                </div>
                                <span className={`badge ${statusClass}`}>{booking.status}</span>
                              </div>

                              <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-sm mb-3">
                                <div className="text-gray-500">Tanggal:</div>
                                <div className="text-gray-700">{formattedDate}</div>

                                <div className="text-gray-500">Waktu:</div>
                                <div className="text-gray-700">{booking.bookingTime}</div>

                                <div className="text-gray-500">Terapis:</div>
                                <div className="text-gray-700 truncate">{booking.therapist.name}</div>

                                <div className="text-gray-500">Layanan:</div>
                                <div className="text-gray-700 truncate">{serviceText}</div>
                              </div>

                              <div className="card-actions justify-end border-t border-gray-100 pt-3 flex-wrap">
                                {/* Tombol Konfirmasi (jika PENDING) */}
                                {booking.status === 'PENDING' && (
                                  <button
                                    type="button"
                                    className="btn btn-xs btn-ghost text-blue-500"
                                    onClick={() => handleUpdateBookingStatus(booking.id, 'CONFIRMED')}
                                    aria-label="Konfirmasi Booking"
                                  >
                                    <FiCheckCircle className="mr-1" /> Konfirmasi
                                  </button>
                                )}
                                {/* Tombol Selesaikan (jika CONFIRMED) */}
                                {booking.status === 'CONFIRMED' && (
                                  <button
                                    type="button"
                                    className="btn btn-xs btn-ghost text-green-500"
                                    onClick={() => handleUpdateBookingStatus(booking.id, 'COMPLETED')}
                                    aria-label="Selesaikan Booking"
                                  >
                                    <FiCheckCircle className="mr-1" /> Selesai
                                  </button>
                                )}
                                {/* Tombol WhatsApp */}
                                {(booking.status === 'PENDING' || booking.status === 'CONFIRMED') && booking.customer?.phone && (
                                  <button
                                    type="button"
                                    className="btn btn-xs btn-ghost text-green-600"
                                    onClick={() => handleSendWhatsAppMessage(booking)}
                                    aria-label="Kirim konfirmasi via WhatsApp"
                                  >
                                    <FiMessageSquare className="mr-1" /> WhatsApp
                                  </button>
                                )}
                                {/* Tombol Edit */}
                                <button
                                  type="button"
                                  className="btn btn-xs btn-ghost text-amber-500"
                                  onClick={() => handleEditBooking(booking)}
                                  aria-label="Edit Booking"
                                >
                                  <FiEdit className="mr-1" /> Edit
                                </button>
                                {/* Tombol Batalkan (jika PENDING atau CONFIRMED) */}
                                {(booking.status === 'PENDING' || booking.status === 'CONFIRMED') && (
                                  <button
                                    type="button"
                                    className="btn btn-xs btn-ghost text-orange-500"
                                    onClick={() => handleUpdateBookingStatus(booking.id, 'CANCELLED')}
                                    aria-label="Batalkan Booking"
                                  >
                                    <FiXCircle className="mr-1" /> Batal
                                  </button>
                                )}
                                {/* Tombol Hapus */}
                                <button
                                  type="button"
                                  className="btn btn-xs btn-ghost text-red-500"
                                  onClick={() => handleDeleteBooking(booking)}
                                  aria-label="Hapus Booking"
                                >
                                  <FiTrash2 className="mr-1" /> Hapus
                                </button>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>

                {/* Pagination Controls */}
                {totalPages > 1 && (
                  <div className="flex flex-col sm:flex-row justify-between items-center p-4 border-t border-gray-200 bg-gray-50">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-3 sm:mb-0">
                      <div className="text-sm text-gray-600">
                        Halaman {currentPage} dari {totalPages}
                      </div>
                      {/* Quick jump to page */}
                      {totalPages > 5 && (
                        <div className="flex items-center gap-2 text-sm">
                          <span className="text-gray-500">Ke halaman:</span>
                          <input
                            type="number"
                            min="1"
                            max={totalPages}
                            value={currentPage}
                            onChange={(e) => {
                              const page = parseInt(e.target.value);
                              if (page >= 1 && page <= totalPages) {
                                setCurrentPage(page);
                              }
                            }}
                            className="input input-xs input-bordered w-16 text-center"
                          />
                        </div>
                      )}
                    </div>
                    
                    {/* Desktop pagination */}
                    <div className="hidden sm:flex">
                      <div className="join">
                        <button
                          className="join-item btn btn-sm"
                          onClick={() => setCurrentPage(1)}
                          disabled={currentPage === 1}
                          title="Halaman pertama"
                        >
                          ⟪
                        </button>
                        <button
                          className="join-item btn btn-sm"
                          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                          disabled={currentPage === 1}
                          title="Halaman sebelumnya"
                        >
                          ‹
                        </button>
                        
                        {/* Generate page numbers */}
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          let pageNum;
                          if (totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (currentPage <= 3) {
                            pageNum = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            pageNum = totalPages - 4 + i;
                          } else {
                            pageNum = currentPage - 2 + i;
                          }
                          
                          return (
                            <button
                              key={pageNum}
                              className={`join-item btn btn-sm ${currentPage === pageNum ? 'btn-active bg-teal-500 text-white' : ''}`}
                              onClick={() => setCurrentPage(pageNum)}
                            >
                              {pageNum}
                            </button>
                          );
                        })}
                        
                        <button
                          className="join-item btn btn-sm"
                          onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                          disabled={currentPage === totalPages}
                          title="Halaman selanjutnya"
                        >
                          ›
                        </button>
                        <button
                          className="join-item btn btn-sm"
                          onClick={() => setCurrentPage(totalPages)}
                          disabled={currentPage === totalPages}
                          title="Halaman terakhir"
                        >
                          ⟫
                        </button>
                      </div>
                    </div>
                    
                    {/* Mobile pagination - simplified */}
                    <div className="flex sm:hidden justify-center w-full">
                      <div className="join">
                        <button
                          className="join-item btn btn-sm"
                          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                          disabled={currentPage === 1}
                        >
                          ‹ Prev
                        </button>
                        <button className="join-item btn btn-sm btn-active bg-teal-500 text-white">
                          {currentPage}
                        </button>
                        <button
                          className="join-item btn btn-sm"
                          onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                          disabled={currentPage === totalPages}
                        >
                          Next ›
                        </button>
                      </div>
                    </div>
                    
                    {/* Items info for mobile */}
                    <div className="sm:hidden mt-2 text-xs text-gray-500 text-center">
                      {startIndex + 1} - {Math.min(endIndex, totalBookings)} dari {totalBookings} booking
                    </div>
                  </div>
                )}
              </div>
           )}
        </motion.div>
      )}

      <dialog id="booking_confirm_modal" className={`modal ${isBookingComplete ? 'modal-open' : ''}`}>
        <div className="modal-box border border-gray-200 bg-white">
          {bookingDetails && (
            <>
              <div className="text-center mb-6">
                 <FiCheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4"/>
                 <h3 className="font-bold text-xl text-gray-800">Booking Dibuat!</h3>
                 <p className="py-2 text-sm text-gray-700">
                    Booking <span className="font-semibold">{bookingDetails.displayId || bookingDetails.id.substring(0, 8)}</span> untuk
                    <span className="font-semibold">
                      {bookingDetails.customer?.name || 'Pelanggan'}
                      {bookingDetails.customers && bookingDetails.customers.length > 1 && bookingDetails.customer && (
                        <span className="font-normal"> dan {bookingDetails.customers.filter(c => c.id !== bookingDetails.customer.id).map(c => c.name).join(', ')}</span>
                      )}
                    </span> pada
                    <span className="font-semibold"> {new Date(bookingDetails.bookingDate).toLocaleDateString('id-ID', { weekday:'long', day:'numeric', month:'long'})} pukul {bookingDetails.bookingTime}</span> telah dikonfirmasi.
                 </p>
              </div>

              <div className="flex flex-col gap-3 mb-4">
                <button
                  type="button"
                  onClick={() => handleSendWhatsAppMessage(bookingDetails)}
                  className="btn btn-success text-white gap-2 w-full"
                  aria-label="Kirim konfirmasi via WhatsApp"
                >
                  <FiMessageSquare className="text-lg" /> Kirim Konfirmasi via WhatsApp
                </button>

                <div className="text-xs text-gray-500 text-center px-4">
                  Kirim pesan konfirmasi booking ke WhatsApp pelanggan untuk memberikan informasi lengkap tentang booking yang telah dibuat.
                </div>
              </div>

              <div className="modal-action justify-center gap-4">
                 <button
                   type="button"
                   onClick={handleNewBooking}
                   className="btn bg-teal-500 hover:bg-teal-600 text-white"
                   aria-label="Buat booking baru"
                 >
                    <FiPlus className="mr-2"/> Buat Booking Baru
                 </button>
              </div>
            </>
          )}
        </div>
        <form method="dialog" className="modal-backdrop">
          <button
            type="button"
            onClick={handleNewBooking}
            aria-label="Tutup dialog"
          >
            close
          </button>
        </form>
      </dialog>

      <dialog id="add_customer_modal" className={`modal ${showAddCustomerModal ? 'modal-open' : ''}`}>
        <div className="modal-box border border-gray-200 bg-white">
          <h3 className="font-bold text-lg text-gray-800 mb-4">Tambah Pelanggan Baru</h3>
          <div className="form-control">
            <label className="label"><span className="label-text text-gray-600">Nama Pelanggan</span></label>
            <input type="text" placeholder="Nama Lengkap" className="input input-bordered w-full text-gray-700 border-gray-300" value={newCustomerName} onChange={(e) => setNewCustomerName(e.target.value)} />
          </div>
          <div className="form-control mt-4">
            <label className="label"><span className="label-text text-gray-600">Nomor HP</span></label>
            <input type="tel" placeholder="08xxxx" className="input input-bordered w-full text-gray-700 border-gray-300" value={newCustomerPhone} onChange={(e) => setNewCustomerPhone(e.target.value)} />
          </div>
          <div className="form-control mt-4">
            <label className="label"><span className="label-text text-gray-600">Alamat (Opsional)</span></label>
            <textarea placeholder="Alamat lengkap" className="textarea textarea-bordered w-full text-gray-700 border-gray-300" value={newCustomerAddress} onChange={(e) => setNewCustomerAddress(e.target.value)} />
          </div>
          <div className="modal-action mt-6">
             <button
               type="button"
               className="btn btn-sm bg-gray-200 hover:bg-gray-300 text-gray-700"
               onClick={() => setShowAddCustomerModal(false)}
               aria-label="Batal tambah pelanggan"
             >
               Batal
             </button>
            <button
              type="button"
              className="btn bg-teal-500 hover:bg-teal-600 text-white btn-sm"
              onClick={handleAddNewCustomer}
              disabled={!newCustomerName || !newCustomerPhone}
              aria-label="Simpan pelanggan baru"
            >
              Simpan Pelanggan
            </button>
          </div>
        </div>
         <form method="dialog" className="modal-backdrop">
           <button
             type="button"
             onClick={() => setShowAddCustomerModal(false)}
             aria-label="Tutup dialog"
           >
             close
           </button>
         </form>
      </dialog>

      {/* --> Modal Konfirmasi Hapus Booking (BARU) <-- */}
      <dialog id="delete_booking_modal" className={`modal ${isDeleteModalOpen ? 'modal-open' : ''}`}>
        <div className="modal-box max-w-sm border border-base-300">
          <button
            type="button"
            onClick={handleCloseDeleteModal}
            className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2 z-10"
            aria-label="Tutup dialog"
          >
            ✕
          </button>
          <h3 className="font-bold text-lg text-error">Konfirmasi Hapus Booking</h3>
          <p className="py-4 text-base-content/80">
            Anda yakin ingin menghapus booking ini (ID: <span className="font-mono text-xs">{bookingToDelete?.displayId || bookingToDelete?.id.substring(0, 8)}</span>)?
            <br/> Pelanggan: <span className="font-semibold">
              {bookingToDelete?.customer?.name || 'Pelanggan'}
              {bookingToDelete?.customers && bookingToDelete.customers.length > 1 && bookingToDelete.customer && (
                <span className="font-normal"> dan {bookingToDelete.customers.filter(c => c.id !== bookingToDelete.customer.id).map(c => c.name).join(', ')}</span>
              )}
            </span>
            <br/> Layanan: <span className="font-semibold">{bookingToDelete?.bookingServices?.map(bs => bs.service?.name).join(', ') || '-'}</span>
            <br/><br/> Tindakan ini tidak dapat dibatalkan.
          </p>
          <div className="modal-action">
            <button
              type="button"
              className="btn btn-sm btn-ghost"
              onClick={handleCloseDeleteModal}
              disabled={isLoading}
              aria-label="Batal hapus booking"
            >
              Batal
            </button>
            <button
              type="button"
              className="btn btn-sm btn-error"
              onClick={confirmDeleteBooking}
              disabled={isLoading}
              aria-label="Konfirmasi hapus booking"
            >
              {isLoading && <span className="loading loading-spinner loading-xs"></span>}
              Ya, Hapus
            </button>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop">
          <button
            type="button"
            onClick={handleCloseDeleteModal}
            aria-label="Tutup dialog"
          >
            close
          </button>
        </form>
      </dialog>

    </motion.div>
  );
}