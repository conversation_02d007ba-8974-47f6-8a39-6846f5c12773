'use client';

import React, { useState, useEffect, useMemo, useRef } from 'react';
import { motion } from 'framer-motion';
import { FiCalendar, FiUserCheck, FiScissors, FiArrowRight, FiArrowLeft, 
  FiClock, FiCheckCircle, FiMapPin, FiPhone, FiMail, FiUser, FiInfo, FiAlertCircle, FiShoppingCart, FiHome, FiDownload } from 'react-icons/fi';
import { addMinutes, format, parse, isValid, parseISO } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { toast } from 'sonner';
import Image from 'next/image';
import Link from 'next/link';
import html2canvas from 'html2canvas';

// Interfaces
interface Outlet {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  operationalHours?: string;
  isOpen?: boolean;
  isMain?: boolean;
}

interface Service {
  id: string;
  name: string;
  duration: number;
  price: number;
  description?: string;
  isActive: boolean;
  isFavorite?: boolean;
}

interface Therapist {
  id: string;
  name: string;
  specialization?: string;
  isAvailable: boolean;
  isServing?: boolean;
  availableAt?: string;
  isActive: boolean;
}

interface SelectedService extends Service {
  quantity: number;
}

interface CustomerInfo {
  name: string;
  phone: string;
  email?: string;
}

interface BookingResult {
  id: string;
  bookingId?: string;
  displayId?: string;
  createdAt: string;
  status: string;
}

// Animasi
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.4 }
  }
};

// Definisi animasi 
// Tidak digunakan saat ini tapi mungkin akan digunakan di masa depan
// const staggerContainer = {
//   hidden: { opacity: 0 },
//   visible: {
//     opacity: 1,
//     transition: {
//       staggerChildren: 0.1
//     }
//   }
// };

// Helper functions
// Format tanggal untuk tampilan (tidak digunakan saat ini)
// const formatDateDisplay = (date: Date | null): string => {
//   if (!date) return '';
//   return format(date, 'EEEE, d MMMM yyyy', { locale: idLocale });
// };

const parseOperatingHours = (hoursString?: string): { start: Date | null, end: Date | null } => {
  if (!hoursString) return { start: null, end: null };
  
  const defaultTime = { start: null, end: null };
  
  // Pattern matching untuk format seperti "09:00-21:00" atau "09:00 - 21:00"
  const matches = hoursString.match(/(\d{1,2}:\d{2})\s*-\s*(\d{1,2}:\d{2})/);
  if (!matches || matches.length < 3) return defaultTime;
  
  try {
    // Parse sebagai waktu pada hari ini
    const today = new Date();
    const startTime = parse(matches[1], 'HH:mm', today);
    const endTime = parse(matches[2], 'HH:mm', today);
    
    if (!isValid(startTime) || !isValid(endTime)) return defaultTime;
    
    return { start: startTime, end: endTime };
  } catch (error) {
    console.error('Error parsing operating hours:', error);
    return defaultTime;
  }
};

const generateTimeSlots = (startTime: Date, endTime: Date, intervalMinutes: number): string[] => {
  if (!startTime || !endTime) return [];
  
  const slots: string[] = [];
  let currentTime = startTime;
  
  while (currentTime < endTime) {
    // Format waktu sebagai string "HH:mm"
    slots.push(format(currentTime, 'HH:mm'));
    
    // Tambahkan interval
    currentTime = addMinutes(currentTime, intervalMinutes);
  }
  
  return slots;
};

const getTodayDate = () => {
  const today = new Date();
  return format(today, 'yyyy-MM-dd');
};

// Komponen utama
export default function ReservasiOnlinePage() {
  // State untuk langkah reservasi
  const [currentStep, setCurrentStep] = useState<number>(1);
  const formRef = useRef<HTMLDivElement>(null);
  
  // Konstanta untuk pengunjung yang tidak login
  const BOOKING_USER_ID = '46c718b7-e8ed-4b76-8b7b-1237ab9bb257';
  const BOOKING_USERNAME = 'Booking';
  
  // State data dari API
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [availableServices, setAvailableServices] = useState<Service[]>([]);
  const [availableTherapists, setAvailableTherapists] = useState<Therapist[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // State untuk form reservasi
  const [selectedOutletId, setSelectedOutletId] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(getTodayDate());
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [selectedTherapistId, setSelectedTherapistId] = useState<string>('');
  const [selectedServices, setSelectedServices] = useState<SelectedService[]>([]);
  const [bookingNote, setBookingNote] = useState<string>('');
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    phone: '',
    email: ''
  });
  
  // State untuk pencarian
  const [searchQuery, setSearchQuery] = useState<string>('');
  
  // State untuk kategori layanan
  const [selectedCategory, setSelectedCategory] = useState<string>('Semua');
  
  // State untuk menampilkan layanan favorit
  const [showFavorites, setShowFavorites] = useState<boolean>(false);
  
  // State untuk pelanggan lama
  const [isExistingCustomer, setIsExistingCustomer] = useState<boolean>(false);
  const [isSearchingCustomer, setIsSearchingCustomer] = useState<boolean>(false);
  const [customerPoints, setCustomerPoints] = useState<number>(0);
  const [searchPhone, setSearchPhone] = useState<string>('');
  
  // State untuk proses submission
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitted, setSubmitted] = useState<boolean>(false);
  const [bookingResult, setBookingResult] = useState<BookingResult | null>(null);
  
  // State untuk menyimpan layanan yang berhasil dipesan
  const [confirmedServices, setConfirmedServices] = useState<SelectedService[]>([]);
  const [confirmedDuration, setConfirmedDuration] = useState<number>(0);
  
  // State untuk loading (tidak digunakan saat ini, bisa dihapus nanti)
  // const [servicesLoading] = useState<boolean>(false);
  // const [therapistsLoading] = useState<boolean>(false);
  
  // Kalkulasi
  const serviceGroups = useMemo(() => {
    return selectedServices.reduce((acc, service) => {
      const existing = acc.find(item => item.id === service.id);
      if (existing) {
        existing.quantity += 1;
      } else {
        acc.push({ ...service, quantity: 1 });
      }
      return acc;
    }, [] as Array<Service & { quantity: number }>);
  }, [selectedServices]);
  
  const totalDuration = useMemo(() => 
    selectedServices.reduce((sum, s) => sum + s.duration, 0), 
    [selectedServices]
  );
  
  // Filter layanan berdasarkan kata kunci pencarian
  const filteredServices = useMemo(() => {
    // Filter layanan yang tidak mengandung kata "Free"
    let filtered = availableServices.filter(service => 
      !service.name.toLowerCase().includes('free')
    );
    
    // Filter untuk menampilkan hanya layanan favorit jika opsi dipilih
    if (showFavorites) {
      filtered = filtered.filter(service => service.isFavorite);
    }
    
    // Filter berdasarkan kata kunci pencarian
    if (searchQuery.trim()) {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (service.description && service.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }
    
    // Filter berdasarkan kategori
    if (selectedCategory !== 'Semua') {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(selectedCategory.toLowerCase())
      );
    }
    
    return filtered;
  }, [availableServices, searchQuery, selectedCategory, showFavorites]);
  
  // Mendapatkan slot waktu berdasarkan outlet yang dipilih
  const availableTimeSlots = useMemo(() => {
    const selectedOutlet = outlets.find(o => o.id === selectedOutletId);
    if (!selectedOutlet || !selectedOutlet.operationalHours) return [];
    
    const { start, end } = parseOperatingHours(selectedOutlet.operationalHours);
    if (!start || !end) return [];
    
    // Generate time slots dengan interval 30 menit
    return generateTimeSlots(start, end, 30);
  }, [outlets, selectedOutletId]);
  
  // Mendapatkan terapis berdasarkan outlet yang dipilih
  const filteredTherapists = useMemo(() => {
    if (!selectedOutletId) return [];
    // Sort terapis agar yang tidak sedang melayani muncul lebih dulu
    return availableTherapists
      .filter(therapist => therapist.isActive)
      .sort((a, b) => {
        // Jika a sedang melayani dan b tidak, b lebih dulu
        if (a.isServing && !b.isServing) return 1;
        // Jika b sedang melayani dan a tidak, a lebih dulu
        if (!a.isServing && b.isServing) return -1;
        // Jika keduanya sama-sama melayani atau tidak melayani, urutkan berdasarkan nama
        return a.name.localeCompare(b.name);
      });
  }, [availableTherapists, selectedOutletId]);
  
  // Effect untuk fetch data awal
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Fetch outlets dengan cookie auth
        const resOutlets = await fetch('/api/outlets', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            'x-user-id': BOOKING_USER_ID,
            'x-user-name': BOOKING_USERNAME
          }
        });
        
        if (!resOutlets.ok) throw new Error('Gagal memuat data outlet');
        const outletsData = await resOutlets.json();
        
        if (outletsData.outlets && Array.isArray(outletsData.outlets)) {
          setOutlets(outletsData.outlets.filter((o: Outlet) => o.isOpen));
          
          // Set outlet default jika ada outlet utama
          const mainOutlet = outletsData.outlets.find((o: Outlet) => o.isMain && o.isOpen);
          if (mainOutlet) {
            setSelectedOutletId(mainOutlet.id);
          } else if (outletsData.outlets.length > 0) {
            setSelectedOutletId(outletsData.outlets[0].id);
          }
        }
        
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching initial data:', err);
        setError('Terjadi kesalahan saat memuat data. Silakan coba lagi.');
        setIsLoading(false);
      }
    };
    
    fetchInitialData();
  }, []);

  // Effect untuk fetch services berdasarkan outlet yang dipilih
  useEffect(() => {
    const fetchServices = async () => {
      if (!selectedOutletId) return;
      
      try {
        setIsLoading(true);
        const res = await fetch(`/api/services?outletId=${selectedOutletId}`, {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            'x-user-id': BOOKING_USER_ID,
            'x-user-name': BOOKING_USERNAME
          }
        });
        
        if (!res.ok) throw new Error('Gagal memuat data layanan');
        
        const data = await res.json();
        if (data.services && Array.isArray(data.services)) {
          // Menandai layanan favorit
          const servicesWithFavorites = data.services.map((service: Service) => ({
            ...service,
            isFavorite: [
              'Pijat 90 Menit',
              'Pijat 120 Menit',
              'Pijat 60 Menit untuk home service',
              'Pijat Home Service 90 Menit',
              'Pijat Home Service 120 Menit'
            ].some(favName => service.name.includes(favName))
          }));
          setAvailableServices(servicesWithFavorites);
        }
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching services:', err);
        setError('Terjadi kesalahan saat memuat data layanan');
        setIsLoading(false);
      }
    };
    
    fetchServices();
  }, [selectedOutletId]);
  
  // Effect untuk fetch terapis berdasarkan outlet yang dipilih
  useEffect(() => {
    const fetchTherapists = async () => {
      if (!selectedOutletId) return;
      
      try {
        setIsLoading(true);
        const res = await fetch(`/api/therapists?outletId=${selectedOutletId}&includeActiveSessions=true`, {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            'x-user-id': BOOKING_USER_ID,
            'x-user-name': BOOKING_USERNAME
          }
        });
        
        if (!res.ok) throw new Error('Gagal memuat data terapis');
        
        const data = await res.json();
        if (data.therapists && Array.isArray(data.therapists)) {
          setAvailableTherapists(data.therapists);
          
          // Reset selected therapist jika sudah tidak tersedia di outlet baru
          if (selectedTherapistId) {
            const stillAvailable = data.therapists.some((t: Therapist) => t.id === selectedTherapistId);
            if (!stillAvailable) {
              setSelectedTherapistId('');
            }
          }
        }
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching therapists:', err);
        setError('Terjadi kesalahan saat memuat data terapis');
        setIsLoading(false);
      }
    };
    
    fetchTherapists();
  }, [selectedOutletId, selectedTherapistId]);
  
  // Scroll ke form saat langkah berubah
  useEffect(() => {
    if (formRef.current) {
      formRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [currentStep]);
  
  // Handlers
  const handleNextStep = () => {
    setCurrentStep(prev => {
      // Validasi setiap langkah dengan pesan error yang lebih friendly
      if (prev === 1 && !selectedOutletId) {
        toast.error('🏪 Silakan pilih outlet terlebih dahulu');
        return prev;
      }
      if (prev === 2 && selectedServices.length === 0) {
        toast.error('✂️ Silakan pilih minimal satu layanan');
        return prev;
      }
      if (prev === 3 && !selectedTime) {
        toast.error('⏰ Silakan pilih waktu terlebih dahulu');
        return prev;
      }
      if (prev === 4) {
        // Validasi data customer sebelum ke konfirmasi
        if (!customerInfo.name.trim()) {
          toast.error('👤 Nama lengkap harus diisi');
          return prev;
        }
        if (!customerInfo.phone.trim()) {
          toast.error('📱 Nomor HP harus diisi');
          return prev;
        }
        
        // Validasi format nomor HP
        const cleanPhone = customerInfo.phone.replace(/\D/g, '');
        const isValidPhone = cleanPhone.length >= 10 && 
                           (cleanPhone.startsWith('08') || 
                            cleanPhone.startsWith('628') || 
                            cleanPhone.startsWith('62'));
        
        if (!isValidPhone) {
          toast.error('📱 Format nomor HP tidak valid. Gunakan format 08xxx atau +62xxx');
          return prev;
        }
      }
      
      return Math.min(prev + 1, 5);
    });
  };
  
  const handlePrevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };
  
  const handleTimeChange = (time: string) => {
    setSelectedTime(time);
  };
  
  const handleAddService = (service: Service) => {
    setSelectedServices(prev => [...prev, { ...service, quantity: 1 }]);
  };
  
  const handleRemoveService = (serviceId: string) => {
    // Remove only one instance of the service with the given ID
    const index = selectedServices.findIndex(s => s.id === serviceId);
    if (index !== -1) {
      setSelectedServices(prev => [...prev.slice(0, index), ...prev.slice(index + 1)]);
    }
  };

  const handleSelectService = (service: Service) => {
    const existingService = selectedServices.find(s => s.id === service.id);
    
    if (existingService) {
      handleUpdateServiceQuantity(service.id, existingService.quantity + 1);
    } else {
      handleAddService(service);
    }
  };
  
  const handleUpdateServiceQuantity = (serviceId: string, quantity: number) => {
    if (quantity <= 0) {
      // Remove semua instance dari service ini
      setSelectedServices(prev => prev.filter(s => s.id !== serviceId));
      return;
    }
    
    // Update quantity untuk service yang sudah ada atau tambah baru jika belum ada
    setSelectedServices(prev => {
      const existingService = availableServices.find(s => s.id === serviceId);
      if (!existingService) return prev;
      
      const hasService = prev.some(s => s.id === serviceId);
      if (hasService) {
        return prev.map(s => 
          s.id === serviceId ? { ...s, quantity } : s
        );
      } else {
        return [...prev, { ...existingService, quantity }];
      }
    });
  };
  
  const handleSelectTherapist = (therapistId: string) => {
    setSelectedTherapistId(prev => prev === therapistId ? '' : therapistId);
  };

  // Event handler untuk perubahan outlet dan tanggal
  // Fungsi-fungsi di bawah ini digantikan oleh setSelectedOutletId dan onClick langsung
  // const handleOutletChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
  //   setSelectedOutletId(e.target.value);
  // };
  
  // const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   setSelectedDate(e.target.value);
  // };
  
  // const handleSelectOutlet = (outletId: string) => {
  //   setSelectedOutletId(outletId);
  //   // Auto proceed to next step
  //   setCurrentStep(2);
  // };
  
  const handleSelectCategory = (category: string) => {
    setSelectedCategory(category);
  };
  
  const handleToggleFavorites = () => {
    setShowFavorites(prev => !prev);
  };
  
  // Ref untuk debounce timeout
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleCustomerInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (name === 'notes') {
      setBookingNote(value);
    } else if (name === 'phone' && isExistingCustomer) {
      setCustomerInfo(prev => ({
        ...prev,
        [name]: value
      }));
      setSearchPhone(value);
      
      // Clear timeout sebelumnya jika ada
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      
      // Debounce pencarian untuk menghindari terlalu banyak request
      debounceTimeoutRef.current = setTimeout(() => {
        // Validasi format nomor HP Indonesia
        const cleanPhone = value.replace(/\D/g, '');
        if (cleanPhone.length >= 10 && (cleanPhone.startsWith('08') || cleanPhone.startsWith('628') || cleanPhone.startsWith('62'))) {
          searchCustomerByPhone(value);
        }
      }, 500);
    } else {
      setCustomerInfo(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // customerInfo digunakan langsung
  
  const handleStartOver = () => {
    // Clear any pending debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
      debounceTimeoutRef.current = null;
    }
    
    // Reset semua state ke nilai awal
    setCurrentStep(1);
    setSelectedServices([]);
    setSelectedTime('');
    setSelectedTherapistId('');
    setBookingNote('');
    setCustomerInfo({
      name: '',
      phone: '',
      email: ''
    });
    
    // Reset state terkait hasil booking 
    setSubmitted(false);
    setBookingResult(null);
    setConfirmedServices([]);
    setConfirmedDuration(0);
    
    // Reset pencarian pelanggan
    setSearchPhone('');
    setCustomerPoints(0);
    setIsExistingCustomer(false);
    setIsSearchingCustomer(false);
    
    // Reset search dan filter states
    setSearchQuery('');
    setSelectedCategory('Semua');
    setShowFavorites(false);
    
    // Reset any error states
    setError(null);
    setIsSubmitting(false);
    
    // Scroll ke bagian atas halaman setelah state diperbarui
    setTimeout(() => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }, 100);
    
    // Toast untuk memberi tahu pengguna
    toast.success('🔄 Form reservasi direset, silakan mulai dari awal');
  };
  
  // Fungsi untuk validasi data telah digabungkan ke dalam handleNextStep
  // sehingga tidak diperlukan lagi fungsi terpisah
  // const handleShowConfirmation = () => { ... }
  
  // Fungsi ini tidak digunakan saat ini
  // const handleCancelConfirmation = () => {
  //   setIsSubmitting(false);
  // };
  
  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      toast.loading('⏳ Sedang memproses reservasi Anda...');
      
      // Validasi data sebelum submit
      if (!customerInfo.name.trim()) {
        throw new Error('Nama lengkap harus diisi');
      }
      
      if (!customerInfo.phone.trim()) {
        throw new Error('Nomor HP harus diisi');
      }
      
      if (selectedServices.length === 0) {
        throw new Error('Silakan pilih minimal satu layanan');
      }
      
      // Membuat/mencari customer berdasarkan nomor telepon
      const customerRes = await fetch('/api/customers', {
        method: 'POST',
        credentials: 'include',
        headers: { 
          'Content-Type': 'application/json',
          'x-user-id': BOOKING_USER_ID,
          'x-user-name': BOOKING_USERNAME
        },
        body: JSON.stringify({
          name: customerInfo.name.trim(),
          phone: customerInfo.phone.trim(),
          email: customerInfo.email?.trim() || undefined
        })
      });
      
      const customerData = await customerRes.json();
      
      // Jika customer sudah ada, gunakan ID yang sudah ada
      let customerId;
      if (customerRes.status === 400 && customerData.existingCustomer) {
        customerId = customerData.existingCustomer.id;
      } else if (customerRes.ok && customerData.customer) {
        customerId = customerData.customer.id;
      } else {
        throw new Error(customerData.error || 'Gagal membuat data pelanggan');
      }
      
      // Format service ids dengan quantity
      const services = serviceGroups.map(sg => ({
        id: sg.id,
        quantity: sg.quantity
      }));
      
      // Membuat booking
      const bookingRes = await fetch('/api/bookings', {
        method: 'POST',
        credentials: 'include',
        headers: { 
          'Content-Type': 'application/json',
          'x-user-id': BOOKING_USER_ID,
          'x-user-name': BOOKING_USERNAME
        },
        body: JSON.stringify({
          customerId,
          services,
          therapistId: selectedTherapistId || undefined,
          outletId: selectedOutletId,
          date: selectedDate,
          time: selectedTime,
          notes: bookingNote.trim() || undefined,
          status: 'PENDING',
          createdById: BOOKING_USER_ID
        })
      });
      
      if (!bookingRes.ok) {
        const errorData = await bookingRes.json();
        throw new Error(errorData.error || 'Gagal membuat reservasi');
      }
      
      const bookingData = await bookingRes.json();
      setBookingResult(bookingData.booking);
      setSubmitted(true);
      setIsSubmitting(false);
      
      // Simpan layanan terpilih sebelum reset form
      setConfirmedServices([...selectedServices]);
      setConfirmedDuration(totalDuration);
      
      // Reset form
      setSelectedServices([]);
      setSelectedTime('');
      setBookingNote('');
      setCurrentStep(5); // Move to success step
      
      toast.dismiss();
      toast.success('🎉 Reservasi berhasil dibuat! Silakan tunggu konfirmasi dari kami.');
    } catch (err) {
      console.error('Error creating booking:', err);
      setIsSubmitting(false);
      toast.dismiss();
      
      // User-friendly error messages
      let errorMessage = 'Terjadi kesalahan tidak terduga';
      if (err instanceof Error) {
        if (err.message.includes('network') || err.message.includes('fetch')) {
          errorMessage = '🔄 Koneksi bermasalah. Pastikan Anda terhubung internet dan coba lagi.';
        } else if (err.message.includes('validation') || err.message.includes('required')) {
          errorMessage = '📝 Data yang Anda masukkan tidak lengkap atau tidak valid.';
        } else if (err.message.includes('already exists') || err.message.includes('duplicate')) {
          errorMessage = '⚠️ Reservasi dengan data serupa sudah ada. Silakan periksa kembali.';
        } else {
          errorMessage = `❌ ${err.message}`;
        }
      }
      
      toast.error(errorMessage);
    }
  };

  // Handle retry services
  const handleRetryServices = async () => {
    if (!selectedOutletId) return;
    
    try {
      // Tidak menggunakan setServicesLoading karena tidak ada setter
      // setServicesLoading(true);
      setError(null);
      
      // Fetch services
      const res = await fetch(`/api/services?outletId=${selectedOutletId}`, {
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': BOOKING_USER_ID,
          'x-user-name': BOOKING_USERNAME
        }
      });
      
      if (!res.ok) {
        throw new Error('Gagal memuat data layanan');
      }
      
      const data = await res.json();
      
      if (data.services && Array.isArray(data.services)) {
        // Tag layanan favorit
        const servicesWithFavorites = data.services.map((service: Service) => ({
          ...service,
          isFavorite: [
            'Pijat 90 Menit',
            'Pijat 120 Menit',
            'Pijat 60 Menit',
            'Pijat Home Service 90 Menit',
            'Pijat Home Service 120 Menit'
          ].some(favName => service.name.includes(favName))
        }));
        
        setAvailableServices(servicesWithFavorites);
      }
      
      // Tidak menggunakan setServicesLoading karena tidak ada setter
      // setServicesLoading(false);
    } catch (e) {
      console.error('Error fetching services:', e);
      setError('Gagal memuat data layanan');
      // Tidak menggunakan setServicesLoading karena tidak ada setter
      // setServicesLoading(false);
    }
  };

  // Fungsi untuk mencari data pelanggan berdasarkan nomor HP
  const searchCustomerByPhone = async (phone: string) => {
    // Validasi format nomor HP yang lebih ketat
    const cleanPhone = phone.replace(/\D/g, '');
    if (!cleanPhone || cleanPhone.length < 10) return;
    
    // Validasi format HP Indonesia
    const isValidIndonesianPhone = cleanPhone.startsWith('08') || 
                                   cleanPhone.startsWith('628') || 
                                   cleanPhone.startsWith('62');
    
    if (!isValidIndonesianPhone) {
      toast.error('Format nomor HP tidak valid. Gunakan format 08xxx atau +62xxx');
      return;
    }
    
    try {
      setIsSearchingCustomer(true);
      const response = await fetch(`/api/customers/by-phone?phone=${encodeURIComponent(phone)}`, {
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': BOOKING_USER_ID,
          'x-user-name': BOOKING_USERNAME
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('Data pelanggan ditemukan:', data);
        
        if (data.customer) {
          // Update data pelanggan yang ditemukan
          setCustomerInfo({
            name: data.customer.name || '',
            phone: data.customer.phone,
            email: data.customer.email || ''
          });
          
          // Set poin pelanggan jika ada
          const points = data.customer.points || 0;
          console.log('Points:', points);
          setCustomerPoints(points);
          
          if (data.customer.name) {
            toast.success(`🎉 Data pelanggan ${data.customer.name} ditemukan!`);
          } else {
            toast.success('✅ Data pelanggan ditemukan!');
          }
        } else {
          // Reset data jika pelanggan tidak ditemukan
          setCustomerInfo(prev => ({
            ...prev,
            name: ''
          }));
          setCustomerPoints(0);
          toast.info('💡 Nomor HP ini belum terdaftar. Silakan isi nama lengkap untuk mendaftar sebagai pelanggan baru.');
        }
      } else {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        toast.error('❌ Gagal mencari data pelanggan. Silakan coba lagi.');
      }
      
      setIsSearchingCustomer(false);
    } catch (error) {
      console.error('Error searching customer:', error);
      toast.error('🔄 Koneksi bermasalah. Pastikan Anda terhubung internet dan coba lagi.');
      setIsSearchingCustomer(false);
    }
  };

  // Steps content
  const steps = [
    { id: 1, title: 'Pilih Outlet', icon: <FiMapPin /> },
    { id: 2, title: 'Pilih Layanan', icon: <FiScissors /> },
    { id: 3, title: 'Pilih Jadwal', icon: <FiCalendar /> },
    { id: 4, title: 'Data Diri', icon: <FiUser /> },
    { id: 5, title: 'Konfirmasi', icon: <FiCheckCircle /> }
  ];
  
  // Render step indicator
  const renderStepIndicator = () => {
    return (
      <div className="flex justify-between mb-8">
        {steps.map((step, index) => (
          <div 
            key={step.id} 
            className="flex flex-col items-center relative"
          >
            {/* Garis penghubung antar steps */}
            {index < steps.length - 1 && (
              <div className={`absolute h-[3px] top-5 w-full left-1/2 -z-10 ${
                currentStep > step.id ? 'bg-gradient-to-r from-teal-500 to-amber-400' : 'bg-gray-200'
              }`}></div>
            )}
            
            {/* Step circle */}
            <div className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 transition-all duration-300 ${
              currentStep === step.id 
                ? 'bg-gradient-to-r from-teal-500 to-amber-400 text-white shadow-lg shadow-teal-200'
                : currentStep > step.id 
                  ? 'bg-gradient-to-r from-teal-500 to-amber-400 text-white' 
                  : 'bg-gray-100 text-gray-400'
            }`}>
              {currentStep > step.id ? <FiCheckCircle className="w-5 h-5" /> : step.icon}
            </div>
            
            {/* Step title */}
            <span className={`text-xs sm:text-sm font-medium text-center transition-colors ${
              currentStep === step.id 
                ? 'text-gray-800' 
                : currentStep > step.id 
                  ? 'text-teal-500' 
                  : 'text-gray-400'
            }`}>
              {step.title}
            </span>
            
            {/* Step number */}
            <span className={`text-[10px] transition-colors ${
              currentStep === step.id 
                ? 'text-amber-500 font-medium' 
                : currentStep > step.id 
                  ? 'text-teal-400' 
                  : 'text-gray-400'
            }`}>
              Langkah {step.id}
            </span>
          </div>
        ))}
      </div>
    );
  };
  
  // Render outlet selection
  const renderOutletSelection = () => {
    return (
      <motion.div variants={fadeInUp} className="mb-6">
        <h2 className="text-xl font-bold mb-4 text-gray-800 flex items-center">
          <div className="w-8 h-8 rounded-full bg-teal-100 flex items-center justify-center mr-3">
            <FiMapPin className="w-4 h-4 text-teal-600" />
          </div>
          Pilih Outlet
        </h2>
        <p className="text-sm text-gray-600 mb-6 ml-11">Silakan pilih outlet terdekat atau favorit Anda untuk memulai reservasi.</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {isLoading ? (
            <div className="col-span-full p-12 text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full border-4 border-teal-50 border-t-teal-500 animate-spin"></div>
              <p className="text-sm text-gray-500">Memuat data outlet...</p>
            </div>
          ) : error ? (
            <div className="col-span-full p-8 text-center bg-red-50 rounded-xl border border-red-100">
              <FiAlertCircle className="w-10 h-10 text-red-400 mx-auto mb-3" />
              <p className="text-red-600 mb-3">{error}</p>
              <button 
                className="btn btn-sm bg-red-500 hover:bg-red-600 text-white"
                onClick={() => handleRetryServices()}
              >
                Coba Lagi
              </button>
            </div>
          ) : outlets.length === 0 ? (
            <div className="col-span-full p-8 text-center bg-gray-50 rounded-xl">
              <p className="text-gray-500">Tidak ada outlet yang tersedia saat ini</p>
            </div>
          ) : (
            outlets.map(outlet => (
                            <div                key={outlet.id}                className={`p-5 rounded-xl cursor-pointer transition-all duration-300 hover:shadow-md ${                  selectedOutletId === outlet.id                     ? 'bg-gradient-to-br from-teal-50 to-amber-50 border-2 border-amber-300'                     : 'bg-white border border-gray-200 hover:border-amber-200'                }`}                onClick={() => setSelectedOutletId(outlet.id)}              >
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-bold text-gray-800 text-lg">{outlet.name}</h3>
                    {outlet.address && (
                      <p className="text-gray-600 mt-1 flex items-start">
                        <FiMapPin className="w-3 h-3 text-amber-500 mt-1 mr-1 flex-shrink-0" />
                        <span>{outlet.address}</span>
                      </p>
                    )}
                    {outlet.phone && (
                      <p className="text-gray-600 mt-1 flex items-center">
                        <FiPhone className="w-3 h-3 text-amber-500 mr-1 flex-shrink-0" />
                        <span>{outlet.phone}</span>
                      </p>
                    )}
                  </div>
                  
                  {outlet.isMain && (
                    <span className="bg-gradient-to-r from-amber-500 to-amber-400 text-white text-xs px-2 py-1 rounded-full font-medium">
                      Outlet Utama
                    </span>
                  )}
                </div>
                
                {outlet.operationalHours && (
                  <div className="mt-3 flex items-center p-2 bg-gray-50 rounded-lg">
                    <FiClock className="text-teal-500 mr-2 w-4 h-4 flex-shrink-0" />
                    <span className="text-sm text-gray-600">{outlet.operationalHours}</span>
                  </div>
                )}
                
                {selectedOutletId === outlet.id && (
                  <div className="mt-3 text-teal-600 text-sm font-medium flex items-center">
                    <FiCheckCircle className="mr-1" />
                    Outlet terpilih
                  </div>
                )}
                
                {outlet.isOpen === false && (
                  <div className="mt-2 text-xs text-red-600 font-medium bg-red-50 rounded-lg p-2 flex items-center">
                    <FiAlertCircle className="mr-1" />
                    Saat ini tutup
                  </div>
                )}
              </div>
            ))
          )}
        </div>
        
        <div className="mt-8 flex justify-end">
          <button
            className={`btn px-6 ${
              !selectedOutletId || isLoading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-teal-500 to-amber-500 hover:from-teal-600 hover:to-amber-600 text-white shadow-md hover:shadow-lg transition-all duration-300'
            }`}
            onClick={handleNextStep}
            disabled={!selectedOutletId || isLoading}
          >
            Lanjutkan <FiArrowRight className="ml-2"/>
          </button>
        </div>
      </motion.div>
    );
  };
  
  // Render service selection
  const renderServiceSelection = () => {
    return (
      <motion.div variants={fadeInUp} className="mb-6">
        <h2 className="text-xl font-bold mb-4 text-gray-800 flex items-center">
          <div className="w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center mr-3">
            <FiScissors className="w-4 h-4 text-amber-600" />
          </div>
          Pilih Layanan
        </h2>
        <p className="text-sm text-gray-600 mb-6 ml-11">Silakan pilih layanan spa dan pijat yang Anda inginkan.</p>
        
        <div className="mb-6 relative">
          <div className="relative">
            <input
              type="text"
              className="w-full pl-12 pr-4 py-3 rounded-xl bg-white border border-gray-200 focus:border-teal-400 focus:ring focus:ring-teal-200 transition-all"
              placeholder="Cari layanan..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" 
                stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
              </svg>
            </div>
            {searchQuery && (
              <button 
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                onClick={() => setSearchQuery('')}
              >
                &times;
              </button>
            )}
          </div>
        </div>
        
        <div className="mb-5">
          <h3 className="text-sm font-medium text-gray-500 mb-2">Kategori Layanan</h3>
          <div className="flex flex-wrap gap-2">
            {['Semua', 'Pijat', 'Lulur', 'Reflexi', 'Masker'].map(category => (
              <button
                key={category}
                className={`px-3 py-1.5 rounded-full text-sm border transition-colors ${
                  category === selectedCategory 
                    ? 'bg-gradient-to-r from-teal-500 to-amber-500 text-white border-transparent'
                    : 'bg-white text-gray-700 border-gray-200 hover:border-amber-300 hover:bg-amber-50'
                }`}
                onClick={() => handleSelectCategory(category)}
              >
                {category}
              </button>
            ))}
            <button
              className={`px-3 py-1.5 rounded-full text-sm border transition-colors flex items-center ${
                showFavorites
                  ? 'bg-gradient-to-r from-amber-500 to-red-500 text-white border-transparent'
                  : 'bg-white text-gray-700 border-gray-200 hover:border-amber-300 hover:bg-amber-50'
              }`}
              onClick={handleToggleFavorites}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill={showFavorites ? "currentColor" : "none"} 
                stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
              </svg>
              Favorit
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-h-96 overflow-y-auto pr-1 pb-1">
          {isLoading ? (
            <div className="col-span-full p-12 text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full border-4 border-amber-50 border-t-amber-500 animate-spin"></div>
              <p className="text-sm text-gray-500">Memuat data layanan...</p>
            </div>
          ) : error ? (
            <div className="col-span-full p-8 text-center bg-red-50 rounded-xl border border-red-100">
              <FiAlertCircle className="w-10 h-10 text-red-400 mx-auto mb-3" />
              <p className="text-red-600 mb-3">{error}</p>
              <button 
                className="btn btn-sm bg-red-500 hover:bg-red-600 text-white"
                onClick={() => handleRetryServices()}
              >
                Coba Lagi
              </button>
            </div>
          ) : filteredServices.length === 0 ? (
            <div className="col-span-full p-8 text-center bg-gray-50 rounded-xl">
              <p className="text-gray-500">
                {searchQuery 
                  ? `Tidak ada layanan yang cocok dengan "${searchQuery}"` 
                  : 'Tidak ada layanan yang tersedia saat ini'}
              </p>
            </div>
          ) : (
            filteredServices.map((service) => {
              const isSelected = selectedServices.some(
                s => s.id === service.id
              );
              const selectedService = selectedServices.find(
                s => s.id === service.id
              );
              const quantity = selectedService?.quantity || 0;
              
              return (
                <div 
                  key={service.id}
                  className={`p-5 rounded-xl transition-transform duration-300 hover:-translate-y-1 cursor-pointer ${
                    isSelected 
                      ? 'bg-gradient-to-br from-teal-50 to-amber-50 border-2 border-amber-300 shadow-md' 
                      : service.isFavorite 
                        ? 'bg-gradient-to-br from-amber-50 to-red-50 border border-amber-200 hover:border-amber-300 hover:shadow-md'
                        : 'bg-white border border-gray-200 hover:border-amber-200 hover:shadow-md'
                  }`}
                  onClick={() => handleSelectService(service)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center">
                        <h3 className="font-bold text-gray-800">{service.name}</h3>
                        {service.isFavorite && (
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor" 
                            stroke="none" className="ml-2 text-amber-500">
                            <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
                          </svg>
                        )}
                      </div>
                      {service.description && (
                        <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                      )}
                      <div className="flex items-center mt-2">
                        <span className="text-amber-600 font-bold">
                          Rp {service.price.toLocaleString('id-ID')}
                        </span>
                        <span className="mx-2 text-gray-300">•</span>
                        <span className="text-xs text-gray-500 flex items-center">
                          <FiClock className="mr-1" />
                          {service.duration} menit
                        </span>
                      </div>
                    </div>
                    
                    {isSelected && (
                      <div className="flex items-center space-x-1 bg-white rounded-full shadow-sm border border-gray-100 p-1">
                        <button
                          className="w-7 h-7 flex items-center justify-center rounded-full bg-gray-50 hover:bg-gray-100 text-gray-500"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleUpdateServiceQuantity(service.id, quantity - 1);
                          }}
                          disabled={quantity <= 1}
                        >
                          -
                        </button>
                        <span className="w-6 text-center font-medium text-teal-600">{quantity}</span>
                        <button
                          className="w-7 h-7 flex items-center justify-center rounded-full bg-gradient-to-r from-teal-500 to-amber-500 text-white"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleUpdateServiceQuantity(service.id, quantity + 1);
                          }}
                        >
                          +
                        </button>
                      </div>
                    )}
                  </div>
                  
                  {isSelected && (
                    <div className="mt-3 text-teal-600 text-sm font-medium flex items-center">
                      <FiCheckCircle className="mr-1" />
                      Ditambahkan
                    </div>
                  )}
                </div>
              );
            })
          )}
        </div>
        
        {selectedServices.length > 0 && (
          <div className="mt-6 p-5 bg-white rounded-xl border border-amber-200 shadow-md">
            <h3 className="text-gray-800 font-bold flex items-center mb-3">
              <FiShoppingCart className="text-amber-500 mr-2" />
              Layanan yang dipilih
            </h3>
            <div className="space-y-2">
              {selectedServices.map((service) => (
                <div key={service.id} className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                  <div className="flex items-center flex-1 min-w-0 mr-2">
                    <FiScissors className="text-teal-500 mr-2 flex-shrink-0" />
                    <span className="text-gray-800 break-words">
                      {service.name} {service.quantity > 1 ? `(x${service.quantity})` : ''}
                    </span>
                  </div>
                  <div className="flex items-center gap-3 flex-shrink-0">
                    <span className="text-amber-600 font-medium whitespace-nowrap">
                      Rp {(service.price * (service.quantity || 1)).toLocaleString('id-ID')}
                    </span>
                    <button
                      className="w-6 h-6 rounded-full bg-red-50 flex items-center justify-center text-red-500 hover:bg-red-100"
                      onClick={() => handleRemoveService(service.id)}
                    >
                      <span className="text-sm">&times;</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 pt-3 border-t border-amber-100 flex justify-between items-center">
              <div>
                <span className="text-xs text-gray-500">Total Durasi</span>
                <p className="font-medium text-gray-700 flex items-center">
                  <FiClock className="mr-1 text-amber-500" />
                  {totalDuration} menit
                </p>
              </div>
              <div className="text-right">
                <span className="text-xs text-gray-500">Total Harga</span>
                <p className="text-lg font-bold text-teal-600">
                  Rp {selectedServices.reduce((total, service) => 
                    total + (service.price * (service.quantity || 1)), 0
                  ).toLocaleString('id-ID')}
                </p>
              </div>
            </div>
          </div>
        )}
        
        <div className="mt-8 flex justify-between">
          <button
            className="btn px-6 bg-white border border-gray-300 hover:bg-gray-50 text-gray-700"
            onClick={handlePrevStep}
          >
            <FiArrowLeft className="mr-2"/> Kembali
          </button>
          <button
            className={`btn px-6 ${
              selectedServices.length === 0 || isLoading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-teal-500 to-amber-500 hover:from-teal-600 hover:to-amber-600 text-white shadow-md hover:shadow-lg transition-all duration-300'
            }`}
            onClick={handleNextStep}
            disabled={selectedServices.length === 0 || isLoading}
          >
            Lanjutkan <FiArrowRight className="ml-2"/>
          </button>
        </div>
      </motion.div>
    );
  };

  // Render schedule selection
  const renderScheduleSelection = () => {
    return (
      <motion.div variants={fadeInUp} className="mb-6">
        <h2 className="text-xl font-bold mb-4 text-gray-800 flex items-center">
          <div className="w-8 h-8 rounded-full bg-teal-100 flex items-center justify-center mr-3">
            <FiCalendar className="w-4 h-4 text-teal-600" />
          </div>
          Pilih Jadwal & Terapis
        </h2>
        <p className="text-sm text-gray-600 mb-6 ml-11">Silakan pilih tanggal, waktu dan terapis yang Anda inginkan.</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-100">
            <h3 className="font-bold text-gray-700 mb-4 flex items-center">
              <FiCalendar className="text-amber-500 mr-2" />
              Jadwal Kunjungan
            </h3>
            
            <div className="mb-5">
              <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor="visitDate">
                Tanggal Kunjungan
              </label>
              
              {/* Modern Calendar Dropdown */}
              <div className="dropdown dropdown-end w-full">
                <div tabIndex={0} role="button" className="btn btn-outline w-full justify-between bg-gray-50 border-gray-200 hover:bg-gray-100 hover:border-teal-300 text-gray-700">
                  {selectedDate ? format(new Date(selectedDate), 'dd MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                  <FiCalendar className="ml-2" />
                </div>
                <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-full max-w-72 p-2">
                  <div className="calendar">
                    <div className="flex justify-between items-center py-2 mb-2 px-1">
                      <button
                        className="btn btn-sm btn-ghost p-1"
                        onClick={(e) => {
                          e.preventDefault();
                          const currentDate = selectedDate ? new Date(selectedDate) : new Date();
                          currentDate.setMonth(currentDate.getMonth() - 1);
                          setSelectedDate(format(currentDate, 'yyyy-MM-dd'));
                        }}
                      >
                        «
                      </button>
                      <div className="text-sm font-medium">
                        {selectedDate ? format(new Date(selectedDate), 'MMMM yyyy', { locale: idLocale }) : format(new Date(), 'MMMM yyyy', { locale: idLocale })}
                      </div>
                      <button
                        className="btn btn-sm btn-ghost p-1"
                        onClick={(e) => {
                          e.preventDefault();
                          const currentDate = selectedDate ? new Date(selectedDate) : new Date();
                          currentDate.setMonth(currentDate.getMonth() + 1);
                          setSelectedDate(format(currentDate, 'yyyy-MM-dd'));
                        }}
                      >
                        »
                      </button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {/* Hari dalam seminggu */}
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}

                      {/* Tanggal */}
                      {Array.from({ length: 42 }, (_, i) => {
                        const currentDate = selectedDate ? new Date(selectedDate) : new Date();
                        const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === currentDate.getMonth();
                        
                        // Format tanggal untuk perbandingan
                        const dateYear = date.getFullYear();
                        const dateMonth = String(date.getMonth() + 1).padStart(2, '0');
                        const dateDay = String(date.getDate()).padStart(2, '0');
                        const dateStr = `${dateYear}-${dateMonth}-${dateDay}`;

                        const isSelected = selectedDate && dateStr === selectedDate;
                        const isToday = dateStr === getTodayDate();
                        const isPastDate = dateStr < getTodayDate();

                        // Tanggal maksimal adalah 30 hari ke depan
                        const maxDate = new Date();
                        maxDate.setDate(maxDate.getDate() + 30);
                        const maxDateStr = format(maxDate, 'yyyy-MM-dd');
                        const isFutureDisabled = dateStr > maxDateStr;

                        const isDisabled = !isCurrentMonth || isPastDate || isFutureDisabled;

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all
                                     ${isCurrentMonth ? '' : 'text-gray-300'}
                                     ${isSelected ? 'bg-teal-500 text-white' : ''}
                                     ${isToday && !isSelected ? 'border border-teal-500 text-teal-600' : ''}
                                     ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-gray-100' : ''}
                                     ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={(e) => {
                              e.preventDefault();
                              if (!isDisabled) {
                                setSelectedDate(dateStr);
                              }
                            }}
                            disabled={isDisabled}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                    <div className="flex justify-between mt-2">
                      <button
                        className="btn btn-xs btn-ghost"
                        onClick={(e) => {
                          e.preventDefault();
                          setSelectedDate(getTodayDate());
                        }}
                      >
                        Hari Ini
                      </button>
                      <button
                        className="btn btn-xs btn-ghost"
                        onClick={(e) => {
                          e.preventDefault();
                          setSelectedDate('');
                        }}
                      >
                        Reset
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mb-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Waktu Kunjungan
              </label>
              
              <div className="grid grid-cols-3 gap-2">
                {selectedDate ? (
                  availableTimeSlots.length === 0 ? (
                    <div className="col-span-full p-4 text-center bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-500">Tidak ada slot waktu tersedia</p>
                    </div>
                  ) : (
                    <>
                      {selectedDate === getTodayDate() && (
                        <div className="col-span-3 mb-2">
                          <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded-md flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Slot waktu yang sudah lewat tidak tersedia
                          </div>
                        </div>
                      )}
                      
                      {availableTimeSlots.map(time => {
                        // Cek apakah slot waktu sudah lewat pada hari ini
                        const isToday = selectedDate === getTodayDate();
                        let isTimeSlotPassed = false;
                        
                        if (isToday) {
                          const currentTime = new Date();
                          const timeSlot = new Date();
                          const [hours, minutes] = time.split(':').map(Number);
                          timeSlot.setHours(hours, minutes, 0, 0);
                          isTimeSlotPassed = currentTime > timeSlot;
                        }
                        
                        return (
                          <div
                            key={time}
                            className={`px-3 py-3 text-center rounded-lg cursor-pointer transition-all duration-200 ${
                              selectedTime === time 
                                ? 'bg-gradient-to-r from-teal-500 to-amber-500 text-white font-medium shadow-md' 
                                : isTimeSlotPassed
                                  ? 'bg-gray-100 text-gray-400 opacity-50 cursor-not-allowed'
                                  : 'bg-gray-50 text-gray-700 hover:bg-amber-50 hover:text-amber-700'
                            }`}
                            onClick={() => !isTimeSlotPassed && handleTimeChange(time)}
                          >
                            {time}
                          </div>
                        );
                      })}
                    </>
                  )
                ) : (
                  <div className="col-span-full p-4 text-center bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-500">Pilih tanggal terlebih dahulu</p>
                  </div>
                )}
              </div>
              
              <p className="text-xs text-gray-500 mt-3 flex items-center">
                <FiInfo className="mr-1 text-amber-500" />
                Durasi pijat disesuaikan dengan layanan yang dipilih
              </p>
            </div>
          </div>
          
          <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-100">
            <h3 className="font-bold text-gray-700 mb-4 flex items-center">
              <FiUserCheck className="text-amber-500 mr-2" />
              Pilih Terapis
            </h3>
            
            {/* Tambahkan tombol random terapis */}
            <div className="mb-4">
              <button
                className="w-full py-3 px-4 bg-gradient-to-r from-amber-400 to-amber-500 text-white rounded-lg hover:shadow-md transition-all flex items-center justify-center gap-2"
                onClick={() => {
                  // Filter terapis yang tersedia (tidak sedang melayani)
                  const availableTherapists = filteredTherapists.filter(t => !t.isServing);
                  if (availableTherapists.length > 0) {
                    // Pilih terapis secara acak
                    const randomIndex = Math.floor(Math.random() * availableTherapists.length);
                    const randomTherapist = availableTherapists[randomIndex];
                    setSelectedTherapistId(randomTherapist.id);
                    
                    toast.success(`Terapis ${randomTherapist.name} dipilih secara acak!`);
                  } else {
                    toast.error('Tidak ada terapis yang tersedia saat ini');
                  }
                }}
                disabled={isLoading || filteredTherapists.filter(t => !t.isServing).length === 0}
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 00-3.7-3.7 48.678 48.678 0 00-7.324 0 4.006 4.006 0 00-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3l-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 003.7 3.7 48.656 48.656 0 007.324 0 4.006 4.006 0 003.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3l-3 3" />
                </svg>
                Pilih Terapis Secara Acak
              </button>
              <p className="text-xs text-center text-gray-500 mt-2">
                Bingung memilih terapis? Klik tombol di atas untuk memilih secara acak
              </p>
            </div>
            
            <div className="h-64 overflow-y-auto pr-2">
              {isLoading ? (
                <div className="p-8 text-center">
                  <div className="w-10 h-10 mx-auto mb-3 rounded-full border-4 border-teal-50 border-t-teal-500 animate-spin"></div>
                  <p className="text-sm text-gray-500">Memuat terapis...</p>
                </div>
              ) : filteredTherapists.length === 0 ? (
                <div className="p-6 text-center bg-gray-50 rounded-xl">
                  <p className="text-gray-500">Tidak ada terapis yang tersedia</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-3">
                  {filteredTherapists.map(therapist => (
                    <div 
                      key={therapist.id}
                      className={`p-4 rounded-xl cursor-pointer transition-all duration-200 ${
                        selectedTherapistId === therapist.id 
                          ? 'bg-gradient-to-br from-teal-50 to-amber-50 border-2 border-amber-300' 
                          : therapist.isServing
                            ? 'bg-gray-100 border border-gray-200 opacity-60 cursor-not-allowed'
                            : 'bg-gray-50 border border-gray-200 hover:border-amber-200'
                      }`}
                      onClick={() => !therapist.isServing && handleSelectTherapist(therapist.id)}
                    >
                      <div className="flex items-center">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-r from-teal-500 to-amber-500 flex-shrink-0 flex items-center justify-center text-white mr-3 shadow-md">
                          <span className="font-bold text-lg">{therapist.name.charAt(0)}</span>
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-800">{therapist.name}</h4>
                          {therapist.specialization && (
                            <p className="text-xs text-gray-500">{therapist.specialization}</p>
                          )}
                          
                          {selectedTherapistId === therapist.id && (
                            <p className="text-teal-600 text-xs font-medium mt-1 flex items-center">
                              <FiCheckCircle className="mr-1" /> Terpilih
                            </p>
                          )}
                        </div>
                      </div>
                      
                      {therapist.isServing && therapist.availableAt && (
                        <div className="mt-2 text-xs text-amber-600 font-medium bg-amber-50 p-2 rounded-lg flex items-center">
                          <FiClock className="mr-1" />
                          <span>Sedang melayani, tersedia setelah {new Date(therapist.availableAt).toLocaleTimeString('id-ID', {hour: '2-digit', minute: '2-digit'})}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="mt-6 bg-white p-5 rounded-xl shadow-sm border border-gray-100">
          <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor="bookingNote">
            Catatan Khusus <span className="text-gray-400">(Opsional)</span>
          </label>
          <textarea
            id="bookingNote"
            className="w-full px-4 py-4 rounded-xl bg-gray-50 border border-gray-200 focus:border-teal-400 focus:ring focus:ring-teal-200 transition-all h-24 text-base text-gray-800"
            placeholder="Masukkan preferensi atau permintaan khusus Anda di sini..."
            value={bookingNote}
            onChange={(e) => setBookingNote(e.target.value)}
          ></textarea>
        </div>
        
        <div className="mt-8 flex justify-between">
          <button
            className="btn px-6 bg-white border border-gray-300 hover:bg-gray-50 text-gray-700"
            onClick={handlePrevStep}
          >
            <FiArrowLeft className="mr-2"/> Kembali
          </button>
          <button
            className={`btn px-6 ${
              !selectedDate || !selectedTime || !selectedTherapistId || isLoading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-teal-500 to-amber-500 hover:from-teal-600 hover:to-amber-600 text-white shadow-md hover:shadow-lg transition-all duration-300'
            }`}
            onClick={handleNextStep}
            disabled={!selectedDate || !selectedTime || !selectedTherapistId || isLoading}
          >
            Lanjutkan <FiArrowRight className="ml-2"/>
          </button>
        </div>
      </motion.div>
    );
  };
  
  // Render customer info
  const renderCustomerInfo = () => {
    return (
      <motion.div variants={fadeInUp} className="mb-6">
        <h2 className="text-xl font-bold mb-4 text-gray-800 flex items-center">
          <div className="w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center mr-3">
            <FiUser className="w-4 h-4 text-amber-600" />
          </div>
          Informasi Pelanggan
        </h2>
        <p className="text-sm text-gray-600 mb-6 ml-11">Silakan lengkapi data diri Anda untuk reservasi.</p>
        
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          {/* Checkbox Pelanggan Lama */}
          <div className="mb-6">
            <div className="form-control">
              <label className="flex items-center gap-2 cursor-pointer">
                <input 
                  type="checkbox" 
                  className="checkbox checkbox-success" 
                  checked={isExistingCustomer}
                  onChange={() => {
                    setIsExistingCustomer(!isExistingCustomer);
                    // Reset poin dan data pelanggan saat checkbox berubah
                    if (!isExistingCustomer) {
                      setCustomerPoints(0);
                      setSearchPhone('');
                    } else {
                      // Reset form jika uncheck
                      setCustomerInfo(prev => ({
                        ...prev,
                        name: ''
                      }));
                    }
                  }}
                />
                <span className="text-gray-700 font-medium">Saya sudah pernah berkunjung sebelumnya</span>
              </label>
              {isExistingCustomer && (
                <p className="text-xs text-gray-500 mt-2 ml-7">
                  Masukkan nomor HP Anda, kami akan mengambil data Anda secara otomatis
                </p>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-1 gap-5">
            {/* Nomor HP - Selalu ditampilkan */}
            <div>
              <label className="block text-base font-medium text-gray-700 mb-2" htmlFor="phone">
                Nomor HP <span className="text-amber-500">*</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FiPhone className="text-gray-400" />
                </div>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  className="w-full pl-12 px-4 py-4 rounded-xl bg-gray-50 border border-gray-200 focus:border-teal-400 focus:ring focus:ring-teal-200 transition-all text-base text-gray-800"
                  placeholder="Contoh: 081234567890"
                  value={isExistingCustomer ? searchPhone : customerInfo.phone}
                  onChange={handleCustomerInfoChange}
                  required
                />
                {isSearchingCustomer && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="w-5 h-5 border-2 border-teal-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-2 flex items-start">
                <FiInfo className="mr-1 mt-0.5 text-amber-500 flex-shrink-0" />
                Format: 08xxxxxxxxxx (tanpa spasi atau karakter khusus)
              </p>
            </div>
            
            {/* Tampilkan poin pelanggan jika ditemukan */}
            {isExistingCustomer && customerPoints > 0 && (
              <div className="bg-amber-50 p-4 rounded-xl border border-amber-100 mb-2">
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-amber-800">Poin Anda</p>
                    <p className="text-xl font-bold text-amber-600">{customerPoints} Poin</p>
                  </div>
                </div>
                <p className="text-xs text-amber-700 mt-2">
                  Poin dapat ditukarkan dengan berbagai hadiah menarik
                </p>
              </div>
            )}
            
            {/* Nama Lengkap - Hanya jika bukan pelanggan lama atau belum ditemukan data */}
            {(!isExistingCustomer || (isExistingCustomer && !customerInfo.name)) && (
              <div>
                <label className="block text-base font-medium text-gray-700 mb-2" htmlFor="name">
                  Nama Lengkap <span className="text-amber-500">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <FiUser className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    className="w-full pl-12 px-4 py-4 rounded-xl bg-gray-50 border border-gray-200 focus:border-teal-400 focus:ring focus:ring-teal-200 transition-all text-base text-gray-800"
                    placeholder="Masukkan nama lengkap Anda"
                    value={customerInfo.name}
                    onChange={handleCustomerInfoChange}
                    required
                  />
                </div>
              </div>
            )}
            
            {/* Tampilkan nama pelanggan jika ditemukan */}
            {isExistingCustomer && customerInfo.name && (
              <div className="bg-teal-50 p-4 rounded-xl border border-teal-100">
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-teal-100 flex items-center justify-center mr-3 flex-shrink-0">
                    <FiUser className="h-5 w-5 text-teal-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-teal-800">Nama Pelanggan</p>
                    <p className="text-lg font-bold text-teal-600 break-words">{customerInfo.name}</p>
                  </div>
                </div>
              </div>
            )}
            
            {/* Catatan Khusus untuk Reservasi */}
            <div className="mt-4">
              <label className="block text-base font-medium text-gray-700 mb-2" htmlFor="notes">
                Catatan Khusus <span className="text-gray-400">(Opsional)</span>
              </label>
              <textarea
                id="notes"
                name="notes"
                className="w-full px-4 py-4 rounded-xl bg-gray-50 border border-gray-200 focus:border-teal-400 focus:ring focus:ring-teal-200 transition-all h-24 text-base text-gray-800"
                placeholder="Masukkan preferensi atau permintaan khusus Anda di sini..."
                value={bookingNote}
                onChange={handleCustomerInfoChange}
              ></textarea>
            </div>
          </div>
          
          <div className="mt-6 pt-5 border-t border-gray-100">
            <div className="bg-amber-50 p-4 rounded-xl border border-amber-200">
              <h4 className="text-amber-800 font-medium flex items-center mb-2">
                <FiInfo className="mr-2 text-amber-500" />
                Informasi Penting
              </h4>
              <ul className="text-sm text-amber-700 space-y-2">
                <li className="flex items-start">
                  <FiCheckCircle className="mt-0.5 mr-2 text-amber-500 flex-shrink-0" />
                  <span>Informasi Anda akan kami simpan untuk mempermudah reservasi berikutnya</span>
                </li>
                <li className="flex items-start">
                  <FiCheckCircle className="mt-0.5 mr-2 text-amber-500 flex-shrink-0" />
                  <span>Mohon pastikan nomor HP Anda aktif dan dapat menerima SMS/WhatsApp</span>
                </li>
                <li className="flex items-start">
                  <FiCheckCircle className="mt-0.5 mr-2 text-amber-500 flex-shrink-0" />
                  <span>Kami akan menghubungi Anda untuk konfirmasi reservasi</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="mt-8 flex justify-between">
          <button
            className="btn px-6 bg-white border border-gray-300 hover:bg-gray-50 text-gray-700"
            onClick={handlePrevStep}
          >
            <FiArrowLeft className="mr-2"/> Kembali
          </button>
          <button
            className={`btn px-6 ${
              !customerInfo.name || !customerInfo.phone
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-teal-500 to-amber-500 hover:from-teal-600 hover:to-amber-600 text-white shadow-md hover:shadow-lg transition-all duration-300'
            }`}
            onClick={handleNextStep}
            disabled={!customerInfo.name || !customerInfo.phone}
          >
            Lanjutkan <FiArrowRight className="ml-2"/>
          </button>
        </div>
      </motion.div>
    );
  };

  // Render confirmation
  const renderConfirmation = () => {
    // Format tanggal dan waktu untuk ditampilkan dengan error handling yang lebih robust
    let formattedDate;
    let displayDate;
    
    try {
      // Coba parse sebagai ISO string dulu
      formattedDate = parseISO(selectedDate);
      
      // Jika tidak valid, coba sebagai Date constructor
      if (!isValid(formattedDate)) {
        formattedDate = new Date(selectedDate);
      }
      
      // Jika masih tidak valid, gunakan fallback
      if (!isValid(formattedDate)) {
        console.warn('Invalid date format:', selectedDate);
        displayDate = selectedDate; // Gunakan string asli sebagai fallback
      } else {
        displayDate = format(formattedDate, 'EEEE, dd MMMM yyyy', { locale: idLocale });
      }
    } catch (error) {
      console.error('Error parsing date:', error);
      displayDate = selectedDate; // Fallback ke string asli
    }

    // Hitung total harga
    const totalConfirmationPrice = (() => {
      if (!selectedServices || selectedServices.length === 0) return 0;
      return selectedServices.reduce((acc, curr) =>
        acc + (curr.price * (curr.quantity || 1)), 0);
    })();

    const selectedOutlet = outlets.find((o: Outlet) => o.id === selectedOutletId);
    return (
      <motion.div variants={fadeInUp} className="mb-6">
        <h2 className="text-xl font-bold mb-4 text-gray-800 flex items-center">
          <div className="w-8 h-8 rounded-full bg-teal-100 flex items-center justify-center mr-3">
            <FiCheckCircle className="w-4 h-4 text-teal-600" />
          </div>
          Konfirmasi Reservasi
        </h2>
        <p className="text-sm text-gray-600 mb-6 ml-11">Harap periksa kembali detail reservasi Anda sebelum melanjutkan.</p>

        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-teal-500 to-amber-500 px-6 py-4">
            <h3 className="text-white font-bold text-lg">Detail Reservasi</h3>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-5">
                <div className="bg-teal-50 p-4 rounded-xl border border-teal-100">
                  <div className="flex items-start">
                    <FiMapPin className="text-teal-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="text-sm font-medium text-teal-800">Outlet</h4>
                      <p className="text-gray-800 font-bold mt-1">{selectedOutlet?.name}</p>
                      {selectedOutlet?.address && (
                        <p className="text-sm text-gray-600 mt-1">{selectedOutlet.address}</p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="bg-amber-50 p-4 rounded-xl border border-amber-100">
                  <div className="flex items-start">
                    <FiCalendar className="text-amber-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="text-sm font-medium text-amber-800">Jadwal</h4>
                      <p className="text-gray-800 font-bold mt-1">{displayDate}</p>
                      <p className="text-sm text-gray-600">Pukul: {selectedTime} WITA</p>
                    </div>
                  </div>
                </div>

                <div className="bg-teal-50 p-4 rounded-xl border border-teal-100">
                  <div className="flex items-start">
                    <FiUserCheck className="text-teal-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="text-sm font-medium text-teal-800">Terapis</h4>
                      <p className="text-gray-800 font-bold mt-1">
                        {selectedTherapistId ?
                          (availableTherapists.find(t => t.id === selectedTherapistId)?.name || 'Tidak ditemukan') :
                          'Belum dipilih'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <div className="bg-gray-50 p-4 rounded-xl border border-gray-200">
                  <div className="flex items-start mb-4">
                    <FiUser className="text-gray-500 mr-3 mt-1 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-700">Data Pelanggan</h4>
                      <p className="text-gray-800 font-bold mt-1 break-words">{customerInfo.name}</p>
                      <p className="text-sm text-gray-600 break-all">{customerInfo.phone}</p>
                      {customerInfo.email && (
                        <p className="text-sm text-gray-500 break-all">{customerInfo.email}</p>
                      )}
                    </div>
                  </div>

                  {bookingNote && (
                    <div className="flex items-start pt-3 border-t border-gray-200">
                      <FiInfo className="text-gray-500 mr-3 mt-1 flex-shrink-0" />
                      <div>
                        <h4 className="text-sm font-medium text-gray-700">Catatan</h4>
                        <p className="text-sm text-gray-600 mt-1">{bookingNote}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="mt-6">
              <h4 className="font-medium text-gray-700 mb-3 flex items-center">
                <FiScissors className="text-amber-500 mr-2" />
                Layanan yang Dipilih
              </h4>

              <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
                <div className="grid grid-cols-3 bg-gray-50 text-sm font-medium text-gray-700">
                  <div className="p-3 border-r border-gray-200">Layanan</div>
                  <div className="p-3 border-r border-gray-200 text-center">Durasi</div>
                  <div className="p-3 text-right">Harga</div>
                </div>

                <div className="divide-y divide-gray-100">
                  {selectedServices.map((service, index) => (
                    <div key={index} className="grid grid-cols-3 py-3 px-3 text-sm">
                      <div className="font-medium text-gray-800">
                        {service.name} {service.quantity > 1 ? `(x${service.quantity})` : ''}
                      </div>
                      <div className="text-center text-gray-600">
                        {service.duration} menit
                      </div>
                      <div className="text-right font-medium">
                        Rp {(service.price * (service.quantity || 1)).toLocaleString('id-ID')}
                      </div>
                    </div>
                  ))}

                  {/* Total */}
                  <div className="grid grid-cols-3 py-3 px-3 bg-gray-50 font-bold text-gray-800">
                    <div className="col-span-2">Total</div>
                    <div className="text-right text-teal-600">
                      Rp {totalConfirmationPrice.toLocaleString('id-ID')}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 bg-amber-50 rounded-xl border border-amber-200 p-5">
          <div className="flex">
            <FiAlertCircle className="text-amber-500 mr-3 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="font-bold text-amber-800">Informasi Penting:</h4>
              <ul className="list-disc list-inside pl-1 space-y-1 mt-2 text-sm text-amber-700">
                <li>Harap datang 10-15 menit sebelum jadwal reservasi Anda</li>
                <li>Pembayaran dilakukan di tempat setelah layanan selesai</li>
                <li>Silakan hubungi outlet jika ada perubahan jadwal</li>
                <li>Pastikan nomor HP Anda aktif dan dapat dihubungi</li>
              </ul>
              
              {/* Tombol untuk menghubungi outlet via WhatsApp */}
              {selectedOutlet?.phone && (
                <div className="mt-4 pt-3 border-t border-amber-200">
                  <p className="text-sm font-medium text-amber-800 mb-2">Ada pertanyaan atau perubahan jadwal?</p>
                  <a 
                    href={(() => {
                      try {
                        const cleanPhone = selectedOutlet.phone.replace(/^0/, '62').replace(/[^0-9]/g, '');
                        const outletName = selectedOutlet.name || 'Breaktime';
                        const services = selectedServices.map(s => 
                          `${s.name}${s.quantity > 1 ? ` (${s.quantity}x)` : ""}`
                        ).join(', ');
                        const durationHours = Math.floor(totalDuration / 60);
                        const durationMinutes = totalDuration % 60;
                        const durationText = durationHours > 0 ? `${durationHours} jam ${durationMinutes} menit` : `${durationMinutes} menit`;
                        
                        const message = `Halo ${outletName}, saya ingin bertanya tentang reservasi di Breaktime untuk tanggal ${displayDate} pukul ${selectedTime}.\n\nLayanan: ${services}\nDurasi Total: ${durationText}`;
                        
                        return `https://api.whatsapp.com/send?phone=${cleanPhone}&text=${encodeURIComponent(message)}`;
                      } catch (error) {
                        console.error('Error creating WhatsApp URL:', error);
                        return '#';
                      }
                    })()}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center gap-2 bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg transition-all"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M17.6 6.32A7.85 7.85 0 0 0 12.05 4c-4.38 0-7.93 3.55-7.93 7.93a7.9 7.9 0 0 0 1.07 3.96L4 20l4.19-1.1a7.91 7.91 0 0 0 3.79.96h.04c4.38 0 7.93-3.55 7.93-7.94a7.9 7.9 0 0 0-2.35-5.6zm-5.55 12.18h-.03a6.57 6.57 0 0 1-3.35-.92l-.24-.14-2.49.65.67-2.43-.16-.25a6.59 6.59 0 0 1-1.01-3.47c0-3.64 2.96-6.59 6.61-6.59a6.58 6.58 0 0 1 6.59 6.59c0 3.64-2.96 6.56-6.59 6.56zm3.61-4.93c-.2-.1-1.17-.58-1.35-.64-.18-.07-.32-.1-.45.1-.13.2-.5.64-.62.77-.11.13-.23.15-.43.05-.2-.1-.84-.31-1.6-.99-.59-.53-.99-1.18-1.1-1.38-.12-.2-.01-.31.09-.41.09-.09.2-.23.3-.35.1-.12.13-.2.2-.34.07-.13.03-.25-.02-.35-.05-.1-.45-1.08-.62-1.48-.16-.39-.33-.33-.45-.34-.12-.01-.25-.01-.38-.01-.13 0-.34.05-.52.25-.18.2-.67.66-.67 1.61 0 .95.69 1.87.79 2 .1.13 1.38 2.11 3.35 2.96.47.2.83.32 1.12.41.47.15.9.13 1.24.08.38-.06 1.17-.48 1.33-.94.17-.46.17-.86.12-.94-.05-.08-.19-.13-.4-.23z"/>
                    </svg>
                    Hubungi outlet via WhatsApp
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="mt-8 flex justify-between">
          <button
            className="btn px-6 bg-white border border-gray-300 hover:bg-gray-50 text-gray-700"
            onClick={handlePrevStep}
            disabled={isSubmitting}
          >
            <FiArrowLeft className="mr-2"/> Kembali
          </button>
          <button
            className="btn px-6 bg-gradient-to-r from-teal-500 to-amber-500 hover:from-teal-600 hover:to-amber-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <div className="w-5 h-5 rounded-full border-2 border-white border-t-transparent animate-spin mr-2"></div>
                Memproses...
              </>
            ) : (
              <>
                Konfirmasi & Pesan <FiCheckCircle className="ml-2"/>
              </>
            )}
          </button>
        </div>
      </motion.div>
    );
  };

  // Render success message
  const renderSuccess = () => {
    // Tambahkan log untuk debug
    console.log('Rendering success page with data:');
    console.log('selectedServices:', selectedServices); // Harusnya sudah kosong
    console.log('confirmedServices:', confirmedServices); // Data layanan yang terkonfirmasi
    console.log('totalDuration:', totalDuration); // Harusnya 0
    console.log('confirmedDuration:', confirmedDuration); // Durasi yang tersimpan
    
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-10"
      >
        <div className="mx-auto w-24 h-24 bg-gradient-to-r from-teal-500 to-amber-500 rounded-full flex items-center justify-center mb-8 shadow-lg">
          <FiCheckCircle className="w-12 h-12 text-white" />
        </div>

        <h2 className="text-3xl font-bold text-gray-800 mb-4">Reservasi Berhasil!</h2>
        <p className="text-gray-600 mb-8 max-w-md mx-auto">
          Terima kasih telah melakukan reservasi. Kami akan segera menghubungi Anda untuk konfirmasi.
        </p>

        <div ref={screenshotRef} className="bg-white p-6 rounded-xl shadow-md border border-gray-100 mb-4 max-w-lg mx-auto">
          {bookingResult && (
            <div className="mb-5 pb-5 border-b border-gray-100">
              <h3 className="font-bold text-gray-800 mb-2">Detail Booking</h3>
              <div className="bg-gray-50 rounded-lg p-3 flex justify-between items-center">
                <span className="text-sm text-gray-600">Booking ID:</span>
                <span className="font-bold text-teal-600">{bookingResult.displayId || bookingResult.id.substring(0, 8)}</span>
              </div>
              
              {/* Tambahkan informasi nama pelanggan */}
              <div className="mt-3">
                <div className="bg-blue-50 rounded-lg p-3 flex items-center">
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                    <FiUser className="text-blue-600 h-5 w-5" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-blue-800">Nama Pelanggan</p>
                    <p className="text-base font-bold text-blue-600 break-words">{customerInfo.name}</p>
                  </div>
                </div>
              </div>
              
              {confirmedServices.length > 0 && (
                <div className="mt-3">
                  <h4 className="text-sm font-semibold text-gray-700 mb-2">Layanan:</h4>
                  <div className="bg-gray-50 rounded-lg p-3">
                    <ul className="text-sm text-gray-600 space-y-1">
                      {confirmedServices.map((service, index) => (
                        <li key={index} className="flex justify-between">
                          <span className="break-words">{service.name} {service.quantity > 1 ? `(${service.quantity}x)` : ''}</span>
                          <span className="font-medium whitespace-nowrap ml-2">Rp {(service.price * service.quantity).toLocaleString('id-ID')}</span>
                        </li>
                      ))}
                    </ul>
                    <div className="border-t border-gray-200 mt-2 pt-2 flex justify-between font-medium text-gray-700">
                      <span>Durasi Total:</span>
                      <span className="whitespace-nowrap ml-2">
                        {Math.floor(confirmedDuration / 60) > 0 ? 
                          `${Math.floor(confirmedDuration / 60)} jam ` : 
                          ''
                        }
                        {confirmedDuration % 60} menit
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          <h3 className="font-bold text-gray-800 mb-3">Detail kontak Anda:</h3>
          <div className="space-y-3">
            <div className="bg-gray-50 p-3 rounded-lg flex items-center">
              <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center mr-3 flex-shrink-0">
                <FiPhone className="text-amber-600" />
              </div>
              <span className="text-gray-800 break-all">{customerInfo.phone}</span>
            </div>

            {customerInfo.email && (
              <div className="bg-gray-50 p-3 rounded-lg flex items-center">
                <div className="w-10 h-10 rounded-full bg-teal-100 flex items-center justify-center mr-3 flex-shrink-0">
                  <FiMail className="text-teal-600" />
                </div>
                <span className="text-gray-800 break-all">{customerInfo.email}</span>
              </div>
            )}
          </div>
        </div>
        
        {/* Tombol Save Screenshot */}
        <button
          onClick={handleTakeScreenshot}
          className="btn bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 text-white mb-6 flex items-center justify-center gap-2 mx-auto"
        >
          <FiDownload className="h-5 w-5" />
          Simpan Bukti Booking
        </button>
          
        {/* WhatsApp Konfirmasi tetap berada di sini */}
        {bookingResult && (
          <div className="max-w-lg mx-auto mb-8">
            <h3 className="font-bold text-gray-800 mb-3">Konfirmasi melalui WhatsApp:</h3>
            
            {/* Cari outlet yang dipilih */}
            {outlets.find(o => o.id === selectedOutletId)?.phone && (
              <a 
                href={(() => {
                  try {
                    const outlet = outlets.find(o => o.id === selectedOutletId);
                    if (!outlet?.phone) return '#';
                    
                    const cleanPhone = outlet.phone.replace(/^0/, '62').replace(/[^0-9]/g, '');
                    const outletName = outlet.name || 'Breaktime';
                    const bookingId = bookingResult.displayId || bookingResult.id.substring(0, 8);
                    const dateFormatted = format(new Date(selectedDate), 'EEEE, dd MMMM yyyy', { locale: idLocale });
                    const services = confirmedServices.map(s => 
                      `${s.name}${s.quantity > 1 ? ` (${s.quantity}x)` : ""}`
                    ).join(', ');
                    const durationHours = Math.floor(confirmedDuration / 60);
                    const durationMinutes = confirmedDuration % 60;
                    const durationText = durationHours > 0 ? `${durationHours} jam ${durationMinutes} menit` : `${durationMinutes} menit`;
                    
                    const message = `Halo ${outletName},\n\nSaya baru saja melakukan reservasi dengan detail:\n\nBooking ID: ${bookingId}\nTanggal: ${dateFormatted}\nWaktu: ${selectedTime}\nNama: ${customerInfo.name}\nLayanan: ${services}\nDurasi Total: ${durationText}\n\nMohon konfirmasi reservasi saya. Terima kasih.`;
                    
                    return `https://api.whatsapp.com/send?phone=${cleanPhone}&text=${encodeURIComponent(message)}`;
                  } catch (error) {
                    console.error('Error creating WhatsApp confirmation URL:', error);
                    return '#';
                  }
                })()}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center gap-2 bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-lg transition-all w-full"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M17.6 6.32A7.85 7.85 0 0 0 12.05 4c-4.38 0-7.93 3.55-7.93 7.93a7.9 7.9 0 0 0 1.07 3.96L4 20l4.19-1.1a7.91 7.91 0 0 0 3.79.96h.04c4.38 0 7.93-3.55 7.93-7.94a7.9 7.9 0 0 0-2.35-5.6zm-5.55 12.18h-.03a6.57 6.57 0 0 1-3.35-.92l-.24-.14-2.49.65.67-2.43-.16-.25a6.59 6.59 0 0 1-1.01-3.47c0-3.64 2.96-6.59 6.61-6.59a6.58 6.58 0 0 1 6.59 6.59c0 3.64-2.96 6.56-6.59 6.56zm3.61-4.93c-.2-.1-1.17-.58-1.35-.64-.18-.07-.32-.1-.45.1-.13.2-.5.64-.62.77-.11.13-.23.15-.43.05-.2-.1-.84-.31-1.6-.99-.59-.53-.99-1.18-1.1-1.38-.12-.2-.01-.31.09-.41.09-.09.2-.23.3-.35.1-.12.13-.2.2-.34.07-.13.03-.25-.02-.35-.05-.1-.45-1.08-.62-1.48-.16-.39-.33-.33-.45-.34-.12-.01-.25-.01-.38-.01-.13 0-.34.05-.52.25-.18.2-.67.66-.67 1.61 0 .95.69 1.87.79 2 .1.13 1.38 2.11 3.35 2.96.47.2.83.32 1.12.41.47.15.9.13 1.24.08.38-.06 1.17-.48 1.33-.94.17-.46.17-.86.12-.94-.05-.08-.19-.13-.4-.23z"/>
                </svg>
                Konfirmasi Reservasi via WhatsApp
              </a>
            )}
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            className="btn bg-white border border-gray-300 hover:bg-gray-50 hover:border-teal-300 transition-all text-gray-700 px-6 flex items-center justify-center gap-2 active:scale-95 active:bg-teal-50"
            onClick={() => {
              // Tambahkan efek visual bahwa tombol diklik
              const btn = document.getElementById('new-booking-btn');
              if (btn) {
                btn.classList.add('animate-pulse');
                setTimeout(() => {
                  btn.classList.remove('animate-pulse');
                }, 500);
              }
              handleStartOver();
            }}
            id="new-booking-btn"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Buat Reservasi Baru
          </button>

          <Link 
            href="/" 
            className="btn bg-gradient-to-r from-teal-500 to-amber-500 hover:from-teal-600 hover:to-amber-600 text-white shadow-md hover:shadow-lg transition-all duration-300 px-6 flex items-center justify-center gap-2 active:scale-95"
          >
            <FiHome className="h-5 w-5" />
            Kembali ke Beranda
          </Link>
        </div>
      </motion.div>
    );
  };

  // Tambahkan ref untuk container yang akan di-screenshot
  const screenshotRef = useRef<HTMLDivElement>(null);
  
  // Fungsi untuk mengambil screenshot dengan memory cleanup yang proper
  const handleTakeScreenshot = () => {
    if (!screenshotRef.current) {
      toast.error('❌ Element screenshot tidak ditemukan');
      return;
    }

    const toastId = toast.loading('📸 Menyiapkan screenshot...');
    let tempContainer: HTMLElement | null = null;
    
    try {
      // Buat salinan elemen untuk screenshot
      const clone = screenshotRef.current.cloneNode(true) as HTMLElement;
      
      // Buat container sementara dan tambahkan clone ke dalamnya
      tempContainer = document.createElement('div');
      tempContainer.appendChild(clone);
      tempContainer.style.position = 'absolute';
      tempContainer.style.left = '-9999px';
      tempContainer.style.top = '-9999px';
      tempContainer.style.width = 'auto';
      tempContainer.style.height = 'auto';
      document.body.appendChild(tempContainer);
      
      // Ganti semua warna gradient dengan warna solid untuk kompatibilitas
      const gradientElements = clone.querySelectorAll('[class*="gradient"], [class*="bg-gradient"]');
      gradientElements.forEach((el: Element) => {
        (el as HTMLElement).style.background = '#ffffff';
        (el as HTMLElement).style.color = '#1f2937'; // text-gray-800
      });
      
      // Style untuk elemen parent
      clone.style.backgroundColor = '#ffffff';
      clone.style.borderRadius = '12px';
      clone.style.padding = '24px';
      clone.style.maxWidth = '600px';
      clone.style.margin = '0 auto';
      
      // Delay untuk memastikan styling ter-apply
      setTimeout(() => {
        if (!tempContainer) return;
        
        html2canvas(clone, {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          width: clone.scrollWidth,
          height: clone.scrollHeight,
          onclone: (clonedDoc) => {
            // Cleanup styles tambahan pada clone
            const elements = clonedDoc.querySelectorAll('*');
            elements.forEach((el) => {
              const computedStyle = window.getComputedStyle(el);
              if (computedStyle.background.includes('gradient') || 
                  computedStyle.backgroundImage.includes('gradient')) {
                (el as HTMLElement).style.background = '#ffffff';
                (el as HTMLElement).style.backgroundImage = 'none';
              }
            });
          }
        }).then(canvas => {
          try {
            // Convert canvas ke blob untuk performa yang lebih baik
            canvas.toBlob((blob) => {
              if (!blob) {
                throw new Error('Gagal membuat screenshot blob');
              }
              
              // Buat URL untuk download
              const url = URL.createObjectURL(blob);
              const link = document.createElement('a');
              link.download = `Breaktime-Booking-${bookingResult?.displayId || Date.now()}.png`;
              link.href = url;
              
              // Trigger download
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              
              // Cleanup URL object
              URL.revokeObjectURL(url);
              
              toast.dismiss(toastId);
              toast.success('📱 Screenshot berhasil disimpan!');
            }, 'image/png', 0.95);
          } catch (downloadError) {
            console.error('Error during download:', downloadError);
            toast.dismiss(toastId);
            toast.error('❌ Gagal menyimpan screenshot');
          }
        }).catch(err => {
          console.error('Error taking screenshot:', err);
          toast.dismiss(toastId);
          toast.error(`❌ Gagal mengambil screenshot: ${err.message}`);
        }).finally(() => {
          // Cleanup tempContainer
          if (tempContainer && document.body.contains(tempContainer)) {
            document.body.removeChild(tempContainer);
          }
        });
      }, 300);
      
    } catch (error) {
      console.error('Error in screenshot setup:', error);
      toast.dismiss(toastId);
      toast.error('❌ Gagal menyiapkan screenshot');
      
      // Cleanup jika ada error
      if (tempContainer && document.body.contains(tempContainer)) {
        document.body.removeChild(tempContainer);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 py-8 px-4 relative overflow-hidden">
      {/* Elemen dekoratif */}
      <div className="absolute top-0 right-0 -z-10 w-96 h-96 rounded-full opacity-10 bg-gradient-to-r from-teal-500/20 to-teal-400/10 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 -z-10 w-96 h-96 rounded-full opacity-10 bg-gradient-to-r from-amber-500/20 to-amber-300/10 blur-3xl"></div>
      
      <div className="max-w-4xl mx-auto">
        <header className="mb-12 text-center">
          <div className="flex flex-col items-center justify-center mb-6">
            <Image
              src="/logo.png"
              alt="Breaktime Logo"
              width={120}
              height={120}
              className="mb-2"
              priority
            />
            <p className="text-teal-600 text-sm">Badan Segar Urusan Lancar</p>
          </div>

          <h1 className="text-3xl sm:text-4xl font-bold text-gray-800 mb-3 bg-gradient-to-r from-teal-600 to-amber-500 inline-block text-transparent bg-clip-text">Reservasi Online</h1>
          <p className="text-gray-600 max-w-md mx-auto">Mudahkan Reservasimu Dengan Reservasi Online Yang Selalu Siap Untuk Mengatur Waktu Anda</p>
        </header>
        
        {submitted ? (
          renderSuccess()
        ) : (
          <div 
            ref={formRef} 
            className="bg-white rounded-xl shadow-xl p-6 overflow-hidden border border-gray-100"
          >
            {renderStepIndicator()}
            
            {currentStep === 1 && renderOutletSelection()}
            {currentStep === 2 && renderServiceSelection()}
            {currentStep === 3 && renderScheduleSelection()}
            {currentStep === 4 && renderCustomerInfo()}
            {currentStep === 5 && renderConfirmation()}
          </div>
        )}
      </div>
    </div>
  );
} 