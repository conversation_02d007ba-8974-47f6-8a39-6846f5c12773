import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Gender } from '@/lib/types/prisma';

// Cache untuk menyimpan hasil penentuan gender dari API
const genderCache = new Map<string, Gender>();

// Fungsi untuk menentukan gender berdasarkan nama menggunakan Genderize.io API
async function determineGenderFromNameAPI(name: string): Promise<Gender> {
  if (!name) return Gender.OTHER;

  // Cek jika nama sudah ada di cache
  if (genderCache.has(name.toLowerCase())) {
    return genderCache.get(name.toLowerCase())!;
  }

  // Ambil nama depan saja (kata pertama)
  const firstName = name.split(' ')[0].toLowerCase();

  // Cek jika nama depan sudah ada di cache
  if (genderCache.has(firstName)) {
    return genderCache.get(firstName)!;
  }

  try {
    // Panggil API Genderize.io
    const response = await fetch(`https://api.genderize.io?name=${encodeURIComponent(firstName)}`);

    if (!response.ok) {
      throw new Error(`Genderize API error: ${response.status}`);
    }

    const data = await response.json();

    // Tentukan gender berdasarkan hasil API
    let gender = Gender.OTHER;
    if (data.gender === 'male') {
      gender = Gender.MALE;
    } else if (data.gender === 'female') {
      gender = Gender.FEMALE;
    }

    // Simpan ke cache
    genderCache.set(firstName, gender);

    return gender;
  } catch (error) {
    console.error(`Error menggunakan Genderize API: ${error}`);
    // Fallback ke metode lama jika API gagal
    return determineGenderFromNameFallback(name);
  }
}

// Fungsi fallback menggunakan metode lama untuk menentukan gender
function determineGenderFromNameFallback(name: string): Gender {
  if (!name) return Gender.OTHER;

  // Daftar nama yang biasanya untuk laki-laki
  const maleNames = [
    'adi', 'agus', 'ahmad', 'andi', 'andri', 'anton', 'arief', 'arifin', 'aris', 'asep',
    'bambang', 'bayu', 'budi', 'dedi', 'denny', 'dian', 'dicky', 'dodi', 'dwi', 'eko',
    'fajar', 'fauzi', 'ferry', 'firman', 'hadi', 'hendra', 'herman', 'herry', 'ikhsan', 'imam',
    'indra', 'irfan', 'joko', 'kurniawan', 'lukman', 'mahmud', 'muhammad', 'mulyono', 'nana', 'nugroho',
    'purnomo', 'rahmat', 'reza', 'ridwan', 'rizal', 'rizky', 'rudy', 'santoso', 'sigit', 'slamet',
    'sugianto', 'suharto', 'sukarno', 'supriadi', 'surya', 'sutrisno', 'syaiful', 'taufik', 'teguh', 'tri',
    'udin', 'wahyu', 'wawan', 'wibowo', 'widodo', 'yanto', 'yudi', 'yusuf', 'zaenal', 'zainudin',
    // Tambahan nama laki-laki
    'adam', 'aditya', 'agung', 'akbar', 'alex', 'ali', 'anwar', 'arya', 'bagus', 'bima',
    'candra', 'dani', 'darma', 'david', 'dimas', 'edi', 'fahmi', 'faisal', 'febri', 'galih',
    'gani', 'gunawan', 'haris', 'harry', 'hendri', 'ilham', 'iwan', 'jaya', 'johan', 'koko',
    'leo', 'lutfi', 'malik', 'maman', 'mario', 'maulana', 'nanda', 'putra', 'raden', 'rama',
    'randy', 'ricky', 'rio', 'ryan', 'satria', 'tono', 'toto', 'vicky', 'willy', 'yoga'
  ];

  // Daftar nama yang biasanya untuk perempuan
  const femaleNames = [
    'aini', 'ajeng', 'amalia', 'anisa', 'annisa', 'ayu', 'bunga', 'citra', 'dewi', 'dian',
    'dina', 'dini', 'dwi', 'eka', 'endang', 'erika', 'erni', 'eva', 'farah', 'farida',
    'fatimah', 'fitri', 'fitriani', 'hana', 'hani', 'hasanah', 'ida', 'indah', 'intan', 'ira',
    'kartika', 'kiki', 'laila', 'lestari', 'lia', 'lina', 'linda', 'lisa', 'lusi', 'mawar',
    'mega', 'melati', 'mira', 'nadia', 'nia', 'nina', 'nita', 'novi', 'novita', 'nur',
    'nurul', 'putri', 'rahma', 'ratna', 'rini', 'rita', 'rosa', 'rosita', 'sari', 'sinta',
    'siti', 'sri', 'suci', 'tari', 'tika', 'tina', 'tuti', 'umi', 'vera', 'wati',
    'winda', 'wulan', 'yani', 'yanti', 'yuli', 'yulia', 'yuni', 'yunita', 'zahra', 'zubaidah',
    // Tambahan nama perempuan
    'adinda', 'agnes', 'alya', 'amanda', 'angela', 'anggi', 'ani', 'anita', 'anny', 'astri',
    'bella', 'betty', 'carla', 'clara', 'diana', 'dini', 'ella', 'elsa', 'erna', 'fanny',
    'feby', 'fina', 'gita', 'hesti', 'icha', 'ika', 'irma', 'jessica', 'julia', 'karin',
    'lala', 'lili', 'maya', 'melly', 'mila', 'nana', 'neni', 'olive', 'puti', 'ratu',
    'rena', 'rina', 'sarah', 'shinta', 'silvi', 'tania', 'tasya', 'vina', 'wina', 'yolanda'
  ];

  // Ambil nama depan saja (kata pertama)
  const firstName = name.split(' ')[0].toLowerCase();

  // Cek apakah nama depan ada di daftar nama laki-laki
  if (maleNames.includes(firstName)) {
    return Gender.MALE;
  }

  // Cek apakah nama depan ada di daftar nama perempuan
  if (femaleNames.includes(firstName)) {
    return Gender.FEMALE;
  }

  // Jika nama depan tidak cocok, coba cek substring dari nama depan
  // untuk menangani kasus seperti "Bambang" yang tidak ada di daftar tapi "bambang" ada
  for (const maleName of maleNames) {
    if (firstName.startsWith(maleName) || maleName.startsWith(firstName)) {
      return Gender.MALE;
    }
  }

  for (const femaleName of femaleNames) {
    if (firstName.startsWith(femaleName) || femaleName.startsWith(firstName)) {
      return Gender.FEMALE;
    }
  }

  // Jika tidak ada yang cocok, kembalikan MALE sebagai default (karena lebih banyak pelanggan pria)
  return Gender.MALE;
}

// Fungsi untuk menentukan gender berdasarkan nama
// Karena fungsi async tidak bisa langsung digunakan di forEach, kita gunakan fungsi wrapper
function determineGenderFromName(name: string): Gender {
  // Pertama coba gunakan cache untuk menghemat API call
  if (genderCache.has(name.toLowerCase())) {
    return genderCache.get(name.toLowerCase())!;
  }

  // Jika tidak ada di cache, gunakan metode fallback untuk respon cepat
  const gender = determineGenderFromNameFallback(name);

  // Inisiasi API call untuk update cache di background tanpa menunggu hasilnya
  determineGenderFromNameAPI(name)
    .then(apiGender => {
      // Update cache jika hasilnya berbeda
      if (apiGender !== gender) {
        genderCache.set(name.toLowerCase(), apiGender);
      }
    })
    .catch(err => {
      console.error(`Error background update gender cache: ${err}`);
    });

  return gender;
}

// Fungsi untuk menentukan kategori pelanggan
function determineCustomerCategory(totalVisits: number, totalSpent: number): 'A' | 'B' | 'C' | 'D' {
  // Kategori A: Pelanggan dengan kunjungan tinggi dan pengeluaran tinggi
  // Minimal 3 kunjungan dan total pengeluaran minimal 750.000
  if (totalVisits >= 3 && totalSpent >= 750000) {
    return 'A';
  }
  // Kategori B: Pelanggan dengan kunjungan tinggi tetapi pengeluaran rendah
  // Minimal 3 kunjungan tapi pengeluaran di bawah 750.000
  else if (totalVisits >= 3 && totalSpent < 750000) {
    return 'B';
  }
  // Kategori C: Pelanggan dengan kunjungan rendah tetapi pengeluaran tinggi
  // Kurang dari 3 kunjungan tapi pengeluaran minimal 750.000
  else if (totalVisits < 3 && totalSpent >= 750000) {
    return 'C';
  }
  // Kategori D: Pelanggan dengan kunjungan rendah dan pengeluaran rendah
  // Kurang dari 3 kunjungan dan pengeluaran di bawah 750.000
  else {
    return 'D';
  }
}

// Fungsi untuk memproses batch nama sekaligus dengan Genderize.io API (max 10 nama per request)
async function processBatchGenderAPI(names: string[]): Promise<Map<string, Gender>> {
  const result = new Map<string, Gender>();

  // Filter nama yang belum ada di cache
  const namesToProcess = names.filter(name => !genderCache.has(name.toLowerCase()));

  if (namesToProcess.length === 0) {
    // Semua nama sudah ada di cache, gunakan cache saja
    names.forEach(name => {
      const firstName = name.split(' ')[0].toLowerCase();
      if (genderCache.has(firstName)) {
        result.set(name, genderCache.get(firstName)!);
      } else {
        result.set(name, determineGenderFromNameFallback(name));
      }
    });
    return result;
  }

  // Proses batch maksimal 10 nama per request (batas API Genderize.io gratis)
  const batchSize = 10;

  try {
    // Bagi nama menjadi batch-batch
    for (let i = 0; i < namesToProcess.length; i += batchSize) {
      const batch = namesToProcess.slice(i, i + batchSize);

      // Buat query string untuk API
      const queryParams = batch.map(name => {
        const firstName = name.split(' ')[0].toLowerCase();
        return `name[]=${encodeURIComponent(firstName)}`;
      }).join('&');

      // Panggil API Genderize.io dengan batch request
      const response = await fetch(`https://api.genderize.io?${queryParams}`);

      if (!response.ok) {
        throw new Error(`Genderize API batch error: ${response.status}`);
      }

      const data = await response.json();

      // Proses hasil dan simpan ke cache
      if (Array.isArray(data)) {
        data.forEach((item, index) => {
          const originalName = batch[index];
          const firstName = originalName.split(' ')[0].toLowerCase();

          let gender = Gender.OTHER;
          if (item.gender === 'male') {
            gender = Gender.MALE;
          } else if (item.gender === 'female') {
            gender = Gender.FEMALE;
          }

          // Simpan ke cache dan hasil
          genderCache.set(firstName, gender);
          result.set(originalName, gender);
        });
      }
    }
  } catch (error) {
    console.error(`Error batch gender API: ${error}`);
    // Fallback ke metode lama jika API gagal
    namesToProcess.forEach(name => {
      result.set(name, determineGenderFromNameFallback(name));
    });
  }

  // Tambahkan nama yang sudah ada di cache
  names.forEach(name => {
    if (!result.has(name)) {
      const firstName = name.split(' ')[0].toLowerCase();
      if (genderCache.has(firstName)) {
        result.set(name, genderCache.get(firstName)!);
      } else {
        result.set(name, determineGenderFromNameFallback(name));
      }
    }
  });

  return result;
}

export async function GET(request: NextRequest) {
  try {
    const startTime = Date.now(); // Track performance
    const { searchParams } = new URL(request.url);

    // Parse parameter
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');
    const outletIdParam = searchParams.get('outletId');

    // Validasi parameter
    if (!startDateParam || !endDateParam) {
      return NextResponse.json(
        { error: 'Parameter startDate dan endDate diperlukan' },
        { status: 400 }
      );
    }

    // Parse tanggal
    const startDate = new Date(startDateParam);
    const endDate = new Date(endDateParam);
    endDate.setHours(23, 59, 59, 999); // Set ke akhir hari

    // Validasi tanggal
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: 'Format tanggal tidak valid' },
        { status: 400 }
      );
    }

    // Buat where clause untuk filter outlet
    let outletWhere = {};
    if (outletIdParam && outletIdParam !== 'all') {
      outletWhere = { id: outletIdParam };
    }

    // 1. Ambil semua outlet
    const outlets = await prisma.outlet.findMany({
      where: outletWhere,
      select: {
        id: true,
        name: true,
      },
    });

    // 2. Ambil data transaksi untuk periode yang dipilih (optimized)
    const transactions = await prisma.transaction.findMany({
      where: {
        transactionDate: {
          gte: startDate,
          lte: endDate,
        },
        outletId: outletIdParam !== 'all' ? outletIdParam : undefined,
      },
      select: {
        id: true,
        transactionDate: true,
        totalAmount: true,
        customerId: true,
        outletId: true,
        customer: {
          select: {
            id: true,
            name: true,
            gender: true,
            tags: true, // Langsung ambil tags dari awal
          },
        },
        outlet: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        transactionDate: 'asc'
      }
    });

    // Kumpulkan semua nama customer untuk diproses sekaligus
    const customerNames = new Set<string>();
    transactions.forEach(transaction => {
      if (transaction.customer?.name) {
        customerNames.add(transaction.customer.name);
      }
    });

    // Pre-process semua nama customer untuk memperoleh gender mereka (optimized batch)
    const customerGenders = customerNames.size > 0 && customerNames.size <= 100
      ? await processBatchGenderAPI(Array.from(customerNames))
      : new Map<string, Gender>(); // Skip API call untuk dataset besar

    // 3. Hitung data pelanggan berdasarkan transaksi dalam rentang tanggal
    const customerMap = new Map();
    const customerIdsInRange = new Set(); // Untuk melacak pelanggan yang memiliki transaksi dalam rentang

    transactions.forEach(transaction => {
      if (!transaction.customer) return;

      const customerId = transaction.customer.id;
      customerIdsInRange.add(customerId); // Tambahkan ke set pelanggan dalam rentang

      const transactionDate = new Date(transaction.transactionDate);
      const transactionHour = transactionDate.getHours();

      // Inisialisasi data pelanggan jika belum ada
      if (!customerMap.has(customerId)) {
        // Tentukan gender berdasarkan nama jika tidak ada atau OTHER
        let gender = transaction.customer.gender;
        if (!gender || gender === Gender.OTHER) {
          // Gunakan hasil dari batch processing atau fallback
          if (customerGenders.has(transaction.customer.name)) {
            gender = customerGenders.get(transaction.customer.name);
          } else {
            gender = determineGenderFromName(transaction.customer.name);
          }
        }

        customerMap.set(customerId, {
          id: customerId,
          name: transaction.customer.name,
          gender: gender,
          totalVisits: 0,
          totalSpent: 0,
          visitsInRange: 0, // Tambahkan penghitung untuk kunjungan dalam rentang
          spentInRange: 0,  // Tambahkan penghitung untuk pengeluaran dalam rentang
          lastVisit: new Date(0),
          outletId: transaction.outlet.id,
          outletName: transaction.outlet.name,
          lastTransactionDate: new Date(0),
          tags: [], // Tambahkan field untuk tag pelanggan
          points: transaction.customer.points || 0, // Tambahkan field points
          address: transaction.customer.address || '', // Tambahkan field address
        });
      }

      const customerData = customerMap.get(customerId);

      // Update data pelanggan untuk transaksi dalam rentang
      customerData.visitsInRange += 1;
      customerData.spentInRange += transaction.totalAmount || 0;

      // Update data total (akan diperbarui dengan semua transaksi nanti)
      customerData.totalVisits += 1;
      customerData.totalSpent += transaction.totalAmount || 0;

      // Update tanggal kunjungan terakhir jika lebih baru
      if (transactionDate > customerData.lastVisit) {
        customerData.lastVisit = transactionDate;
      }

      // Update outlet terakhir jika transaksi ini lebih baru
      if (transactionDate > customerData.lastTransactionDate) {
        customerData.lastTransactionDate = transactionDate;
        customerData.outletId = transaction.outlet.id;
        customerData.outletName = transaction.outlet.name;
      }
    });

    // 3.5 Ambil semua transaksi untuk pelanggan yang memiliki transaksi dalam rentang (optimized)
    // OPTIMASI: Hanya ambil data yang dibutuhkan untuk menghitung total visits/spent
    const allTransactionsByCustomer = new Map<string, any[]>();

    if (customerIdsInRange.size > 0) {
      // Query yang lebih efisien: hanya ambil field yang dibutuhkan
      const allRawTransactions = await prisma.transaction.findMany({
        where: {
          customerId: {
            in: Array.from(customerIdsInRange) as string[]
          },
        },
        select: {
          customerId: true,
          totalAmount: true,
          transactionDate: true,
        },
        orderBy: {
          transactionDate: 'asc',
        }
      });

      // Kelompokkan transaksi per pelanggan
      allRawTransactions.forEach(txn => {
        if (!allTransactionsByCustomer.has(txn.customerId)) {
          allTransactionsByCustomer.set(txn.customerId, []);
        }
        allTransactionsByCustomer.get(txn.customerId)!.push(txn);
      });
    }

    // Reset dan hitung ulang total kunjungan dan pengeluaran
    customerIdsInRange.forEach(customerId => {
      if (customerMap.has(customerId)) {
        const customerData = customerMap.get(customerId);
        // Reset total (kita akan menghitung ulang dari semua transaksi)
        customerData.totalVisits = 0;
        customerData.totalSpent = 0;
        // customerData.allTransactions = allTransactionsByCustomer.get(customerId) || []; // Simpan semua transaksi
      }
    });

    // Hitung total dari semua transaksi
    // allCustomerTransactions.forEach(transaction => {
    //   if (customerMap.has(transaction.customerId)) {
    //     const customerData = customerMap.get(transaction.customerId);
    //     customerData.totalVisits += 1;
    //     customerData.totalSpent += transaction.totalAmount || 0;
    //   }
    // });
    allTransactionsByCustomer.forEach((transactions, customerId) => {
      if (customerMap.has(customerId)) {
        const customerData = customerMap.get(customerId);
        customerData.totalVisits = transactions.length;
        customerData.totalSpent = transactions.reduce((sum, txn) => sum + (txn.totalAmount || 0), 0);
        if (transactions.length > 0) {
          // Kunjungan terakhir (overall) dan outlet terakhir (overall) dari semua transaksi historis
          const lastHistoricalTx = transactions[transactions.length - 1];
          customerData.lastVisit = lastHistoricalTx.transactionDate; // Ini akan jadi lastVisit overall
          // customerData.outletId = lastHistoricalTx.outletId; // Ini akan jadi outletId dari lastVisit overall
          // customerData.outletName = outlets.find(o => o.id === lastHistoricalTx.outletId)?.name || 'Outlet Tidak Diketahui';
        }
      }
    });

    // OPTIMASI: Tags sudah diambil langsung dari query transactions, skip query tambahan
    console.log(`Processed ${customerMap.size} customers with transactions in range`);
  }

    // 4. Konversi Map ke array dan tentukan kategori berdasarkan tag customer
    const customers = Array.from(customerMap.values()).map(customer => {
      // Gunakan tag customer sebagai kategori utama
      let finalCategory: 'A' | 'B' | 'C' | 'D' | 'Baru' = 'Baru'; // Default ke Baru
      
      // Cari tag kategori yang sesuai (A, B, C, D, atau Baru)
      const categoryTags = ['A', 'B', 'C', 'D', 'Baru', 'BARU'];
      const customerTags = Array.isArray(customer.tags) ? customer.tags : [];
      
      // Cek apakah ada tag kategori di customer tags (optimized)
      const foundTag = categoryTags.find(tag => customerTags.includes(tag));
      if (foundTag) {
        finalCategory = foundTag === 'BARU' ? 'Baru' : foundTag as 'A' | 'B' | 'C' | 'D' | 'Baru';
      }

      // Log hanya untuk debugging (remove in production)
      // console.log(`Customer ${customer.name}: tags=${JSON.stringify(customerTags)}, finalCategory=${finalCategory}`);

      return {
        ...customer,
        finalCategory: finalCategory,
        category: finalCategory === 'Baru' ? 'D' : finalCategory, // Fallback untuk kompatibilitas
        lastVisit: customer.lastVisit instanceof Date ? customer.lastVisit.toISOString() : new Date(0).toISOString(),
        visitsInRange: customer.visitsInRange,
        spentInRange: customer.spentInRange,
        tags: customerTags
      };
    });

    // 5. Hitung kategori pelanggan per outlet
    const customerCategoriesMap = new Map();

    outlets.forEach(outlet => {
      customerCategoriesMap.set(outlet.id, {
        outletId: outlet.id,
        outletName: outlet.name,
        categoryA: 0,
        categoryB: 0,
        categoryC: 0,
        categoryD: 0,
        categoryBaru: 0, // Tambahkan kategori Baru
        total: 0,
        customerIds: new Set(), // Tambahkan Set untuk melacak pelanggan unik
      });
    });

    // Ambil data transaksi per outlet untuk semua pelanggan
    if (customerIdsInRange.size > 0) {
      // Ambil semua transaksi untuk semua pelanggan yang difilter, dikelompokkan berdasarkan outlet
      const customerOutletTransactions = await prisma.transaction.findMany({
        where: {
          customerId: {
            in: Array.from(customerIdsInRange)
          },
          // Tidak ada filter tanggal di sini, kita ingin semua transaksi
        },
        select: {
          customerId: true,
          outletId: true,
        },
        distinct: ['customerId', 'outletId'], // Hanya ambil kombinasi unik customer-outlet
      });

      // Buat map untuk melacak outlet mana saja yang dikunjungi oleh setiap pelanggan
      const customerOutletsMap = new Map();
      customerOutletTransactions.forEach(transaction => {
        if (!customerOutletsMap.has(transaction.customerId)) {
          customerOutletsMap.set(transaction.customerId, new Set());
        }
        customerOutletsMap.get(transaction.customerId).add(transaction.outletId);
      });

      // Hitung kategori pelanggan untuk setiap outlet
      customers.forEach(customer => {
        // Ambil semua outlet yang dikunjungi pelanggan ini
        const outletsToProcess = customerOutletsMap.get(customer.id) || new Set([customer.outletId]);

        if (!outletsToProcess || outletsToProcess.size === 0) {
            console.warn(`Customer ${customer.name} (${customer.id}) could not be assigned to any outlet for category counting.`);
            return; // Skip this customer if no outlets can be determined
        }

        // Untuk setiap outlet yang dikunjungi, tambahkan pelanggan ke kategori yang sesuai
        outletsToProcess.forEach(outletId => {
          const outletData = customerCategoriesMap.get(outletId);
          // Hanya update counts for outlets that are part of the current query
          // (either the specific filtered outlet, or all outlets if filter is 'all')
          if (!outletData) return;

          // Hanya tambahkan jika pelanggan belum dihitung untuk outlet ini
          // Ensure each customer is counted only once per outlet for the summary
          if (!outletData.customerIds.has(customer.id)) {
            let incrementedCategory = false;
            
            // Optimized category counting
            switch (customer.finalCategory) {
              case 'A':
                outletData.categoryA += 1;
                incrementedCategory = true;
                break;
              case 'B':
                outletData.categoryB += 1;
                incrementedCategory = true;
                break;
              case 'C':
                outletData.categoryC += 1;
                incrementedCategory = true;
                break;
              case 'D':
                outletData.categoryD += 1;
                incrementedCategory = true;
                break;
              case 'Baru':
                outletData.categoryBaru += 1;
                incrementedCategory = true;
                break;
            }

            if (incrementedCategory) {
              outletData.total += 1; // Increment total unique customers for this outlet
              outletData.customerIds.add(customer.id); // Mark customer as counted for this outlet
            }
          }
        });
      });
    } else {
      // Fallback jika tidak ada pelanggan dalam rentang
      customers.forEach(customer => {
        const outletData = customerCategoriesMap.get(customer.outletId);
        if (!outletData) return;

        if (!outletData.customerIds.has(customer.id)) {
          let incrementedCategory = false;
          
          // Optimized fallback category counting
          switch (customer.finalCategory) {
            case 'A':
              outletData.categoryA += 1;
              incrementedCategory = true;
              break;
            case 'B':
              outletData.categoryB += 1;
              incrementedCategory = true;
              break;
            case 'C':
              outletData.categoryC += 1;
              incrementedCategory = true;
              break;
            case 'D':
              outletData.categoryD += 1;
              incrementedCategory = true;
              break;
            case 'Baru':
              outletData.categoryBaru += 1;
              incrementedCategory = true;
              break;
          }

          if (incrementedCategory) {
            outletData.total += 1;
            outletData.customerIds.add(customer.id);
          }
        }
      });
    }

    // Hapus Set customerIds dari hasil akhir
    const customerCategories = Array.from(customerCategoriesMap.values()).map(outlet => ({
      outletId: outlet.outletId,
      outletName: outlet.outletName,
      categoryA: outlet.categoryA,
      categoryB: outlet.categoryB,
      categoryC: outlet.categoryC,
      categoryD: outlet.categoryD,
      categoryBaru: outlet.categoryBaru, // Tambahkan kategori Baru
      total: outlet.total,
    }));

    // 6. Hitung jam sibuk dengan data gender
    // PENTING: Data jam akan dikonversi ke zona waktu Makassar (WITA/UTC+8)
    // untuk memastikan visualisasi jam sibuk sesuai dengan waktu lokal outlet
    const hourlyData = new Map();

    // Buat data untuk semua jam operasional (9:00-23:00)
    for (let hour = 9; hour <= 23; hour++) {
      hourlyData.set(hour, {
        hour,
        count: 0,
        maleCount: 0,
        femaleCount: 0,
        otherCount: 0,
      });
    }

    transactions.forEach(transaction => {
      if (!transaction.customer) return;

      const transactionDate = new Date(transaction.transactionDate);

      // Konversi ke zona waktu Makassar (WITA/UTC+8)
      // Dapatkan offset lokal dalam menit dan tambahkan perbedaan ke UTC+8 (480 menit)
      const localOffset = transactionDate.getTimezoneOffset();
      const makassarOffset = -480; // UTC+8 dalam menit (negatif karena getTimezoneOffset() mengembalikan nilai negatif untuk UTC+)
      const offsetDiff = makassarOffset - localOffset;

      // Buat salinan tanggal dan sesuaikan dengan zona waktu Makassar
      const makassarDate = new Date(transactionDate.getTime() + offsetDiff * 60000);
      const hour = makassarDate.getHours();

      // Hanya proses jam operasional (9:00-23:00)
      if (hour < 9 || hour > 23) {
        return; // Skip jam di luar operasional
      }

      const hourData = hourlyData.get(hour);

      hourData.count += 1;

      // Tentukan gender berdasarkan nama jika tidak ada
      let gender = transaction.customer.gender;

      // Jika gender tidak ada atau OTHER, tentukan berdasarkan nama
      if (!gender || gender === Gender.OTHER) {
        // Gunakan hasil dari batch processing yang sudah dilakukan
        if (customerGenders.has(transaction.customer.name)) {
          gender = customerGenders.get(transaction.customer.name);
        } else {
          gender = determineGenderFromName(transaction.customer.name);
        }
      }

      if (gender === Gender.MALE) {
        hourData.maleCount += 1;
      } else if (gender === Gender.FEMALE) {
        hourData.femaleCount += 1;
      } else {
        hourData.otherCount += 1;
      }
    });

    // Ambil semua data jam operasional (termasuk yang kosong) dan urutkan berdasarkan jam
    const peakHours = Array.from(hourlyData.values())
      .sort((a, b) => a.hour - b.hour); // Urutkan berdasarkan jam (dari pagi ke malam)

    // 7. Kembalikan data CRM
    const endTime = Date.now();
    console.log(`CRM API completed in ${endTime - startTime}ms - ${customers.length} customers, ${transactions.length} transactions`);
    
    return NextResponse.json({
      message: 'Data CRM berhasil diambil',
      crmData: {
        customers,
        peakHours,
        customerCategories,
      },
      filters: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        outletId: outletIdParam,
      },
      performance: {
        duration: endTime - startTime,
        customerCount: customers.length,
        transactionCount: transactions.length
      }
    });
  } catch (error: unknown) {
    console.error('[API GET /api/reports/crm] Error:', error);

    let errorMessage = 'Terjadi kesalahan saat mengambil data CRM';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat mengambil data CRM',
      crmData: {
        customers: [],
        peakHours: [],
        customerCategories: [],
      },
      filters: {
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        outletId: null,
      },
    }, { status: statusCode });
  }
}

// Tambahkan endpoint khusus untuk debugging penentuan gender
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { names } = body;

    if (!names || !Array.isArray(names) || names.length === 0) {
      return NextResponse.json(
        { error: 'Parameter names diperlukan dan harus berupa array tidak kosong' },
        { status: 400 }
      );
    }

    // Batasi jumlah nama yang dapat diproses
    const processNames = names.slice(0, 50);

    // Proses batch nama
    const results = await processBatchGenderAPI(processNames);

    // Format hasil untuk output
    const formattedResults = processNames.map(name => {
      const firstName = name.split(' ')[0];
      const gender = results.get(name) || Gender.OTHER;
      const fallbackGender = determineGenderFromNameFallback(name);

      return {
        name,
        firstName,
        apiGender: gender,
        fallbackGender,
        match: gender === fallbackGender
      };
    });

    return NextResponse.json({
      message: 'Berhasil memproses nama',
      totalProcessed: processNames.length,
      results: formattedResults,
      cacheSize: genderCache.size
    });
  } catch (error: unknown) {
    console.error('[API POST /api/reports/crm] Error:', error);

    let errorMessage = 'Terjadi kesalahan saat memproses nama';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat memproses nama'
    }, { status: statusCode });
  }
}
