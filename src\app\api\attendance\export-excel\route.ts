import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { formatISO, subDays, parseISO, format } from 'date-fns';
import { id } from 'date-fns/locale';
import ExcelJS from 'exceljs';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const outletId = searchParams.get('outletId') || undefined;
    const type = searchParams.get('type'); // 'therapist' atau 'user'
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Default tanggal: 30 hari terakhir
    const defaultEndDate = new Date();
    const defaultStartDate = subDays(defaultEndDate, 30);

    // Filter berdasarkan tanggal
    const dateStart = startDate ? parseISO(startDate) : defaultStartDate;
    const dateEnd = endDate ? parseISO(endDate) : defaultEndDate;

    // Format tanggal untuk nama file
    const formattedStartDate = format(dateStart, 'dd-MM-yyyy');
    const formattedEndDate = format(dateEnd, 'dd-MM-yyyy');

    // Filter untuk therapistAttendance
    const therapistAttendanceFilter: any = {
      createdAt: {
        gte: dateStart,
        lte: dateEnd,
      }
    };

    // Filter untuk systemLog
    const systemLogFilter: any = {
      createdAt: {
        gte: dateStart,
        lte: dateEnd,
      },
      type: {
        in: ['USER_ATTENDANCE_IN', 'USER_ATTENDANCE_OUT']
      }
    };

    // Tambahkan filter outletId jika ada
    if (outletId) {
      therapistAttendanceFilter.outletId = outletId;
      systemLogFilter.outletId = outletId;
    }

    let attendances = [];

    // Ambil data berdasarkan tipe
    if (!type || type === 'therapist') {
      // Ambil data absensi terapis
      const therapistAttendances = await prisma.therapistAttendance.findMany({
        where: therapistAttendanceFilter,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          therapist: true,
          outlet: true
        }
      });

      // Format data absensi terapis
      const formattedTherapistAttendances = therapistAttendances.map(attendance => ({
        id: attendance.id,
        userId: attendance.therapistId,
        name: attendance.therapist.name,
        role: 'Terapis', 
        outletId: attendance.outletId,
        outletName: attendance.outlet.name,
        attendanceType: attendance.attendanceType,
        timestamp: attendance.createdAt,
        type: 'therapist',
        notes: attendance.notes || ''
      }));

      attendances = [...attendances, ...formattedTherapistAttendances];
    }

    if (!type || type === 'user') {
      // Ambil data absensi user (staff/admin)
      const userAttendances = await prisma.systemLog.findMany({
        where: systemLogFilter,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          user: true,
          outlet: true
        }
      });

      // Format data absensi user
      const formattedUserAttendances = userAttendances.map(log => ({
        id: log.id,
        userId: log.userId,
        name: log.user?.name || 'Unknown',
        role: log.user?.role || 'Staff',
        outletId: log.outletId,
        outletName: log.outlet?.name || 'Unknown',
        attendanceType: log.type === 'USER_ATTENDANCE_IN' ? 'IN' : 'OUT',
        timestamp: log.createdAt,
        type: 'user',
        notes: log.details || ''
      }));

      attendances = [...attendances, ...formattedUserAttendances];
    }

    // Sort gabungan data berdasarkan timestamp terbaru
    attendances.sort((a, b) => {
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    });

    // Buat workbook Excel
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Breaktime Dashboard';
    workbook.created = new Date();
    
    // Buat worksheet
    const worksheet = workbook.addWorksheet('Riwayat Absensi');
    
    // Tambahkan header
    worksheet.columns = [
      { header: 'No', key: 'no', width: 5 },
      { header: 'Nama', key: 'name', width: 20 },
      { header: 'Tipe', key: 'type', width: 10 },
      { header: 'Jabatan', key: 'role', width: 15 },
      { header: 'Outlet', key: 'outletName', width: 20 },
      { header: 'Status', key: 'attendanceType', width: 10 },
      { header: 'Tanggal', key: 'date', width: 15 },
      { header: 'Jam', key: 'time', width: 10 },
      { header: 'Catatan', key: 'notes', width: 30 }
    ];
    
    // Style header
    worksheet.getRow(1).font = {
      bold: true,
      size: 12,
      color: { argb: '000000' }
    };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E6F2EF' } // Light teal color
    };
    worksheet.getRow(1).border = {
      bottom: {style: 'thin', color: {argb: '000000'}}
    };
    
    // Tambahkan data
    attendances.forEach((attendance, index) => {
      const attendanceDate = new Date(attendance.timestamp);
      worksheet.addRow({
        no: index + 1,
        name: attendance.name,
        type: attendance.type === 'therapist' ? 'Terapis' : 'Staff',
        role: attendance.role,
        outletName: attendance.outletName,
        attendanceType: attendance.attendanceType === 'IN' ? 'Masuk' : 'Keluar',
        date: format(attendanceDate, 'dd MMM yyyy', { locale: id }),
        time: format(attendanceDate, 'HH:mm:ss'),
        notes: attendance.notes
      });
    });
    
    // Freeze top row
    worksheet.views = [
      { state: 'frozen', xSplit: 0, ySplit: 1, activeCell: 'A2' }
    ];
    
    // Auto filter untuk semua kolom
    worksheet.autoFilter = {
      from: { row: 1, column: 1 },
      to: { row: 1, column: 9 }
    };
    
    // Set semua kolom agar bisa difilter dan diurutkan
    worksheet.getColumn('name').width = 20;
    worksheet.getColumn('date').width = 15;
    
    // Tambahkan informasi filter yang digunakan
    const infoSheet = workbook.addWorksheet('Informasi');
    infoSheet.columns = [
      { header: 'Parameter', key: 'param', width: 20 },
      { header: 'Nilai', key: 'value', width: 30 }
    ];
    
    infoSheet.getRow(1).font = { bold: true };
    
    infoSheet.addRow({ param: 'Tanggal Mulai', value: format(dateStart, 'dd MMMM yyyy', { locale: id }) });
    infoSheet.addRow({ param: 'Tanggal Akhir', value: format(dateEnd, 'dd MMMM yyyy', { locale: id }) });
    
    if (outletId) {
      // Ambil nama outlet
      try {
        const outlet = await prisma.outlet.findUnique({
          where: { id: outletId },
          select: { name: true }
        });
        if (outlet) {
          infoSheet.addRow({ param: 'Outlet', value: outlet.name });
        }
      } catch (error) {
        console.error('Error fetching outlet name:', error);
        infoSheet.addRow({ param: 'Outlet', value: outletId });
      }
    } else {
      infoSheet.addRow({ param: 'Outlet', value: 'Semua Outlet' });
    }
    
    if (type) {
      infoSheet.addRow({ param: 'Tipe', value: type === 'therapist' ? 'Terapis' : 'Staff' });
    } else {
      infoSheet.addRow({ param: 'Tipe', value: 'Semua (Terapis dan Staff)' });
    }
    
    infoSheet.addRow({ param: 'Jumlah Data', value: attendances.length });
    infoSheet.addRow({ param: 'Tanggal Export', value: format(new Date(), 'dd MMMM yyyy HH:mm:ss', { locale: id }) });
    
    // Ubah workbook menjadi buffer
    const buffer = await workbook.xlsx.writeBuffer();
    
    // Buat nama file Excel
    const fileName = `laporan_absensi_${formattedStartDate}_${formattedEndDate}.xlsx`;
    
    // Kirim response dengan file Excel
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${fileName}"`
      }
    });

  } catch (error) {
    console.error('Error exporting attendance records:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengekspor data absensi' },
      { status: 500 }
    );
  }
} 