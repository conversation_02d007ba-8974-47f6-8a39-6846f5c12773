'use client';

import { motion } from 'framer-motion';
import { FiMail, FiSend } from 'react-icons/fi';

export default function Subscription() {
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number = 1) => ({
      opacity: 1,
      y: 0,
      transition: { staggerChildren: 0.1, delayChildren: i * 0.1, duration: 0.5 }
    })
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  return (
    <motion.section 
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      variants={staggerContainer}
      className="py-16 px-4 md:px-20 mb-20"
    >
      <motion.div 
        variants={fadeIn} 
        className="bg-white rounded-3xl p-8 md:p-12 relative overflow-hidden max-w-5xl mx-auto shadow-xl border border-gray-100"
      >
        {/* Decorative Elements */}
        <div className="absolute top-0 right-0 w-40 h-40 bg-secondary/20 rounded-full -translate-y-1/2 translate-x-1/4"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-primary/20 rounded-full translate-y-1/2 -translate-x-1/4"></div>
        
        <div className="text-center mb-8 relative z-10">
          <h2 className="text-2xl md:text-3xl font-bold mb-4 text-gray-800">
            Dapatkan Diskon Khusus <br className="hidden md:block" />untuk Pelanggan Baru
          </h2>
          <p className="text-gray-600 max-w-md mx-auto">
            Berlangganan untuk mendapatkan info tentang promo dan layanan terbaru kami
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto relative z-10">
          <div className="relative flex-grow w-full">
            <FiMail className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
            <input 
              type="email" 
              placeholder="Email Anda" 
              className="input w-full pl-10 pr-4 bg-white border-gray-200 focus:border-primary focus:ring-0"
            />
          </div>
          <button className="btn bg-secondary hover:bg-secondary-dark text-white gap-2 w-full sm:w-auto border-0">
            <FiSend size={16} />
            <span>Berlangganan</span>
          </button>
        </div>
      </motion.div>
    </motion.section>
  );
}
