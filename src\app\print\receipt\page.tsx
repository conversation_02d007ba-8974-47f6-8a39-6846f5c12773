'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Image from 'next/image';

// Komponen untuk menangani loading state
function LoadingState() {
  return (
    <div className="flex justify-center items-center min-h-screen bg-white">
      <div className="text-center">
        <div className="loading loading-spinner loading-lg text-gray-700"></div>
        <p className="mt-4 text-gray-800 font-medium">Mempersiapkan struk...</p>
      </div>
    </div>
  );
}

// Komponen utama yang menggunakan useSearchParams
function ReceiptContent() {
  const searchParams = useSearchParams();
  const [receiptData, setReceiptData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Ambil ID transaksi dari URL
    const transactionId = searchParams.get('id');
    if (!transactionId) {
      setIsLoading(false);
      return;
    }

    // Ambil data transaksi dari API
    const fetchTransactionData = async () => {
      try {
        // Cek apakah ID dalam format TRXXXXXXX dan ekstrak angkanya
        let idToFetch = transactionId;
        if (transactionId.startsWith('TR')) {
          const numericPart = transactionId.substring(2);
          // Hapus leading zeros jika ada
          idToFetch = String(parseInt(numericPart, 10));
        }

        console.log('Fetching transaction with ID:', idToFetch);
        const response = await fetch(`/api/transactions/${idToFetch}`);

        if (!response.ok) {
          const errorData = await response.json();
          console.error('API error:', errorData);
          throw new Error(errorData.error || 'Gagal memuat data transaksi');
        }

        const data = await response.json();
        console.log('Transaction data received:', data);

        // Pastikan field diskon dan biaya tambahan selalu ada dengan nilai default jika tidak ada
        // Hitung subtotal yang benar jika belum dihitung di API
        const items = data.items || [];
        const itemsTotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        // Jika ada diskon dan biaya tambahan, subtotal = total - biaya tambahan + diskon
        // Jika tidak ada, gunakan total dari items
        const calculatedSubtotal = data.subtotal || (
          (data.discountAmount > 0 || data.additionalCharge > 0)
            ? data.totalAmount - (data.additionalCharge || 0) + (data.discountAmount || 0)
            : itemsTotal > 0 ? itemsTotal : data.totalAmount
        );

        const processedData = {
          ...data,
          discountType: data.discountType || 'none',
          discountValue: data.discountValue || 0,
          discountAmount: data.discountAmount || 0,
          additionalCharge: data.additionalCharge || 0,
          tax: data.tax || 0,
          subtotal: calculatedSubtotal
        };

        console.log('Processed data with defaults:', processedData);
        setReceiptData(processedData);

        // Cetak otomatis setelah data dimuat
        setTimeout(() => {
          console.log('Memulai proses cetak otomatis...');
          try {
            window.print();
            console.log('Perintah cetak berhasil dipanggil');
            // Tambahkan tombol untuk mencetak manual jika otomatis gagal
            const printButton = document.createElement('button');
            printButton.id = 'manual-print-button'; // Tambahkan ID untuk CSS
            printButton.innerText = 'Cetak Manual';
            printButton.style.position = 'fixed';
            printButton.style.top = '10px';
            printButton.style.right = '10px';
            printButton.style.zIndex = '9999';
            printButton.style.padding = '8px 16px';
            printButton.style.backgroundColor = '#4CAF50';
            printButton.style.color = 'white';
            printButton.style.border = 'none';
            printButton.style.borderRadius = '4px';
            printButton.style.cursor = 'pointer';
            printButton.style.fontWeight = 'bold';
            printButton.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
            printButton.onclick = () => {
              console.log('Tombol cetak manual diklik');
              window.print();
            };
            document.body.appendChild(printButton);
          } catch (error) {
            console.error('Error saat mencetak:', error);
          }
          // Tutup jendela setelah mencetak (opsional)
          // window.close();
        }, 1000);
      } catch (error) {
        console.error('Error fetching transaction data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTransactionData();
  }, [searchParams]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-white">
        <div className="text-center">
          <div className="loading loading-spinner loading-lg text-gray-700"></div>
          <p className="mt-4 text-gray-800 font-medium">Mempersiapkan struk...</p>
        </div>
      </div>
    );
  }

  if (!receiptData) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-white">
        <div className="text-center p-6 bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold text-red-600 mb-2">Data Transaksi Tidak Ditemukan</h2>
          <p className="text-gray-700 mb-2">ID transaksi: <span className="font-mono font-medium">{searchParams.get('id')}</span></p>
          <p className="text-gray-700 mb-4">Pastikan ID transaksi valid dan transaksi masih ada di database.</p>
          <button
            onClick={() => window.close()}
            className="mt-4 px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 font-medium"
          >
            Tutup Jendela
          </button>
        </div>
      </div>
    );
  }

  // Format data untuk struk
  const transaction = receiptData;
  const items = transaction.items || [];
  const customer = transaction.customer || { name: 'Pelanggan' };
  const therapist = transaction.therapist || { name: 'Terapis' };
  const outlet = transaction.outlet || { name: 'Breaktime', address: '', phone: '' };

  // Format tanggal
  const transactionDate = new Date(transaction.transactionDate || transaction.createdAt);
  const formattedDate = `${transactionDate.getDate()} ${transactionDate.toLocaleString('id-ID', { month: 'short' })} ${transactionDate.getFullYear()}`;
  const formattedTime = transactionDate.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' });

  return (
    <>
      <style jsx global>{`
        @page {
          size: 58mm auto !important;
          margin: 0 !important;
        }
        @media print {
          html, body {
            width: 58mm !important;
            height: auto !important;
            margin: 0 !important;
            padding: 0 !important;
            font-family: monospace !important;
            color: #000000 !important;
            background-color: #ffffff !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }

          /* Sembunyikan semua elemen kecuali struk */
          body * {
            visibility: hidden !important;
          }

          .receipt-print-container,
          .receipt-print-container * {
            visibility: visible !important;
          }

          .receipt-print-container {
            position: absolute !important;
            left: 0 !important;
            top: 0 !important;
            width: 58mm !important;
            padding: 4mm !important;
            color: #000000 !important;
            background-color: #ffffff !important;
            overflow: visible !important;
          }

          /* Sembunyikan tombol cetak manual saat mencetak */
          #manual-print-button {
            display: none !important;
          }
        }

        /* Styles for preview (non-print) */
        .receipt-print-container {
          width: 58mm;
          margin: 0 auto;
          color: #000000;
          background-color: #ffffff;
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          padding: 4mm;
        }
      `}</style>

      <div className="receipt-print-container">
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '8px' }}>
          <div style={{ marginBottom: '4px' }}>
            <Image
              src="/logo.png"
              alt="Breaktime Logo"
              width={30}
              height={30}
              style={{ margin: '0 auto' }}
            />
          </div>
          <div style={{ fontWeight: 'bold', fontSize: '12px', textTransform: 'uppercase', color: '#000000' }}>
            {outlet.name}
          </div>
          <div style={{ fontSize: '9px', color: '#333333' }}>
            {outlet.address || 'Alamat Outlet Tidak Tersedia'}
          </div>
          <div style={{ fontSize: '9px', color: '#333333' }}>
            {outlet.phone ? `Telp: ${outlet.phone}` : 'Telepon Outlet Tidak Tersedia'}
          </div>
        </div>

        {/* Separator */}
        <div style={{ borderTop: '1px dashed #000', marginBottom: '8px' }}></div>

        {/* Transaction Info */}
        <div style={{ fontSize: '10px', marginBottom: '8px', color: '#000000' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span style={{ fontWeight: '500' }}>No: {transaction.displayId || transaction.id}</span>
            <span>{formattedDate}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span>Kasir: {transaction.createdBy?.name || 'Admin'}</span>
            <span>Plg: {customer.name}</span>
          </div>
          <div>Terapis: {therapist.name}</div>
        </div>

        {/* Items */}
        <div style={{ fontSize: '10px', marginBottom: '8px', color: '#000000' }}>
          {items.map((item: any, index: number) => (
            <div key={index} style={{ marginBottom: '4px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ maxWidth: '70%', paddingRight: '4px' }}>{item.name}</span>
                <span style={{ fontWeight: '500' }}>{(item.price * item.quantity).toLocaleString('id-ID')}</span>
              </div>
              {item.quantity > 1 && (
                <div style={{ fontSize: '9px', paddingLeft: '8px', color: '#555555' }}>
                  {item.quantity} x {item.price.toLocaleString('id-ID')}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Separator */}
        <div style={{ borderTop: '1px dashed #000', marginBottom: '8px' }}></div>

        {/* Totals */}
        <div style={{ fontSize: '10px', marginBottom: '8px', color: '#000000' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span>Subtotal</span>
            <span>{transaction.subtotal?.toLocaleString('id-ID') || transaction.totalAmount?.toLocaleString('id-ID')}</span>
          </div>

          {/* Diskon (jika ada dan nilainya lebih dari 0) */}
          {transaction.discountAmount && transaction.discountAmount > 0 && (
            <div style={{ display: 'flex', justifyContent: 'space-between', color: '#555555' }}>
              <span>Diskon {transaction.discountType === 'percentage' ? `(${transaction.discountValue}%)` : ''}</span>
              <span>- {transaction.discountAmount.toLocaleString('id-ID')}</span>
            </div>
          )}

          {/* Biaya Tambahan (jika ada dan nilainya lebih dari 0) */}
          {transaction.additionalCharge && transaction.additionalCharge > 0 && (
            <div style={{ display: 'flex', justifyContent: 'space-between', color: '#555555' }}>
              <span>Biaya Tambahan</span>
              <span>+ {transaction.additionalCharge.toLocaleString('id-ID')}</span>
            </div>
          )}

          <div style={{ borderTop: '1px dotted #000', margin: '4px 0' }}></div>

          <div style={{ display: 'flex', justifyContent: 'space-between', fontWeight: 'bold', color: '#000000' }}>
            <span>TOTAL</span>
            <span>{transaction.totalAmount.toLocaleString('id-ID')}</span>
          </div>
        </div>

        {/* Payment Method */}
        <div style={{ fontSize: '10px', marginBottom: '8px', borderTop: '1px dashed #000', paddingTop: '4px', color: '#000000' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span>Metode Bayar</span>
            <span style={{ fontWeight: '500' }}>{transaction.paymentMethod}</span>
          </div>
        </div>

        {/* Footer */}
        <div style={{ textAlign: 'center', marginTop: '16px', fontSize: '9px', borderTop: '1px solid #000', paddingTop: '4px', color: '#333333' }}>
          <p style={{ margin: '4px 0' }}>{transaction.note || "Terima kasih atas kunjungan Anda!"}</p>
          <p style={{ margin: '4px 0', fontWeight: '500' }}>Badan Segar Urusan Lancar</p>
        </div>
      </div>
    </>
  );
}

// Komponen utama yang menggunakan Suspense
export default function PrintReceiptPage() {
  return (
    <Suspense fallback={<LoadingState />}>
      <ReceiptContent />
    </Suspense>
  );
}
