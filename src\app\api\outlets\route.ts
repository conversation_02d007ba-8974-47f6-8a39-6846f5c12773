import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { logOutlet } from '@/lib/logger';
import { getUserIdFromToken } from '@/lib/auth-utils';

// GET semua outlets
export async function GET(request: NextRequest) {
  try {
    // Ambil semua outlets dari database yang isOpen = true (filter outlet yang sudah di-soft delete)
    const outlets = await prisma.outlet.findMany({
      where: {
        isOpen: true // Hanya ambil outlet yang masih aktif
      },
      orderBy: [
        { isMain: 'desc' }, // Outlet utama ditampilkan terlebih dahulu
        { name: 'asc' }     // Urutkan berdasarkan nama jika bukan utama
      ],
      select: {
        id: true,
        name: true,
        address: true,
        city: true,
        phone: true,
        operationalHours: true,
        isMain: true,
        isOpen: true,
        // Tambahkan relasi ke services melalui ServiceOutlet
        services: {
          include: {
            service: {
              select: {
                id: true,
                name: true,
                description: true,
                duration: true,
                price: true,
                isActive: true
              }
            }
          },
          where: {
            service: {
              isActive: true
            }
          }
        }
      }
    });

    // Kembalikan data outlets (tidak perlu cek kosong, biarkan frontend handle)
    return NextResponse.json({
      message: 'Data outlet berhasil diambil',
      outlets
    });
  } catch (error) {
    console.error('Error fetching outlets:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data outlet' },
      { status: 500 }
    );
  }
}

// POST untuk membuat outlet baru
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, address, city, phone, operationalHours, isOpen, isMain } = body;

    // Validasi input
    if (!name || !address || !phone || !operationalHours) {
      return NextResponse.json(
        { error: 'Nama, alamat, nomor telepon, dan jam operasional diperlukan' },
        { status: 400 }
      );
    }

    // Jika isMain adalah true, maka outlet lain harus diset false
    if (isMain) {
      // Cek apakah sudah ada outlet utama
      const existingMain = await prisma.outlet.findFirst({
        where: {
          isMain: true
        }
      });

      // Jika ada, update outlet tersebut menjadi bukan utama
      if (existingMain) {
        await prisma.outlet.update({
          where: {
            id: existingMain.id
          },
          data: {
            isMain: false
          }
        });
      }
    }

    // Buat outlet baru
    const newOutlet = await prisma.outlet.create({
      data: {
        name,
        address,
        city: city || null,
        phone,
        operationalHours,
        isOpen: isOpen !== undefined ? isOpen : true,
        isMain: isMain || false
      }
    });

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Log pembuatan outlet baru
    await logOutlet(
      'create',
      newOutlet.id,
      {
        name: newOutlet.name,
        address: newOutlet.address,
        city: newOutlet.city,
        phone: newOutlet.phone,
        operationalHours: newOutlet.operationalHours,
        isOpen: newOutlet.isOpen,
        isMain: newOutlet.isMain
      },
      userId // Gunakan userId dari token
    );

    return NextResponse.json({
      message: 'Outlet berhasil dibuat',
      outlet: newOutlet
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating outlet:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat outlet' },
      { status: 500 }
    );
  }
}