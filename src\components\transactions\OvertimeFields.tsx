import React from 'react';
import { FormControl, FormLabel, Input, HStack, Text, Box, Tooltip } from '@chakra-ui/react';
import { InfoIcon } from '@chakra-ui/icons';

interface OvertimeFieldsProps {
  overtimeMinutes: number;
  setOvertimeMinutes: (minutes: number) => void;
  overtimeAmount: number;
  setOvertimeAmount: (amount: number) => void;
  readOnly?: boolean;
}

/**
 * Komponen untuk menampilkan dan mengelola field lembur terapis pada form transaksi
 */
const OvertimeFields: React.FC<OvertimeFieldsProps> = ({
  overtimeMinutes,
  setOvertimeMinutes,
  overtimeAmount,
  setOvertimeAmount,
  readOnly = false,
}) => {
  // Handler untuk mengubah jumlah menit lembur dan menghitung otomatis jumlah kompensasi
  const handleOvertimeMinutesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const minutes = parseInt(e.target.value) || 0;
    setOvertimeMinutes(minutes);
    
    // Hitung kompensasi lembur (Rp 100 per menit)
    const amount = minutes * 100;
    setOvertimeAmount(amount);
  };

  // Handler untuk mengubah jumlah kompensasi lembur secara manual (jika diperlukan)
  const handleOvertimeAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const amount = parseFloat(e.target.value) || 0;
    setOvertimeAmount(amount);
  };

  return (
    <Box 
      p={4} 
      borderWidth="1px" 
      borderRadius="md" 
      borderColor="gray.200"
      bg="gray.50"
      mt={4}
    >
      <HStack spacing={4} align="flex-start">
        <FormControl>
          <FormLabel display="flex" alignItems="center">
            Menit Lembur
            <Tooltip 
              label="Jumlah menit lembur terapis pada transaksi ini" 
              placement="top"
            >
              <InfoIcon ml={2} color="blue.500" />
            </Tooltip>
          </FormLabel>
          <Input
            type="number"
            value={overtimeMinutes}
            onChange={handleOvertimeMinutesChange}
            min={0}
            isReadOnly={readOnly}
            bg={readOnly ? "gray.100" : "white"}
          />
        </FormControl>

        <FormControl>
          <FormLabel display="flex" alignItems="center">
            Kompensasi Lembur (Rp)
            <Tooltip 
              label="Jumlah kompensasi lembur terapis (Rp 100 per menit)" 
              placement="top"
            >
              <InfoIcon ml={2} color="blue.500" />
            </Tooltip>
          </FormLabel>
          <Input
            type="number"
            value={overtimeAmount}
            onChange={handleOvertimeAmountChange}
            min={0}
            isReadOnly={readOnly}
            bg={readOnly ? "gray.100" : "white"}
          />
        </FormControl>
      </HStack>

      {overtimeMinutes > 0 && (
        <Text fontSize="sm" color="blue.600" mt={2}>
          Terapis akan menerima kompensasi lembur sebesar Rp {overtimeAmount.toLocaleString()} untuk {overtimeMinutes} menit lembur.
        </Text>
      )}
    </Box>
  );
};

export default OvertimeFields;