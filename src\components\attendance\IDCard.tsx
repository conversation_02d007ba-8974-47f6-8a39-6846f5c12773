import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';

interface IDCardProps {
  name: string;
  role: string;
  id: string;
  qrCode: string;
  outletName?: string;
  type: 'therapist' | 'user';
  profileImage?: string | null;
  customRoleText?: string; // Custom role text prop baru
}

// Fungsi untuk mengambil inisial (2 huruf pertama) dari nama
const getInitials = (name: string): string => {
  if (!name) return '??';
  
  const nameParts = name.trim().split(' ');
  
  if (nameParts.length === 1) {
    // Jika hanya ada satu kata, ambil 2 huruf pertama
    return nameParts[0].substring(0, 2).toUpperCase();
  } else {
    // Jika ada 2 kata atau lebih, ambil huruf pertama dari 2 kata pertama
    return (nameParts[0].charAt(0) + nameParts[1].charAt(0)).toUpperCase();
  }
};

// Format ID ke format yang benar
const formatId = (id: string): string => {
  if (!id) return 'XXXXXXXX';
  
  // Membuat ID custom jika diperlukan
  if (id === 'THERAPIS' || id.toLowerCase() === 'therapis') {
    return 'BT' + Math.floor(100000 + Math.random() * 900000).toString();
  }
  
  // Pastikan ID adalah string dan ambil 8 karakter
  const idStr = id.toString();
  
  // Jika ID terlalu pendek, tambahkan padding karakter
  if (idStr.length < 8) {
    return (idStr + '00000000').substring(0, 8).toUpperCase();
  }
  
  return idStr.substring(0, 8).toUpperCase();
};

const IDCard: React.FC<IDCardProps> = ({ 
  name, 
  role, 
  id, 
  qrCode, 
  type, 
  profileImage,
  customRoleText 
}) => {
  const initials = getInitials(name);
  
  let sourceIdString = id; // Default to using the id prop
  if (type === 'user' && role === 'STAFF') {
    // For STAFF, use their full name (spaces removed) as the source for the displayed ID
    sourceIdString = name.replace(/\s+/g, ''); 
  }
  // The special case for 'THERAPIS' id is already handled inside formatId.
  // If the passed 'id' is 'THERAPIS', formatId will correctly generate a BTxxxxxx number.
  // If it's a specific therapist ID (e.g., "T001"), it will be processed normally by formatId.
  // If it's another user type (e.g., ADMIN), it will also use the 'id' prop as is, unless specified otherwise.

  const formattedId = formatId(sourceIdString);
  
  // Gunakan customRoleText jika disediakan, jika tidak gunakan role default
  const displayRole = customRoleText || role;
  
  return (
    <div className="flex justify-center print:p-0">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-[320px] rounded-xl shadow-lg overflow-hidden border border-gray-200 print:shadow-none bg-white relative"
      >
        {/* Bubble decorations - absolute positioned */}
        <div className="absolute w-full h-full overflow-hidden pointer-events-none">
          {/* Bubble 1 - Big green bubble di pojok kiri bawah */}
          <div 
            className="absolute rounded-full opacity-15"
            style={{
              width: '160px',
              height: '160px',
              bottom: '-40px',
              left: '-40px',
              background: 'radial-gradient(circle at 30% 30%, #22c55e, #15803d)',
              zIndex: 1
            }}
          />
          
          {/* Bubble 2 - Medium yellow bubble di pojok kanan atas */}
          <div 
            className="absolute rounded-full opacity-10"
            style={{
              width: '120px',
              height: '120px',
              top: '70px',
              right: '-30px',
              background: 'radial-gradient(circle at 70% 70%, #eab308, #a16207)',
              zIndex: 1
            }}
          />
          
          {/* Bubble 3 - Small light blue bubble di tengah kanan */}
          <div 
            className="absolute rounded-full opacity-10"
            style={{
              width: '50px',
              height: '50px',
              top: '240px',
              right: '20px',
              background: 'radial-gradient(circle at 30% 30%, #3b82f6, #1d4ed8)',
              zIndex: 1
            }}
          />
        </div>
        
        {/* Header dengan gradien & logo - menggunakan hex color yang kompatibel dengan html2canvas */}
        <div 
          className="h-[86px] flex items-center px-4 relative z-10"
          style={{ 
            background: 'linear-gradient(135deg, #22c55e, #15803d, #eab308)'
          }}
        >
          {/* Logo di kiri */}
          <div className="bg-white rounded-full w-16 h-16 flex justify-center items-center shadow-md">
            <div className="relative w-14 h-14">
              <Image
                src="/images/logo.png"
                alt="Breaktime Logo"
                width={56}
                height={56}
                className="object-contain"
                priority
              />
            </div>
          </div>
          
          {/* Teks di tengah */}
          <div className="flex-1 text-center ml-3">
            <h1 className="text-xl font-bold text-white tracking-wide drop-shadow-sm">BREAKTIME</h1>
            <p className="text-xs text-white opacity-90 drop-shadow-sm">Badan Segar Urusan Lancar</p>
          </div>
        </div>

        {/* Spasi kecil antara header dan avatar */}
        <div className="h-1 bg-white relative z-10"></div>
        
        {/* Avatar Inisial di tengah, lebih rendah dari header */}
        <div className="flex justify-center relative z-10" style={{ marginTop: '-10px' }}>
          <div className="w-20 h-20 rounded-full border-4 border-white flex items-center justify-center shadow-lg overflow-hidden" style={{ 
            backgroundColor: profileImage ? 'transparent' : '#3b82f6', // Menggunakan hex color untuk blue-500
          }}>
            {profileImage ? (
              <div className="w-full h-full relative">
                <Image 
                  src={profileImage}
                  alt={`${name} profile`}
                  fill
                  className="object-cover"
                  priority
                />
              </div>
            ) : (
              <span className="text-2xl font-bold text-white drop-shadow-sm">
                {initials}
              </span>
            )}
          </div>
        </div>

        {/* Informasi profil dengan spacing yang lebih baik */}
        <div className="px-4 pt-5 pb-2 text-center relative z-10">
          <h2 className="text-xl font-bold text-gray-800 mb-1">
            {name}
          </h2>
          <p className="text-sm text-gray-600 mb-2">
            {displayRole}
          </p>
        </div>

        {/* Divider sebelum QR code seperti di gambar */}
        <div className="border-t border-gray-200 mx-6 my-1 relative z-10"></div>

        {/* QR Code */}
        <div className="px-6 py-2 flex justify-center relative z-10">
          <div className="p-2 bg-white border-0 shadow-sm rounded-lg">
            {qrCode && (
              <Image
                src={qrCode}
                alt="ID QR Code"
                width={180}
                height={180}
                className="object-contain"
                unoptimized
                priority
              />
            )}
          </div>
        </div>

        {/* ID Number - sesuai contoh gambar */}
        <div className="px-6 pt-1 pb-4 text-center relative z-10">
          <p className="text-xs text-gray-400 mb-1">ID Number</p>
          <p className="text-base font-mono font-semibold tracking-wider text-gray-800 bg-gray-50 py-1 px-3 rounded-lg inline-block shadow-sm">
            {formattedId}
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default IDCard; 