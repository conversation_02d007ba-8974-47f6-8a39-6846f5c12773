import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { logSystem } from '@/lib/logger';

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { customerIds } = body;

    // Validasi input
    if (!customerIds || !Array.isArray(customerIds) || customerIds.length < 2) {
      return NextResponse.json(
        { error: 'Minimal 2 ID pelanggan diperlukan untuk penggabungan' },
        { status: 400 }
      );
    }

    console.log(`<PERSON><PERSON><PERSON> proses penggabungan ${customerIds.length} pelanggan: ${customerIds.join(', ')}`);

    // 1. Verifikasi semua ID pelanggan valid
    const customers = await prisma.customer.findMany({
      where: {
        id: { in: customerIds },
        isActive: true
      },
      include: {
        _count: {
          select: {
            transactions: true
          }
        }
      }
    });

    if (customers.length !== customerIds.length) {
      return NextResponse.json(
        { error: 'Beberapa ID pelanggan tidak valid atau tidak aktif' },
        { status: 400 }
      );
    }

    // 2. Identifikasi pelanggan dengan transaksi terbanyak sebagai pelanggan utama
    const primaryCustomer = customers.reduce((max, current) =>
      (current._count.transactions > max._count.transactions) ? current : max,
      customers[0]
    );

    console.log(`Pelanggan utama: ${primaryCustomer.name} (ID: ${primaryCustomer.id}) dengan ${primaryCustomer._count.transactions} transaksi`);

    // 3. Identifikasi pelanggan yang akan digabungkan (selain pelanggan utama)
    const customersToMerge = customers.filter(c => c.id !== primaryCustomer.id);
    const customerIdsToMerge = customersToMerge.map(c => c.id);

    console.log(`Pelanggan yang akan digabungkan: ${customersToMerge.map(c => c.name).join(', ')}`);

    // 4. Mulai transaksi database untuk memastikan atomicity
    const result = await prisma.$transaction(async (tx) => {
      // 4.1 Update semua transaksi dari pelanggan yang digabungkan ke pelanggan utama
      await tx.transaction.updateMany({
        where: {
          customerId: { in: customerIdsToMerge }
        },
        data: {
          customerId: primaryCustomer.id
        }
      });

      // 4.2 Update semua booking dari pelanggan yang digabungkan ke pelanggan utama
      await tx.booking.updateMany({
        where: {
          customerId: { in: customerIdsToMerge }
        },
        data: {
          customerId: primaryCustomer.id
        }
      });

      // 4.3 Update semua BookingCustomer dari pelanggan yang digabungkan ke pelanggan utama
      await tx.bookingCustomer.updateMany({
        where: {
          customerId: { in: customerIdsToMerge }
        },
        data: {
          customerId: primaryCustomer.id
        }
      });

      // 4.4 Gabungkan poin loyalitas dari semua pelanggan
      const totalPoints = customers.reduce((sum, customer) => sum + customer.points, 0);

      // 4.5 Gabungkan tag unik dari semua pelanggan
      const allTags = customers.flatMap(customer => customer.tags);
      const uniqueTags = [...new Set(allTags)];

      // 4.6 Update pelanggan utama dengan poin dan tag gabungan
      const updatedPrimaryCustomer = await tx.customer.update({
        where: { id: primaryCustomer.id },
        data: {
          points: totalPoints,
          tags: uniqueTags,
          // Jika ada alamat yang kosong pada pelanggan utama, gunakan dari pelanggan lain
          address: primaryCustomer.address || customersToMerge.find(c => c.address)?.address || null,
          // Jika ada email yang kosong pada pelanggan utama, gunakan dari pelanggan lain
          email: primaryCustomer.email || customersToMerge.find(c => c.email)?.email || null,
          // Jika ada tanggal lahir yang kosong pada pelanggan utama, gunakan dari pelanggan lain
          birthdate: primaryCustomer.birthdate || customersToMerge.find(c => c.birthdate)?.birthdate || null,
          // Jika ada gender yang kosong pada pelanggan utama, gunakan dari pelanggan lain
          gender: primaryCustomer.gender || customersToMerge.find(c => c.gender)?.gender || null,
        }
      });

      // 4.7 Hapus pelanggan yang sudah digabungkan
      await tx.customer.deleteMany({
        where: {
          id: { in: customerIdsToMerge }
        }
      });

      return {
        primaryCustomer: updatedPrimaryCustomer,
        mergedCustomers: customersToMerge,
        totalTransactionsMerged: customersToMerge.reduce((sum, c) => sum + c._count.transactions, 0)
      };
    });

    // 5. Log aktivitas sistem
    await logSystem(
      `Menggabungkan ${customersToMerge.length} pelanggan ke pelanggan ${primaryCustomer.name} (ID: ${primaryCustomer.id})`,
      {
        primaryCustomerId: primaryCustomer.id,
        mergedCustomerIds: customerIdsToMerge,
        action: 'merge',
        module: 'customers'
      }
    );

    return NextResponse.json({
      message: `Berhasil menggabungkan ${customersToMerge.length} pelanggan ke pelanggan ${primaryCustomer.name}`,
      primaryCustomer: result.primaryCustomer,
      mergedCustomers: result.mergedCustomers.map(c => ({ id: c.id, name: c.name })),
      totalTransactionsMerged: result.totalTransactionsMerged
    });
  } catch (error) {
    console.error('Error merging customers:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menggabungkan pelanggan' },
      { status: 500 }
    );
  }
}
