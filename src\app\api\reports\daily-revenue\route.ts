import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import { format, eachDayOfInterval, parseISO } from 'date-fns';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');

    // Ambil parameter tanggal dari query
    const rawStartDate = searchParams.get('startDate');
    const rawEndDate = searchParams.get('endDate');

    if (!rawStartDate || !rawEndDate) {
      throw new Error('Tanggal mulai dan tanggal akhir harus disediakan');
    }

    // Parse tanggal dengan pendekatan yang sama seperti di reports/route.ts
    // Format: YYYY-MM-DD -> parse sebagai tanggal lokal
    const [startYear, startMonth, startDay] = rawStartDate.split('-').map(Number);
    const [endYear, endMonth, endDay] = rawEndDate.split('-').map(Number);

    // Buat tanggal dengan komponen tanggal lokal
    // Bulan dalam JavaScript dimulai dari 0 (Januari = 0)
    const startDate = new Date(startYear, startMonth - 1, startDay, 0, 0, 0, 0);
    const endDate = new Date(endYear, endMonth - 1, endDay, 23, 59, 59, 999);

    // Log untuk debugging
    console.log(`[API GET /api/reports/daily-revenue] Using dates: startDate=${startDate.toISOString()}, endDate=${endDate.toISOString()}`);
    console.log(`[API GET /api/reports/daily-revenue] Local date parts: startDay=${startDate.getDate()}, endDay=${endDate.getDate()}`);

    // Validasi tanggal
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      throw new Error('Format tanggal tidak valid');
    }

    // Filter dasar untuk transaksi berdasarkan tanggal dan outlet terpilih
    const transactionWhere: Prisma.TransactionWhereInput = {
      transactionDate: { gte: startDate, lte: endDate },
      ...(outletId && { outletId: outletId }),
    };

    // Log filter transaksi untuk debugging
    console.log(`[API GET /api/reports/daily-revenue] Transaction filter: ${JSON.stringify({
      transactionDate: { gte: startDate.toISOString(), lte: endDate.toISOString() },
      ...(outletId && { outletId: outletId }),
    })}`);

    // Ambil semua transaksi dalam rentang tanggal
    const transactions = await prisma.transaction.findMany({
      where: transactionWhere,
      select: {
        id: true,
        transactionDate: true,
        totalAmount: true,
      },
    });

    // Buat array tanggal dalam rentang
    const dateRange = eachDayOfInterval({
      start: startDate,
      end: endDate,
    });

    // Inisialisasi data pendapatan harian
    const dailyRevenue = dateRange.map(date => {
      const dateString = format(date, 'yyyy-MM-dd');
      return {
        date: dateString,
        revenue: 0,
      };
    });

    // Hitung pendapatan untuk setiap hari
    transactions.forEach(transaction => {
      const transactionDate = new Date(transaction.transactionDate);
      const dateString = format(transactionDate, 'yyyy-MM-dd');

      // Cari indeks tanggal dalam array dailyRevenue
      const dateIndex = dailyRevenue.findIndex(item => item.date === dateString);

      if (dateIndex !== -1) {
        dailyRevenue[dateIndex].revenue += transaction.totalAmount || 0;
        console.log(`[API GET /api/reports/daily-revenue] Added revenue for ${dateString}: ${transaction.totalAmount || 0}, total now: ${dailyRevenue[dateIndex].revenue}`);
      } else {
        console.log(`[API GET /api/reports/daily-revenue] Warning: Transaction date ${dateString} not found in date range`);
      }
    });

    // Log hasil akhir untuk debugging
    console.log(`[API GET /api/reports/daily-revenue] Final daily revenue data: ${JSON.stringify(dailyRevenue)}`);


    return NextResponse.json({
      message: 'Data pendapatan harian berhasil diambil',
      dailyRevenue,
    });

  } catch (error: unknown) {
    console.error('[API GET /api/reports/daily-revenue] Error:', error);

    let errorMessage = 'Terjadi kesalahan saat mengambil data pendapatan harian';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat mengambil data pendapatan harian',
      dailyRevenue: []
    }, { status: statusCode });
  }
}
