'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Tab } from '@headlessui/react';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { Toaster } from 'react-hot-toast';
import QRScanner from '@/components/attendance/QRScanner';
import Link from 'next/link';

export default function ScanPage() {
  const [recentScans, setRecentScans] = useState<any[]>([]);

  const handleScanSuccess = (data: string) => {
    try {
      // Update recent scans list
      const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
      const newScan = {
        id: parsedData.id,
        name: parsedData.name,
        type: parsedData.type,
        timestamp: new Date().toISOString()
      };
      
      setRecentScans(prev => [newScan, ...prev].slice(0, 10)); // Keep only 10 recent scans
    } catch (error) {
      console.error('Error handling scan success:', error);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Toaster position="top-right" />
      
      <div className="mb-6 flex items-center">
        <Link href="/dashboard/absensi" className="mr-4">
          <ArrowLeftIcon className="w-5 h-5 text-gray-600 hover:text-gray-900" />
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Scan Absensi</h1>
          <p className="text-gray-600">Scan QR code untuk absensi terapis dan staff</p>
        </div>
      </div>
      
      <Tab.Group>
        <Tab.List className="flex rounded-lg bg-gray-100 p-1 max-w-md mx-auto mb-8">
          <Tab as={motion.div} 
            whileHover={{ scale: 1.03 }} 
            whileTap={{ scale: 0.97 }}
            className={({ selected }) => 
              `flex-1 py-2 px-4 text-center rounded-md font-medium focus:outline-none ${
                selected 
                  ? 'bg-emerald-500 text-white shadow-md' 
                  : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
              }`
            }
          >
            Absen Masuk
          </Tab>
          <Tab as={motion.div} 
            whileHover={{ scale: 1.03 }} 
            whileTap={{ scale: 0.97 }}
            className={({ selected }) => 
              `flex-1 py-2 px-4 text-center rounded-md font-medium focus:outline-none ${
                selected 
                  ? 'bg-blue-500 text-white shadow-md' 
                  : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
              }`
            }
          >
            Absen Keluar
          </Tab>
        </Tab.List>
        
        <Tab.Panels className="max-w-lg mx-auto">
          <Tab.Panel>
            <QRScanner onScanSuccess={handleScanSuccess} scanType="IN" />
          </Tab.Panel>
          <Tab.Panel>
            <QRScanner onScanSuccess={handleScanSuccess} scanType="OUT" />
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
      
      {recentScans.length > 0 && (
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-10 max-w-lg mx-auto"
        >
          <h2 className="text-lg font-semibold mb-3 text-gray-800">Riwayat Scan Terbaru</h2>
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nama
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tipe
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Waktu
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {recentScans.map((scan, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {scan.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {scan.type === 'therapist' ? 'Terapis' : 'Staff'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(scan.timestamp).toLocaleTimeString('id-ID')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
} 