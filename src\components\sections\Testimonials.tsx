'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';
import { FiStar } from 'react-icons/fi';

export default function Testimonials() {
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number = 1) => ({
      opacity: 1,
      y: 0,
      transition: { staggerChildren: 0.1, delayChildren: i * 0.1, duration: 0.5 }
    })
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  return (
    <motion.section 
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      variants={staggerContainer}
      className="py-20 px-4 md:px-20"
    >
      <motion.div variants={fadeIn} className="text-center mb-16">
        <p className="text-secondary uppercase font-bold tracking-widest mb-2">TESTIMONI</p>
        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800">
          Apa Kata Pelanggan <br className="hidden md:block" />Tentang <PERSON>
        </h2>
      </motion.div>
      
      <div className="relative max-w-4xl mx-auto">
        <div className="carousel w-full">
          {[1, 2, 3].map((num, index) => (
            <div key={num} id={`testi${num}`} className="carousel-item w-full">
              <motion.div 
                variants={fadeIn} 
                custom={index + 1}
                className="card w-full bg-white shadow-lg rounded-3xl p-8 md:p-10 border border-gray-100"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                  <div>
                    <Image 
                      src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=400&h=400&fit=crop" 
                      alt={`Pelanggan ${num}`}
                      width={300}
                      height={300}
                      className="rounded-3xl shadow-md w-full h-auto"
                    />
                  </div>
                  <div>
                    <div className="flex mb-4">
                      {[...Array(5)].map((_, i) => 
                        <FiStar key={i} className="text-secondary" size={20} />
                      )}
                    </div>
                    <p className="italic text-gray-600 mb-6">&quot;Pengalaman pijat yang luar biasa! Terapis sangat profesional dan ruangannya nyaman. Saya merasa segar kembali setelah sesi pijat.&quot;</p>
                    <h3 className="font-bold text-xl">Ahmad Fauzi</h3>
                    <p className="text-gray-500">Pengusaha</p>
                  </div>
                </div>
              </motion.div>
            </div> 
          ))}
        </div> 
        
        {/* Carousel Navigation */}
        <div className="absolute flex justify-between transform -translate-y-1/2 left-1 right-1 md:left-5 md:right-5 top-1/2">
          <a href="#testi3" className="btn btn-circle btn-sm md:btn-md btn-ghost bg-white shadow-md opacity-80 hover:opacity-100">❮</a>
          <a href="#testi2" className="btn btn-circle btn-sm md:btn-md btn-ghost bg-white shadow-md opacity-80 hover:opacity-100">❯</a>
        </div>
      </div>

      {/* Logos Section */}
      <div className="mt-20">
        <motion.p 
          variants={fadeIn}
          className="text-center text-gray-500 mb-10"
        >
          DIPERCAYA OLEH RATUSAN PELANGGAN DARI PERUSAHAAN TERKEMUKA
        </motion.p>
        <motion.div 
          variants={fadeIn}
          className="flex flex-wrap justify-center items-center gap-8 md:gap-12 opacity-60"
        >
          {['Google', 'Amazon', 'Microsoft', 'Slack', 'Shopify'].map((brand, i) => (
            <div key={i} className="grayscale hover:grayscale-0 transition-all duration-300">
              <p className="font-bold text-xl text-gray-700">{brand}</p>
            </div>
          ))}
        </motion.div>
      </div>
    </motion.section>
  );
}
