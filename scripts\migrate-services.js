// Script untuk memigrasikan layanan dari struktur lama ke struktur baru
// Jalankan dengan: node scripts/migrate-services.js

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function migrateServices() {
  console.log('Me<PERSON>lai migrasi layanan...');

  try {
    // 1. Ambil semua layanan dari database
    const services = await prisma.service.findMany({
      include: {
        outlets: true
      }
    });

    console.log(`Ditemukan ${services.length} layanan di database.`);

    // 2. Ambil semua outlet dari database
    const outlets = await prisma.outlet.findMany({
      where: {
        isOpen: true
      }
    });

    if (outlets.length === 0) {
      console.log('Tidak ada outlet yang ditemukan. Migrasi dibatalkan.');
      return;
    }

    console.log(`Ditemukan ${outlets.length} outlet aktif.`);

    // 3. Untuk setiap layanan yang belum memiliki relasi outlet, tambahkan ke outlet pertama
    let migratedCount = 0;
    for (const service of services) {
      // Jika layanan belum memiliki relasi outlet
      if (service.outlets.length === 0) {
        // Gunakan outlet pertama (biasanya outlet utama)
        const mainOutlet = outlets.find(o => o.isMain) || outlets[0];

        // Buat relasi baru
        await prisma.serviceOutlet.create({
          data: {
            serviceId: service.id,
            outletId: mainOutlet.id
          }
        });

        console.log(`Menambahkan layanan "${service.name}" ke outlet "${mainOutlet.name}".`);
        migratedCount++;
      }
    }

    console.log(`Berhasil memigrasikan ${migratedCount} layanan ke struktur baru.`);
    console.log('Migrasi selesai!');
  } catch (error) {
    console.error('Terjadi kesalahan saat migrasi:', error);
  } finally {
    await prisma.$disconnect();
  }
}

migrateServices();
