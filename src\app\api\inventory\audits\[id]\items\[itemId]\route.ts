import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserIdFromToken } from '@/lib/auth-utils';

// PUT - Update item audit (untuk proses audit)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; itemId: string }> }
) {
  try {
    const userId = getUserIdFromToken(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id, itemId } = await params;
    const body = await request.json();
    const {
      actualGood,
      actualDamaged,
      actualLost,
      notes
    } = body;

    // Cek apakah audit item ada
    const existingAuditItem = await prisma.inventoryAuditItem.findFirst({
      where: {
        auditId: id,
        itemId: itemId
      },
      include: {
        audit: true,
        item: true
      }
    });

    if (!existingAuditItem) {
      return NextResponse.json(
        { error: 'Item audit tidak ditemukan' },
        { status: 404 }
      );
    }

    // Cek apakah audit masih bisa diupdate
    if (existingAuditItem.audit.status === 'COMPLETED') {
      return NextResponse.json(
        { error: 'Audit sudah selesai, tidak dapat diubah' },
        { status: 400 }
      );
    }

    // Validasi input
    if (actualGood !== undefined && actualGood < 0) {
      return NextResponse.json(
        { error: 'Jumlah baik tidak boleh negatif' },
        { status: 400 }
      );
    }

    if (actualDamaged !== undefined && actualDamaged < 0) {
      return NextResponse.json(
        { error: 'Jumlah rusak tidak boleh negatif' },
        { status: 400 }
      );
    }

    if (actualLost !== undefined && actualLost < 0) {
      return NextResponse.json(
        { error: 'Jumlah hilang tidak boleh negatif' },
        { status: 400 }
      );
    }

    // Hitung variance
    const goodVariance = actualGood !== undefined 
      ? actualGood - existingAuditItem.expectedGood 
      : null;
    
    const damagedVariance = actualDamaged !== undefined 
      ? actualDamaged - existingAuditItem.expectedDamaged 
      : null;
    
    const lostVariance = actualLost !== undefined 
      ? actualLost - existingAuditItem.expectedLost 
      : null;

    // Update audit item
    const auditItem = await prisma.inventoryAuditItem.update({
      where: { id: existingAuditItem.id },
      data: {
        actualGood: actualGood !== undefined ? actualGood : existingAuditItem.actualGood,
        actualDamaged: actualDamaged !== undefined ? actualDamaged : existingAuditItem.actualDamaged,
        actualLost: actualLost !== undefined ? actualLost : existingAuditItem.actualLost,
        variance: goodVariance || damagedVariance || lostVariance || 0,
        notes: notes?.trim() || existingAuditItem.notes,
        isChecked: true,
        checkedAt: new Date(),
        checkedById: userId
      },
      include: {
        item: {
          include: {
            category: true
          }
        },
        checkedBy: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    // Update status audit menjadi IN_PROGRESS jika masih PENDING
    if (existingAuditItem.audit.status === 'PENDING') {
      await prisma.inventoryAudit.update({
        where: { id },
        data: {
          status: 'IN_PROGRESS',
          startedAt: new Date()
        }
      });
    }

    return NextResponse.json({
      message: 'Item audit berhasil diperbarui',
      auditItem
    });
  } catch (error) {
    console.error('Error updating audit item:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui item audit' },
      { status: 500 }
    );
  }
}

// PATCH - Update item audit (untuk proses audit)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; itemId: string }> }
) {
  try {
    const userId = getUserIdFromToken(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id, itemId } = await params;
    const body = await request.json();
    const {
      actualGood,
      actualDamaged,
      actualLost,
      notes
    } = body;

    // Cek apakah audit item ada
    const existingAuditItem = await prisma.inventoryAuditItem.findFirst({
      where: {
        auditId: id,
        itemId: itemId
      },
      include: {
        audit: true,
        item: true
      }
    });
    
    if (!existingAuditItem) {
      return NextResponse.json(
        { error: 'Item audit tidak ditemukan' },
        { status: 404 }
      );
    }

    // Cek apakah audit masih bisa diupdate
    if (existingAuditItem.audit.status === 'COMPLETED') {
      return NextResponse.json(
        { error: 'Audit sudah selesai, tidak dapat diubah' },
        { status: 400 }
      );
    }

    // Validasi input
    if (actualGood !== undefined && actualGood < 0) {
      return NextResponse.json(
        { error: 'Jumlah baik tidak boleh negatif' },
        { status: 400 }
      );
    }

    if (actualDamaged !== undefined && actualDamaged < 0) {
      return NextResponse.json(
        { error: 'Jumlah rusak tidak boleh negatif' },
        { status: 400 }
      );
    }

    if (actualLost !== undefined && actualLost < 0) {
      return NextResponse.json(
        { error: 'Jumlah hilang tidak boleh negatif' },
        { status: 400 }
      );
    }

    // Hitung variance
    const goodVariance = actualGood !== undefined 
      ? actualGood - existingAuditItem.expectedGood 
      : null;
    
    const damagedVariance = actualDamaged !== undefined 
      ? actualDamaged - existingAuditItem.expectedDamaged 
      : null;
    
    const lostVariance = actualLost !== undefined 
      ? actualLost - existingAuditItem.expectedLost 
      : null;

    // Update audit item
    const auditItem = await prisma.inventoryAuditItem.update({
      where: { id: existingAuditItem.id },
      data: {
        actualGood: actualGood !== undefined ? actualGood : existingAuditItem.actualGood,
        actualDamaged: actualDamaged !== undefined ? actualDamaged : existingAuditItem.actualDamaged,
        actualLost: actualLost !== undefined ? actualLost : existingAuditItem.actualLost,
        variance: goodVariance || damagedVariance || lostVariance || 0,
        notes: notes?.trim() || existingAuditItem.notes,
        isChecked: true,
        checkedAt: new Date(),
        checkedById: userId
      },
      include: {
        item: {
          include: {
            category: true
          }
        },
        checkedBy: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    // Update status audit menjadi IN_PROGRESS jika masih PENDING
    if (existingAuditItem.audit.status === 'PENDING') {
      await prisma.inventoryAudit.update({
        where: { id },
        data: {
          status: 'IN_PROGRESS',
          startedAt: new Date()
        }
      });
    }

    return NextResponse.json({
      message: 'Item audit berhasil diperbarui',
      auditItem
    });
  } catch (error) {
    console.error('Error updating audit item:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui item audit' },
      { status: 500 }
    );
  }
}