---
description:
globs:
alwaysApply: true
---

# File Konfigurasi Utama ⚙️

Berikut adalah file konfigurasi penting dalam proyek ini:

- **Next.js**: Konfigurasi Next.js ada di `[next.config.mjs](mdc:next.config.mjs)`. (Perhatikan ada juga `next.config.js` dan `next.config.ts`, pastikan mana yang aktif).
- **Tailwind CSS**: Konfigurasi Tailwind CSS ada di `[tailwind.config.ts](mdc:tailwind.config.ts)`. (Perhatikan ada juga `tailwind.config.js`).
- **TypeScript**: Konfigurasi TypeScript ada di `[tsconfig.json](mdc:tsconfig.json)`.
- **ESLint**: Konfigurasi linting ada di `[eslint.config.mjs](mdc:eslint.config.mjs)` dan/atau `[.eslintrc.json](mdc:.eslintrc.json)`.
- **Package Manager**: Dependensi dan skrip proyek didefinisikan dalam `[package.json](mdc:package.json)`. File lock adalah `[bun.lock](mdc:bun.lock)`.
- **Environment Variables**: Variabel lingkungan disimpan di file `.env` (tidak terlihat di sini, tapi biasanya ada). Pastikan ada file `.env.example` atau dokumentasi tentang variabel yang diperlukan.
- **PostCSS**: Konfigurasi PostCSS ada di `[postcss.config.mjs](mdc:postcss.config.mjs)`.
