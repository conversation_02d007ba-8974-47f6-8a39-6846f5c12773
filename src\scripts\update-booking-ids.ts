import { prisma } from '@/lib/prisma';

/**
 * <PERSON>ript untuk memperbarui ID booking yang sudah ada menjadi format yang lebih singkat
 * Jalankan dengan: npx ts-node -r tsconfig-paths/register src/scripts/update-booking-ids.ts
 */
async function updateBookingIds() {
  try {
    console.log('<PERSON><PERSON><PERSON> proses pembaruan ID booking...');
    
    // Ambil semua booking yang belum memiliki displayId
    const bookings = await prisma.booking.findMany({
      where: {
        displayId: null
      },
      orderBy: {
        createdAt: 'asc'
      }
    });
    
    console.log(`Ditemukan ${bookings.length} booking yang perlu diperbarui.`);
    
    let counter = 1;
    for (const booking of bookings) {
      // Buat ID booking dengan format B0000001, B0000002, dst.
      const displayId = `B${String(counter).padStart(7, '0')}`;
      
      // Perbarui booking
      await prisma.booking.update({
        where: {
          id: booking.id
        },
        data: {
          displayId
        }
      });
      
      console.log(`Booking ${booking.id} diperbarui menjadi ${displayId}`);
      counter++;
    }
    
    console.log('Proses pembaruan ID booking selesai.');
  } catch (error) {
    console.error('Terjadi kesalahan:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Jalankan fungsi
updateBookingIds();
