-- <PERSON><PERSON><PERSON> untuk implementasi fitur lembur terapis

-- Menambahkan kolom overtimeMinutes dan overtimeAmount ke tabel Transaction
ALTER TABLE "Transaction" ADD COLUMN "overtimeMinutes" INTEGER DEFAULT 0;
ALTER TABLE "Transaction" ADD COLUMN "overtimeAmount" DECIMAL(10, 2) DEFAULT 0;

-- Menambahkan kolom totalOvertimeMinutes dan totalOvertimeEarnings ke tabel Therapist
ALTER TABLE "Therapist" ADD COLUMN "totalOvertimeMinutes" INTEGER DEFAULT 0;
ALTER TABLE "Therapist" ADD COLUMN "totalOvertimeEarnings" DECIMAL(10, 2) DEFAULT 0;

-- Membuat indeks untuk mempercepat query yang melibatkan lembur
CREATE INDEX "Transaction_overtimeMinutes_idx" ON "Transaction"("overtimeMinutes");

-- Komentar untuk dokumentasi
COMMENT ON COLUMN "Transaction"."overtimeMinutes" IS 'Jumlah menit lembur terapis pada transaksi ini';
COMMENT ON COLUMN "Transaction"."overtimeAmount" IS 'Jumlah kompensasi lembur terapis pada transaksi ini (Rp 100 per menit)';
COMMENT ON COLUMN "Therapist"."totalOvertimeMinutes" IS 'Total menit lembur terapis sepanjang waktu';
COMMENT ON COLUMN "Therapist"."totalOvertimeEarnings" IS 'Total pendapatan lembur terapis sepanjang waktu';